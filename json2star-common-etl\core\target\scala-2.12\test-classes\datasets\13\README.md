# Readme

## Closure for segment disparition

The files in the data/pnr and data/tkt folders correspond to the following versions of PNR 22USLC and TKT 0142172648268:

```
PNR v15
PNR v20
TKT v9
```

This data has been extracted from EY UAT data.

in `PNR v15`, we have the following segments :
```
2022-09-22-YYZ-AUH
2022-09-23-AUH-ICN
2022-09-30-ICN-YVR
2022-09-30-YVR-YEG
2022-10-01-YEG-YYZ
```
in `PNR v20`, the number of segments will decrease :
```
2022-09-22-YYZ-AUH
2022-09-23-AUH-ICN
2022-09-30-ICN-YVR
```

To make efficient test of closure, you have to feed input json progressively :
 - First you add PNR v15 and TKT v9 and you make the first run
 - Then you add PNR v20 and you run again to update results