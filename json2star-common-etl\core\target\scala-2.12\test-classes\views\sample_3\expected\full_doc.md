
### Tables overview
#### Fact Tables
| Table | Description | GDPR Zone | Granularity | Primary key | Subdomain |
| ---- | ---- | ---- | ---- | ---- | ---- |
| FACT_A | Latest view of FACT_A_HISTO | green | Same as FACT_A_HISTO, considering only the latest version | VERSION-FACT_A_ID |  |
| FACT_A_HISTO |  |  |  | VERSION-FACT_A_ID |  |
| FACT_B | Latest view of FACT_B_HISTO | green | Same as FACT_B_HISTO, considering only the latest version | VERSION-FACT_B_ID |  |
| FACT_B_HISTO |  |  |  | VERSION-FACT_B_ID |  |
| FACT_RESERVATION | Latest view of FACT_RESERVATION_HISTO | red | Same as FACT_RESERVATION_HISTO, considering only the latest version | RESERVATION_ID-VERSION |  |
| FACT_RESERVATION_HISTO | It contains information related to the booking on PNR-level. | red | 1 PNR, version | RESERVATION_ID-VERSION |  |

#### Dimension Tables
| Table | Description | GDPR Zone | Granularity | Primary key |
| ---- | ---- | ---- | ---- | ---- |
| DIM_POINT_OF_SALE |  | green |  | POINT_OF_SALE_ID |
| DIM_TABLE |  | green |  | TABLE_ID |

#### Association Tables
No Table is available
### Tables details with fields
#### Fact Tables Fields
| Table | Field | Description | Example | GDPR Zone | Format | Mandatory | In FK to | Source Path |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| FACT_A | VERSION |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.version</p> |
| FACT_A | FACT_A_ID |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.id2</p> |
| FACT_A | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| FACT_A_HISTO | VERSION |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.version</p> |
| FACT_A_HISTO | FACT_A_ID |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.id2</p> |
| FACT_A_HISTO | DATE_BEGIN |  |  |  | TIMESTAMP | N |  | <p>$.dummy.lastModification.dateTime</p> |
| FACT_A_HISTO | DATE_END |  |  |  | TIMESTAMP | N |  |  |
| FACT_A_HISTO | IS_LAST_VERSION |  |  |  | BOOLEAN | N |  |  |
| FACT_A_HISTO | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| FACT_B | VERSION |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.version</p> |
| FACT_B | FACT_B_ID |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.id2</p> |
| FACT_B | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| FACT_B_HISTO | VERSION |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.version</p> |
| FACT_B_HISTO | FACT_B_ID |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.id2</p> |
| FACT_B_HISTO | DATE_BEGIN |  |  |  | TIMESTAMP | N |  | <p>$.dummy.lastModification.dateTime</p> |
| FACT_B_HISTO | DATE_END |  |  |  | TIMESTAMP | N |  |  |
| FACT_B_HISTO | IS_LAST_VERSION |  |  |  | BOOLEAN | N |  |  |
| FACT_B_HISTO | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| FACT_RESERVATION | RESERVATION_ID |  |  | <p>green</p> | STRING | PK |  | <p>hashM(.id)</p> |
| FACT_RESERVATION | POINT_OF_SALE_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | DIM_POINT_OF_SALE | <p>hashM(.owner.office.id - .owner.office.iataNumber - .owner.office.systemCode - .owner.office.agentType - .owner.login.cityCode - .owner.login.countryCode - .owner.login.numericSign - .owner.login.initials - .owner.login.dutyCode)</p> |
| FACT_RESERVATION | POINT_OF_SALE_OWNER_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | DIM_POINT_OF_SALE | <p>hashM(.owner.office.id - .owner.office.iataNumber - .owner.office.systemCode - .owner.office.agentType - .owner.login.cityCode - .owner.login.countryCode - .owner.login.numericSign - .owner.login.initials - .owner.login.dutyCode)</p> |
| FACT_RESERVATION | POINT_OF_SALE_CREATION_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | DIM_POINT_OF_SALE | <p>hashM(.creation.pointOfSale.office.id - .creation.pointOfSale.office.iataNumber - .creation.pointOfSale.office.systemCode - .creation.pointOfSale.office.agentType - .creation.pointOfSale.login.cityCode - .creation.pointOfSale.login.countryCode - .creation.pointOfSale.login.numericSign - .creation.pointOfSale.login.initials - .creation.pointOfSale.login.dutyCode)</p> |
| FACT_RESERVATION | POINT_OF_SALE_LAST_UPDATE_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | DIM_POINT_OF_SALE, DUMMY | <p>hashM(.lastModification.pointOfSale.office.id - .lastModification.pointOfSale.office.iataNumber - .lastModification.pointOfSale.office.systemCode - .lastModification.pointOfSale.office.agentType - .lastModification.pointOfSale.login.cityCode - .lastModification.pointOfSale.login.countryCode - .lastModification.pointOfSale.login.numericSign - .lastModification.pointOfSale.login.initials - .lastModification.pointOfSale.login.dutyCode)</p> |
| FACT_RESERVATION | MY_TABLE_CODE |  |  | <p>red</p> | STRING | N |  | <p>.table.id</p> |
| FACT_RESERVATION | TABLE |  |  |  | STRING | N |  | <p>.table.id</p> |
| FACT_RESERVATION | COLUMN_NAME_A |  |  |  | STRING | N |  | <p>.table.id</p> |
| FACT_RESERVATION | VERSION |  |  | <p>green</p> | STRING | PK | FACT_A, FACT_B | <p>.version</p> |
| FACT_RESERVATION | FACT_A_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | FACT_A | <p>hashM(.ida2)</p> |
| FACT_RESERVATION | FACT_B_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | FACT_B | <p>hashM(.idb2)</p> |
| FACT_RESERVATION | PNR_CREATION_DATE |  |  |  | TIMESTAMP | N |  | <p>.creation.dateTime</p> |
| FACT_RESERVATION | MY_TABLE_CODE_ID | <p>Hashed Foreign Key</p> |  | <p>green</p> | BIGINT | N | DIM_TABLE | <p>hashXS(.table.id)</p> |
| FACT_RESERVATION | TABLE_ID | <p>Hashed Foreign Key</p> |  | <p>green</p> | BIGINT | N | DIM_TABLE | <p>hashXS(.table.id)</p> |
| FACT_RESERVATION | COLUMN_ID_A | <p>Hashed Foreign Key</p> |  | <p>green</p> | STRING | N |  | <p>hashXS(.table.id)</p> |
| FACT_RESERVATION | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| FACT_RESERVATION_HISTO | RESERVATION_ID |  |  | <p>green</p> | STRING | PK |  | <p>hashM(.id)</p> |
| FACT_RESERVATION_HISTO | POINT_OF_SALE_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | DIM_POINT_OF_SALE | <p>hashM(.owner.office.id - .owner.office.iataNumber - .owner.office.systemCode - .owner.office.agentType - .owner.login.cityCode - .owner.login.countryCode - .owner.login.numericSign - .owner.login.initials - .owner.login.dutyCode)</p> |
| FACT_RESERVATION_HISTO | POINT_OF_SALE_OWNER_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | DIM_POINT_OF_SALE | <p>hashM(.owner.office.id - .owner.office.iataNumber - .owner.office.systemCode - .owner.office.agentType - .owner.login.cityCode - .owner.login.countryCode - .owner.login.numericSign - .owner.login.initials - .owner.login.dutyCode)</p> |
| FACT_RESERVATION_HISTO | POINT_OF_SALE_CREATION_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | DIM_POINT_OF_SALE | <p>hashM(.creation.pointOfSale.office.id - .creation.pointOfSale.office.iataNumber - .creation.pointOfSale.office.systemCode - .creation.pointOfSale.office.agentType - .creation.pointOfSale.login.cityCode - .creation.pointOfSale.login.countryCode - .creation.pointOfSale.login.numericSign - .creation.pointOfSale.login.initials - .creation.pointOfSale.login.dutyCode)</p> |
| FACT_RESERVATION_HISTO | POINT_OF_SALE_LAST_UPDATE_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | DIM_POINT_OF_SALE, DUMMY | <p>hashM(.lastModification.pointOfSale.office.id - .lastModification.pointOfSale.office.iataNumber - .lastModification.pointOfSale.office.systemCode - .lastModification.pointOfSale.office.agentType - .lastModification.pointOfSale.login.cityCode - .lastModification.pointOfSale.login.countryCode - .lastModification.pointOfSale.login.numericSign - .lastModification.pointOfSale.login.initials - .lastModification.pointOfSale.login.dutyCode)</p> |
| FACT_RESERVATION_HISTO | MY_TABLE_CODE |  |  | <p>red</p> | STRING | N |  | <p>.table.id</p> |
| FACT_RESERVATION_HISTO | TABLE |  |  |  | STRING | N |  | <p>.table.id</p> |
| FACT_RESERVATION_HISTO | COLUMN_NAME_A |  |  |  | STRING | N |  | <p>.table.id</p> |
| FACT_RESERVATION_HISTO | VERSION |  |  | <p>green</p> | STRING | PK | FACT_A_HISTO, FACT_B_HISTO | <p>.version</p> |
| FACT_RESERVATION_HISTO | FACT_A_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | FACT_A_HISTO | <p>hashM(.ida2)</p> |
| FACT_RESERVATION_HISTO | FACT_B_ID | <p>Hashed foreign key</p> |  | <p>green</p> | STRING | N | FACT_B_HISTO | <p>hashM(.idb2)</p> |
| FACT_RESERVATION_HISTO | PNR_CREATION_DATE |  |  |  | TIMESTAMP | N |  | <p>.creation.dateTime</p> |
| FACT_RESERVATION_HISTO | DATE_BEGIN |  |  |  | TIMESTAMP | N |  | <p>.lastModification.dateTime</p> |
| FACT_RESERVATION_HISTO | DATE_END |  |  |  | TIMESTAMP | N |  |  |
| FACT_RESERVATION_HISTO | IS_LAST_VERSION |  |  |  | BOOLEAN | N |  |  |
| FACT_RESERVATION_HISTO | MY_TABLE_CODE_ID | <p>Hashed Foreign Key</p> |  | <p>green</p> | BIGINT | N | DIM_TABLE | <p>hashXS(.table.id)</p> |
| FACT_RESERVATION_HISTO | TABLE_ID | <p>Hashed Foreign Key</p> |  | <p>green</p> | BIGINT | N | DIM_TABLE | <p>hashXS(.table.id)</p> |
| FACT_RESERVATION_HISTO | COLUMN_ID_A | <p>Hashed Foreign Key</p> |  | <p>green</p> | STRING | N |  | <p>hashXS(.table.id)</p> |
| FACT_RESERVATION_HISTO | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |

#### Dimension Tables Fields
| Table | Field | Description | Example | GDPR Zone | Format | Mandatory | In FK to | Source Path |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| DIM_POINT_OF_SALE | POINT_OF_SALE_ID |  |  | <p>green</p> | STRING | PK |  | <p>$.dummy.id</p> |
| DIM_POINT_OF_SALE | OFFICE_IATA_NUMBER |  |  | <p>green</p> | STRING | N |  | <p>$.dummy.iataNumber</p> |
| DIM_POINT_OF_SALE | OFFICE_SYSTEM_CODE |  |  | <p>green</p> | STRING | N |  | <p>$.dummy.systemCode</p> |
| DIM_POINT_OF_SALE | OFFICE_AGENT_TYPE |  |  | <p>green</p> | STRING | N |  | <p>$.dummy.agentType</p> |
| DIM_POINT_OF_SALE | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| DIM_TABLE | TABLE_ID |  |  | <p>green</p> | BIGINT | PK |  | <p>hashXS($.dummy.table.id)</p> |
| DIM_TABLE | TABLE_CODE |  |  | <p>green</p> | STRING | N |  | <p>$.dummy.table.id</p> |
| DIM_TABLE | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |

#### Association Tables Fields
No Table is available

