-- Databricks notebook source
--The threshold value can be set individually for each validation test.
SET db.task = "dcspax-global-stats-checks.sql";
SET db.validation_database = ${valDatabase};
SET db.validation_table = ${valTableName};
SET db.now_datetime = current_timestamp();
SET db.table_fields = (domain, domain_version, customer, phase, test_time, task, covered_functional_case, status, nb_failed_records, nb_total_records, fail_ratio, result_details);

--test 1
--set variables for this test
SET db.func_case = "Test 1 - Check the average number of created passengers";
SET db.NB_PAX_THRESHOLD = 100;

WITH pax_stats AS (
  SELECT avg(T.nb) average_nb_pax
  FROM
  (
    SELECT 
      to_date(LOAD_DATE), 
      count(distinct REFERENCE_KEY) as nb
    from ${DB_NAME}.fact_passenger_histo
    group by to_date(LOAD_DATE)
  ) T
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select 
  "${DOMAIN}",
  "${DOMAIN_VERSION}",
  "${CUSTOMER}",
  "${PHASE}",
  ${db.now_datetime},
  ${db.task},
  ${db.func_case},
  (average_nb_pax > ${db.NB_PAX_THRESHOLD}) as status,
  (CASE WHEN average_nb_pax > ${db.NB_PAX_THRESHOLD} THEN 0 else 1 end) as nb_failed_records,
  1 as nb_total_records, 
  (CASE WHEN average_nb_pax > ${db.NB_PAX_THRESHOLD} THEN 0 else 1 end) as fail_ratio, 
  concat("Average number of passengers : ", average_nb_pax) as result_details
from pax_stats;

--test 2
--set variables for this test
SET db.func_case = "Test 2 - Check the average number of segments by passengers";
SET db.threshold_min = 1;
SET db.threshold_max = 3;

WITH pax_stats AS (
  select avg(T.nb) average_nb_seg_by_pax
  from
  (
    select 
      SPLIT(reference_key, "-")[0] passenger_ref,
      count(distinct REFERENCE_KEY) nb
    from ${DB_NAME}.fact_flight_segment_histo
    group by SPLIT(reference_key, "-")[0]
  ) T
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select 
  "${DOMAIN}",
  "${DOMAIN_VERSION}",
  "${CUSTOMER}",
  "${PHASE}",
  ${db.now_datetime},
  ${db.task},
  ${db.func_case},
  (average_nb_seg_by_pax >= ${db.threshold_min} and average_nb_seg_by_pax < ${db.threshold_max}) as status,
  (CASE WHEN (average_nb_seg_by_pax >= ${db.threshold_min} and average_nb_seg_by_pax < ${db.threshold_max}) THEN 0 else 1 end) as nb_failed_records,
  1 as nb_total_records, 
  (CASE WHEN (average_nb_seg_by_pax >= ${db.threshold_min} and average_nb_seg_by_pax < ${db.threshold_max}) THEN 0 else 1 end) as fail_ratio, 
  concat("Average number of segments by passengers : ", average_nb_seg_by_pax, ". Expected >= ${db.threshold_min} and < ${db.threshold_max}") as result_details
from pax_stats;