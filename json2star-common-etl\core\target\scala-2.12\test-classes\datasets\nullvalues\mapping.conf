{"tables": [{"name": "FACT_RESERVATION_HISTO", "mapping": {"merge": {"key-columns": ["RESERVATION_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}], "columns": [{"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "RESERVATION_ID", "pit-version": "VERSION", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_VERSION"}}}}, {"name": "FACT_NULLVALUES", "mapping": {"merge": {"key-columns": ["ITEM_ID"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"item": "$.customTestItems[*]"}]}], "columns": [{"name": "ITEM_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"item": "$.itemId"}]}, "expr": "hashM({0})"}, {"name": "VAL1_MANDATORY_NO_EXPR", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.val1"}]}}, {"name": "VAL2_MANDATORY_EXPR_HASHXS", "column-type": "longColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.val2"}]}, "expr": "hashXS({0})"}, {"name": "VAL3_MANDATORY_EXPR_HASHS", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.val3"}]}, "expr": "hashS({0})"}, {"name": "VAL4_MANDATORY_EXPR_HASHM", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.val4"}]}, "expr": "hashM({0})"}, {"name": "VAL5_MANDATORY_EXPR_HASHL", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.val5"}]}, "expr": "hashL({0})"}], "pit": {"type": "no-pit-table"}}}]}