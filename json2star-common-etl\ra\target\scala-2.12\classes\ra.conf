versionTypeVal: "strColumn"
// Version expression:
versionExprLeftPart: "lpad(split_part({0}, '.', 1), 3, '0')" // apply left padding
versionExprRightPart: "lpad(split_part({0}, '.', 2), 3, '0')" // apply left padding
versionExpr: "concat_ws('.', "${versionExprLeftPart}", "${versionExprRightPart}")"

// Fare Amount expression:
amount_part: "split_part({0}, '-', array_size(split({0}, '-')))"
convertToNegative: "concat('-', "${amount_part}")" // not done for now
isRefundAccounted: "array_size(split({0}, '-')) > 1 and split_part({0}, '-', 1) == 'ACCOUNTED'"
convertAccountedRefundToNegative: "if("${isRefundAccounted}", "${amount_part}", "${amount_part}")"
fareAmountExpr: "if(split_part({0}, '-', 1) == '_', NULL, "${convertAccountedRefundToNegative}")"

ifFirstValueNullTakeSecondValue : "if(split_part({0}, '-', 1) == '_', split_part({0}, '-', 2), split_part({0}, '-', 1))"
hashSIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashS({0}) )"
// - CORR FLIGHT_DATE_ID
// Inputs: [1-3] Marketing Flight Part - [4-6] Operating Flight Part - [7-9] Departure Date
// Rules: take the {CUSTOMER} Flight Part
getCorrFlightDateId: "regexp_replace(concat_ws('-', if(split_part({0}, '-', 4) == '{CUSTOMER}', slice(split({0}, '-'), 4, 3), slice(split({0}, '-'), 1, 3)), slice(split({0}, '-'), 7, 3)), '-_|T..:..:..T|T..:..:..', '')"

// - CORR FLIGHT_SEGMENT_ID
// Inputs: [1-3] Marketing Flight Part - [4-6] Operating Flight Part - [7-12] Departure Date *2 - [13-14] Departure and Arrival Airport
// Rules: - take the {CUSTOMER} Flight Part
//        - in case Marketing Carrier and Operating Carrier are not {CUSTOMER}, invalidate the ID (null)

getCorrFlightSegmentId: "if(split_part({0}, '-', 1) == '{CUSTOMER}' or split_part({0}, '-', 4) == '{CUSTOMER}', regexp_replace(concat_ws('-', if(split_part({0}, '-', 1) == '{CUSTOMER}', slice(split({0}, '-'), 1, 3), slice(split({0}, '-'), 4, 3)), slice(split({0}, '-'), 7, 8)), '-_|T..:..:..Z|T..:..:..', ''), null)"

// - CORR CODESHARE_FLIGHT_SEGMENT_ID
// Inputs: [1-6] Marketing Flight Date Part - [7-12] Operating Flight Date Part - [13-15] Departure Date - [16-17] Departure and Arrival Airport
// Rules: - in case Marketing Carrier is not equal to Operating Carrier (codeshare), put the {CUSTOMER} Flight Date Part at the beginning and add the non-{CUSTOMER} Flight Date Part at the end
//        - in case Marketing Carrier is equal to Operating Carrier (prime), invalidate the ID (null)
//        - in case Marketing Carrier and Operating Carrier are not {CUSTOMER}, invalidate the ID (null)
getCorrCodeshareFlightSegmentId: "if(split_part({0}, '-', 1) != split_part({0}, '-', 7) and (split_part({0}, '-', 1) == '{CUSTOMER}' or split_part({0}, '-', 7) == '{CUSTOMER}') and (split_part({0}, '-', 1) != '_' and split_part({0}, '-', 7) != '_'), regexp_replace(concat_ws('-', if(split_part({0}, '-', 1) == '{CUSTOMER}', slice(split({0}, '-'), 1, 6), slice(split({0}, '-'), 7, 6)), slice(split({0}, '-'), 13, 5), if(split_part({0}, '-', 1) != '{CUSTOMER}', slice(split({0}, '-'), 1, 6), slice(split({0}, '-'), 7, 6))), '-_|T..:..:..Z|T..:..:..', ''), null)"

removeTimePart: "regexp_replace({0}, '-_|T..:..:..T|T..:..:..', '')"
removeDatePart: "regexp_replace({0}, '-(\\\\d\\\\d\\\\d\\\\d)-(\\\\d\\\\d)-(\\\\d\\\\d)-', '-')"
createInternalCorrIdEmd: "concat_ws('-', split_part({0}, '-', 1), split_part({0}, '-', 2), split_part({0}, '-', 6))"

"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.mainResource.current.image": "revenueAccountingDataPush.RevenueAccountingAirTravelDocument"
    }
  }
},

"tables": [
  {
    "name": "FACT_TRAVEL_DOCUMENT",
    "latest": {
      "histo-table-name": "FACT_TRAVEL_DOCUMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_TRAVEL_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Travel Document",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "This is the main fact table of the Revenue Accounting star schema. Contains general information of travel documents (tickets, EMDs) such as document numbers (primary, conjunctive, original), overall monetary information for payment (total price/taxes) and refund (total refund/taxes/fees), related points of sale and basic passenger information.",
        "granularity": "1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: primDocNum-issuanceDate", "rule": "replace"}, "example": {"value": "*************-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "CREATION_DATETIME_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ISSUANCE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.issuanceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ORIGIN_CITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.originCityIataCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DESTINATION_CITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.destinationCityIataCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVELER_FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.name.firstName"}]},
          "meta": {"description": {"value": "First name of the travel document's passenger", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "TRAVELER_LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.name.lastName"}]},
          "meta": {"description": {"value": "Last name of the travel document's passenger", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "TRAVELER_PAX_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.passengerTypeCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ENDORSEMENT_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.endorsementFreeFlow"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NUMBER_OF_BOOKLETS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.numberOfBooklets"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CONJUNCTIVE_DOCUMENT_1", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[1]"}]},
          "meta": {"description": {"value": "Document number of first conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CONJUNCTIVE_DOCUMENT_2", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[2]"}]},
          "meta": {"description": {"value": "Document number of second conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CONJUNCTIVE_DOCUMENT_3", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[3]"}]},
          "meta": {"description": {"value": "Document number of third conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ORIGINAL_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.originalDocuments[0].documentNumber"}]},
          "meta": {"description": {"value": "Primary document number of the original travel document further to an exchange", "rule": "replace"}, "example": {"value": "*************", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ORIGINAL_DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.originalDocuments[0].creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the original document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TOTAL_FARE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.afterSaleFares[?(@.fareType == 'NET_FARE')].detailedPrices[?(@.elementaryPriceType == 'ACCOUNTED')].amount"}]},
          "expr": "aggregate(split({0}, '-'), cast(0 as double), (acc, x) -> acc + cast(x as double))",
          "meta": {"gdpr-zone": "green"}},
        {"name": "VALIDATING_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.validatingCarrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VALIDATING_CARRIER_RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.validatingCarrierPnr.reference"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.revenueAccountingStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVENUE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.revenueStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRANSACTION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.transactionCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}]}},
        {"name": "CREATION_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.inHouseIdentification"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_LOGIN_CITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.cityCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_LOGIN_COUNTRY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.countryCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_LOGIN_NUMERIC_SIGN", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.numericSign"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "CREATION_LOGIN_INITIALS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.initials"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "CREATION_LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.dutyCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.triggerEventName"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.inHouseIdentification"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_LOGIN_CITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.cityCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_LOGIN_COUNTRY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.countryCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_LOGIN_NUMERIC_SIGN", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.numericSign"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "LAST_UPDATE_LOGIN_INITIALS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.initials"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "LAST_UPDATE_LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.dutyCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}, {"base": "$.creation.pointOfSale.office.iataNumber"}, {"base": "$.creation.pointOfSale.office.inHouseIdentification"}, {"base": "$.creation.pointOfSale.office.systemCode"}, {"base": "$.creation.pointOfSale.office.agentType"}, {"base": "$.creation.pointOfSale.login.cityCode"}, {"base": "$.creation.pointOfSale.login.countryCode"}, {"base": "$.creation.pointOfSale.login.numericSign"}, {"base": "$.creation.pointOfSale.login.initials"}, {"base": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}]},
        {"name": "LAST_UPDATE_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.id"}, {"base": "$.latestEvent.pointOfSale.office.iataNumber"}, {"base": "$.latestEvent.pointOfSale.office.inHouseIdentification"}, {"base": "$.latestEvent.pointOfSale.office.systemCode"}, {"base": "$.latestEvent.pointOfSale.office.agentType"}, {"base": "$.latestEvent.pointOfSale.login.cityCode"}, {"base": "$.latestEvent.pointOfSale.login.countryCode"}, {"base": "$.latestEvent.pointOfSale.login.numericSign"}, {"base": "$.latestEvent.pointOfSale.login.initials"}, {"base": "$.latestEvent.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}]},
        {"name": "VALIDATING_CARRIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.validatingCarrierCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "TRAVELER_PAX_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.traveler.passengerTypeCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_PASSENGER_TYPE", "column": "PASSENGER_TYPE_ID"}]},
        {"name": "ORIGIN_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.originCityIataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "DESTINATION_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.destinationCityIataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "RA_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_VERSION"
        }
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ASSOCIATED_RESERVATION",
    "latest": {
      "histo-table-name": "FACT_ASSOCIATED_RESERVATION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ASSOCIATED_RESERVATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Travel Document",
    "mapping": {
      "description": {
        "description": "Contains references of reservations/PNRs related to a given travel document, including the system code in which the related PNR was created.",
        "granularity": "1 associated reservation for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ASSOCIATED_RESERVATION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"assoRes": "$.associatedPnrs[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ASSOCIATED_RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"assoRes": "$.reference"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"assoRes": "$.reference"}]},
          "meta": {"description": {"value": "Functional key: docId-recordLoc", "rule": "replace"}, "example": {"value": "*************-2020-01-31-AB{0}23", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"assoRes": "$.reference"}]},
          "meta": {"description": {"value": "The record locator of the associated reservation/PNR", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "PNR_CREATION_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"assoRes": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "PNR_CREATION_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"assoRes": "$.creation.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"assoRes": "$.creation.pointOfSale.office.id"}, {"assoRes": "$.creation.pointOfSale.office.iataNumber"}, {"assoRes": "$.creation.pointOfSale.office.inHouseIdentification"}, {"assoRes": "$.creation.pointOfSale.office.systemCode"}, {"assoRes": "$.creation.pointOfSale.office.agentType"}, {"assoRes": "$.creation.pointOfSale.login.cityCode"}, {"assoRes": "$.creation.pointOfSale.login.countryCode"}, {"assoRes": "$.creation.pointOfSale.login.numericSign"}, {"assoRes": "$.creation.pointOfSale.login.initials"}, {"assoRes": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the creator of the associated reservation)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["ASSOCIATED_RESERVATION_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_TAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_TAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_TAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Travel Document",
    "mapping": {
      "description": {
        "description": "Contains details on taxes which are applicable to a quotation or a fee, such as tax code, nature, category and amount.",
        "granularity": "1 tax in 1 quotation or fee"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "quot", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"price": "$.price"}, {"tax": "$.taxes[*]"} ]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"tax": "$.code"}, {"tax": "$.description"} ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"tax": "$.code"}, {"tax": "$.description"} ]},
          "meta": {"description": {"value": "Functional key: quotationId-taxCode-taxNature", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-QT23-ZV-GO", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TYPE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.taxType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.category"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NATURE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.nature"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"tax": "$.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY_CONVERSION_RATE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.convertedAmount.currencyConversionRate.rate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TAX_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tax": "$.code"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_TAX_CODE", "column": "TAX_CODE_ID"}]},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COMMISSION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COMMISSION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COMMISSION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Travel Document",
    "mapping": {
      "description": {
        "description": "Contains details on commissions which are applicable to a fare element. Can refer to pricing or payment.",
        "granularity": "1 commission in 1 fare element"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COMMISSION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "quot", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"price": "$.price"}, {"com": "$.commissions[*]"} ]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COMMISSION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"com": "$.commissionType"}, {"com": "$.amount.elementaryPriceType"} ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"com": "$.commissionType"}, {"com": "$.amount.elementaryPriceType"} ]},
          "meta": {"description": {"value": "Functional key: fareElemId-commType", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT33-NEW", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COMMISSION_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.commissionType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"com": "$.amount.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.amount.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.amount.elementaryPriceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FARE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FARE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FARE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Travel Document",
    "mapping": {
      "description": {
        "description": "Contains details on fares.",
        "granularity": "1 commission in 1 fare element"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FARE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "payment", "rs": {"blocks": [ {"base": "$.mainResource.current.image"}, {"fare": "$.afterSaleFares[?(@.fareType empty false && 'REFUNDED' nin @.detailedPrices[*].elementaryPriceType)]"}, {"det": "$.detailedPrices[*]"} ]}},
        {"name": "unknown", "rs": {"blocks": [ {"base": "$.mainResource.current.image"}, {"fare": "$.afterSaleFares[?(!(@.fareType))]"}, {"det": "$.detailedPrices[*]"} ]}},
        {"name": "refund", "rs": {"blocks": [ {"base": "$.mainResource.current.image"}, {"fare": "$.afterSaleFares[?(@.fareType empty false && 'REFUNDED' in @.detailedPrices[*].elementaryPriceType)]"}, {"det": "$.detailedPrices[*]"} ]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FARE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"fare": "$.fareType"}, {"det": "$.elementaryPriceType"}, {"det": "$.amount"} ]}, "expr": "hashM({0})", // TODO amount
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"fare": "$.fareType"}, {"det": "$.elementaryPriceType"}, {"det": "$.amount"} ]},
          "meta": {"description": {"value": "Functional key: fareElemId-commType", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT33-NEW", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FARE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"fare": "$.fareType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "strColumn", "sources": {
          "root-specific": [
            {"rs-name": "payment", "blocks": [{"det": "$.amount"}]},
            {"rs-name": "unknown", "blocks": [{"det": "$.amount"}]},
            {"rs-name": "refund", "blocks": [{"det": "$.elementaryPriceType"}, {"det": "$.amount"}]},
          ]
        }, "expr": ${fareAmountExpr}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"det": "$.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"det": "$.elementaryPriceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_REFUND", "column-type": "booleanColumn", "sources": { "root-specific": [
          {"rs-name": "payment", "literal": "false"},
          {"rs-name": "unknown", "literal": "false"},
          {"rs-name": "refund", "literal": "true"},
        ]}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FEE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FEE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FEE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Travel Document",
    "mapping": {
      "description": {
        "description": "Contains details on fees which are applicable to a quotation, mainly fee code and nature and its amount",
        "granularity": "1 fee in 1 quotation"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FEE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"price": "$.price"}, {"fee": "$.fees[*]"} ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FEE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"fee": "$.code"}, {"fee": "$.description"} ]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"fee": "$.code"}, {"fee": "$.description"} ]}, "meta": {"description": {"value": "Functional key: quotationId-feeCode", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-QT18-OB", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.subCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"fee": "$.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.description"}]}, "meta": {"example": {"value": "CARD FEE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_REQUESTED", "column-type": "booleanColumn", "sources": {"blocks": [{"fee": "$.requested"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_EXEMPTED", "column-type": "booleanColumn", "sources": {"blocks": [{"fee": "$.exempted"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_TAX_INCLUDED", "column-type": "booleanColumn", "sources": {"blocks": [{"fee": "$.taxIncluded"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}], "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}, "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FORM_OF_PAYMENT",
    "table-selectors": ["RA_CORE"],
    "latest": {
      "histo-table-name": "FACT_FORM_OF_PAYMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_FORM_OF_PAYMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Payment",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information on forms of payment used to pay or refund for travel documents, such as category, payment amount converted to home currency, and details on card and authorization. It can refer to a loyalty account used for redemption.",
        "granularity": "1 form of payment for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FORM_OF_PAYMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "payment", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"fop": "$.formsOfPayment[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FORM_OF_PAYMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fop": "$.code"}, {"fop": "$.fopIndicator"}, {"fop": "$.displayedAmount.amount"}, {"fop": "$..displayedAmount.currency"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fop": "$.code"}, {"fop": "$.fopIndicator"}, {"fop": "$.displayedAmount.amount"}, {"fop": "$..displayedAmount.currency"}]},
          "meta": {"description": {"value": "Functional key: docId-fopCode-fopIndicator-amount-currency", "rule": "replace"}, "example": {"value": "*************-2020-01-31-CASH-NEW-100.50-EUR", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.fopIndicator"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted fop_amount_payment into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.displayedAmount.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.displayedAmount.currency"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AUTHORIZATION_APPROVAL_SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.authorization.sourceOfApprovalCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AUTHORIZATION_APPROVAL_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.authorization.approvalCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AUTHORIZATION_EXTENDED_PAYMENT_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.authorization.extendedPaymentCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CARD_VENDOR_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.paymentCard.vendorCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CARD_MASKED_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.paymentCard.maskedCardNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CARD_HOLDER_NAME", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.paymentCard.holderName"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "CARD_EXPIRY_DATE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.paymentCard.expiryDate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.freeText"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fop": "$.code"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_FORM_OF_PAYMENT_TYPE", "column": "FORM_OF_PAYMENT_TYPE_ID"}]},
        {"name": "AUTHORIZATION_APPROVAL_SOURCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fop": "$.authorization.sourceOfApprovalCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_FORM_OF_PAYMENT_TYPE", "column": "FORM_OF_PAYMENT_TYPE_ID"}]},
        {"name": "CARD_VENDOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fop": "$.paymentCard.vendorCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_CARD_VENDOR", "column": "CARD_VENDOR_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["FORM_OF_PAYMENT_ID","RELATES_TO", "VERSION"]
    }
  },
  {
    "name": "FACT_PRICING_CONDITION",
    "table-selectors": ["RA_CORE"],
    "latest": {
      "histo-table-name": "FACT_PRICING_CONDITION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_PRICING_CONDITION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Pricing Conditions",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains detailed information on pricing conditions of a travel document, such as fare calculation and various fare conditions (exchangeable, refundable, domestic sale, …) and nego fare information if applicable.",
        "granularity": "1 pricing condition for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PRICING_CONDITION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"price": "$.pricingConditions"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PRICING_CONDITION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId", "rule": "replace"}, "example": {"value": "*************-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FARE_CALCULATION_TEXT", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.fareCalculation.text"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_CALCULATION_PRICING_INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.fareCalculation.pricingIndicator"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_DOMESTIC_SALE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isDomesticSale"}]},
          "meta": {"example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_INTERNATIONAL_SALE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isInternationalSale"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_ENDORSABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isNonEndorsable"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_EXCHANGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isNonExchangeable"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_REFUNDABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isNonRefundable"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_PENALTY_RESTRICTION", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isPenaltyRestriction"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NEGO_FARE_INCENTIVE_SCHEME", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.negoFareContract.incentiveScheme"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NEGO_FARE_TOUR_CODE", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.negoFareContract.tourCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "FARE_CALC_PRICING_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"price": "$.fareCalculation.pricingIndicator"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_FARE_CALC_PRICING_INDICATOR", "column": "FARE_CALC_PRICING_INDICATOR_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["PRICING_CONDITION_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_COUPON",
    "latest": {
      "histo-table-name": "FACT_COUPON_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains detailed information on coupons of travel documents, such as related flight segments (sold, used, current - with marketing and operating flight information), coupon status, estimated prorated fare, fare basis/family and further indicators on fare conditions (exchangeable, refundable) and baggage allowance.",
        "granularity": "1 coupon for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cpn": "$.revenueAccountingCoupons[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "*************-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.documentNumber"}]},
          "meta": {"example": {"value": "*************", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.primeSequenceNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SEQUENCE_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.primeSequenceNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reservationStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.status"}]}, "meta": {"gdpr-zone": "green"}},
        //CURRENT SEGMENT
//        {"name": "CURRENT_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.localDateTime"}]},
//          "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.localDateTime"}]},
//          "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.suffix"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.currentSegment.carrierCode"}]},
//          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${ifFirstValueNullTakeSecondValue}},
//        {"name": "CURRENT_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "CURRENT_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //SOLD SEGMENT
        {"name": "SOLD_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.marketing.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.marketing.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.marketing.flightDesignator.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.marketing.bookingClass.cabin.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.soldSegment.carrierCode"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${ifFirstValueNullTakeSecondValue}},
        {"name": "SOLD_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        //USED SEGMENT
        {"name": "USED_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.marketing.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.marketing.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.marketing.flightDesignator.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.marketing.bookingClass.cabin.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.usedSegment.carrierCode"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${ifFirstValueNullTakeSecondValue}},
        {"name": "USED_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_BASIS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareBasis.fareBasisCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_OWNER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.owner"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.subCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_REMARK", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.serviceRemark"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_CODESHARE", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isCodeshare"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_EXCHANGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonExchangeable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-exchangeable (true), or if it can be exchanged (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_NON_REFUNDABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonRefundable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-refundable (true), or if it can be refunded (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_FROM_CONNECTION", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isFromConnection"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE", "column-type": "floatColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT", "column-type": "strColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.unit"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_QUANTITY", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.quantity"}]},
          "meta": {"example": {"value": "2", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "NOT_VALID_BEFORE_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidBeforeDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_AFTER_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidAfterDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VOLUNTARY_INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SETTLEMENT_AUTHORIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.settlementAuthorizationCode"}]}, "meta": {"gdpr-zone": "green"}},
        //IDs
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
//        {"name": "CURRENT_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
//        {"name": "CURRENT_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "VOLUNTARY_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_COUPON_VOLUNTARY_INDICATOR", "column": "COUPON_VOLUNTARY_INDICATOR_ID"}]},

        //final columns
        {"name": "RA_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["COUPON_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_COUPON_TAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COUPON_TAX_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_TAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "mapping": {
      "description": {
        "description": "Contains details on taxes which are applicable to a quotation or a fee, such as tax code, nature, category and amount.",
        "granularity": "1 tax in 1 quotation or fee"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_TAX_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "quot", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"price": "$.price"}, {"tax": "$.taxes[*]"} ]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_TAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"tax": "$.code"}, {"tax": "$.description"}, {"tax": "$.taxType"} ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"tax": "$.code"}, {"tax": "$.description"}, {"tax": "$.taxType"} ]},
          "meta": {"description": {"value": "Functional key: quotationId-taxCode-taxNature", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-QT23-ZV-GO", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TYPE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.taxType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.category"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NATURE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.nature"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"tax": "$.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY_CONVERSION_RATE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.convertedAmount.currencyConversionRate.rate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TAX_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tax": "$.code"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_TAX_CODE", "column": "TAX_CODE_ID"}]},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_SURCHARGE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COUPON_SURCHARGE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_SURCHARGE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "mapping": {
      "description": {
        "description": "Contains details on coupon surcharges which are applicable to a quotation or a fee, such as tax code, nature, category and amount.",
        "granularity": "1 tax in 1 quotation or fee"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_SURCHARGE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "quot", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"price": "$.price"}, {"sur": "$.surcharges[*]"} ]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_SURCHARGE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"sur": "$.pricingSubType"}, {"sur": "$.description"} ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"sur": "$.pricingSubType"}, {"sur": "$.description"} ]},
          "meta": {"description": {"value": "Functional key: quotationId-taxCode-taxNature", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-QT23-ZV-GO", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TYPE", "column-type": "strColumn", "sources": {"blocks": [{"sur": "$.pricingSubType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"sur": "$.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"sur": "$.currencyCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"sur": "$.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_COMMISSION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COUPON_COMMISSION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_COMMISSION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "mapping": {
      "description": {
        "description": "Contains details on commissions which are applicable to a fare element. Can refer to pricing or payment.",
        "granularity": "1 commission in 1 fare element"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_COMMISSION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "quot", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"price": "$.price"}, {"com": "$.commissions[*]"} ]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_COMMISSION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"com": "$.commissionType"}, {"com": "$.amount.elementaryPriceType"} ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"com": "$.commissionType"}, {"com": "$.amount.elementaryPriceType"} ]},
          "meta": {"description": {"value": "Functional key: fareElemId-commType", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT33-NEW", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COMMISSION_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.commissionType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"com": "$.amount.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.amount.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.amount.elementaryPriceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}],
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_FEE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COUPON_FEE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_FEE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "mapping": {
      "description": {
        "description": "Contains details on fees which are applicable to a quotation, mainly fee code and nature and its amount",
        "granularity": "1 fee in 1 quotation"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FEE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"price": "$.price"}, {"fee": "$.fees[*]"} ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FEE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"fee": "$.code"}, {"fee": "$.description"} ]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"fee": "$.code"}, {"fee": "$.description"} ]}, "meta": {"description": {"value": "Functional key: quotationId-feeCode", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-QT18-OB", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.subCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"fee": "$.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.description"}]}, "meta": {"example": {"value": "CARD FEE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_REQUESTED", "column-type": "booleanColumn", "sources": {"blocks": [{"fee": "$.requested"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_EXEMPTED", "column-type": "booleanColumn", "sources": {"blocks": [{"fee": "$.exempted"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_TAX_INCLUDED", "column-type": "booleanColumn", "sources": {"blocks": [{"fee": "$.taxIncluded"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}, "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}], "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}, "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_FARE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COUPON_FARE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_FARE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "mapping": {
      "description": {
        "description": "Contains details on fares at coupon level, resulting from proration",
        "granularity": "1 fee in 1 quotation"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_FARE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"prorationMethod": "$.prorationMethods"}, {"prorationValue": "$.prorationValues[*]"} ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_FARE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"prorationValue": "$.prorationCode"}, {"prorationValue": "$.proratedAmount.elementaryPriceType"} ]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"prorationValue": "$.prorationCode"}, {"prorationValue": "$.proratedAmount.elementaryPriceType"} ]}, "meta": {"description": {"value": "Functional key: quotationId-feeCode", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-QT18-OB", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PRORATE_FACTOR", "column-type": "strColumn", "sources": {"blocks": [{"prorationMethod": "$.prorateFactor"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TICKETED_POINT_MILEAGE", "column-type": "strColumn", "sources": {"blocks": [{"prorationMethod": "$.ticketedPointMileage"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"prorationValue": "$.prorationCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"prorationValue": "$.prorationSource"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"prorationValue": "$.proratedAmount.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"prorationValue": "$.proratedAmount.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"prorationValue": "$.proratedAmount.elementaryPriceType"}]}, "meta": {"example": {"value": "CARD FEE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "expr": "to_date({0})",
          "meta": {"description": {"value": "The date for which the home currency conversion rate is required: document creation date", "rule": "replace"}, "example": {"value": "2023-10-10", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "example": {"value": "2023-10-10", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}, "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}], "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}, "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "AMOUNT_ORIGINAL", "src-unit-col": "CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "AMOUNT", "dst-unit-col": "CURRENCY", "dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"}
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_INTERLINE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COUPON_INTERLINE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_INTERLINE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "mapping": {
      "description": {
        "description": "Contains details on fees which are applicable to a quotation, mainly fee code and nature and its amount",
        "granularity": "1 fee in 1 quotation"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERLINE_CHARGE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"int": "$.interlineBilling"}, {"isc": "$.interlineableServiceCharge[*]"} ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERLINE_CHARGE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"isc": "$.elementaryPriceType"} ]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"isc": "$.elementaryPriceType"} ]}, "meta": {"description": {"value": "Functional key: quotationId-feeCode", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-QT18-OB", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"int": "$.billingStatus"} ]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"isc": "$.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"isc": "$.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"isc": "$.elementaryPriceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}, "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}], "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}, "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  }
  {
    "name": "FACT_COUPON_CODESHARE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COUPON_CODESHARE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_COUPON_CODESHARE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "mapping": {
      "description": {
        "description": "Contains details on fees which are applicable to a quotation, mainly fee code and nature and its amount",
        "granularity": "1 fee in 1 quotation"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_CODESHARE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"codeshare": "$.codeshareBilling"}, {"details": "$.detailedPrices[*]"} ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_CODESHARE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"codeshare": "$.codeshareAgreement"}, {"details": "$.elementaryPriceType"} ]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [ {"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}, {"codeshare": "$.codeshareAgreement"}, {"details": "$.elementaryPriceType"} ]}, "meta": {"description": {"value": "Functional key: quotationId-feeCode", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-QT18-OB", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"codeshare": "$.codeshareAgreement"} ]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {"blocks": [{"details": "$.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {"blocks": [{"details": "$.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"details": "$.elementaryPriceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})", "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}, "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}], "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RA_VERSION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExpr}, "meta": {"description": {"value": "Envelope/version of the RA message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}, "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "DIM_AIRPORT",
    "mapping": {
      "description": {"description": "Lists the airports used in departure/arrival airports of flight segments (sold, used, current) related to coupons", "granularity": "1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"airport": "$.mainResource.current.image.revenueAccountingCoupons[*].soldSegment.departure"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.revenueAccountingCoupons[*].usedSegment.departure"}]},
//        { "blocks": [{"airport": "$.mainResource.current.image.revenueAccountingCoupons[*].currentSegment.departure"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.revenueAccountingCoupons[*].soldSegment.arrival"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.revenueAccountingCoupons[*].usedSegment.arrival"}]},
//        { "blocks": [{"airport": "$.mainResource.current.image.revenueAccountingCoupons[*].currentSegment.arrival"}]}
      ],
      "columns": [
        {"name": "AIRPORT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"airport": "$.iataCode"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (airport IATA code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"airport": "$.iataCode"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRLINE",
    "mapping": {
      "description": {"description": "Lists the airlines used as validating/marketing/operating carriers on travel documents or coupons, or for loyalty programs.", "granularity": "1 airline"},
      "merge": {
        "key-columns": ["AIRLINE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"companyCode": "$.mainResource.current.image.validatingCarrierCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.revenueAccountingCoupons[*].frequentFlyer.applicableAirlineCode"}]} // TODO??
      ],
      "columns": [
        {"name": "AIRLINE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"companyCode": "$.value"}]},"expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (airline IATA code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRLINE_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"companyCode": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_TAX_CODE",
    "mapping": {
      "description": {"description": "Lists the various tax codes", "granularity": "1 tax code"},
      "merge": {
        "key-columns": ["TAX_CODE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"base": "$.mainResource.current.image"}, {"price": "$.price"}, {"tax": "$.taxes[*]"}]},
        { "blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"price": "$.price"}, {"tax": "$.taxes[*]"}]}
      ],
      "columns": [
        {"name": "TAX_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tax": "$.code"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TAX_CODE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]}},
        {"name": "TAX_CODE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]}
          , "meta": {"description": {"value": "Label corresponding to the tax code", "rule": "replace"}, "example": {"value": "Safety Charge (International) - GABON", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "TAX_CODE",
      "column-filler" : [
        {"dim-col" : "TAX_CODE_ID", "src-col" : "CODE"},
        {"dim-col" : "TAX_CODE", "src-col" : "CODE"},
        {"dim-col" : "TAX_CODE_LABEL", "src-col" : "NAME"}
      ]
    },
      {
        "data-source-key": "COUNTRY",
        "column-filler" : [
          {"dim-col" : "TAX_CODE_ID", "src-col" : "ISO2"},
          {"dim-col" : "TAX_CODE", "src-col" : "ISO2"},
          {"dim-col" : "TAX_CODE_LABEL", "src-col" : "LABEL"}
        ]
      }]
  },
  {
    "name": "DIM_REASON_FOR_ISSUANCE",
    "mapping": {
      "description": {"description": "Lists the reasons for issuance codes used on coupons", "granularity": "1 reason code (RFIC)"},
      "merge": {
        "key-columns": ["REASON_FOR_ISSUANCE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.revenueAccountingCoupons[*].reasonForIssuance"}]}],
      "columns": [
        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.code"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (reason code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.code"}]}},
        {"name": "REASON_FOR_ISSUANCE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.code"}]},
          "meta": {"description": {"value": "Label corresponding to the reason for issuance code", "rule": "replace"}, "example": {"value": "Air Transportation", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "REASON_FOR_ISSUANCE",
      "column-filler" : [
        {"dim-col" : "REASON_FOR_ISSUANCE_ID", "src-col" : "CODE"},
        {"dim-col" : "REASON_FOR_ISSUANCE_CODE", "src-col" : "CODE"},
        {"dim-col" : "REASON_FOR_ISSUANCE_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_POINT_OF_SALE",
    "mapping": {
      "description": {"description": "Lists the point of sales (office-level and user-level where available) used as document creator/updater, revalidation, quotation override or creator of associated PNRs.", "granularity": "1 point of sale"},
      "merge": {
        "key-columns": ["POINT_OF_SALE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "creation", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "updater", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.latestEvent.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "revalidation", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.associatedPnrs[*].creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
      ],
      "columns": [
        { "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": { "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.inHouseIdentification"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (officeId-inhouseId-officeIATA-system-agentType-city-country-numSign-initials-duty)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"office": "$.id"}]}},
        {"name": "OFFICE_IATA_NUMBER","column-type": "strColumn", "sources": {"blocks": [{"office": "$.iataNumber"}]}},
        {"name": "OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"office": "$.inHouseIdentification"}]}},
        {"name": "OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"office": "$.systemCode"}]}},
        {"name": "OFFICE_AGENT_TYPE","column-type": "strColumn","sources": {"blocks": [{"office": "$.agentType"}]}},
        {"name": "LOGIN_CITY_CODE","column-type": "strColumn","sources": {"blocks": [{"pos": "$.login.cityCode"}]}},
        {"name": "LOGIN_COUNTRY_CODE","column-type": "strColumn", "sources": {"blocks": [{"pos": "$.login.countryCode"}]}},
        {"name": "LOGIN_NUMERIC_SIGN", "column-type": "strColumn", "sources": {"blocks": [{"pos": "$.login.numericSign"}]},"meta": {"gdpr-zone": "orange"}},
        {"name": "LOGIN_INITIALS", "column-type": "strColumn", "sources": {"blocks": [{"pos": "$.login.initials"}]},"meta": {"gdpr-zone": "orange"}},
        {"name": "LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"pos": "$.login.dutyCode"}]}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_COUPON_VOLUNTARY_INDICATOR",
    "mapping": {
      "description": {"description": "Lists the coupon voluntary indicators (manual operations) used on coupons", "granularity": "1 coupon voluntary indicator"},
      "merge": {
        "key-columns": ["COUPON_VOLUNTARY_INDICATOR_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.revenueAccountingCoupons[*].voluntaryIndicator"}]}],
      "columns": [
        {"name": "COUPON_VOLUNTARY_INDICATOR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (voluntary indicator code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COUPON_VOLUNTARY_INDICATOR_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}},
        {"name": "COUPON_VOLUNTARY_INDICATOR_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]},
          "meta": {"description": {"value": "The label of the coupon voluntary indicator, as part of reference data", "rule": "replace"}, "example": {"value": "Schedule change", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "COUPON_VOLUNTARY_INDICATOR",
      "column-filler" : [
        {"dim-col" : "COUPON_VOLUNTARY_INDICATOR_ID", "src-col" : "CODE"},
        {"dim-col" : "COUPON_VOLUNTARY_INDICATOR_CODE", "src-col" : "CODE"},
        {"dim-col" : "COUPON_VOLUNTARY_INDICATOR_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_FORM_OF_PAYMENT_TYPE",
    "mapping": {
      "description": {"description": "Lists the categories of forms of payment, such as cash, credit card, cheque, etc. used for travel document payment or refund", "granularity": "1 form of payment category"},
      "merge": {
        "key-columns": ["FORM_OF_PAYMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.formsOfPayment[*].code"}]}
      ],
      "columns": [
        {"name": "FORM_OF_PAYMENT_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (fop code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FORM_OF_PAYMENT_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}},
        {"name": "FORM_OF_PAYMENT_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]},
          "meta": {"description": {"value": "The label of the form of payment category, as part of reference data", "rule": "replace"}, "example": {"value": "Credit card", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "FORM_OF_PAYMENT_TYPE",
      "column-filler" : [
        {"dim-col" : "FORM_OF_PAYMENT_TYPE_ID", "src-col" : "CODE"},
        {"dim-col" : "FORM_OF_PAYMENT_TYPE_CODE", "src-col" : "CODE"},
        {"dim-col" : "FORM_OF_PAYMENT_TYPE_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_CARD_VENDOR",
    "mapping": {
      "description": {"description": "Lists the card vendors used on forms of payments of type credit card", "granularity": "1 card vendor"},
      "merge": {
        "key-columns": ["CARD_VENDOR_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.formsOfPayment[*].paymentCard.vendorCode"}]}
      ],
      "columns": [
        {"name": "CARD_VENDOR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (vendor code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CARD_VENDOR_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}},
        {"name": "CARD_VENDOR_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]},
          "meta": {"description": {"value": "The name of the card vendor, as part of reference data", "rule": "replace"}, "example": {"value": "Visa", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "CARD_VENDOR",
      "column-filler" : [
        {"dim-col" : "CARD_VENDOR_ID", "src-col" : "CODE"},
        {"dim-col" : "CARD_VENDOR_CODE", "src-col" : "CODE"},
        {"dim-col" : "CARD_VENDOR_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_PASSENGER_TYPE",
    "mapping": {
      "description": {"description": "Lists the different passenger types", "granularity": "1 passenger type"},
      "merge": {
        "key-columns": ["PASSENGER_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.traveler"}]}],
      "columns": [
        {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
        {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "PASSENGER_TYPE",
      "column-filler" : [
        {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
        {"dim-col" : "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
        {"dim-col" : "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_FOP_AUTHORIZATION_SOURCE",
    "mapping": {
      "description": {"description": "Lists the different form of payment authorization sources", "granularity": "1 FOP authorization source"},
      "merge": {
        "key-columns": ["FOP_AUTHORIZATION_SOURCE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "payment", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"fop": "$.formsOfPayment[*].authorization.sourceOfApprovalCode"}]}},
        {"name": "refund", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"fop": "$.documentRefund.paymentDetails.authorization.sourceOfApprovalCode"}]}} // TODO ??
      ],
      "columns": [
        {"name": "FOP_AUTHORIZATION_SOURCE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"fop":"$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FOP_AUTHORIZATION_SOURCE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"fop":"$.value"}]}},
        {"name": "FOP_AUTHORIZATION_SOURCE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"fop":"$.value"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "FORM_OF_PAYMENT_APPROVAL_CODE",
      "column-filler" : [
        {"dim-col" : "FOP_AUTHORIZATION_SOURCE_ID", "src-col" : "CODE"},
        {"dim-col" : "FOP_AUTHORIZATION_SOURCE_CODE", "src-col" : "CODE"},
        {"dim-col" : "FOP_AUTHORIZATION_SOURCE_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_FARE_CALC_PRICING_INDICATOR",
    "mapping": {
      "description": {"description": "Lists the various fare calc pricing indicators", "granularity": "1 fare calc pricing indicator"},
      "merge": {
        "key-columns": ["FARE_CALC_PRICING_INDICATOR_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"price": "$.pricingConditions.fareCalculation.pricingIndicator"}]}
      ],
      "columns": [
        {"name": "FARE_CALC_PRICING_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"price": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FARE_CALC_PRICING_INDICATOR_CODE", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.value"}]}},
        {"name": "FARE_CALC_PRICING_INDICATOR_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.value"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "FARE_CALC_PRICING_INDICATOR",
      "column-filler" : [
        {"dim-col" : "FARE_CALC_PRICING_INDICATOR_ID", "src-col" : "CODE"},
        {"dim-col" : "FARE_CALC_PRICING_INDICATOR_CODE", "src-col" : "CODE"},
        {"dim-col" : "FARE_CALC_PRICING_INDICATOR_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_CITY",
    "mapping": {
      "description": {"description": "Lists the cities used on travel documents and fare components as start/end cities", "granularity": "1 city"},
      "merge": {
        "key-columns": ["CITY_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"city": "$.mainResource.current.image.originCityIataCode"}]},
        { "blocks": [{"city": "$.mainResource.current.image.destinationCityIataCode"}]}
      ],
      "columns": [
        {"name": "CITY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"city": "$.value"}]},"expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (city IATA code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CITY_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"city": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },

  //// RA-PNR Internal associations

  {
    "name": "INTERNAL_ASSO_RESERVATION_RA_TRAVEL_DOCUMENT_HISTO", //PNR -> RA 1/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": []}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains PNR-TKT and PNR-EMD correlation information as seen by RA.",
        "granularity": "1 PNR-TRAVEL_DOCUMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVELER_RA_TRAVEL_DOCUMENT_HISTO", //PNR -> RA 2/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVELER_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVELER", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains PNR-TKT and PNR-EMD correlation information as seen by RA.",
        "granularity": "1 TRAVELER-TRAVEL_DOCUMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_RA_COUPON_HISTO", //PNR -> RA 3/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {}},
        {"name": "AIR_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "TECH_RELATED_AIR_SEGMENT_IDENTIFIER_FOR_FILTERING_PURPOSE", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains AIR_SEGMENT_PAX-COUPON correlation information as seen by RA.",
        "granularity": "1 AIR_SEGMENT_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_SERVICE_PAX_RA_COUPON_HISTO",  //PNR -> RA 4/5 // TODO : add filter to get only services that are subtype: SERVICE
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SERVICE_PAX_ID", "INTERNAL_CORR_ID_EMD", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"root1": "$"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "SERVICE_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_SERVICE_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER_EMD", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SERVICE_PAX-COUPON correlation information as seen by RA.",
        "granularity": "1 SERVICE_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO",  //PNR -> RA 5/5// TODO : add filter to get only services that are subtype: SEATING
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEATING_PAX_ID", "INTERNAL_CORR_ID_EMD", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"root1": "$"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "SEATING_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_SEATING_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER_EMD", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SEATING_PAX-COUPON correlation information as seen by RA.",
        "granularity": "1 SEATING_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_RA_TRAVEL_DOCUMENT_SKD_FLIGHT_DATE_HISTO",  //SKD -> RA 1/3
    "table-selectors": ["RA_ACTIVE", "SKD_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.soldSegment"}]},
//        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.currentSegment"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.usedSegment"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains RA-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 TRAVEL_DOCUMENT-FLIGHT_DATE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_RA_COUPON_SKD_FLIGHT_SEGMENT_HISTO", //SKD -> RA 2/3
    "table-selectors": ["RA_ACTIVE", "SKD_ACTIVE", "DAAS_CORRELATION"],

    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.soldSegment"}]},
//        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.currentSegment"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.usedSegment"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrFlightSegmentId}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains RA-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_SEGMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_RA_COUPON_SKD_CODESHARE_FLIGHT_SEGMENT_HISTO", //SKD -> RA 3/3
    "table-selectors": ["RA_ACTIVE", "SKD_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.soldSegment"}]},
//        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.currentSegment"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"}, {"seg": "$.usedSegment"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"},  {"seg": "$.departure.localDateTime"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrCodeshareFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true","sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrCodeshareFlightSegmentId}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.marketing.flightDesignator.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains RA-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_CODESHARE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO",
    "table-selectors": ["DCSPAX_ACTIVE","RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_CORR_ID", "PASSENGER_ID", "VERSION_PASSENGER", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "tkt", "rs": {"blocks": [{"root2": "$"}]}},
        {"name": "emd", "rs": {"blocks": [{"root2": "$"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {},  "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "VERSION_PASSENGER", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX-TRAVEL_DOCUMENT correlation information as seen by RA.",
      "granularity": "1 PAX-TKT/EMD",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SEGMENT_DELIVERY_HISTO",
    "table-selectors": ["DCSPAX_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_CORR_ID", "SEGMENT_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "tkt", "rs": {"blocks": [{"root2": "$"}]}},
        {"name": "emd", "rs": {"blocks": [{"root2": "$"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SEGMENT_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by RA.",
      "granularity": "1 PAX_SEG_DEL-TKT/EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO",
    "table-selectors": ["DCSPAX_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_CORR_ID", "SERVICE_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root2": "$"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "SERVICE_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SERVICE_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by EMD.",
      "granularity": "1 PAX_SERVICE_DEL-EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_FACT_ASSOCIATED_RESERVATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "description": {
        "description": "Contains references of reservations/PNRs related to a given travel document, including the system code in which the related PNR was created.",
        "granularity": "1 associated reservation for 1 travel document",
        "subdomain": "Travel Document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "basePnr", "rs": {"blocks": [{"base": "$.mainResource.current.image"}]}},
        {"name": "associatedPnrs", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"assoRes": "$.associatedPnrs[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnr", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}]},
            {"rs-name": "associatedPnrs", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}]}
          ]}, "expr": "hashM({0})"
        },
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnr", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}]},
            {"rs-name": "associatedPnrs", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}]}
          ]}
        },
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-recordLoc", "rule": "replace"}, "example": {"value": "*************-2020-01-31-AB{0}23", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["INTERNAL_CORR_ID", "VERSION"]
    }
  },
  {
    "name": "INTERNAL_FACT_ASSOCIATED_RESERVATION_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "description": {
        "description": "Contains references of reservations/PNRs related to a given travel document, including the system code in which the related PNR was created.",
        "granularity": "1 associated reservation for 1 travel document",
        "subdomain": "Travel Document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "basePnrSold", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.reference)]"},{"seg": "$.soldSegment"}]}},
        {"name": "basePnrUSed", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.reference)]"},{"seg": "$.usedSegment"}]}},

        {"name": "associatedPnrsSold", "rs": {"blocks": [
          {"base": "$.mainResource.current.image"},
          {
            "cartesian": [
              [{"cpn": "$.revenueAccountingCoupons[*]"},{"seg": "$.soldSegment"}],
              [{"assoRes": "$.associatedPnrs[*]"}],
            ]
          }
        ]}},
        {"name": "associatedPnrsUSed", "rs": {"blocks": [
          {"base": "$.mainResource.current.image"},
          {
            "cartesian": [
              [{"cpn": "$.revenueAccountingCoupons[*]"},{"seg": "$.usedSegment"}],
              [{"assoRes": "$.associatedPnrs[*]"}],
            ]
          }
        ]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnrSold", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]},
            {"rs-name": "associatedPnrsSold", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]}
            {"rs-name": "basePnrUsed", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]},
            {"rs-name": "associatedPnrsUsed", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]}
          ]}, "expr": "hashM("${removeTimePart}")"
        },
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnrSold", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]},
            {"rs-name": "associatedPnrsSold", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]}
            {"rs-name": "basePnrUsed", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]},
            {"rs-name": "associatedPnrsUsed", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]}
          ]}, "expr": ${removeTimePart}
        },
        {"name": "INTERNAL_CORR_ID_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnrSold", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]},
            {"rs-name": "associatedPnrsSold", "blocks": [{"assoRes": "$.reference"}, {"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]},
            {"rs-name": "basePnrUsed", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]},
            {"rs-name": "associatedPnrsUsed", "blocks": [{"assoRes": "$.reference"}, {"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]}
          ]}, "expr": "hashM("${removeDatePart}")"
        },
        {"name": "INTERNAL_CORR_IDENTIFIER_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnrSold", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]},
            {"rs-name": "associatedPnrsSold", "blocks": [{"assoRes": "$.reference"}, {"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]},
            {"rs-name": "basePnrUsed", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]},
            {"rs-name": "associatedPnrsUsed", "blocks": [{"assoRes": "$.reference"}, {"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]}
          ]}, "expr": ${removeDatePart}
        },
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-recordLoc", "rule": "replace"}, "example": {"value": "*************-2020-01-31-AB{0}23", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "*************-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["INTERNAL_CORR_ID", "VERSION"]
    }
  },

  {
    "name": "INTERNAL_FACT_ASSOCIATED_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSPAX_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "description": {
        "description": "Contains references of reservations/PNRs related to a given travel document, including the system code in which the related PNR was created.",
        "granularity": "1 associated reservation for 1 travel document",
        "subdomain": "Travel Document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "SegSold", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"},{"seg": "$.soldSegment"}]}},
        {"name": "SegUsed", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[*]"},{"seg": "$.usedSegment"}]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "SegSold", "blocks": [ {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]},
            {"rs-name": "SegUsed", "blocks": [ {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]},
          ]}, "expr": "hashM("${removeTimePart}")"
        },
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "SegSold", "blocks": [ {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]},
            {"rs-name": "SegUsed", "blocks": [ {"base": "$.primaryDocumentNumber"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.localDateTime"}]},
          ]}, "expr": ${removeTimePart}
        },
        {"name": "TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-recordLoc", "rule": "replace"}, "example": {"value": "*************-2020-01-31-AB{0}23", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"base": "$.id"},{"cpn": "$.primeSequenceNumber"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "*************-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["INTERNAL_CORR_ID", "VERSION"]
    }
  },
  {
    "name": "INTERNAL_PARTIAL_RA_TRAVEL_DOCUMENT_INV_FLIGHT_DATE_HISTO",
    "table-selectors": ["RA_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.soldSegment"}]}, // filter revenueAccountingCoupons[?(@.number)] ?
//        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.currentSegment"}]}, // filter revenueAccountingCoupons[?(@.number)] ?
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.usedSegment"}]}, // filter revenueAccountingCoupons[?(@.number)] ?
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains RA-INV correlation information as seen by TKT/EMD.",
        "granularity": "1 TRAVEL_DOCUMENT-FLIGHT_DATE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_RA_COUPON_INV_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["RA_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"],

    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.soldSegment"}]},
//        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.currentSegment"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.usedSegment"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})"}, // document number + document creation date + coupon number
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrFlightSegmentId}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains RA-INV correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_SEGMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_RA_COUPON_INV_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["RA_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.soldSegment"}]}, // filter revenueAccountingCoupons[?(@.number)] ?
//        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.currentSegment"}]}, // filter revenueAccountingCoupons[?(@.number)] ?
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "revenueAccountingCoupons[?(@.number)]"}, {"seg": "$.usedSegment"}]}, // filter revenueAccountingCoupons[?(@.number)] ?
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})"}, // document number + document creation date + coupon number
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.suffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrCodeshareFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}}, // document number + document creation date + coupon number
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.marketing.flightDesignator.carrierCode"}, {"seg": "$.marketing.flightDesignator.flightNumber"}, {"seg": "$.suffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrCodeshareFlightSegmentId}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains RA-INV correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_CODESHARE",
        "links": ["???"]
      }
    }
  },
  //// RA-PNR Internal associations
  {
    "name": "INTERNAL_ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO", //PNR -> TKT 1/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": []}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains PNR-TKT and PNR-EMD correlation information as seen by TKTEMD.",
        "granularity": "1 PNR-TRAVEL_DOCUMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO", //PNR -> TKT 2/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVELER_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVELER", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains PNR-TKT and PNR-EMD correlation information as seen by TKTEMD.",
        "granularity": "1 TRAVELER-TRAVEL_DOCUMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {}},
        {"name": "AIR_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "TECH_RELATED_AIR_SEGMENT_IDENTIFIER_FOR_FILTERING_PURPOSE", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains AIR_SEGMENT_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 AIR_SEGMENT_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_SERVICE_PAX_COUPON_HISTO", // TODO : add filter to get only services that are subtype: SERVICE
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SERVICE_PAX_ID", "INTERNAL_CORR_ID_EMD", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrEmdPnr.items[*]"}, {"cpn": "$.emdCouponId"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "SERVICE_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_SERVICE_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER_EMD", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SERVICE_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 SERVICE_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO", // TODO : add filter to get only services that are subtype: SEATING
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEATING_PAX_ID", "INTERNAL_CORR_ID_EMD", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrEmdPnr.items[*]"}, {"cpn": "$.emdCouponId"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "SEATING_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_SEATING_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER_EMD", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SEATING_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 SEATING_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_RA_TKTEMD_TRAVEL_DOCUMENT", // TKTEMD-RA #1/2
    "table-selectors": [["TKTEMD_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TKTEMD_TRAVEL_DOCUMENT_ID", "TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TKTEMD_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RA_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY_TKTEMD_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_RA_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_RA_TRAVEL_DOCUMENT", "column-type": "floatColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-RA correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  }
  {
    "name": "INTERNAL_PARTIAL_CORR_RA_COUPON", // TKTEMD-RA #1/2
    "table-selectors": [["TKTEMD_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TKTEMD_COUPON_ID", "RA_COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cpn": "$.revenueAccountingCoupons[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TKTEMD_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RA_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY_TKTEMD_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}},
        {"name": "REFERENCE_KEY_RA_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.primeSequenceNumber"}]}},
        {"name": "TKTEMD_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RA_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY_TKTEMD_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_RA_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION_RA_TRAVEL_DOCUMENT", "column-type": "floatColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-RA correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },

]
