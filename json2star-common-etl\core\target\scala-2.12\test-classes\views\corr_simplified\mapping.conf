versionPassengerTypeVal: "longColumn"
versionBagTypeVal: "longColumn"
versionReservationTypeVal: "longColumn"
versionTktEmdTypeVal: "longColumn"
versionSkdTypeVal: "longColumn"

"tables": [
  {
    "name": "ASSO_RESERVATION_TRAVEL_DOCUMENT",
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO"
    }
  },
  {
    "name": "ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO",                // PNR<->TKT #1/5
    "zorder-columns": ["RESERVATION_ID", "TRAVEL_DOCUMENT_ID"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "RESERVATION_ID",
          "partial-corr-secondary-key": "TRAVEL_DOCUMENT_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "TRAVEL_DOCUMENT_ID",
          "partial-corr-secondary-key": "RESERVATION_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "RESERVATION_ID", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "TKTEMD", "name": "TRAVEL_DOCUMENT_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": ["DATE_BEGIN"]
      },
      "correlation-field-a-to-b": "TRAVEL_DOCUMENT_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_TRAVELER_TRAVEL_DOCUMENT",
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO"
    }
  },
  {
    "name": "ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO",                // PNR<->TKT #2/5
    "zorder-columns": ["TRAVELER_ID", "TRAVEL_DOCUMENT_ID"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "TRAVELER_ID",
          "partial-corr-secondary-key": "TRAVEL_DOCUMENT_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "TRAVEL_DOCUMENT_ID",
          "partial-corr-secondary-key": "TRAVELER_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "TRAVELER_ID", "fk": [{"schema": "PNR", "table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "TKTEMD", "name": "TRAVEL_DOCUMENT_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_TRAVELER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_TRAVELER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: pnrTravelerId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": ["DATE_BEGIN"]
      },
      "correlation-field-a-to-b": "TRAVEL_DOCUMENT_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_COUPON",                // PNR<->TKT #3/5
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_COUPON_HISTO"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_COUPON_HISTO",
    "zorder-columns": ["AIR_SEGMENT_PAX_ID", "COUPON_ID"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "AIR_SEGMENT_PAX_ID",
          "partial-corr-secondary-key": "COUPON_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "COUPON_ID",
          "partial-corr-secondary-key": "AIR_SEGMENT_PAX_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "AIR_SEGMENT_PAX_ID", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "TKTEMD", "name": "COUPON_ID", "fk" :[{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk" :[{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "meta": {"description": {"value": "The unique functional identifier of the correlated object: pnrAirSegmentId-pnrTravelerId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_COUPON", "meta": {"description": {"value": "The unique functional identifier of the correlated object: couponId", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": ["DATE_BEGIN"]
      },
      "correlation-field-a-to-b": "TRAVEL_DOCUMENT_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_SERVICE_PAX_COUPON",
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_SERVICE_PAX_COUPON_HISTO"
    }
  },
  {
    "name": "ASSO_SERVICE_PAX_COUPON_HISTO",                // PNR<->TKT #4/5
    "zorder-columns": ["SERVICE_PAX_ID", "COUPON_ID"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_SERVICE_PAX_COUPON_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "SERVICE_PAX_ID",
          "partial-corr-secondary-key": "COUPON_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_ASSO_SERVICE_PAX_COUPON_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "COUPON_ID",
          "partial-corr-secondary-key": "SERVICE_PAX_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "SERVICE_PAX_ID", "fk": [{"schema": "PNR", "table": "FACT_SERVICE_PAX_HISTO", "column": "SERVICE_PAX_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "TKTEMD", "name": "COUPON_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_SERVICE_PAX_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SERVICE_PAX", "meta": {"description": {"value": "The unique functional identifier of the correlated object: pnrServiceId-pnrTravelerId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_COUPON", "meta": {"description": {"value": "The unique functional identifier of the correlated object: couponId", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": ["DATE_BEGIN"]
      },
      "correlation-field-a-to-b": "TRAVEL_DOCUMENT_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_SEATING_PAX_COUPON",
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_SEATING_PAX_COUPON_HISTO"
    }
  },
  {
    "name": "ASSO_SEATING_PAX_COUPON_HISTO",                // PNR<->TKT #5/5
    "zorder-columns": ["SEATING_PAX_ID", "COUPON_ID"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "SEATING_PAX_ID",
          "partial-corr-secondary-key": "COUPON_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "COUPON_ID",
          "partial-corr-secondary-key": "SEATING_PAX_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "SEATING_PAX_ID", "fk": [{"schema": "PNR", "table": "FACT_SEATING_PAX_HISTO", "column": "SEATING_PAX_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "TKTEMD", "name": "COUPON_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_SEATING_PAX_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEATING_PAX", "meta": {"description": {"value": "The unique functional identifier of the correlated object: pnrSeatingId-pnrTravelerId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_COUPON", "meta": {"description": {"value": "The unique functional identifier of the correlated object: couponId", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": ["DATE_BEGIN"]
      },
      "correlation-field-a-to-b": "TRAVEL_DOCUMENT_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_RESERVATION_DCS_PASSENGER",
    "table-selectors": [["DCSPAX_ACTIVE","PNR_ACTIVE"],["DCSPAX_ACTIVE","PNR_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_RESERVATION_DCS_PASSENGER_HISTO"
    }
  },
  {
    "name": "ASSO_RESERVATION_DCS_PASSENGER_HISTO",                    // PNR<->PAX #1/2
    "zorder-columns": ["RESERVATION_ID", "PASSENGER_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "PNR_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "DCSPAX", "table-name": "INTERNAL_ASSO_RESERVATION_DCS_PASSENGER_HISTO", "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "RESERVATION_ID", "fk" :[{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of RESERVATION/FACT_RESERVATION (see REFERENCE_KEY)", "rule": "replace"}}},
          {"name": "PASSENGER_ID", "fk" :[{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of DCS_PASSENGER/FACT_PASSENGER (see REFERENCE_KEY)", "rule": "replace"}}},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The functional key of RESERVATION/FACT_RESERVATION (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "PASSENGER_ID", "domain-b-key": "RESERVATION_ID", "domain-a-version": "VERSION_PASSENGER",
        "domain-b-version": "VERSION_RESERVATION", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_RESERVATION_DCS_PASSENGER_HISTO",                // PNR<->PAX #1/2
    "zorder-columns": ["RESERVATION_ID", "PASSENGER_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "PNR_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_RESERVATION_DCS_PASSENGER_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "RESERVATION_ID",
          "partial-corr-secondary-key": "PASSENGER_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_ASSO_RESERVATION_DCS_PASSENGER_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "PASSENGER_ID",
          "partial-corr-secondary-key": "RESERVATION_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "RESERVATION_ID", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSPAX", "name": "PASSENGER_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "PASSENGER_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY",
    "table-selectors": [["DCSPAX_ACTIVE","PNR_ACTIVE"],["DCSPAX_ACTIVE","PNR_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO",                // PNR<->PAX #2/2
    "zorder-columns": ["AIR_SEGMENT_PAX_ID", "SEGMENT_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "PNR_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "DCSPAX", "table-name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {}
            , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "SEGMENT_DELIVERY_ID", "fk" :[{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {}
            , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: airSegmentPaxId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01-ST1-ABCDEF-2023-01-01-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_PASSENGER", "fk" :[{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "SEGMENT_DELIVERY_ID", "domain-b-key": "AIR_SEGMENT_PAX_ID", "domain-a-version": "VERSION_PASSENGER",
        "domain-b-version": "VERSION_RESERVATION", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO",                // PNR<->PAX #2/2
    "zorder-columns": ["AIR_SEGMENT_PAX_ID", "SEGMENT_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "PNR_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "AIR_SEGMENT_PAX_ID",
          "partial-corr-secondary-key": "SEGMENT_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "SEGMENT_DELIVERY_ID",
          "partial-corr-secondary-key": "AIR_SEGMENT_PAX_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "AIR_SEGMENT_PAX_ID", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSPAX", "name": "SEGMENT_DELIVERY_ID", "fk" :[{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk" :[{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "meta": {"description": {"value": "The unique functional identifier of the correlated object: airSegmentPaxId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01-ST1-ABCDEF-2023-01-01-PT1", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "PASSENGER_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE"]],
    "latest": {
      "histo-table-name": "ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO"
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO",                // TKT<->PAX #1/3
    "zorder-columns": ["TRAVEL_DOCUMENT_ID", "PASSENGER_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "DCSPAX", "table-name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "TKTEMD",
      "target": {
        "columns": [
          {"name": "TRAVEL_DOCUMENT_ID", "fk" :[{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "PASSENGER_ID", "fk" :[{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "VERSION"}], "column-type": ${versionTktEmdTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "PASSENGER_ID", "domain-b-key": "TRAVEL_DOCUMENT_ID", "domain-a-version": "VERSION_PASSENGER",
        "domain-b-version": "VERSION_TRAVEL_DOCUMENT", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO",                // TKT<->PAX #1/3
    "zorder-columns": ["TRAVEL_DOCUMENT_ID", "PASSENGER_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "TRAVEL_DOCUMENT_ID",
          "partial-corr-secondary-key": "PASSENGER_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "PASSENGER_ID",
          "partial-corr-secondary-key": "TRAVEL_DOCUMENT_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "TKTEMD", "name": "TRAVEL_DOCUMENT_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSPAX", "name": "PASSENGER_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "PASSENGER_ID", "correlation-field-b-to-a": "TRAVEL_DOCUMENT_ID"
    }
  },
  {
    "name": "ASSO_COUPON_SEGMENT_DELIVERY",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE"]],
    "latest": {
      "histo-table-name": "ASSO_COUPON_SEGMENT_DELIVERY_HISTO"
    }
  },
  {
    "name": "ASSO_COUPON_SEGMENT_DELIVERY_HISTO",                // TKT<->PAX #2/3
    "zorder-columns": ["COUPON_ID", "SEGMENT_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "DCSPAX", "table-name": "INTERNAL_ASSO_COUPON_SEGMENT_DELIVERY_HISTO", "version-column-name": "VERSION"
      },
      "domain-b": "TKTEMD",
      "target": {
        "columns": [
          {"name": "COUPON_ID", "fk" :[{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "SEGMENT_DELIVERY_ID", "fk" :[{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate-cpnNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}], "column-type": ${versionTktEmdTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "SEGMENT_DELIVERY_ID", "domain-b-key": "COUPON_ID", "domain-a-version": "VERSION_PASSENGER",
        "domain-b-version": "VERSION_TRAVEL_DOCUMENT", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_COUPON_SEGMENT_DELIVERY_HISTO",                // TKT<->PAX #2/3
    "zorder-columns": ["COUPON_ID", "SEGMENT_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_ASSO_COUPON_SEGMENT_DELIVERY_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "COUPON_ID",
          "partial-corr-secondary-key": "SEGMENT_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_ASSO_COUPON_SEGMENT_DELIVERY_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "SEGMENT_DELIVERY_ID",
          "partial-corr-secondary-key": "COUPON_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "TKTEMD", "name": "COUPON_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSPAX", "name": "SEGMENT_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_COUPON",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate-cpnNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_PASSENGER",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "meta": {"description": {"value": "Whether the segment delivery is from a TKT or EMD document", "rule": "replace"}, "example": {"value": "TKT", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "PASSENGER_ID", "correlation-field-b-to-a": "TRAVEL_DOCUMENT_ID"
    }
  },
  {
    "name": "ASSO_COUPON_SERVICE_DELIVERY",                // TKT<->PAX #3/3
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE"]],
    "latest": {
      "histo-table-name": "ASSO_COUPON_SERVICE_DELIVERY_HISTO"
    }
  },
  {
    "name": "ASSO_COUPON_SERVICE_DELIVERY_HISTO",
    "zorder-columns": ["COUPON_ID", "SERVICE_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "DCSPAX", "table-name": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO", "version-column-name": "VERSION"
      },
      "domain-b": "TKTEMD",
      "target": {
        "columns": [
          {"name": "COUPON_ID", "fk" :[{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "SERVICE_DELIVERY_ID", "fk" :[{"schema": "DCSPAX", "table": "FACT_SERVICE_DELIVERY_HISTO", "column": "SERVICE_DELIVERY_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate-cpnNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000999", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}], "column-type": ${versionTktEmdTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SERVICE_DELIVERY_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "SERVICE_DELIVERY_ID", "domain-b-key": "COUPON_ID", "domain-a-version": "VERSION_PASSENGER",
        "domain-b-version": "VERSION_TRAVEL_DOCUMENT", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_COUPON_SERVICE_DELIVERY_HISTO",
    "zorder-columns": ["COUPON_ID", "SERVICE_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "COUPON_ID",
          "partial-corr-secondary-key": "SERVICE_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "SERVICE_DELIVERY_ID",
          "partial-corr-secondary-key": "COUPON_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "TKTEMD", "name": "COUPON_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSPAX", "name": "SERVICE_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SERVICE_DELIVERY_HISTO", "column": "SERVICE_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SERVICE_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_COUPON",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate-cpnNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_PASSENGER",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_SERVICE_DELIVERY",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000999", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "PASSENGER_ID", "correlation-field-b-to-a": "TRAVEL_DOCUMENT_ID"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_BAG_GROUP",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_DCS_PASSENGER_BAG_GROUP_HISTO"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_BAG_GROUP_HISTO",                // PAX<->BAG #1/4
    "zorder-columns": ["PASSENGER_ID", "BAG_GROUP_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE", "DIH_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSPAX_DCSBAG", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "PASSENGER_ID",
          "partial-corr-secondary-key": "BAG_GROUP_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_DCSPAX", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "BAG_GROUP_ID",
          "partial-corr-secondary-key": "PASSENGER_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_GROUP_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "PASSENGER_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_GROUP_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_GROUP_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_BAG_GROUP_HISTO",                // PAX<->BAG #1/4
    "zorder-columns": ["INTERNAL_CORR_ID", "BAG_GROUP_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE", "DAAS_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSPAX_DCSBAG", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "INTERNAL_CORR_ID",
          "partial-corr-secondary-key": "BAG_GROUP_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_DCSPAX", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "BAG_GROUP_ID",
          "partial-corr-secondary-key": "INTERNAL_CORR_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_GROUP_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "PASSENGER_RESPONSIBLE_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}], "source": {"domain-a-pit": "PASSENGER_ID"}
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_GROUP_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER_RESPONSIBLE", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "INTERNAL_CORR_ID", "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_GROUP_ID", "correlation-field-b-to-a": "INTERNAL_CORR_ID"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_BAG",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_DCS_PASSENGER_BAG_HISTO"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_BAG_HISTO",                // PAX<->BAG #2/4
    "zorder-columns": ["PASSENGER_ID", "BAG_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE", "DIH_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_OWNER_PASSENGER", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "PASSENGER_ID",
          "partial-corr-secondary-key": "BAG_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_OWNER_PASSENGER", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "BAG_ID",
          "partial-corr-secondary-key": "PASSENGER_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "PASSENGER_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_HISTO", "column": "BAG_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESPONSIBLE_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "RESPONSIBLE_PASSENGER_ID", "meta": {"description": {"value": "Hash of the functional key of the unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291-10FBC42026758255", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_BAG_HISTO",                // PAX<->BAG #2/4
    "zorder-columns": ["INTERNAL_CORR_ID", "BAG_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE", "DAAS_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_OWNER_PASSENGER", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "INTERNAL_CORR_ID",
          "partial-corr-secondary-key": "BAG_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_OWNER_PASSENGER", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "BAG_ID",
          "partial-corr-secondary-key": "INTERNAL_CORR_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "PASSENGER_RESPONSIBLE_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}], "source": {"domain-a-pit": "PASSENGER_ID"}
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_HISTO", "column": "BAG_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER_RESPONSIBLE", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "PASSENGER_OWNER_ID", "meta": {"description": {"value": "Hash of the functional key of the unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER_OWNER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291-10FBC42026758255", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "INTERNAL_CORR_ID", "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_ID", "correlation-field-b-to-a": "INTERNAL_CORR_ID"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_BAG_LEG_DELIVERY",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_SEGMENT_DELIVERY_BAG_LEG_DELIVERY_HISTO"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_BAG_LEG_DELIVERY_HISTO",                // PAX<->BAG #3/4
    "zorder-columns": ["SEGMENT_DELIVERY_ID", "BAG_LEG_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE", "DIH_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSPAX_SEGMENT_DEL_DCSBAG_LEG_DEL", "start-date": "DATE_BEGIN",
          "end-date": "DATE_END", "partial-corr-version": "VERSION", "partial-corr-key": "SEGMENT_DELIVERY_ID",
          "partial-corr-secondary-key": "BAG_LEG_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_LEG_DEL_DCSPAX_SEGMENT_DEL", "start-date": "DATE_BEGIN",
          "end-date": "DATE_END", "partial-corr-version": "VERSION", "partial-corr-key": "BAG_LEG_DELIVERY_ID",
          "partial-corr-secondary-key": "SEGMENT_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_GROUP_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "SEGMENT_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_LEG_DELIVERY_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "BAG_LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "meta": {"description": {"value": "Functional key: bagsGroupId-bagId-LegId", "rule": "replace"}, "example": {"value": "10AC810000344E97-10AC81000060D17B-MUC", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_GROUP_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_BAG_LEG_DELIVERY_HISTO",                // PAX<->BAG #3/4
    "zorder-columns": ["INTERNAL_CORR_ID", "BAG_LEG_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE", "DAAS_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          // (not used) - it is empty by model definition
          "table": "INTERNAL_PARTIAL_CORR_DCSPAX_SEGMENT_DEL_DCSBAG_LEG_DEL", "start-date": "DATE_BEGIN",
          "end-date": "DATE_END", "partial-corr-version": "VERSION", "partial-corr-key": "INTERNAL_CORR_ID",
          "partial-corr-secondary-key": "BAG_LEG_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          // (used) - for each SEGMENT_DELIVERY_ID from DCSPAX.SEG_DEL
          //    keep the correlation DCSBAG_LEG_DEL-DCSPAX_SEG from the DCSBAG.PARTIAL to generate the correlation DCSBAG_LEG_DEL-DCSPAX_SEG_DEL
          // INTERNAL_CORR_ID is at SEG lvl, SEGMENT_DELIVERY_ID is lower than INTERNAL_CORR_ID
          "table": "FACT_SEGMENT_DELIVERY_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION", "pit-corr-key": "SEGMENT_DELIVERY_ID"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          // (used) - correlation between DCSBAG_LEG_DEL and DCSPAX_SEG lvl (note: at DCSPAX_SEG lvl and not at the lower lvl DCSPAX_SEGMENT_DEL)
          // INTERNAL_CORR_ID is at SEG lvl, SEG_DELIVERY_ID is not available in DCSBAG
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_LEG_DEL_DCSPAX_SEGMENT_DEL", "start-date": "DATE_BEGIN",
          "end-date": "DATE_END", "partial-corr-version": "VERSION", "partial-corr-key": "BAG_LEG_DELIVERY_ID",
          "partial-corr-secondary-key": "INTERNAL_CORR_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          // (not used) - as its domain-a.partial is empty
          "table": "FACT_BAG_LEG_DELIVERY_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "SEGMENT_DELIVERY_RESPONSIBLE_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}], "source": {"domain-a-pit": "SEGMENT_DELIVERY_ID"}
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_LEG_DELIVERY_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "BAG_LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER_RESPONSIBLE", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: RESPONSIBLE UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER_OWNER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY_RESPONSIBLE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: RESPONSIBLE UCI-segmentId", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}, "source": {"domain-a-pit": "REFERENCE_KEY"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "meta": {"description": {"value": "Functional key: bagsGroupId-bagId-LegId", "rule": "replace"}, "example": {"value": "10AC810000344E97-10AC81000060D17B-MUC", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "INTERNAL_CORR_ID", "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}},
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_LEG_DELIVERY_ID", "correlation-field-b-to-a": "INTERNAL_CORR_ID"
    }
  },
  {
    "name": "ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY_HISTO"
    }
  },
  {
    "name": "ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY_HISTO",                // PAX<->BAG #4/4
    "zorder-columns": ["LEG_DELIVERY_ID", "BAG_LEG_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE", "DIH_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSPAX_LEG_DEL_DCSBAG_LEG_DEL", "start-date": "DATE_BEGIN",
          "end-date": "DATE_END", "partial-corr-version": "VERSION", "partial-corr-key": "LEG_DELIVERY_ID",
          "partial-corr-secondary-key": "BAG_LEG_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_LEG_DEL_DCSPAX_LEG_DEL", "start-date": "DATE_BEGIN",
          "end-date": "DATE_END", "partial-corr-version": "VERSION", "partial-corr-key": "BAG_LEG_DELIVERY_ID",
          "partial-corr-secondary-key": "LEG_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_GROUP_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "LEG_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_LEG_DELIVERY_HISTO", "column": "LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_LEG_DELIVERY_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "BAG_LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_LEG_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_LEG_DELIVERY", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI-leg", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "meta": {"description": {"value": "Functional key: bagsGroupId-bagId-LegId", "rule": "replace"}, "example": {"value": "10AC810000344E97-10AC81000060D17B-MUC", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_GROUP_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_LEG_DELIVERY_BAG_LEG_DELIVERY_HISTO",                // PAX<->BAG #4/4
    "zorder-columns": ["INTERNAL_CORR_ID", "BAG_LEG_DELIVERY_ID"],
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE", "DAAS_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX",
        "partial": {
          // (not used) - it is empty by model definition
          "table": "INTERNAL_PARTIAL_CORR_DCSPAX_LEG_DEL_DCSBAG_LEG_DEL", "start-date": "DATE_BEGIN",
          "end-date": "DATE_END", "partial-corr-version": "VERSION", "partial-corr-key": "INTERNAL_CORR_ID",
          "partial-corr-secondary-key": "BAG_LEG_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          // (used) - for each LEG_DELIVERY_ID from DCSPAX.PIT
          //    keep the correlation DCSBAG_LEG_DEL-DCSPAX_LEG from the DCSBAG.PARTIAL to generate the correlation DCSBAG_LEG_DEL-DCSPAX_LEG_DEL
          // INTERNAL_CORR_ID is at LEG lvl, LEG_DELIVERY_ID is lower than INTERNAL_CORR_ID
          "table": "FACT_LEG_DELIVERY_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION", "pit-corr-key": "LEG_DELIVERY_ID"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          // (used) - correlation between DCSBAG_LEG_DEL and DCSPAX_LEG lvl (note: at DCSPAX_LEG lvl and not at the lower lvl DCSPAX_LEG_DEL)
          // INTERNAL_CORR_ID is at LEG lvl, LEG_DELIVERY_ID is not available in DCSBAG
          "table": "INTERNAL_PARTIAL_CORR_DCSBAG_LEG_DEL_DCSPAX_LEG_DEL", "start-date": "DATE_BEGIN",
          "end-date": "DATE_END", "partial-corr-version": "VERSION", "partial-corr-key": "BAG_LEG_DELIVERY_ID",
          "partial-corr-secondary-key": "INTERNAL_CORR_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          // (not used) - as its domain-a.partial is empty
          "table": "FACT_BAG_LEG_DELIVERY_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "LEG_DELIVERY_RESPONSIBLE_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_LEG_DELIVERY_HISTO", "column": "LEG_DELIVERY_ID"}], "source": {"domain-a-pit": "LEG_DELIVERY_ID"}
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_LEG_DELIVERY_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "BAG_LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER_RESPONSIBLE", "fk": [{"schema": "DCSPAX", "table": "FACT_LEG_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: RESPONSIBLE UCI", "rule": "replace"}, "example": {"value": "2501ADE000000011", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_PASSENGER_OWNER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_LEG_DELIVERY_RESPONSIBLE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-segmentId-legId", "example": {"value": "2501ADE000000011-6X-1234-MUC-JFK-MUC"}, "rule": "replace"}, "gdpr-zone": "orange"}, "source": {"domain-a-pit": "REFERENCE_KEY"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "meta": {"description": {"value": "Functional key: bagsGroupId-bagId-LegId", "rule": "replace"}, "example": {"value": "10AC810000344E97-10AC81000060D17B-MUC", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "INTERNAL_CORR_ID", "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}},
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_LEG_DELIVERY_ID", "correlation-field-b-to-a": "INTERNAL_CORR_ID"
    }
  },
  {
    "name": "ASSO_RESERVATION_BAG_GROUP",
    "table-selectors": [["DCSBAG_ACTIVE","PNR_ACTIVE"],["DCSBAG_ACTIVE","PNR_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_RESERVATION_BAG_GROUP_HISTO"
    }
  },
  {
    "name": "ASSO_RESERVATION_BAG_GROUP_HISTO",                // PNR<->BAG #1/3
    "zorder-columns": ["RESERVATION_ID", "BAG_GROUP_ID"],
    "table-selectors": ["DCSBAG_ACTIVE", "PNR_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "DCSBAG", "table-name": "INTERNAL_ASSO_RESERVATION_BAG_GROUP_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "RESERVATION_ID", "fk" :[{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}], "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "BAG_GROUP_ID", "fk" :[{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}], "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "VERSION"}], "column-type": ${versionBagTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "domain-a-key": "BAG_GROUP_ID",
        "domain-b-key": "RESERVATION_ID",
        "domain-a-version": "VERSION_BAG_GROUP",
        "domain-b-version": "VERSION_RESERVATION",
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_RESERVATION_BAG_GROUP_HISTO",                // PNR<->BAG #1/3
    "zorder-columns": ["RESERVATION_ID", "BAG_GROUP_ID"],
    "table-selectors": ["DCSBAG_ACTIVE", "PNR_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_RESERVATION_BAG_GROUP_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "RESERVATION_ID",
          "partial-corr-secondary-key": "BAG_GROUP_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_ASSO_RESERVATION_BAG_GROUP_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "BAG_GROUP_ID",
          "partial-corr-secondary-key": "RESERVATION_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_GROUP_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "RESERVATION_ID", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_GROUP_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_GROUP_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_TRAVELER_BAG",
    "table-selectors":  [["DCSBAG_ACTIVE","PNR_ACTIVE"],["DCSBAG_ACTIVE","PNR_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_TRAVELER_BAG_HISTO"
    }
  },
  {
    "name": "ASSO_TRAVELER_BAG_HISTO",                        // PNR<->BAG #2/3
    "zorder-columns": ["TRAVELER_ID", "BAG_ID"],
    "table-selectors": ["DCSBAG_ACTIVE","PNR_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "DCSBAG",
        "table-name": "INTERNAL_ASSO_TRAVELER_BAG_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "TRAVELER_ID", "fk" :[{"schema": "PNR", "table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}], "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "BAG_ID", "fk" :[{"schema": "DCSBAG", "table": "FACT_BAG_HISTO", "column": "BAG_ID"}], "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_TRAVELER", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: pnrId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagId", "rule": "replace"}, "example": {"value": "10BE68000302AED8", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_TRAVELER_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_HISTO", "column": "VERSION"}], "column-type": ${versionBagTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "domain-a-key": "BAG_ID",
        "domain-b-key": "TRAVELER_ID",
        "domain-a-version": "VERSION_BAG_GROUP",
        "domain-b-version": "VERSION_RESERVATION",
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_TRAVELER_BAG_HISTO",                        // PNR<->BAG #2/3
    "zorder-columns": ["TRAVELER_ID", "BAG_ID"],
    "table-selectors": ["DCSBAG_ACTIVE", "PNR_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_TRAVELER_BAG_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "TRAVELER_ID",
          "partial-corr-secondary-key": "BAG_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_ASSO_TRAVELER_BAG_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "BAG_ID",
          "partial-corr-secondary-key": "TRAVELER_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_GROUP_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "TRAVELER_ID", "fk": [{"schema": "PNR", "table": "FACT_TRAVELER_HISTO", "column": "TRAVELER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_ID", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_HISTO", "column": "BAG_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_TRAVELER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk": [{"schema": "DCSBAG", "table": "FACT_BAG_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_TRAVELER",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: pnrId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagId", "rule": "replace"}, "example": {"value": "10BE68000302AED8", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_GROUP_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY",
    "table-selectors": [["DCSBAG_ACTIVE","PNR_ACTIVE"],["DCSBAG_ACTIVE","PNR_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO",      // PNR<->BAG #3/3
    "zorder-columns": ["AIR_SEGMENT_PAX_ID", "BAG_LEG_DELIVERY_ID"],
    "table-selectors": ["DCSBAG_ACTIVE", "PNR_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "DCSBAG",
        "table-name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "fk":[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}], "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "BAG_LEG_DELIVERY_ID", "fk" :[{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "BAG_LEG_DELIVERY_ID"}], "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: airSegmentPaxId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01-ST1-ABCDEF-2023-01-01-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "Functional key: bagsGroupId-bagId-LegId", "rule": "replace"}, "example": {"value": "10AC810000344E97-10AC81000060D17B-MUC", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_BAG_GROUP", "fk" :[{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "VERSION"}], "column-type": ${versionBagTypeVal}, "sources": {},
            "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "domain-a-key": "BAG_LEG_DELIVERY_ID",
        "domain-b-key": "AIR_SEGMENT_PAX_ID",
        "domain-a-version": "VERSION_BAG_GROUP",
        "domain-b-version": "VERSION_RESERVATION",
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO",      // PNR<->BAG #3/3
    "zorder-columns": ["AIR_SEGMENT_PAX_ID", "BAG_LEG_DELIVERY_ID"],
    "table-selectors": ["DCSBAG_ACTIVE", "PNR_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "AIR_SEGMENT_PAX_ID",
          "partial-corr-secondary-key": "BAG_LEG_DELIVERY_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO", "is-last": "IS_LAST_VERSION", "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "DCSBAG", "partial": {
          "table": "INTERNAL_ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
          "partial-corr-version": "VERSION", "partial-corr-key": "BAG_LEG_DELIVERY_ID",
          "partial-corr-secondary-key": "AIR_SEGMENT_PAX_ID", "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_BAG_GROUP_HISTO", "pit-version": "VERSION", "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "AIR_SEGMENT_PAX_ID", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_PAX_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "DCSBAG", "name": "BAG_LEG_DELIVERY_ID", "fk" :[{"schema": "DCSBAG", "table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "BAG_LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "DCSBAG", "name": "VERSION_BAG_GROUP", "fk" :[{"schema": "DCSBAG", "table": "FACT_BAG_GROUP_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: reservationId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "meta": {"description": {"value": "The unique functional identifier of the correlated object: airSegmentPaxId", "rule": "replace"}, "example": {"value": "ABCDEF-2023-01-01-ST1-ABCDEF-2023-01-01-PT1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_BAG_GROUP", "meta": {"description": {"value": "The unique functional identifier of the correlated object: bagGroupId", "rule": "replace"}, "example": {"value": "10FBC42026758291", "rule": "replace"}, "gdpr-zone": "orange"}}
          {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "meta": {"description": {"value": "Functional key: bagsGroupId-bagId-LegId", "rule": "replace"}, "example": {"value": "10AC810000344E97-10AC81000060D17B-MUC", "rule": "replace"}, "gdpr-zone": "orange"}}
        ],
        "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "BAG_GROUP_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_FLIGHT_DATE",
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_DCS_PASSENGER_FLIGHT_DATE_HISTO"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_FLIGHT_DATE_HISTO",                 // SKD<->PAX #1/4
    "zorder-columns": ["PASSENGER_ID", "FLIGHT_DATE_ID"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCS_PASSENGER_FLIGHT_DATE",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "PASSENGER_ID",
          "partial-corr-secondary-key": "FLIGHT_DATE_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_DATE_DCS_PAX",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "FLIGHT_DATE_ID",
          "partial-corr-secondary-key": "PASSENGER_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "PASSENGER_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_DATE_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_DCS_PASSENGER_FLIGHT_DATE_HISTO",                 // SKD<->PAX #1/4
    "zorder-columns": ["PASSENGER_ID", "INTERNAL_CORR_ID"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DAAS_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCS_PASSENGER_FLIGHT_DATE",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "PASSENGER_ID",
          "partial-corr-secondary-key": "INTERNAL_CORR_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_DATE_DCS_PAX",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "INTERNAL_CORR_ID",
          "partial-corr-secondary-key": "PASSENGER_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "PASSENGER_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_DATE_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}], "source": {"domain-b-pit": "FLIGHT_DATE_ID"}
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_PASSENGER_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}, "source": {"domain-b-pit": "REFERENCE_KEY"}},
          {"name": "INTERNAL_CORR_ID", "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "INTERNAL_CORR_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_FLIGHT_SEGMENT",
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_SEGMENT_DELIVERY_FLIGHT_SEGMENT_HISTO"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_FLIGHT_SEGMENT_HISTO",                  // SKD<->PAX #2/4
    "zorder-columns": ["SEGMENT_DELIVERY_ID", "FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSPAX_SEGMENT_DEL_SKD_FLIGHT_SEGMENT",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "SEGMENT_DELIVERY_ID",
          "partial-corr-secondary-key": "FLIGHT_SEGMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_SEGMENT_DCSPAX_SEGMENT_DEL",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "FLIGHT_SEGMENT_ID",
          "partial-corr-secondary-key": "SEGMENT_DELIVERY_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "SEGMENT_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_SEGMENT_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-depDate-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_FLIGHT_SEGMENT_HISTO",                  // SKD<->PAX #2/4
    "zorder-columns": ["SEGMENT_DELIVERY_ID", "INTERNAL_CORR_ID"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DAAS_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_DCSPAX_SEGMENT_DEL_SKD_FLIGHT_SEGMENT",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "SEGMENT_DELIVERY_ID",
          "partial-corr-secondary-key": "INTERNAL_CORR_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_SEGMENT_DELIVERY_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_SEGMENT_DCSPAX_SEGMENT_DEL",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "INTERNAL_CORR_ID",
          "partial-corr-secondary-key": "SEGMENT_DELIVERY_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_SEGMENT_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "SEGMENT_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_SEGMENT_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}], "source": {"domain-b-pit": "FLIGHT_SEGMENT_ID"}
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}, "source": {"domain-b-pit": "FLIGHT_DATE_IDENTIFIER"}},
          {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-depDate-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}, "source": {"domain-b-pit": "REFERENCE_KEY"}},
          {"name": "INTERNAL_CORR_ID", "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "INTERNAL_CORR_ID", "correlation-field-b-to-a": "SEGMENT_DELIVERY_ID"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_CODESHARE_FLIGHT_SEGMENT",
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_SEGMENT_DELIVERY_CODESHARE_FLIGHT_SEGMENT_HISTO"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_CODESHARE_FLIGHT_SEGMENT_HISTO",                 // SKD<->PAX #3/4
    "zorder-columns": ["SEGMENT_DELIVERY_ID", "CODESHARE_FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SEGMENT_DEL_CODESHARE_FLIGHT_SEGMENT",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "SEGMENT_DELIVERY_ID",
          "partial-corr-secondary-key": "CODESHARE_FLIGHT_SEGMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SKD_CODESHARE_DCSPAX_FLIGHT_SEGMENT_DEL",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "CODESHARE_FLIGHT_SEGMENT_ID",
          "partial-corr-secondary-key": "SEGMENT_DELIVERY_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "SEGMENT_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "CODESHARE_FLIGHT_SEGMENT_ID", "fk": [{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "CODESHARE_FLIGHT_SEGMENT_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight segment key)-partnerCarrier-partnerFlightNum-partnerSuffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG-8X-5900-D-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_CODESHARE_FLIGHT_SEGMENT_HISTO",                 // SKD<->PAX #3/4
    "zorder-columns": ["SEGMENT_DELIVERY_ID", "INTERNAL_CORR_ID"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DAAS_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SEGMENT_DEL_CODESHARE_FLIGHT_SEGMENT",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "SEGMENT_DELIVERY_ID",
          "partial-corr-secondary-key": "INTERNAL_CORR_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_SEGMENT_DELIVERY_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SKD_CODESHARE_DCSPAX_FLIGHT_SEGMENT_DEL",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "INTERNAL_CORR_ID",
          "partial-corr-secondary-key": "SEGMENT_DELIVERY_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "SEGMENT_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "CODESHARE_FLIGHT_SEGMENT_ID", "fk": [{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "CODESHARE_FLIGHT_SEGMENT_ID"}], "source": {"domain-b-pit": "CODESHARE_FLIGHT_SEGMENT_ID"}
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_SEGMENT_DELIVERY",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI", "rule": "replace"}, "example": {"value": "2501ADE000000001-2501ADF000000888", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}, "source": {"domain-b-pit": "FLIGHT_DATE_IDENTIFIER"}},
          {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight segment key)-partnerCarrier-partnerFlightNum-partnerSuffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG-8X-5900-D-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}, "source": {"domain-b-pit": "REFERENCE_KEY"}},
          {"name": "INTERNAL_CORR_ID", "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "INTERNAL_CORR_ID", "correlation-field-b-to-a": "SEGMENT_DELIVERY_ID"
    }
  },
  {
    "name": "ASSO_LEG_DELIVERY_FLIGHT_LEG",
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE"],
    "latest": {
      "histo-table-name": "ASSO_LEG_DELIVERY_FLIGHT_LEG_HISTO"
    }
  },
  {
    "name": "ASSO_LEG_DELIVERY_FLIGHT_LEG_HISTO",                     // SKD<->PAX #4/4
    "zorder-columns": ["LEG_DELIVERY_ID", "FLIGHT_LEG_ID"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_LEG_DEL_FLIGHT_LEG",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "LEG_DELIVERY_ID",
          "partial-corr-secondary-key": "FLIGHT_LEG_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_PASSENGER_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_LEG_DCSPAX_LEG_DEL",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "FLIGHT_LEG_ID",
          "partial-corr-secondary-key": "LEG_DELIVERY_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "LEG_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_LEG_DELIVERY_HISTO", "column": "LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_LEG_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_LEG_HISTO", "column": "FLIGHT_LEG_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_LEG_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_LEG_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_LEG_DELIVERY", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI-leg", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_FLIGHT_LEG", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "PASSENGER_ID"
    }
  },
  {
    "name": "ASSO_LEG_DELIVERY_FLIGHT_LEG_HISTO",                     // SKD<->PAX #4/4
    "zorder-columns": ["LEG_DELIVERY_ID", "INTERNAL_CORR_ID"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DAAS_CORRELATION"],
    "correlation": {
      "domain-a": {
        "name": "DCSPAX", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_LEG_DEL_FLIGHT_LEG",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "LEG_DELIVERY_ID",
          "partial-corr-secondary-key": "INTERNAL_CORR_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_LEG_DELIVERY_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_LEG_DCSPAX_LEG_DEL",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "INTERNAL_CORR_ID",
          "partial-corr-secondary-key": "LEG_DELIVERY_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_LEG_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "DCSPAX", "name": "LEG_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_LEG_DELIVERY_HISTO", "column": "LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_LEG_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_LEG_HISTO", "column": "FLIGHT_LEG_ID"}], "source": {"domain-b-pit": "FLIGHT_LEG_ID"}
          , "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "DCSPAX", "name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_LEG_DELIVERY_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_LEG_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_PASSENGER", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI", "rule": "replace"}, "example": {"value": "2501ADE000000001", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_LEG_DELIVERY", "meta": {"description": {"value": "The unique functional identifier of the correlated object: UCI-UPI-leg", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}, "source": {"domain-b-pit": "FLIGHT_DATE_IDENTIFIER"}},
          {"name": "REFERENCE_KEY_FLIGHT_LEG", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}, "source": {"domain-b-pit": "REFERENCE_KEY"}},
          {"name": "INTERNAL_CORR_ID", "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "INTERNAL_CORR_ID", "correlation-field-b-to-a": "LEG_DELIVERY_ID"
    }
  },
  {
    "name": "ASSO_RESERVATION_FLIGHT_DATE",
    "table-selectors": [["SKD_ACTIVE", "PNR_ACTIVE"],["SKD_ACTIVE", "PNR_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_RESERVATION_FLIGHT_DATE_HISTO"
    }
  },
  {
    "name": "ASSO_RESERVATION_FLIGHT_DATE_HISTO",                        // SKD<->PNR #1/3
    "zorder-columns": ["RESERVATION_ID", "FLIGHT_DATE_ID"],
    "table-selectors": ["SKD_ACTIVE", "PNR_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_PARTIAL_PNR_RESERVATION_SKD_FLIGHT_DATE_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "RESERVATION_ID",
          "partial-corr-secondary-key": "FLIGHT_DATE_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_ASSO_RESERVATION_FLIGHT_DATE_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "FLIGHT_DATE_ID",
          "partial-corr-secondary-key": "RESERVATION_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "RESERVATION_ID", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_DATE_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_RESERVATION_FLIGHT_DATE_HISTO",
    "zorder-columns": ["RESERVATION_ID", "FLIGHT_DATE_ID"],
    "table-selectors": ["SKD_ACTIVE", "PNR_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "SKD", "table-name": "INTERNAL_ASSO_RESERVATION_FLIGHT_DATE_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "RESERVATION_ID", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "RESERVATION_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "FLIGHT_DATE_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_RESERVATION", "fk": [{"schema": "PNR", "table": "FACT_RESERVATION_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "FLIGHT_DATE_ID", "domain-b-key": "RESERVATION_ID", "domain-a-version": "VERSION_FLIGHT_DATE",
        "domain-b-version": "VERSION_RESERVATION", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT",
    "table-selectors": [["SKD_ACTIVE", "PNR_ACTIVE"],["SKD_ACTIVE", "PNR_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO",                     // SKD<->PNR #2/3
    "zorder-columns": ["AIR_SEGMENT_ID", "CODESHARE_FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "PNR_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_PNR_AIR_SEGMENT_SKD_CODESHARE_FLIGHT_SEGMENT_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "AIR_SEGMENT_ID",
          "partial-corr-secondary-key": "CODESHARE_FLIGHT_SEGMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      },"domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "CODESHARE_FLIGHT_SEGMENT_ID",
          "partial-corr-secondary-key": "AIR_SEGMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      },  "target": {
        "domain-a-key": {"schema": "PNR", "name": "AIR_SEGMENT_ID", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "CODESHARE_FLIGHT_SEGMENT_ID", "fk" :[{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "CODESHARE_FLIGHT_SEGMENT_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk" :[{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: segmentId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight segment key)-partnerCarrier-partnerFlightNum-partnerSuffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG-8X-5900-D-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["AIR_SEGMENT_ID", "CODESHARE_FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "PNR_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "SKD", "table-name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "AIR_SEGMENT_ID", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "fk" :[{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "CODESHARE_FLIGHT_SEGMENT_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: segmentId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight segment key)-partnerCarrier-partnerFlightNum-partnerSuffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG-8X-5900-D-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_FLIGHT_DATE", "fk" :[{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "CODESHARE_FLIGHT_SEGMENT_ID", "domain-b-key": "AIR_SEGMENT_ID", "domain-a-version": "VERSION_FLIGHT_DATE",
        "domain-b-version": "VERSION_RESERVATION", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT",
    "table-selectors": [["SKD_ACTIVE", "PNR_ACTIVE"],["SKD_ACTIVE", "PNR_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO",                   // SKD<->PNR #3/3
    "zorder-columns": ["AIR_SEGMENT_ID", "FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "PNR_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "PNR", "partial": {
          "table": "INTERNAL_PARTIAL_CORR_PNR_AIR_SEGMENT_SKD_FLIGHT_SEGMENT_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "AIR_SEGMENT_ID",
          "partial-corr-secondary-key": "FLIGHT_SEGMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_RESERVATION_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      },
      "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "FLIGHT_SEGMENT_ID",
          "partial-corr-secondary-key": "AIR_SEGMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "PNR", "name": "AIR_SEGMENT_ID", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_SEGMENT_ID", "fk" :[{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "PNR", "name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk" :[{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_RESERVATION", "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: segmentId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-depDate-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "RESERVATION_ID"
    }
  },
  {
    "name": "ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["AIR_SEGMENT_ID", "FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "PNR_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "SKD", "table-name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "AIR_SEGMENT_ID", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "AIR_SEGMENT_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "FLIGHT_SEGMENT_ID", "fk" :[{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: rloc-pnrCreationDate", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: segmentId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-ST-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-depDate-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_RESERVATION", "fk" :[{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_FLIGHT_DATE", "fk" :[{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "FLIGHT_SEGMENT_ID", "domain-b-key": "AIR_SEGMENT_ID", "domain-a-version": "VERSION_FLIGHT_DATE",
        "domain-b-version": "VERSION_RESERVATION", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE",
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_ACTIVE"],["SKD_ACTIVE", "TKTEMD_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO"
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO",                 // SKD<->TKT #1/3
    "zorder-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID"],
    "table-selectors": ["SKD_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_PARTIAL_TKTEMD_TRAVEl_DOCUMENT_SKD_FLIGHT_DATE_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "TRAVEL_DOCUMENT_ID",
          "partial-corr-secondary-key": "FLIGHT_DATE_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "FLIGHT_DATE_ID",
          "partial-corr-secondary-key": "TRAVEL_DOCUMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "TKTEMD", "name": "TRAVEL_DOCUMENT_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_DATE_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "TRAVEL_DOCUMENT_ID"
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO",
    "zorder-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID"],
    "table-selectors": ["SKD_ACTIVE", "TKTEMD_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "SKD", "table-name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "TRAVEL_DOCUMENT_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "FLIGHT_DATE_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_DATE_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "FLIGHT_DATE_ID", "domain-b-key": "TRAVEL_DOCUMENT_ID", "domain-a-version": "VERSION_FLIGHT_DATE",
        "domain-b-version": "VERSION_TRAVEL_DOCUMENT", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_COUPON_FLIGHT_SEGMENT",
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_ACTIVE"],["SKD_ACTIVE", "TKTEMD_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_COUPON_FLIGHT_SEGMENT_HISTO"
    }
  },
  {
    "name": "ASSO_COUPON_FLIGHT_SEGMENT_HISTO",                      // SKD<->TKT #2/3
    "zorder-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_PARTIAL_TKTEMD_COUPON_SKD_FLIGHT_SEGMENT_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "COUPON_ID",
          "partial-corr-secondary-key": "FLIGHT_SEGMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_ASSO_COUPON_FLIGHT_SEGMENT_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "FLIGHT_SEGMENT_ID",
          "partial-corr-secondary-key": "COUPON_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "TKTEMD", "name": "COUPON_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "FLIGHT_SEGMENT_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_COUPON",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate-cpnNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-depDate-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-depDate-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "TRAVEL_DOCUMENT_ID"
    }
  },
  {
    "name": "ASSO_COUPON_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "TKTEMD_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "SKD", "table-name": "INTERNAL_ASSO_COUPON_FLIGHT_SEGMENT_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "COUPON_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "FLIGHT_SEGMENT_ID", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate-cpnNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight date key)-depDate-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "FLIGHT_SEGMENT_ID", "domain-b-key": "COUPON_ID", "domain-a-version": "VERSION_FLIGHT_DATE",
        "domain-b-version": "VERSION_TRAVEL_DOCUMENT", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  },
  {
    "name": "ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT",
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_ACTIVE"],["SKD_ACTIVE", "TKTEMD_PASSIVE"]],
    "latest": {
      "histo-table-name": "ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO"
    }
  },
  {
    "name": "ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO",                        // SKD<->TKT #3/3
    "zorder-columns": ["COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "TKTEMD_ACTIVE"],
    "correlation": {
      "domain-a": {
        "name": "TKTEMD", "partial": {
          "table": "INTERNAL_PARTIAL_TKTEMD_COUPON_SKD_CODESHARE_FLIGHT_SEGMENT_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "COUPON_ID",
          "partial-corr-secondary-key": "CODESHARE_FLIGHT_SEGMENT_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_TRAVEL_DOCUMENT_HISTO",
          "pit-version": "VERSION",
          "is-last": "IS_LAST_VERSION"
        }
      }, "domain-b": {
        "name": "SKD", "partial": {
          "table": "INTERNAL_ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO",
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "partial-corr-version": "VERSION",
          "partial-corr-key": "CODESHARE_FLIGHT_SEGMENT_ID",
          "partial-corr-secondary-key": "COUPON_ID",
          "is-last": "IS_LAST_VERSION"
        }, "pit": {
          "table": "FACT_FLIGHT_DATE_HISTO",
          "is-last": "IS_LAST_VERSION",
          "pit-version": "VERSION"
        }
      }, "target": {
        "domain-a-key": {"schema": "TKTEMD", "name": "COUPON_ID", "fk" :[{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-key": {"schema": "SKD", "name": "CODESHARE_FLIGHT_SEGMENT_ID", "fk" :[{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "CODESHARE_FLIGHT_SEGMENT_ID"}],
          "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-a-version": {"schema": "TKTEMD", "name": "VERSION_TRAVEL_DOCUMENT", "fk" :[{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "domain-b-version": {"schema": "SKD", "name": "VERSION_FLIGHT_DATE", "fk" :[{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}]
          , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        "asso-attributes": [
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_COUPON",
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate-cpnNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight segment key)-partnerCarrier-partnerFlightNum-partnerSuffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG-8X-5900-D-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}}
        ],
        "start-date": "DATE_BEGIN",
        "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION",
        "if-dupe-take-highest": [
          "DATE_BEGIN"
        ]
      }, "correlation-field-a-to-b": "FLIGHT_DATE_ID", "correlation-field-b-to-a": "TRAVEL_DOCUMENT_ID"
    }
  },
  {
    "name": "ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID"],
    "table-selectors": ["SKD_ACTIVE", "TKTEMD_PASSIVE"],
    "source-correlation": {
      "domain-a": {
        "name": "SKD", "table-name": "INTERNAL_ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO",
        "version-column-name": "VERSION"
      },
      "domain-b": "PNR",
      "target": {
        "columns": [
          {"name": "COUPON_ID", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "fk" :[{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "CODESHARE_FLIGHT_SEGMENT_ID"}], "is-mandatory": true, "column-type": "binaryStrColumn", "sources": {},
            "meta": {"description": {"value": "Hash of the functional key of the correlated object (see REFERENCE_KEY in the respective schema/table)", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {},
            "meta": {"description": {"value": "The unique functional identifier of the correlated object: docNum-issueDate-cpnNum", "rule": "replace"}, "example": {"value": "1234567890123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The unique functional identifier of the correlated object: (flight segment key)-partnerCarrier-partnerFlightNum-partnerSuffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG-8X-5900-D-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKTEMD", "table": "FACT_COUPON_HISTO", "column": "VERSION"}], "column-type": ${versionReservationTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "VERSION_FLIGHT_DATE", "fk": [{"schema": "SKD", "table": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO", "column": "VERSION"}], "column-type": ${versionPassengerTypeVal}, "sources": {}
            , "meta": {"description": {"value": "The version of the correlated object", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity start date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
            "meta": {"description": {"value": "Validity end date of correlation", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
            "meta": {"description": {"value": "True if it is the last correlation otherwise False", "rule": "replace"}, "gdpr-zone": "green"}}
        ], "domain-a-key": "CODESHARE_FLIGHT_SEGMENT_ID", "domain-b-key": "COUPON_ID", "domain-a-version": "VERSION_FLIGHT_DATE",
        "domain-b-version": "VERSION_TRAVEL_DOCUMENT", "start-date": "DATE_BEGIN", "end-date": "DATE_END",
        "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": [
          "LOAD_DATE"
        ]
      }
    }
  }
]
