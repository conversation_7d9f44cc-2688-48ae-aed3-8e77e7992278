[[{"${BASE}/core/src/main/resources/json2star_copy_sf_tables.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_copy_sf_tables.scala"], "${BASE}/core/src/main/resources/json2star_jdf_init.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_jdf_init.scala"], "${BASE}/core/src/main/resources/check-databricks-sf-consistency.scala": ["${BASE}/core/target/scala-2.12/classes/check-databricks-sf-consistency.scala"], "${BASE}/core/src/main/resources/log4j2.properties": ["${BASE}/core/target/scala-2.12/classes/log4j2.properties"], "${BASE}/core/src/main/resources/json2star_db_sf_create.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_db_sf_create.scala"], "${BASE}/core/src/main/resources/check-purge.scala": ["${BASE}/core/target/scala-2.12/classes/check-purge.scala"], "${BASE}/core/src/main/resources/json2star_db_delete.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_db_delete.scala"], "${BASE}/core/src/main/resources/check-snowflake-no-dupes-on-islatestversion-true.scala": ["${BASE}/core/target/scala-2.12/classes/check-snowflake-no-dupes-on-islatestversion-true.scala"], "${BASE}/core/src/main/resources/json2star_db_validate.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_db_validate.scala"], "${BASE}/core/src/main/resources/check-tables-count.scala": ["${BASE}/core/target/scala-2.12/classes/check-tables-count.scala"], "${BASE}/core/src/main/resources/json2star_db_create.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_db_create.scala"], "${BASE}/core/src/main/resources/json2star_copy_tables.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_copy_tables.scala"], "${BASE}/core/src/main/resources/json2star_db_sf_delete.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_db_sf_delete.scala"], "${BASE}/core/src/main/resources/check-no-null-columns.scala": ["${BASE}/core/target/scala-2.12/classes/check-no-null-columns.scala"], "${BASE}/core/src/main/resources/json2star_remove_checkpoints.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_remove_checkpoints.scala"], "${BASE}/core/src/main/resources/json2star_purge.scala": ["${BASE}/core/target/scala-2.12/classes/json2star_purge.scala"]}, {"${BASE}/core/target/scala-2.12/classes/check-snowflake-no-dupes-on-islatestversion-true.scala": ["${BASE}/core/src/main/resources/check-snowflake-no-dupes-on-islatestversion-true.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_db_validate.scala": ["${BASE}/core/src/main/resources/json2star_db_validate.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_remove_checkpoints.scala": ["${BASE}/core/src/main/resources/json2star_remove_checkpoints.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_db_delete.scala": ["${BASE}/core/src/main/resources/json2star_db_delete.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_db_create.scala": ["${BASE}/core/src/main/resources/json2star_db_create.scala"], "${BASE}/core/target/scala-2.12/classes/check-tables-count.scala": ["${BASE}/core/src/main/resources/check-tables-count.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_db_sf_create.scala": ["${BASE}/core/src/main/resources/json2star_db_sf_create.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_copy_tables.scala": ["${BASE}/core/src/main/resources/json2star_copy_tables.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_purge.scala": ["${BASE}/core/src/main/resources/json2star_purge.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_db_sf_delete.scala": ["${BASE}/core/src/main/resources/json2star_db_sf_delete.scala"], "${BASE}/core/target/scala-2.12/classes/check-purge.scala": ["${BASE}/core/src/main/resources/check-purge.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_copy_sf_tables.scala": ["${BASE}/core/src/main/resources/json2star_copy_sf_tables.scala"], "${BASE}/core/target/scala-2.12/classes/check-no-null-columns.scala": ["${BASE}/core/src/main/resources/check-no-null-columns.scala"], "${BASE}/core/target/scala-2.12/classes/json2star_jdf_init.scala": ["${BASE}/core/src/main/resources/json2star_jdf_init.scala"], "${BASE}/core/target/scala-2.12/classes/check-databricks-sf-consistency.scala": ["${BASE}/core/src/main/resources/check-databricks-sf-consistency.scala"], "${BASE}/core/target/scala-2.12/classes/log4j2.properties": ["${BASE}/core/src/main/resources/log4j2.properties"]}], {"${BASE}/core/src/main/resources/json2star_copy_sf_tables.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_copy_sf_tables.scala", "lastModified": 1749205496528}, "${BASE}/core/src/main/resources/json2star_jdf_init.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_jdf_init.scala", "lastModified": 1749205496531}, "${BASE}/core/src/main/resources/check-databricks-sf-consistency.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-databricks-sf-consistency.scala", "lastModified": 1749205496526}, "${BASE}/core/src/main/resources/log4j2.properties": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/log4j2.properties", "lastModified": 1749205496533}, "${BASE}/core/src/main/resources/json2star_db_sf_create.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_sf_create.scala", "lastModified": 1749205496530}, "${BASE}/core/src/main/resources/check-purge.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-purge.scala", "lastModified": 1749205496527}, "${BASE}/core/src/main/resources/json2star_db_delete.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_delete.scala", "lastModified": 1749205496528}, "${BASE}/core/src/main/resources/check-snowflake-no-dupes-on-islatestversion-true.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-snowflake-no-dupes-on-islatestversion-true.scala", "lastModified": 1749205496527}, "${BASE}/core/src/main/resources/json2star_db_validate.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_validate.scala", "lastModified": 1749205496531}, "${BASE}/core/src/main/resources/check-tables-count.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-tables-count.scala", "lastModified": 1749205496527}, "${BASE}/core/src/main/resources/json2star_db_create.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_create.scala", "lastModified": 1749205496528}, "${BASE}/core/src/main/resources/json2star_copy_tables.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_copy_tables.scala", "lastModified": 1749205496528}, "${BASE}/core/src/main/resources/json2star_db_sf_delete.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_sf_delete.scala", "lastModified": 1749205496530}, "${BASE}/core/src/main/resources/check-no-null-columns.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-no-null-columns.scala", "lastModified": 1749205496526}, "${BASE}/core/src/main/resources/json2star_remove_checkpoints.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_remove_checkpoints.scala", "lastModified": 1749205496532}, "${BASE}/core/src/main/resources/json2star_purge.scala": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_purge.scala", "lastModified": 1749205496532}}]