{
  "tables": [
    // validation ok because dummy configured to be ok
    {
      "name": "FACT_OK_HISTO",
      "mapping": {
        "description": {"description": "Contains information of a baggage group (a set of bags travelling together on an identical itinerary), such as number and weight of checked bags and hand bags, and the responsible passenger.", "granularity": "1 bags group"},
        "merge": {
          "key-columns": ["BAGS_GROUP_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "BAGS_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "BAGS_GROUP_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        }
      },
      "stackable-addons": [
      ]
    },
    // validation fail because missing input cols
    {
      "name": "FACT_FAIL_HISTO",
      "mapping": {
        "description": {"description": "Contains information of an individual bag", "granularity": " 1 bag in 1 bags group"},
        "merge": {
          "key-columns": ["BAG_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {"blocks": [{"base": "$.mainResource.current.image"}, {"bag": "$.bags[*]"}]}
        ],
        "columns": [
          {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}},
          {"name": "BAGS_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "BAG_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.bagType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BAG_TYPE"}]}},
          {"name": "RATE_AMOUNT", "column-type": "floatColumn", "sources": {}
            , "meta": {"description": {"value": "The rate amount of the excess baggage item, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_CURRENCY", "column-type": "strColumn", "sources": {}
            , "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {}
            , "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
          {"name": "RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.rate.currency"}]}
            , "meta": {"gdpr-zone": "green"}},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      },
      "stackable-addons": [
        {
          "type": "currency-conversion",
          "conversions": [
            {"src-col": "RATE_AMOUNT_ORIGINAL", "src-unit-col": "RATE_CURRENCY_ORIGINAL",  "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "RATE_AMOUNT", "dst-unit-col": "RATE_CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"}          ]
        }
      ]
    },
    // validation fail because latest addon not supported
    {
      "name": "FACT_FAIL",
      "latest": {
        "histo-table-name": "FACT_BAG_HISTO"
      }
      "stackable-addons": [
        {
          "type": "currency-conversion",
          "conversions": [
            {
              "src-col": "RATE_AMOUNT_ORIGINAL",
              "src-unit-col": "RATE_CURRENCY_ORIGINAL",
              "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED",
              "dst-col": "RATE_AMOUNT",
              "dst-unit-col": "RATE_CURRENCY",
              "dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"
            }
          ]
        }
      ]
    }
  ]
}