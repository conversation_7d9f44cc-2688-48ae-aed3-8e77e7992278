hashMIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) )"
hashSIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashS({0}) )"
hashMIdCheckStartAndEndNotNull: "if(element_at(split({0},'-'),1) == '_', NULL, if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) ))"
minDateFromTimestampArray: "to_date(array_min(regexp_extract_all({0}, '(\\\\d+)-(\\\\d+)-(\\\\d+)T(\\\\d+):(\\\\d+):(\\\\d+)Z',0)))"
// input is either A-_ or _-A, output will be A. In case it is A_B, it will be A
createConsolidatedValue: "if(element_at(split({0},'-'),1) == '_', element_at(split({0},'-'),2),element_at(split({0},'-'),1) )"
// input is either A1-A2-B2 : if A1 is _, we use B2 otherwise we use A2
createConsolidatedValueExternalDependency: "if(element_at(split({0},'-'),1) == '_', if(element_at(split({0},'-'),3) == '_', null, element_at(split({0},'-'),3)), if(element_at(split({0},'-'),2) == '_', null, element_at(split({0},'-'),2)) )"
// input is either 2RT2TQ-2022-12-20-OT-68-1111111111111-_ or 2RT2TQ-2022-12-20-OT-68-_-1111111111111 or 2RT2TQ-2022-12-20-OT-68-1111111111111-9999999999999
// it should return 2RT2TQ-2022-12-20-OT-68-1111111111111
createConsolidatedDocumentNumberReferenceKey: "concat_ws ('-', regexp_extract({0}, '(\\\\w+)-(\\\\d+)-(\\\\d+)-(\\\\d+)-(\\\\w+)-(\\\\d+)',0),regexp_extract({0}, '(\\\\d{9,14})',0))"
// input is either 2RT2TQ-2022-12-20-OT-68-1111111111111-9999999999999-1-9
//              or 2RT2TQ-2022-12-20-OT-68-_-1111111111111-_-1
//              or 2RT2TQ-2022-12-20-OT-68-_-1111111111111-1-9
//              or 2RT2TQ-2022-12-20-OT-68-1111111111111-9999999999999-_-1
// it should return 2RT2TQ-2022-12-20-OT-68-1111111111111-1
createConsolidatedCouponReferenceKey: "concat_ws ('-', regexp_extract({0}, '(\\\\w+)-(\\\\d+)-(\\\\d+)-(\\\\d+)-(\\\\w+)-(\\\\d+)',0),regexp_extract({0}, '(\\\\d{9,14})',0),regexp_extract(substring_index({0}, '-', -2), '(\\\\d{1,2})',0))"""
//For SEATING product : check if is_chargeable info is avalaible , if not check if seating info get a CH characteristic : false-N-CH-A => false, true-N-CH-A => true, _-N-CH-A => true, _-N-A => false
isSeatChargeable: "if(startswith({0},'_'),contains({0},'-CH-') or endswith({0},'-CH') ,boolean(split_part({0},'-',1)))"
getRightPartOfPtr: "element_at(split({0},'/'),array_size(split({0},'/')))"


"defaultComment": "ORDER star schema",
"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.data": "OrderOrderChangeEvent",
      "$.attachments": "attachments",
      "$.data.currentImage.orderItems.lifecycle": "OrderOrderLifecycle",
      // point to the right property of a Schema as mismatch between json/yaml paths (expected stakeholders.personalData.personalDataRef)
      "$.data.currentImage.stakeholders.personalDataRef": "OrderServiceFulfillmentStakeholderPersonalData.personalDataRef",
      // point to the right Schema as mismatch between json/yaml paths (expected stakeholders.seller.pointOfSale)
      "$.data.currentImage.stakeholders.pointOfBusiness": "OrderStakeholderPointOfBusiness",
      //  point to the right Schema as mismatch in asyncapi yaml - Leg.v1
      "$.data.currentImage.orderItems.services.context.soldProduct.productInstance.leg": "Leg\\.v1",
      "$.data.currentImage.orderItems.services.context.soldProduct.productInstance.leg.deis.value": "DEI\\.v2.\\value"
    }
  }
},
"tables": [
  {
    "name": "FACT_ORDER",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_ORDER_HISTO"
    }
  },
  {
    "name": "FACT_ORDER_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to the Order itself", "granularity": "1 ORDER"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "VERSION_ORDER"], // "RESERVATION_ID" unicity contained in "INTERNAL_ZORDER" ? (to check with Martin)
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.data.currentImage"}]}],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "ORDER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: orderId", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "CREATION_USER_ORGANIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.creation.user.organizationCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CREATION_USER_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.creation.user.iataNumber"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CREATION_USER_LOGIN", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.creation.user.login"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CREATION_LOCATION_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.creation.location.iataCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CREATION_LOCATION_COUNTRY_ISO2_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.creation.location.address.countryCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "UPDATE_USER_ORGANIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.user.organizationCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "UPDATE_USER_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.user.iataNumber"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "UPDATE_USER_LOGIN", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.user.login"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "UPDATE_LOCATION_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.location.iataCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "UPDATE_LOCATION_COUNTRY_ISO2_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.location.address.countryCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CREATION_DATETIME_ORDER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "VERSION_ORDER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION_ORDER",
          "valid-from": "VALID_FROM",
          "valid-to": "VALID_TO",
          "is-last": "IS_LATEST_VERSION"
        }
      }
    }
  },
  {
    "name": "FACT_ORDER_ITEM",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_ORDER_ITEM_HISTO"
    }
  },

  {
    "name": "FACT_ORDER_ITEM_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to ORDER items.", "granularity": "1 ORDER ITEM"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ORDER_ITEM_ID", "VERSION_ORDER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.data.currentImage"},
            {"oi": "$.orderItems[*]"}
          ]
        }
      ],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "ORDER_ITEM_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"oi": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"oi": "$.id"}]},
          "meta": {"description": {"value": "Functional key: keywordId", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "STATUS", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"oi": "$.status"}]},
          "meta": {"description": {"value": "Functional key: keywordId", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "CREATION_AGREEMENT_ITEM_PTR", "column-type": "binaryStrColumn", "sources": {"blocks": [{"oi": "$.createdByAgreementItemPtr"}]}},
        {"name": "CANCELLATION_AGREEMENT_ITEM_PTR", "column-type": "binaryStrColumn", "sources": {"blocks": [{"oi": "$.cancelledByAgreementItemPtr"}]}},
        {
          "name": "ORDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "CREATION_AGREEMENT_ITEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"oi": "$.createdByAgreementItemPtr"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_AGREEMENT_ITEM_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "CANCELLATION_AGREEMENT_ITEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"oi": "$.cancelledByAgreementItemPtr"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_AGREEMENT_ITEM_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "VERSION_ORDER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        }

      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    }
  },
  {
    "name": "FACT_ORDER_ITEM_SERVICE_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to ORDER items services.", "granularity": "1 ORDER ITEM SERVICE"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ORDER_ITEM_SERVICE_ID", "VERSION_ORDER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.data.currentImage"},
            {"oi": "$.orderItems[*]"},
            {"serv": "$.services[*]"},
            {"product": "$.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')]"}
          ]
        }
      ],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "ORDER_ITEM_SERVICE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"serv": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"serv": "$.id"}]},
          "meta": {"description": {"value": "Functional key: keywordId", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "OFFER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.id"}]}},
        {"name": "OFFER_HREF", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.href"}]}},
        {"name": "OFFER_ITEM_PTR", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}}, //getRightPartOfPtr
        {"name": "OFFER_ITEM_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}, "expr": ${getRightPartOfPtr}},
        {"name": "SERVICE_TYPE", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.type"}]}},
        {"name": "SOLD_PRODUCT_REFERENCE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productRef.id"}]}},
        {"name": "SOLD_PRODUCT_INSTANCE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productInstance.id"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_AIRLINE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.number"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.issuanceLocalDateTime"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_VERSION", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.version"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.documentType"}]}},

        {"name": "OPENSALE_PRIMARY_DOCUMENT_AIRLINE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.number"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_CHECKDIGIT", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.checkDigit"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.issuanceLocalDateTime"}]}},

        {"name": "PNR_REFERENCE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.reference"}]}},
        {"name": "PNR_VERSION", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.version"}]}},
        {"name": "PNR_CREATION_DATETIME", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.creation.dateTime"}]}},


        {
          "name": "SOLD_PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productInstance.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO", "column": "ORDER_SOLD_PRODUCT_SERVICE_ID"}]
        },
        {
          "name": "ORDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "ORDER_ITEM_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"oi": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_ITEM_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}]
        },
        {
          "name": "OFFER_ITEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_OFFER_ITEM_HISTO", "column": "OFFER_ITEM_ID"}]
        },
        {
          "name": "VERSION_ORDER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        }

      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    }
  },
  {
    "name": "FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO"
    }
  },

  {
    "name": "FACT_ITEM_AIR_SEGMENT_BOOKING_CLASS_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to ORDER items services.", "granularity": "1 ORDER ITEM SERVICE"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ITEM_AIR_SEGMENT_BOOKING_ID", "VERSION_ORDER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.data.currentImage"},
            {"oi": "$.orderItems[*]"},
            {"serv": "$.services[*]"},
            {"product": "$.measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')]"}
          ]
        }
      ],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "ITEM_AIR_SEGMENT_BOOKING_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"serv": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"serv": "$.id"}]},
          "meta": {"description": {"value": "Functional key: keywordId", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.status"}]}},

        {"name": "SEGMENT_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.segment.marketing.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SEGMENT_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.segment.marketing.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SEGMENT_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.segment.marketing.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        {
          "name": "SEGMENT_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.segment.operating.flightDesignator.flightNumber"}, {"product": "$.segment.marketing.flightDesignator.carrierCode"}]}, "expr": ${createConsolidatedValue},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "6X", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.segment.operating.flightDesignator.flightNumber"}, {"product": "$.segment.marketing.flightDesignator.flightNumber"}]}, "expr": ${createConsolidatedValue},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "1234", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.segment.operating.flightDesignator.flightNumber"}, {"product": "$.segment.marketing.flightDesignator.operationalSuffix"}]}, "expr": ${createConsolidatedValue},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "D", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.segment.departure.iataCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"product": "$.segment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.segment.arrival.iataCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"product": "$.segment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.cabin.code"}]},
          "meta": {"example": {"value": "C", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.cabin.code"}]},
          "meta": {"example": {"value": "C", "rule": "replace"}, "gdpr-zone": "green"}
        },

        {"name": "OPENTICKET_PRIMARY_DOCUMENT_AIRLINE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.number"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.issuanceLocalDateTime"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_VERSION", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.version"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.documentType"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_AIRLINE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.number"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_CHECKDIGIT", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.checkDigit"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.issuanceLocalDateTime"}]}},
        {"name": "PNR_REFERENCE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.reference"}]}},
        {"name": "PNR_VERSION", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.version"}]}},
        {"name": "PNR_CREATION_DATETIME", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.creation.dateTime"}]}},

        {"name": "OFFER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.id"}]}},
        {"name": "OFFER_HREF", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.href"}]}},
        {"name": "OFFER_ITEM_PTR", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}}, //getRightPartOfPtr
        {"name": "OFFER_ITEM_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}, "expr": ${getRightPartOfPtr}},
        {"name": "ITEM_SERVICE_TYPE", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.type"}]}},
        {"name": "SOLD_PRODUCT_REFERENCE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productRef.id"}]}},
        {"name": "SOLD_PRODUCT_INSTANCE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productInstance.id"}]}},

        {
          "name": "SOLD_PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productInstance.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO", "column": "ORDER_SOLD_PRODUCT_SERVICE_ID"}]
        },
        {
          "name": "ORDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "ORDER_ITEM_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"oi": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_ITEM_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}]
        },
        {
          "name": "OFFER_ITEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_OFFER_ITEM_HISTO", "column": "OFFER_ITEM_ID"}]
        },
        {
          "name": "VERSION_ORDER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        }

      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    }
  },
  {
    "name": "FACT_ITEM_SERVICE_STAKEHOLDER",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_ITEM_SERVICE_STAKEHOLDER_HISTO"
    }
  },
  {
    "name": "FACT_ITEM_SERVICE_STAKEHOLDER_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to ORDER items services.", "granularity": "1 ORDER ITEM SERVICE"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ITEM_SERVICE_STAKEHOLDER_ID", "VERSION_ORDER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.data.currentImage"},
            {"oi": "$.orderItems[*]"},
            {"serv": "$.services[*]"},
            {
              "cartesian": [
                [{"product": "$.measure.product.productInstance[?(@.productType == 'serviceProduct')]"}],
                [{"stakeholder": "$.measure.stakeholders[*]"}, {"id": "$.personalData.personalDataRef.id"}]
              ]
            }

          ]
        }
      ],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "ITEM_SERVICE_STAKEHOLDER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"serv": "$.id"}, {"stakeholder": "$.personalData.personalDataRef.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"serv": "$.id"}, {"stakeholder": "$.personalData.personalDataRef.id"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ITEM_SERVICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.id"}]},
          "meta": {"description": {"value": "Functional key: ORDER_ITEM_SERVICE_PRODUCT identifier", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "STAKEHOLDER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"stakeholder": "$.personalData.personalDataRef.id"}]},
          "meta": {"description": {"value": "Functional key: ORDER_ITEM_SERVICE_STAKEHOLDER identifier", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.status"}]}},
        {"name": "BASELINE_SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.baselineInformation.ssrCode"}]}},
        {"name": "EXECUTION_SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.baselineInformation.ssrCode"}]}},

        {"name": "OPENTICKET_PRIMARY_DOCUMENT_AIRLINE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.number"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.issuanceLocalDateTime"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_VERSION", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.version"}]}},
        {"name": "OPENTICKET_PRIMARY_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.documentType"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_AIRLINE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.number"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_CHECKDIGIT", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.checkDigit"}]}},
        {"name": "OPENSALE_PRIMARY_DOCUMENT_ISSUANCE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.issuanceLocalDateTime"}]}},
        {"name": "PNR_REFERENCE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.reference"}]}},
        {"name": "PNR_VERSION", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.version"}]}},
        {"name": "PNR_CREATION_DATETIME", "column-type": "timestampColumn", "sources": {"blocks": [{"serv": "$.measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.creation.dateTime"}]}},
        {"name": "OFFER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.id"}]}},
        {"name": "OFFER_HREF", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.href"}]}},
        {"name": "OFFER_ITEM_PTR", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}}, //getRightPartOfPtr
        {"name": "OFFER_ITEM_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}, "expr": ${getRightPartOfPtr}},
        {"name": "ITEM_SERVICE_TYPE", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.type"}]}},
        {"name": "SOLD_PRODUCT_REFERENCE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productRef.id"}]}},
        {"name": "SOLD_PRODUCT_INSTANCE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productInstance.id"}]}},


        {
          "name": "SOLD_PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.context.soldProduct.productInstance.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO", "column": "ORDER_SOLD_PRODUCT_SERVICE_ID"}]
        },
        {
          "name": "ORDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "ORDER_ITEM_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"oi": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_ITEM_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerRef.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}]
        },
        {
          "name": "OFFER_ITEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"serv": "$.context.offerServiceItemRef.offerServiceItemPtr"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_OFFER_ITEM_HISTO", "column": "OFFER_ITEM_ID"}]
        },
        {
          "name": "VERSION_ORDER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        }

      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    }
  },
  {
    "name": "FACT_SOLD_AIR_LEG_CABIN_PRODUCT_AIR",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO"
    }
  },
  {
    "name": "FACT_SOLD_AIR_LEG_CABIN_PRODUCT_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to ORDER items services.", "granularity": "1 ORDER ITEM SERVICE"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SOLD_AIR_LEG_CABIN_PRODUCT_ID", "VERSION_ORDER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.data.currentImage"},
            {"oi": "$.orderItems[*]"},
            {"serv": "$.services[*]"},
            {"soldProduct": "$.context.soldProduct"},
            {"product": "$.productInstance[?(@.productType == 'airLegCabinProduct')]"}
          ]
        }
      ],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "SOLD_AIR_LEG_CABIN_PRODUCT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"product": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"product": "$.id"}]},
          "meta": {"description": {"value": "Functional key Sold Product Instane Id", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "PRODUCT_TYPE", "column-type": "binaryStrColumn", "sources": {"blocks": [{"product": "$.productType"}]}},

        {"name": "MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.marketingFlightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.marketingFlightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.marketingFlightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        {
          "name": "OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.operatingFlightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.flightDesignator.carrierCode"}, {"product": "$.marketingFlightDesignator.carrierCode"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "6X", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.operatingFlightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.flightDesignator.flightNumber"}, {"product": "$.marketingFlightDesignator.flightNumber"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "1234", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.operatingFlightDesignator.flightNumber"}, {"seg": "$.airSegment.operating.flightDesignator.operationalSuffix"}, {"product": "$.marketingFlightDesignator.operationalSuffix"}]}, "expr": ${createConsolidatedValueExternalDependency},
          "meta": {"description": {"value": "Operating information of the flight. For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}, "example": {"value": "D", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.cabin.code"}]},
          "meta": {"example": {"value": "C", "rule": "replace"}, "gdpr-zone": "green"}
        },

        {
          "name": "LEG_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.leg.boardPointIataCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "LEG_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"product": "$.leg.scheduledDepartureDateTime"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "LEG_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.leg.offPointIataCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "LEG_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"product": "$.leg.scheduledArrivalDateTime"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "LEG_DEIS_CODE", "column-type": "intColumn", "sources": {"blocks": [{"product": "$.leg.deis[*].code"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "LEG_DEIS_VALUE", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.leg.deis[*].value"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "LEG_AIRCRAFT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"product": "$.leg.aircraftEquipment.aircraftType"}]},
          "meta": {"gdpr-zone": "green"}
        },


        {
          "name": "EXECUTION_ITINERARY_ORDER", "column-type": "strColumn", "sources": {"blocks": [{"soldProduct": "$.serviceItemExecution.itineraryOrder"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "EXECUTION_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"soldProduct": "$.serviceItemExecution.bookingClass"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "EXECUTION_FLIGHT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"soldProduct": "$.serviceItemExecution.flightStatus"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "EXECUTION_VALIDATING_CARRIER_REF", "column-type": "strColumn", "sources": {"blocks": [{"soldProduct": "$.serviceItemExecution.validatingCarrierRef"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "EXECUTION_FLIGHT_CONNECTION_QUALIFIER", "column-type": "strColumn", "sources": {"blocks": [{"soldProduct": "$.serviceItemExecution.flightConnectionQualifier"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "EXECUTION_SEGMENT_ELAPSED_FLYING_TIME", "column-type": "strColumn", "sources": {"blocks": [{"soldProduct": "$.serviceItemExecution.segmentElapsedFlyingTime"}]},
          "meta": {"gdpr-zone": "green"}
        },


        {
          "name": "ORDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_HISTO", "column": "ORDER_ID"}]
        },

        {
          "name": "VERSION_ORDER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        }

      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    }
  },
  {
    "name": "FACT_ORDER_SOLD_PRODUCT_SERVICE",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO"
    }
  },
  {
    "name": "FACT_ORDER_SOLD_PRODUCT_SERVICE_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to ORDER items services.", "granularity": "1 ORDER ITEM SERVICE"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ORDER_SOLD_PRODUCT_SERVICE_ID", "VERSION_ORDER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.data.currentImage"},
            {"oi": "$.orderItems[*]"},
            {"serv": "$.services[*]"},
            {"product": "$.context.soldProduct.productInstance[?(@.productType == 'serviceProduct')]"}
          ]
        }
      ],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "ORDER_SOLD_PRODUCT_SERVICE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"product": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"product": "$.id"}]},
          "meta": {"description": {"value": "Functional key Sold Product Instane Id", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "PRODUCT_TYPE", "column-type": "binaryStrColumn", "sources": {"blocks": [{"product": "$.productType"}]}},


        {
          "name": "ORDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "VERSION_ORDER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        }

      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    }
  },
  {
    "name": "FACT_STAKEHOLDER",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_STAKEHOLDER_HISTO"
    }
  },

  {
    "name": "FACT_STAKEHOLDER_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to STAKEHOLDER.", "granularity": "1 STAKEHOLDER"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "STAKEHOLDER_ID", "VERSION_ORDER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.data.currentImage"},
            {"st": "$.stakeholders[*]"}
          ]
        }
      ],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "STAKEHOLDER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"st": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"st": "$.id"}]},
          "meta": {"description": {"value": "Functional key: keywordId", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "ROLE", "column-type": "strColumn", "sources": {"blocks": [{"st": "$.role"}]}},
        {"name": "PERSONAL_DATA_REF_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"st": "$.personalDataRef.id"}]}},
        {"name": "PERSONAL_DATA_REF_HREF", "column-type": "strColumn", "sources": {"blocks": [{"st": "$.personalDataRef.href"}]}},
        {
          "name": "ORDER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}}, "gdpr-zone": "green",
          "fk": [{"table": "FACT_ORDER_HISTO", "column": "ORDER_ID"}]
        },
        {
          "name": "VERSION_ORDER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        }

      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    }
  }
]