versionExprVal: "bigint({0})"
versionTypeVal: "longColumn"
hashMIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) )"
hashSIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashS({0}) )"
// "_-AF" => "AF", "KL-AF" => "KL"
ifFirstValueNullTakeSecondValue : "if(split_part({0}, '-', 1) == '_', split_part({0}, '-', 2), split_part({0}, '-', 1))"
// "AF-123-456" => "123", "AF-_-456" => null, "_-_-456" => "456"
takeMktInfoIfNoOpeCarrierInfoProvided: "if(split_part({0}, '-', 1) == '_',if(split_part({0}, '-', 2) == '_', split_part({0}, '-', 3), split_part({0}, '-', 2)), if(split_part({0}, '-', 2) == '_', null,split_part({0}, '-', 2)))"
// - CORR FLIGHT_DATE_ID
// Inputs: [1-3] Marketing Flight Part - [4-6] Operating Flight Part - [7-9] Departure Date
// Rules: take the {CUSTOMER} Flight Part
getCorrFlightDateId: "regexp_replace(concat_ws('-', if(split_part({0}, '-', 4) == '{CUSTOMER}', slice(split({0}, '-'), 4, 3), slice(split({0}, '-'), 1, 3)), slice(split({0}, '-'), 7, 3)), '-_|T..:..:..T|T..:..:..', '')"

// - CORR FLIGHT_SEGMENT_ID
// Inputs: [1-3] Marketing Flight Part - [4-6] Operating Flight Part - [7-12] Departure Date *2 - [13-14] Departure and Arrival Airport
// Rules: - take the {CUSTOMER} Flight Part
//        - in case Marketing Carrier and Operating Carrier are not {CUSTOMER}, invalidate the ID (null)

getCorrFlightSegmentId: "if(split_part({0}, '-', 1) == '{CUSTOMER}' or split_part({0}, '-', 4) == '{CUSTOMER}', regexp_replace(concat_ws('-', if(split_part({0}, '-', 1) == '{CUSTOMER}', slice(split({0}, '-'), 1, 3), slice(split({0}, '-'), 4, 3)), slice(split({0}, '-'), 7, 8)), '-_|T..:..:..Z|T..:..:..', ''), null)"

// - CORR CODESHARE_FLIGHT_SEGMENT_ID
// Inputs: [1-6] Marketing Flight Date Part - [7-12] Operating Flight Date Part - [13-15] Departure Date - [16-17] Departure and Arrival Airport
// Rules: - in case Marketing Carrier is not equal to Operating Carrier (codeshare), put the {CUSTOMER} Flight Date Part at the beginning and add the non-{CUSTOMER} Flight Date Part at the end
//        - in case Marketing Carrier is equal to Operating Carrier (prime), invalidate the ID (null)
//        - in case Marketing Carrier and Operating Carrier are not {CUSTOMER}, invalidate the ID (null)
getCorrCodeshareFlightSegmentId: "if(split_part({0}, '-', 1) != split_part({0}, '-', 7) and (split_part({0}, '-', 1) == '{CUSTOMER}' or split_part({0}, '-', 7) == '{CUSTOMER}') and (split_part({0}, '-', 1) != '_' and split_part({0}, '-', 7) != '_'), regexp_replace(concat_ws('-', if(split_part({0}, '-', 1) == '{CUSTOMER}', slice(split({0}, '-'), 1, 6), slice(split({0}, '-'), 7, 6)), slice(split({0}, '-'), 13, 5), if(split_part({0}, '-', 1) != '{CUSTOMER}', slice(split({0}, '-'), 1, 6), slice(split({0}, '-'), 7, 6))), '-_|T..:..:..Z|T..:..:..', ''), null)"

removeTimePart: "regexp_replace({0}, '-_|T..:..:..T|T..:..:..', '')"
createInternalCorrIdEmd: "concat_ws('-', split_part({0}, '-', 1), split_part({0}, '-', 2), split_part({0}, '-', 6))"

"defaultComment": "TKT star schema, V2",

"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.mainResource.current.image": "ticketDataPush.processedTicket"
    }
  }
},

"tables": [
  {
    "name": "FACT_TRAVEL_DOCUMENT",
    "latest": {
      "histo-table-name": "FACT_TRAVEL_DOCUMENT_HISTO"
    }
  },
  {
    "name": "FACT_TRAVEL_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Travel Document",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "This is the main fact table of the Ticketing star schema. Contains general information of travel documents (tickets, EMDs) such as document numbers (primary, conjunctive, original), overall monetary information for payment (total price/taxes) and refund (total refund/taxes/fees), related points of sale and basic passenger information.",
        "granularity": "1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: primDocNum-issuanceDate", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ISSUANCE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.issuanceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ORIGIN_CITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.originCityIataCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DESTINATION_CITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.destinationCityIataCode"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVELER_FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.name.firstName"}]},
          "meta": {"description": {"value": "First name of the travel document's passenger", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "TRAVELER_LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.name.lastName"}]},
          "meta": {"description": {"value": "Last name of the travel document's passenger", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "TRAVELER_PAX_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.passengerTypeCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVELER_PHONE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.contact.phone.number"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "TRAVELER_EMAIL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.traveler.contact.email.address"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "ENDORSEMENT_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.endorsementFreeFlow"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NUMBER_OF_BOOKLETS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.numberOfBooklets"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CONJUNCTIVE_DOCUMENT_1", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[1]"}]},
          "meta": {"description": {"value": "Document number of first conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CONJUNCTIVE_DOCUMENT_2", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[2]"}]},
          "meta": {"description": {"value": "Document number of second conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CONJUNCTIVE_DOCUMENT_3", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.conjunctiveDocumentNumbers[3]"}]},
          "meta": {"description": {"value": "Document number of third conjunctive document, if any", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ORIGINAL_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.originalDocuments[0].documentNumber"}]},
          "meta": {"description": {"value": "Primary document number of the original travel document further to an exchange", "rule": "replace"}, "example": {"value": "*********0123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ORIGINAL_DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.originalDocuments[0].creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the original document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ORIGINAL_ISSUE_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.originalIssueFreeFlow"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VOID_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.void.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VALIDATING_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.validatingCarrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VALIDATING_CARRIER_RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.validatingCarrierPnr.reference"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "PRICE_TOTAL", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted price_payment into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_TAXES", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted total_taxes_payment into airline's home currency", "rule": "replace"}, "example": {"value": "78.53", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_TOTAL_INCREMENTAL_FLAG", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicator of additional collection further to a document exchange. If this field is filled, the corresponding amount field contains the incremental amount.", "rule": "replace"}, "example": {"value": "NO ADC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_TOTAL_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.price.total"}]},
          "meta": {"example": {"value": "758.5", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_TAXES_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.price.totalTaxes"}]},
          "meta": {"description": {"value": "The total amount of taxes contained in price_total_payment, in payment currency", "rule": "replace"}, "example": {"value": "128.12", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.price.currency"}]},
          "meta": {"example": {"value": "CAD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRICE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_TOTAL", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted total_taxes_payment into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_TOTAL_INCREMENTAL_FLAG", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicator of additional collection further to a document exchange. If this field is filled, the corresponding amount field contains the incremental amount.", "rule": "replace"}, "example": {"value": "NO ADC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_FARE_PAID", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_PAID_INCREMENTAL_FLAG", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicator of additional collection further to a document exchange. If this field is filled, the corresponding amount field contains the incremental amount.", "rule": "replace"}, "example": {"value": "NO ADC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_FARE_USED", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_REFUND", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_TAXES", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_CANCELLATION_PENALTY", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_TOTAL_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.refundTotal.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.refundTotal.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_PAID_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.farePaid.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_USED_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.fareUsed.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_FARE_REFUND_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.fareRefund.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_TAXES_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.totalTax.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_CANCELLATION_PENALTY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentRefund.cancellationPenalty.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFUND_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},  // TODO : refund date should be DATE_BEGIN when the refund has happened (arg... so the min() value of the first TKT version which has REFUND filled)
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFUND_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CREATION_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}]}},
        {"name": "CREATION_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.inHouseIdentification"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_LOGIN_CITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.cityCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_LOGIN_COUNTRY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.countryCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_LOGIN_NUMERIC_SIGN", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.numericSign"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "CREATION_LOGIN_INITIALS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.initials"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "CREATION_LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.login.dutyCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.triggerEventName"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.inHouseIdentification"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_LOGIN_CITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.cityCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_LOGIN_COUNTRY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.countryCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "LAST_UPDATE_LOGIN_NUMERIC_SIGN", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.numericSign"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "LAST_UPDATE_LOGIN_INITIALS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.initials"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "LAST_UPDATE_LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.login.dutyCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ORIGIN_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.originCityIataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "DESTINATION_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.destinationCityIataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "TRAVELER_PAX_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.traveler.passengerTypeCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_PASSENGER_TYPE", "column": "PASSENGER_TYPE_ID"}]},
        {"name": "VALIDATING_CARRIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.validatingCarrierCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}, {"base": "$.creation.pointOfSale.office.iataNumber"}, {"base": "$.creation.pointOfSale.office.inHouseIdentification"}, {"base": "$.creation.pointOfSale.office.systemCode"}, {"base": "$.creation.pointOfSale.office.agentType"}, {"base": "$.creation.pointOfSale.login.cityCode"}, {"base": "$.creation.pointOfSale.login.countryCode"}, {"base": "$.creation.pointOfSale.login.numericSign"}, {"base": "$.creation.pointOfSale.login.initials"}, {"base": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}]},
        {"name": "LAST_UPDATE_TRIGGER_EVENT_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.latestEvent.triggerEventName"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_TRIGGER_EVENT", "column":"TRIGGER_EVENT_ID"}]},
        {"name": "LAST_UPDATE_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.latestEvent.pointOfSale.office.id"}, {"base": "$.latestEvent.pointOfSale.office.iataNumber"}, {"base": "$.latestEvent.pointOfSale.office.inHouseIdentification"}, {"base": "$.latestEvent.pointOfSale.office.systemCode"}, {"base": "$.latestEvent.pointOfSale.office.agentType"}, {"base": "$.latestEvent.pointOfSale.login.cityCode"}, {"base": "$.latestEvent.pointOfSale.login.countryCode"}, {"base": "$.latestEvent.pointOfSale.login.numericSign"}, {"base": "$.latestEvent.pointOfSale.login.initials"}, {"base": "$.latestEvent.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}]},
        {"name": "ORIGINAL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.originalDocuments[0].documentNumber"},{"base": "$.originalDocuments[0].creation.dateTime"}]}, "expr": ${hashMIdCheckEndNotNull},
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the original travel document further to an exchange)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]}, "expr": "hashM({0})",
          "meta": {"gdpr-zone": "orange"}},
        {"name": "TKTEMD_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": { "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_VERSION"
        }
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "PRICE_TOTAL_ORIGINAL", "src-unit-col": "PRICE_CURRENCY_ORIGINAL", "src-date-col" : "PRICE_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "PRICE_TOTAL", "dst-unit-col": "PRICE_CURRENCY","dst-date-col" :"PRICE_EXCHANGE_RATE_DATE_TAKEN", "dst-incr-flag-col": "PRICE_TOTAL_INCREMENTAL_FLAG"},
          {"src-col": "PRICE_TAXES_ORIGINAL", "src-unit-col": "PRICE_CURRENCY_ORIGINAL", "src-date-col" : "PRICE_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "PRICE_TAXES", "dst-unit-col": "PRICE_CURRENCY","dst-date-col" :"PRICE_EXCHANGE_RATE_DATE_TAKEN"},
          {"src-col": "REFUND_TOTAL_ORIGINAL", "src-unit-col": "REFUND_CURRENCY_ORIGINAL", "src-date-col" : "REFUND_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "REFUND_TOTAL", "dst-unit-col": "REFUND_CURRENCY","dst-date-col" :"REFUND_EXCHANGE_RATE_DATE_TAKEN", "dst-incr-flag-col": "REFUND_TOTAL_INCREMENTAL_FLAG"},
          {"src-col": "REFUND_FARE_PAID_ORIGINAL", "src-unit-col": "REFUND_CURRENCY_ORIGINAL", "src-date-col" : "REFUND_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "REFUND_FARE_PAID", "dst-unit-col": "REFUND_CURRENCY","dst-date-col" :"REFUND_EXCHANGE_RATE_DATE_TAKEN", "dst-incr-flag-col": "REFUND_FARE_PAID_INCREMENTAL_FLAG"},
          {"src-col": "REFUND_FARE_USED_ORIGINAL", "src-unit-col": "REFUND_CURRENCY_ORIGINAL", "src-date-col" : "REFUND_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "REFUND_FARE_USED", "dst-unit-col": "REFUND_CURRENCY","dst-date-col" :"REFUND_EXCHANGE_RATE_DATE_TAKEN"},
          {"src-col": "REFUND_FARE_REFUND_ORIGINAL", "src-unit-col": "REFUND_CURRENCY_ORIGINAL", "src-date-col" : "REFUND_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "REFUND_FARE_REFUND", "dst-unit-col": "REFUND_CURRENCY","dst-date-col" :"REFUND_EXCHANGE_RATE_DATE_TAKEN"},
          {"src-col": "REFUND_TAXES_ORIGINAL", "src-unit-col": "REFUND_CURRENCY_ORIGINAL", "src-date-col" : "REFUND_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "REFUND_TAXES", "dst-unit-col": "REFUND_CURRENCY","dst-date-col" :"REFUND_EXCHANGE_RATE_DATE_TAKEN"},
          {"src-col": "REFUND_CANCELLATION_PENALTY_ORIGINAL", "src-unit-col": "REFUND_CURRENCY_ORIGINAL", "src-date-col" : "REFUND_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "REFUND_CANCELLATION_PENALTY", "dst-unit-col": "REFUND_CURRENCY","dst-date-col" :"REFUND_EXCHANGE_RATE_DATE_TAKEN"},
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_ASSOCIATED_TRAVEL_DOCUMENT",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_ASSOCIATED_TRAVEL_DOCUMENT_HISTO"
    }
  },
  {
    "name": "FACT_ASSOCIATED_TRAVEL_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Travel Document",
    "mapping": {
      "description": {
        "description": "Contains references of travel documents associated to a given travel document, indicating the direction of the relationship (ticket -> EMD or EMD -> ticket).",
        "granularity": "1 associated travel document  for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ASSOCIATED_TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"assoDoc": "$.associatedDocuments[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ASSOCIATED_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"assoDoc": "$.documentNumber"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"assoDoc": "$.documentNumber"}]},
          "meta": {"value": "Functional key: docId-assocDocNum", "rule": "replace", "example": {"value": "*********0123-2020-01-31-*********0123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ASSOCIATED_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"assoDoc": "$.documentNumber"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "ASSOCIATED_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"assoDoc": "$.documentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["ASSOCIATED_TRAVEL_DOCUMENT_ID", "VERSION"]
    }
  },

  {
    "name": "FACT_ASSOCIATED_RESERVATION",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_ASSOCIATED_RESERVATION_HISTO"
    }
  },
  {
    "name": "FACT_ASSOCIATED_RESERVATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Travel Document",
    "mapping": {
      "description": {
        "description": "Contains references of reservations/PNRs related to a given travel document, including the system code in which the related PNR was created.",
        "granularity": "1 associated reservation for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "ASSOCIATED_RESERVATION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"assoRes": "$.associatedPnrs[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ASSOCIATED_RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"assoRes": "$.reference"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"assoRes": "$.reference"}]},
          "meta": {"description": {"value": "Functional key: docId-recordLoc", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-AB{0}23", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"assoRes": "$.reference"}]},
          "meta": {"description": {"value": "The record locator of the associated reservation/PNR", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "PNR_CREATION_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"assoRes": "$.creation.pointOfSale.office.systemCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CREATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"assoRes": "$.creation.pointOfSale.office.id"}, {"assoRes": "$.creation.pointOfSale.office.iataNumber"}, {"assoRes": "$.creation.pointOfSale.office.inHouseIdentification"}, {"assoRes": "$.creation.pointOfSale.office.systemCode"}, {"assoRes": "$.creation.pointOfSale.office.agentType"}, {"assoRes": "$.creation.pointOfSale.login.cityCode"}, {"assoRes": "$.creation.pointOfSale.login.countryCode"}, {"assoRes": "$.creation.pointOfSale.login.numericSign"}, {"assoRes": "$.creation.pointOfSale.login.initials"}, {"assoRes": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}],
          "meta": {"description": {"value": "Hashed foreign key (representing the creator of the associated reservation)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["ASSOCIATED_RESERVATION_ID", "VERSION"]
    }
  },

  {
    "name": "FACT_FORM_OF_IDENTIFICATION",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_FORM_OF_IDENTIFICATION_HISTO"
    }
  },
  {
    "name": "FACT_FORM_OF_IDENTIFICATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Travel Document",
    "mapping": {
      "description": {
        "description": "Contains the provided means of identification (passport, ID card, …) for passenger of a travel document.",
        "granularity": "1 form of identification for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FORM_OF_IDENTIFICATION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"idForm": "$.formsOfIdentification"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FORM_OF_IDENTIFICATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"idForm": "$.identificationType"}, {"idForm": "$.number"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"idForm": "$.identificationType"}, {"idForm": "$.number"}]},
          "meta": {"description": {"value": "Functional key: docId-type-number", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-PASSPORT-123456", "rule": "replace"}, "gdpr-zone": "red"}},
        {"name": "TYPE", "column-type": "strColumn", "sources": {"blocks": [{"idForm": "$.identificationType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"idForm": "$.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["PRICE_DETAILS_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_PRICE_DETAIL",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_PRICE_DETAIL_HISTO"
    }
  },
  {
    "name": "FACT_PRICE_DETAIL_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Price Details",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains all fine-grained pricing information related to a travel document or quotation override, indicating the elementary price type and its amount. The monetary amounts are converted to the home currency.",
        "granularity": "1 price element for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PRICE_DETAIL_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "trvDoc", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"fare": "$.price.detailedPrices"}]}
        },
        { "name": "fareComp", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.fareComponents"},
          {"fare": "$.price.detailedPrices"}]}
        },
        {
          "name": "fltBnd", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.flightBounds"},
          {"fare": "$.price.detailedPrices"}]}
        },
        {
          "name": "quotOverNew", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.quotationOverrides[*].changeLog[*].quotationOverride"},
          {"cartesian": [
            [{"fare": "$.newEntity.detailedPrices"}],
            [{"fare2": "$.oldEntity.detailedPrices"}]
          ]}]}
        },
        {
          "name": "quotOverOld", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.quotationOverrides[*].changeLog[*].quotationOverride"},
          {"cartesian": [
            [{"fare2": "$.newEntity.detailedPrices"}],
            [{"fare": "$.oldEntity.detailedPrices"}]
          ]}]}
        }
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PRICE_DETAIL_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"type": "$.id"}, {"fare": "$.elementaryPriceType"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"type": "$.id"}, {"fare": "$.elementaryPriceType"}]},
          "meta": {"description": {"value": "Functional key: docId-type-elemPriceType", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1-ReferenceFare", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ELEMENTARY_PRICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"fare": "$.elementaryPriceType"}]},
          "meta": {"example": {"value": "BaseFare", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted amount_original into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_INCREMENTAL_FLAG", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicator of additional collection further to a document exchange. If this field is filled, the corresponding amount field contains the incremental amount.", "rule": "replace"}, "example": {"value": "NO ADC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"fare": "$.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"fare": "$.currency"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RELATES_TO", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "trvDoc", "literal": "TRAVEL DOCUMENT"},
            {"rs-name": "fareComp", "literal": "FARE COMPONENT"},
            {"rs-name": "fltBnd", "literal": "FLIGHT BOUND"},
            {"rs-name": "quotOverNew", "literal": "QUOTATION OVERRIDE NEW"},
            {"rs-name": "quotOverOld", "literal": "QUOTATION OVERRIDE OLD"}
          ]},
          "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to", "rule": "replace"}, "example": {"value": "TRAVEL DOCUMENT", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "QUOTATION_OVERRIDE_CHANGE_OLD_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "quotOverOld", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"fare": "$.elementaryPriceType"}]}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_QUOTATION_OVERRIDE_CHANGE_HISTO", "column": "QUOTATION_OVERRIDE_CHANGE_ID"}]},
        {"name": "QUOTATION_OVERRIDE_CHANGE_NEW_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "quotOverNew", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"fare": "$.elementaryPriceType"}]}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_QUOTATION_OVERRIDE_CHANGE_HISTO", "column": "QUOTATION_OVERRIDE_CHANGE_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "AMOUNT_ORIGINAL", "src-unit-col": "CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "AMOUNT", "dst-unit-col": "CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN", "dst-incr-flag-col": "AMOUNT_INCREMENTAL_FLAG"},
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["PRICE_DETAIL_ID", "RELATES_TO", "VERSION"]
    }
  },
  {
    "name": "FACT_TAX",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_TAX_HISTO"
    }
  },
  {
    "name": "FACT_TAX_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Price Details",
    "mapping": {
      "description": {
        "description": "Contains all fine-grained pricing information related to a travel document (payment or refund), indicating the tax code and its amount. The monetary amounts are converted to the home currency.",
        "granularity": "1 tax for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TAX_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "trvDoc", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"tax": "$.price.taxes"}]}
        },
        { "name": "refund", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.documentRefund"},
          {"tax": "$.taxes"}]}
        },
        {
          "name": "obFee", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.documentRefund.obFees"},
          {"tax": "$.taxes"}]},
        },
        {
          "name": "fareComp", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.fareComponents"},
          {"tax": "$.price.taxes"}]}
        },
        {
          "name": "fltBnd", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.flightBounds"},
          {"tax": "$.price.taxes"}]}
        }
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "trvDoc", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]},
            {"rs-name": "refund", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]},
            {"rs-name": "obFee", "blocks": [{"base": "$.id"}, {"type": "$.subCode"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]},
            {"rs-name": "fareComp", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]},
            {"rs-name": "fltBnd", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]}
          ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "trvDoc", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]},
            {"rs-name": "refund", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]},
            {"rs-name": "obFee", "blocks": [{"base": "$.id"}, {"type": "$.subCode"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]},
            {"rs-name": "fareComp", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]},
            {"rs-name": "fltBnd", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"tax": "$.code"}, {"tax": "$.category"}, {"tax": "$.nature"}]}
          ]},
          "meta": {"description": {"value": "Functional key: docId-subCode-code-category-nature", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-_-RC-NEW-AB", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]},
          "meta": {"example": {"value": "BaseFare", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.category"}]},
          "meta": {"example": {"value": "BaseFare", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted amount_original into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_INCREMENTAL_FLAG", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicator of additional collection further to a document exchange. If this field is filled, the corresponding amount field contains the incremental amount.", "rule": "replace"}, "example": {"value": "NO ADC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TICKET_REPORTED_STATUSES", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.ticketReportedStatuses[*]"}]},
          "meta": {"example": {"value": "DISPLAYED-REPORTED", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "NATURE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.nature"}]},
          "meta": {"example": {"value": "YQ", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RELATES_TO", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "trvDoc", "literal": "TRAVEL DOCUMENT"},
            {"rs-name": "refund", "literal": "REFUND"},
            {"rs-name": "obFee", "literal": "FEE"},
            {"rs-name": "fareComp", "literal": "FARE COMPONENT"},
            {"rs-name": "fltBnd", "literal": "FLIGHT BOUND"}
          ]},
          "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to", "rule": "replace"}, "example": {"value": "TRAVEL DOCUMENT", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tax": "$.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_TAX_CODE", "column": "TAX_CODE_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "AMOUNT_ORIGINAL", "src-unit-col": "CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "AMOUNT", "dst-unit-col": "CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN", "dst-incr-flag-col": "AMOUNT_INCREMENTAL_FLAG"},
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["TAX_ID", "RELATES_TO", "VERSION"]
    }
  },
  {
    "name": "FACT_FEE",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_FEE_HISTO"
    }
  },
  {
    "name": "FACT_FEE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Price Details",
    "mapping": {
      "description": {
        "description": "Contains all fine-grained fee information related to a travel document refund. The monetary amounts are converted to the home currency.",
        "granularity": "1 fee for 1 travel document refund"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FEE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"fee": "$.documentRefund.obFees"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FEE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fee": "$.code"}, {"fee": "$.subCode"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fee": "$.code"}, {"fee": "$.subCode"}]},
          "meta": {"description": {"value": "Functional key: docId-feeCode-feeSubCode", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-OB-Txx", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.code"}]},
          "meta": {"example": {"value": "OB", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.subCode"}]},
          "meta": {"example": {"value": "T001", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted amount_original into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.amount"}]},
          "meta": {"example": {"value": "72.51", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"fee": "$.currency"}]},
          "meta": {"example": {"value": "USD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "AMOUNT_ORIGINAL", "src-unit-col": "CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "AMOUNT", "dst-unit-col": "CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"},
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["FEE_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_COMMISSION",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_COMMISSION_HISTO"
    }
  },
  {
    "name": "FACT_COMMISSION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Price Details",
    "mapping": {
      "description": {
        "description": "Contains all fine-grained commission information related to a travel document, such as percentage and/or amount. The monetary amounts are converted to the home currency.",
        "granularity": "1 commission for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COMMISSION_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "trvDoc", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"comm": "$.price.commissions"}]}
        },
        {
          "name": "fareComp", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.fareComponents"},
          {"comm": "$.price.commissions"}]}
        },
        {
          "name": "fltBnd", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.flightBounds"},
          {"comm": "$.price.commissions"}]}
        }
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COMMISSION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"comm": "$.amount.elementaryPriceType"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"comm": "$.amount.elementaryPriceType"}]},
          "meta": {"description": {"value": "Functional key: docId-elemPriceType", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-NEW", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "ELEMENTARY_PRICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"comm": "$.amount.elementaryPriceType"}]},
          "meta": {"example": {"value": "BaseFare", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PERCENTAGE", "column-type": "strColumn", "sources": {"blocks": [{"comm": "$.percentage"}]},
          "meta": {"example": {"value": "7.00", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted amount_original into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"comm": "$.amount.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"comm": "$.amount.currency"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RELATES_TO", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "trvDoc", "literal": "TRAVEL DOCUMENT"},
            {"rs-name": "fareComp", "literal": "FARE COMPONENT"},
            {"rs-name": "fltBnd", "literal": "FLIGHT BOUND"}
          ]},
          "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to", "rule": "replace"}, "example": {"value": "TRAVEL DOCUMENT", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "AMOUNT_ORIGINAL", "src-unit-col": "CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "AMOUNT", "dst-unit-col": "CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"},
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["COMMISSION_ID", "RELATES_TO", "VERSION"]
    }
  },
  {
    "name": "FACT_DISCOUNT",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_DISCOUNT_HISTO"
    }
  },
  {
    "name": "FACT_DISCOUNT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Price Details",
    "mapping": {
      "description": {
        "description": "Contains fine-grained discount information related to a quotation override, providing code and voucher status where available.",
        "granularity": "1 discount for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "DISCOUNT_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "trvDoc", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"disc": "$.price.discounts"}]}
        },
        { "name": "fareComp", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.fareComponents"},
          {"disc": "$.price.discounts"}]}
        },
        {
          "name": "fltBnd", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.flightBounds"},
          {"disc": "$.price.discounts"}]}
        },
        {
          "name": "quotOverNew", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.quotationOverrides[*].changeLog[*].quotationOverride"},
          {"cartesian": [
            [{"disc": "$.newEntity.discounts"}],
            [{"disc2": "$.oldEntity"}]
          ]}]}
        },
        {
          "name": "quotOverOld", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.quotationOverrides[*].changeLog[*].quotationOverride"},
          {"cartesian": [
            [{"disc2": "$.newEntity"}],
            [{"disc": "$.oldEntity.discounts"}]
          ]}]}
        }
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "DISCOUNT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"disc": "$.code"}, {"disc": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"disc": "$.code"}, {"disc": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-discountCode-discountCorpCode", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-6X-87TVEFN1-OWNTPM20", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"disc": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CORPORATE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"disc": "$.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "VOUCHER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"disc": "$.voucherStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "RELATES_TO", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "trvDoc", "literal": "TRAVEL DOCUMENT"},
            {"rs-name": "fareComp", "literal": "FARE COMPONENT"},
            {"rs-name": "fltBnd", "literal": "FLIGHT BOUND"},
            {"rs-name": "quotOverNew", "literal": "QUOTATION OVERRIDE NEW"},
            {"rs-name": "quotOverOld", "literal": "QUOTATION OVERRIDE OLD"}
          ]},
          "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to", "rule": "replace"}, "example": {"value": "TRAVEL DOCUMENT", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "QUOTATION_OVERRIDE_CHANGE_NEW_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "quotOverNew", "blocks": [{"base": "$.id"}, {"type": "$.id"}, {"disc": "$.elementaryPriceType"}]}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_QUOTATION_OVERRIDE_CHANGE_HISTO", "column": "QUOTATION_OVERRIDE_CHANGE_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["DISCOUNT_ID", "RELATES_TO", "VERSION"]
    }
  },
  {
    "name": "FACT_QUOTATION_OVERRIDE_CHANGE",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_QUOTATION_OVERRIDE_CHANGE_HISTO"
    }
  },
  {
    "name": "FACT_QUOTATION_OVERRIDE_CHANGE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Price Details",
    "mapping": {
      "description": {
        "description": "Contains information on pricing changes following a quotation override (promo/discount) performed on a travel document, such as trigger event name, the date/time of the operation and the user or system which has performed the override.",
        "granularity": "1 quotation override for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "QUOTATION_OVERRIDE_CHANGE_ID", "LOG_TYPE", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"qo": "$.quotationOverrides[*]"},
          {"cartesian": [
            [{"old": "$.changeLog[*].quotationOverride.oldEntity"}],
            [{"new": "$.changeLog[*].quotationOverride.newEntity"}],
            [{"el": "$.eventLog"}],
          ]}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "QUOTATION_OVERRIDE_CHANGE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"qo": "$.changeLog[*].quotationOverride.logType"}, {"el": "$.dateTime"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"qo": "$.changeLog[*].quotationOverride.logType"}, {"el": "$.dateTime"}]},
          "meta": {"description": {"value": "Functional key: docId-quotLogType-dateTime", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-EntityChangeLog-2020-01-31T03:29:41Z", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "LOG_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"qo": "$.changeLog[*].quotationOverride.logType"}]},
          "meta": {"description": {"value": "The type of log for the override change", "rule": "replace"}, "example": {"value": "EntityChangeLog", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHANGE_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"el": "$.dateTime"}]},
          "meta": {"description": {"value": "Date/Time in UTC of the change", "rule": "replace"}, "example": {"value": "2018-10-03T09:35:42.000Z", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHANGE_TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"el": "$.triggerEventName"}]},
          "meta": {"description": {"value": "The name of the triggering event of the override change", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHANGE_OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"el": "$.pointOfSale.office.id"}]},
          "meta": {"description": {"value": "An identifier for a corporate user of a computer reservation system or global distribution system, typically a travel agency, also known as office ID.", "rule": "replace"}, "example": {"value": "NCE1A0980", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHANGE_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"el": "$.pointOfSale.office.iataNumber"}]},
          "meta": {"description": {"value": "IATA assigned agency number", "rule": "replace"}, "example": {"value": "00106137", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHANGE_LOGIN_INITIALS", "column-type": "strColumn", "sources": {"blocks": [{"el": "$.pointOfSale.login.initials"}]},
          "meta": {"description": {"value": "agent initials", "rule": "replace"}, "example": {"value": "JS", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CHANGE_LOGIN_NUMERIC_SIGN", "column-type": "strColumn", "sources": {"blocks": [{"el": "$.pointOfSale.login.numericSign"}]},
          "meta": {"description": {"value": "Authorization code of the agent", "rule": "replace"}, "example": {"value": "2416", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CHANGE_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"el": "$.pointOfSale.office.id"}, {"el": "$.pointOfSale.office.iataNumber"}, {"el": "$.pointOfSale.office.inHouseIdentification"}, {"el": "$.pointOfSale.office.systemCode"}, {"el": "$.pointOfSale.office.agentType"}, {"el": "$.pointOfSale.login.cityCode"}, {"el": "$.pointOfSale.login.countryCode"}, {"el": "$.pointOfSale.login.numericSign"}, {"el": "$.pointOfSale.login.initials"}, {"el": "$.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column":"POINT_OF_SALE_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"]
    }
  },
  {
    "name": "FACT_PRICING_CONDITION",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_PRICING_CONDITION_HISTO"
    }
  },
  {
    "name": "FACT_PRICING_CONDITION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Pricing Conditions",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains detailed information on pricing conditions of a travel document, such as fare calculation and various fare conditions (exchangeable, refundable, domestic sale, …) and nego fare information if applicable.",
        "granularity": "1 pricing condition for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PRICING_CONDITION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"price": "$.pricingConditions"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PRICING_CONDITION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FARE_CALCULATION_TEXT", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.fareCalculation.text"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_CALCULATION_PRICING_INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.fareCalculation.pricingIndicator"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_DOMESTIC_SALE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isDomesticSale"}]},
          "meta": {"example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_INTERNATIONAL_SALE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isInternationalSale"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_ENDORSABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isNonEndorsable"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_EXCHANGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isNonExchangeable"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_REFUNDABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isNonRefundable"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_PENALTY_RESTRICTION", "column-type": "booleanColumn", "sources": {"blocks": [{"price": "$.isPenaltyRestriction"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NEGO_FARE_INCENTIVE_SCHEME", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.negoFareContract.incentiveScheme"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NEGO_FARE_TOUR_CODE", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.negoFareContract.tourCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "FARE_CALC_PRICING_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"price": "$.fareCalculation.pricingIndicator"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_FARE_CALC_PRICING_INDICATOR", "column": "FARE_CALC_PRICING_INDICATOR_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["PRICING_CONDITION_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_FARE_COMPONENT",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_FARE_COMPONENT_HISTO"
    }
  },
  {
    "name": "FACT_FARE_COMPONENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Pricing Conditions",
    "mapping": {
      "description": {
        "description": "Contains the fare component items of which the fare calculation is composed of, related to a travel document.",
        "granularity": "1 fare component for 1 pricing condition"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FARE_COMPONENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"fare": "$.pricingConditions.fareComponents"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FARE_COMPONENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fare": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fare": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-seqNum", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "START_CITY", "column-type": "strColumn", "sources": {"blocks": [{"fare": "$.startCityCode.iataCode"}]},
          "meta": {"example": {"value": "NCE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "END_CITY", "column-type": "strColumn", "sources": {"blocks": [{"fare": "$.endCityCode.iataCode"}]},
          "meta": {"example": {"value": "LON", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "PRICING_CONDITION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_PRICING_CONDITION_HISTO", "column": "PRICING_CONDITION_ID"}]},
        {"name": "START_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fare": "$.startCityCode.iataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "END_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fare": "$.endCityCode.iataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["FARE_COMPONENT_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_FLIGHT_BOUND",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_BOUND_HISTO"
    }
  },
  {
    "name": "FACT_FLIGHT_BOUND_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Pricing Conditions",
    "mapping": {
      "description": {
        "description": "Contains the flight bound items of which the fare calculation is composed of, for a given travel document.",
        "granularity": "1 flight bound element for 1 pricing condition"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_BOUND_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"flight": "$.pricingConditions.flightBounds"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FLIGHT_BOUND_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flight": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flight": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-seqNum", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "START_CITY", "column-type": "strColumn", "sources": {"blocks": [{"flight": "$.startCityCode.iataCode"}]},
          "meta": {"example": {"value": "NCE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "END_CITY", "column-type": "strColumn", "sources": {"blocks": [{"flight": "$.endCityCode.iataCode"}]}
          "meta": {"example": {"value": "LON", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "PRICING_CONDITION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_PRICING_CONDITION_HISTO", "column": "PRICING_CONDITION_ID"}]},
        {"name": "START_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"flight": "$.startCityCode.iataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "END_CITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"flight": "$.endCityCode.iataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_CITY", "column": "CITY_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["FLIGHT_BOUND_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_COUPON",
    "latest": {
      "histo-table-name": "FACT_COUPON_HISTO"
    }
  },
  {
    "name": "FACT_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Coupon",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains detailed information on coupons of travel documents, such as related flight segments (sold, used, current - with marketing and operating flight information), coupon status, estimated prorated fare, fare basis/family and further indicators on fare conditions (exchangeable, refundable) and baggage allowance.",
        "granularity": "1 coupon for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cpn": "$.coupons[*]"}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.documentNumber"}]},
          "meta": {"example": {"value": "*********0123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.number"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SEQUENCE_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.sequenceNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RESERVATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reservationStatus"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUPON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.status"}]}, "meta": {"gdpr-zone": "green"}},
        //CURRENT SEGMENT
        {"name": "CURRENT_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENT_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.currentSegment.carrierCode"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${ifFirstValueNullTakeSecondValue}},
        {"name": "CURRENT_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.currentSegment.operating.flightDesignator.flightNumber"},{"cpn": "$.currentSegment.number"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${takeMktInfoIfNoOpeCarrierInfoProvided}},
        {"name": "CURRENT_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.currentSegment.operating.flightDesignator.operationalSuffix"},{"cpn": "$.currentSegment.suffix"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${takeMktInfoIfNoOpeCarrierInfoProvided}},
        //SOLD SEGMENT
        {"name": "SOLD_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SOLD_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.soldSegment.carrierCode"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${ifFirstValueNullTakeSecondValue}},
        {"name": "SOLD_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.soldSegment.operating.flightDesignator.flightNumber"},{"cpn": "$.soldSegment.number"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${takeMktInfoIfNoOpeCarrierInfoProvided}},
        {"name": "SOLD_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.soldSegment.operating.flightDesignator.operationalSuffix"},{"cpn": "$.soldSegment.suffix"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${takeMktInfoIfNoOpeCarrierInfoProvided}},
        //USED SEGMENT
        {"name": "USED_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.localDateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.number"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.suffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_MKT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.bookingClass.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "USED_OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.usedSegment.carrierCode"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${ifFirstValueNullTakeSecondValue}},
        {"name": "USED_OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.usedSegment.operating.flightDesignator.flightNumber"},{"cpn": "$.usedSegment.number"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${takeMktInfoIfNoOpeCarrierInfoProvided}},
        {"name": "USED_OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.operating.flightDesignator.carrierCode"},{"cpn": "$.usedSegment.operating.flightDesignator.operationalSuffix"},{"cpn": "$.usedSegment.suffix"}]},
          "meta": {"gdpr-zone": "green", "description": {"value": "For prime flights, copied from marketing information if not explicitly received.", "rule": "concat"}},"expr" : ${takeMktInfoIfNoOpeCarrierInfoProvided}},

        {"name": "FARE_BASIS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareBasis.fareBasisCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FARE_FAMILY_OWNER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.fareFamily.owner"}]}, "meta": {"gdpr-zone": "green"}},
        //PRORATION
        {"name": "ESTIMATED_PRORATED_FARE", "column-type": "floatColumn", "sources": {},
          "meta": {"example": {"value": "68.91", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original prorated amount extracted from the fare calc line or by distance", "rule": "replace"}, "example": {"value": "722.55", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "The original currency of proration amount: for FARE-CALC it is the extracted fare calculation currency (can be NUC), and for DISTANCE it is the payment currency", "rule": "replace"}, "example": {"value": "USD", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ESTIMATED_PRORATED_FARE_ALGORITHM", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Indicates which algorithm was used to estimate the prorated fare: FARE-CALC, DISTANCE or NONE in case of error", "rule": "replace"}, "example": {"value": "FARE-CALC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PRORATION_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_PRORATION_METADATA", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Technical field containing details on the used proration estimation algorithm", "rule": "replace"}, "gdpr-zone": "green"}},

        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_SUB_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.subCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.description"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_REMARK", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.serviceRemark"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_CODESHARE", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isCodeshare"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "IS_NON_EXCHANGEABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonExchangeable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-exchangeable (true), or if it can be exchanged (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_NON_REFUNDABLE", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isNonRefundable"}]},
          "meta": {"description": {"value": "Indicates if the coupon is non-refundable (true), or if it can be refunded (false)", "rule": "replace"}, "example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_FROM_CONNECTION", "column-type": "booleanColumn", "sources": {"blocks": [{"cpn": "$.isFromConnection"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE", "column-type": "floatColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT", "column-type": "strColumn", "sources": {},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.weight.unit"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_ALLOWANCE_QUANTITY", "column-type": "intColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.quantity"}]},
          "meta": {"example": {"value": "2", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE", "column-type": "floatColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.amount"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCESS_RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.baggageAllowance.excessRate.currency"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAGGAGE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REVALIDATION_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"cpn": "$.revalidation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.iataNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REVALIDATION_OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.agentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_BEFORE_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidBeforeDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "NOT_VALID_AFTER_DATE_UTC", "column-type": "dateColumn", "sources": {"blocks": [{"cpn": "$.validityDates.notValidAfterDate"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VOLUNTARY_INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "SETTLEMENT_AUTHORIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.settlementAuthorizationCode"}]}, "meta": {"gdpr-zone": "green"}},
        //IDs
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CURRENT_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "CURRENT_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.currentSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "SOLD_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.soldSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_DEPARTURE_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.departure.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "USED_ARRIVAL_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.usedSegment.arrival.iataCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]},
        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.reasonForIssuance.code"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]},
        {"name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"cpn": "$.frequentFlyer.applicableAirlineCode"}, {"cpn": "$.frequentFlyer.frequentFlyerNumber"}]}, "expr": ${hashSIdCheckEndNotNull},
          "fk": [{"table": "FACT_MEMBERSHIP_HISTO", "column": "MEMBERSHIP_ID"}]},
        {"name": "REVALIDATION_POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"cpn": "$.revalidation.pointOfSale.office.id"}, {"cpn": "$.revalidation.pointOfSale.office.iataNumber"}, {"cpn": "$.revalidation.pointOfSale.office.inHouseIdentification"}, {"cpn": "$.revalidation.pointOfSale.office.systemCode"}, {"cpn": "$.revalidation.pointOfSale.office.agentType"}, {"cpn": "$.revalidation.pointOfSale.login.cityCode"}, {"cpn": "$.revalidation.pointOfSale.login.countryCode"}, {"cpn": "$.revalidation.pointOfSale.login.numericSign"}, {"cpn": "$.revalidation.pointOfSale.login.initials"}, {"cpn": "$.revalidation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_POINT_OF_SALE", "column": "POINT_OF_SALE_ID"}]},
        {"name": "VOLUNTARY_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.voluntaryIndicator"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_COUPON_VOLUNTARY_INDICATOR", "column": "COUPON_VOLUNTARY_INDICATOR_ID"}]},

        //final columns
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: primDocNum-issuanceDate", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.documentType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}, {"cpn": "$.currentSegment.departure.iataCode"}, {"cpn": "$.currentSegment.arrival.iataCode"}, {"cpn": "$.currentSegment.departure.localDateTime"}]}, "expr": "hashM("${removeTimePart}")",
          "meta": {"example": {"value": "*********0123", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TKTEMD_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": { "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "proration",
        "json-path-fare-calc": "$.mainResource.current.image.pricingConditions.fareCalculation.text",
        "json-path-price-currency-payment": "$.mainResource.current.image.price.currency",
        "json-path-price-total-payment": "$.mainResource.current.image.price.total",
        "json-path-price-total-taxes-payment": "$.mainResource.current.image.price.totalTaxes"
      },
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "ESTIMATED_PRORATED_FARE_ORIGINAL", "src-unit-col": "ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL", "src-date-col" : "PRORATION_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "ESTIMATED_PRORATED_FARE", "dst-unit-col": "ESTIMATED_PRORATED_FARE_CURRENCY","dst-date-col" :"PRORATION_EXCHANGE_RATE_DATE_TAKEN"},
          {"src-col": "BAGGAGE_EXCESS_RATE_ORIGINAL", "src-unit-col": "BAGGAGE_EXCESS_RATE_CURRENCY_ORIGINAL", "src-date-col" : "BAGGAGE_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "BAGGAGE_EXCESS_RATE", "dst-unit-col": "BAGGAGE_EXCESS_RATE_CURRENCY","dst-date-col" :"BAGGAGE_EXCHANGE_RATE_DATE_TAKEN"},
        ]
      },
      {
        "type": "weight-conversion",
        "conversions": [
          {"src-col": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE_ORIGINAL", "src-unit-col": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT_ORIGINAL", "dst-col": "BAGGAGE_ALLOWANCE_WEIGHT_VALUE", "dst-unit-col": "BAGGAGE_ALLOWANCE_WEIGHT_UNIT"}
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["COUPON_ID", "VERSION"]
    }
  },
  {
    "name": "FACT_FORM_OF_PAYMENT",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_FORM_OF_PAYMENT_HISTO"
    }
  },
  {
    "name": "FACT_FORM_OF_PAYMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Payment",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information on forms of payment used to pay or refund for travel documents, such as category, payment amount converted to home currency, and details on card and authorization. It can refer to a loyalty account used for redemption.",
        "granularity": "1 form of payment for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FORM_OF_PAYMENT_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "payment", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"fop": "$.formsOfPayment[*]"}]}},
        {"name": "refund", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"fop": "$.documentRefund.paymentDetails"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FORM_OF_PAYMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fop": "$.code"}, {"fop": "$.fopIndicator"}, {"fop": "$.displayedAmount.amount"}, {"fop": "$..displayedAmount.currency"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fop": "$.code"}, {"fop": "$.fopIndicator"}, {"fop": "$.displayedAmount.amount"}, {"fop": "$..displayedAmount.currency"}]},
          "meta": {"description": {"value": "Functional key: docId-fopCode-fopIndicator-amount-currency", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-CASH-NEW-100.50-EUR", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INDICATOR", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.fopIndicator"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "AMOUNT", "column-type": "floatColumn", "sources": {},
          "meta": {"description": {"value": "Calculated field: converted fop_amount_payment into airline's home currency", "rule": "replace"}, "example": {"value": "514.13", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CURRENCY", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Static field: airline's home currency", "rule": "replace"}, "example": {"value": "EUR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AMOUNT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.displayedAmount.amount"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.displayedAmount.currency"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {},
          "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AUTHORIZATION_APPROVAL_SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.authorization.sourceOfApprovalCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AUTHORIZATION_APPROVAL_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.authorization.approvalCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AUTHORIZATION_EXTENDED_PAYMENT_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.authorization.extendedPaymentCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CARD_VENDOR_CODE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.paymentCard.vendorCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CARD_MASKED_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.paymentCard.maskedCardNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CARD_HOLDER_NAME", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.paymentCard.holderName"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "CARD_EXPIRY_DATE", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.paymentCard.expiryDate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"fop": "$.freeText"}]}, "meta": {"gdpr-zone": "red"}},
        {"name": "RELATES_TO", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "payment", "literal": "PAYMENT"},
            {"rs-name": "refund", "literal": "REFUND"}
          ]},
          "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to", "rule": "replace"}, "example": {"value": "PAYMENT", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fop": "$.code"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_FORM_OF_PAYMENT_TYPE", "column": "FORM_OF_PAYMENT_TYPE_ID"}]},
        {"name": "AUTHORIZATION_APPROVAL_SOURCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fop": "$.authorization.sourceOfApprovalCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_FORM_OF_PAYMENT_TYPE", "column": "FORM_OF_PAYMENT_TYPE_ID"}]},
        {"name": "CARD_VENDOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"fop": "$.paymentCard.vendorCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_CARD_VENDOR", "column": "CARD_VENDOR_ID"}]},
        {"name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"fop": "$.paymentLoyalty.membership.activeTier.companyCode"}, {"fop": "$.paymentLoyalty.membership.id"}]}, "expr": ${hashSIdCheckEndNotNull},
          "fk": [{"table": "FACT_MEMBERSHIP_HISTO", "column": "MEMBERSHIP_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "AMOUNT_ORIGINAL", "src-unit-col": "CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "AMOUNT", "dst-unit-col": "CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"},
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["FORM_OF_PAYMENT_ID","RELATES_TO", "VERSION"]
    }
  },
  {
    "name": "FACT_MEMBERSHIP",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "FACT_MEMBERSHIP_HISTO"
    }
  },
  {
    "name": "FACT_MEMBERSHIP_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "subdomain": "Loyalty",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains detailed information on loyalty memberships used for accrual or redemption, such as membership number and company.",
        "granularity": "1 membership on 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "MEMBERSHIP_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "fop", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"loy": "$.formsOfPayment[*].paymentLoyalty"}, {"memb": "$.membership"}]}},
        {"name": "refund", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"loy": "$.documentRefund.paymentDetails[*].paymentLoyalty"}, {"memb": "$.membership"}]}},
        {"name": "cpn", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"ff": "$.coupons[*].frequentFlyer"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "fop", "blocks": [{"base": "$.id"}, {"memb": "$.activeTier.companyCode"}, {"memb": "$.id"}]},
            {"rs-name": "refund", "blocks": [{"base": "$.id"}, {"memb": "$.activeTier.companyCode"}, {"memb": "$.id"}]},
            {"rs-name": "cpn", "blocks": [{"base": "$.id"}, {"ff": "$.applicableAirlineCode"}, {"ff": "$.frequentFlyerNumber"}]}
          ]},
          "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "fop", "blocks": [{"base": "$.id"}, {"memb": "$.activeTier.companyCode"}, {"memb": "$.id"}]},
            {"rs-name": "refund", "blocks": [{"base": "$.id"}, {"memb": "$.activeTier.companyCode"}, {"memb": "$.id"}]},
            {"rs-name": "cpn", "blocks": [{"base": "$.id"}, {"ff": "$.applicableAirlineCode"}, {"ff": "$.frequentFlyerNumber"}]}
          ]},
          "meta": {"description": {"value": "Functional key: docId-company-number", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-6X-*********", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "MEMBERSHIP_IDENTIFIER", "column-type": "strColumn",
          "sources": {"root-specific": [
            {"rs-name": "fop", "blocks": [{"memb": "$.id"}]},
            {"rs-name": "refund", "blocks": [{"memb": "$.id"}]},
            {"rs-name": "cpn", "blocks": [{"ff": "$.frequentFlyerNumber"}]}
          ]},
          "meta": {"gdpr-zone": "red"}},
        {"name": "MEMBERSHIP_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"memb": "$.membershipType"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "COMPANY", "column-type": "strColumn",
          "sources": {"root-specific": [
            {"rs-name": "fop", "blocks": [{"memb": "$.activeTier.companyCode"}]},
            {"rs-name": "refund", "blocks": [{"memb": "$.activeTier.companyCode"}]},
            {"rs-name": "cpn", "blocks": [{"ff": "$.applicableAirlineCode"}]}
        ]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CUSTOMER_VALUE", "column-type": "strColumn", "sources": {"blocks": [{"memb": "$.activeTier.customerValue"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CERTIFICATE_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"loy": "$.certificateNumber"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "RELATES_TO", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "fop", "literal": "FOP PAYMENT"},
            {"rs-name": "refund", "literal": "FOP REFUND"},
            {"rs-name": "cpn", "literal": "COUPON ACCRUAL"}
          ]},
          "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to", "rule": "replace"}, "example": {"value": "FOP PAYMENT", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "COMPANY_ID", "column-type": "binaryStrColumn",
          "sources": {"root-specific": [
            {"rs-name": "fop", "blocks": [{"memb": "$.activeTier.companyCode"}]},
            {"rs-name": "refund", "blocks": [{"memb": "$.activeTier.companyCode"}]},
            {"rs-name": "cpn", "blocks": [{"ff": "$.applicableAirlineCode"}]}
          ]},
          "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["MEMBERSHIP_ID", "RELATES_TO", "VERSION"]
    }
  },
  {
    "name": "DIM_POINT_OF_SALE",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the point of sales (office-level and user-level where available) used as document creator/updater, revalidation, quotation override or creator of associated PNRs.", "granularity": "1 point of sale"},
      "merge": {
        "key-columns": ["POINT_OF_SALE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "creation", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "updater", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.latestEvent.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "assoRes", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.coupons[*].revalidation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "revalidation", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.associatedPnrs[*].creation.pointOfSale"},
          {"office" : "$.office"}]}
        },
        { "name": "change", "rs": { "blocks": [
          {"pos": "$.mainResource.current.image.quotationOverrides[*].eventLog.pointOfSale"},
          {"office" : "$.office"}]}
        }
      ],
      "columns": [
        { "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": { "blocks": [
            {"office": "$.id"},
            {"office": "$.iataNumber"},
            {"office": "$.inHouseIdentification"},
            {"office": "$.systemCode"},
            {"office": "$.agentType"},
            {"pos": "$.login.cityCode"},
            {"pos": "$.login.countryCode"},
            {"pos": "$.login.numericSign"},
            {"pos": "$.login.initials"},
            {"pos": "$.login.dutyCode"}
          ]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (officeId-inhouseId-officeIATA-system-agentType-city-country-numSign-initials-duty)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"office": "$.id"}]}},
        {"name": "OFFICE_IATA_NUMBER","column-type": "strColumn", "sources": {"blocks": [{"office": "$.iataNumber"}]}},
        {"name": "OFFICE_IN_HOUSE_IDENTIFICATION", "column-type": "strColumn", "sources": {"blocks": [{"office": "$.inHouseIdentification"}]}},
        {"name": "OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources": {"blocks": [{"office": "$.systemCode"}]}},
        {"name": "OFFICE_AGENT_TYPE","column-type": "strColumn","sources": {"blocks": [{"office": "$.agentType"}]}},
        {"name": "LOGIN_CITY_CODE","column-type": "strColumn","sources": {"blocks": [{"pos": "$.login.cityCode"}]}},
        {"name": "LOGIN_COUNTRY_CODE","column-type": "strColumn", "sources": {"blocks": [{"pos": "$.login.countryCode"}]}},
        {"name": "LOGIN_NUMERIC_SIGN", "column-type": "strColumn", "sources": {"blocks": [{"pos": "$.login.numericSign"}]},"meta": {"gdpr-zone": "orange"}},
        {"name": "LOGIN_INITIALS", "column-type": "strColumn", "sources": {"blocks": [{"pos": "$.login.initials"}]},"meta": {"gdpr-zone": "orange"}},
        {"name": "LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"pos": "$.login.dutyCode"}]}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRLINE",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the airlines used as validating/marketing/operating carriers on travel documents or coupons, or for loyalty programs.", "granularity": "1 airline"},
      "merge": {
        "key-columns": ["AIRLINE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"companyCode": "$.mainResource.current.image.validatingCarrierCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.coupons[*].frequentFlyer.applicableAirlineCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.formsOfPayment[*].paymentLoyalty.membership.activeTier.companyCode"}]},
        { "blocks": [{"companyCode": "$.mainResource.current.image.documentRefund.paymentDetails[*].paymentLoyalty.membership.activeTier.companyCode"}]},
      ],
      "columns": [
        {"name": "AIRLINE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"companyCode": "$.value"}]},"expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (airline IATA code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRLINE_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"companyCode": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_CITY",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the cities used on travel documents and fare components as start/end cities", "granularity": "1 city"},
      "merge": {
        "key-columns": ["CITY_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"city": "$.mainResource.current.image.originCityIataCode"}]},
        { "blocks": [{"city": "$.mainResource.current.image.destinationCityIataCode"}]},
        { "blocks": [{"city": "$.mainResource.current.image.pricingConditions.fareComponents[*].startCityCode.iataCode"}]},
        { "blocks": [{"city": "$.mainResource.current.image.pricingConditions.fareComponents[*].endCityCode.iataCode"}]},
        { "blocks": [{"city": "$.mainResource.current.image.pricingConditions.flightBounds[*].startCityCode.iataCode"}]},
        { "blocks": [{"city": "$.mainResource.current.image.pricingConditions.flightBounds[*].endCityCode.iataCode"}]}
      ],
      "columns": [
        {"name": "CITY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"city": "$.value"}]},"expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (city IATA code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CITY_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"city": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the airports used in departure/arrival airports of flight segments (sold, used, current) related to coupons", "granularity": "1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [{"airport": "$.mainResource.current.image.coupons[*].soldSegment.departure"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.coupons[*].usedSegment.departure"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.coupons[*].currentSegment.departure"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.coupons[*].soldSegment.arrival"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.coupons[*].usedSegment.arrival"}]},
        { "blocks": [{"airport": "$.mainResource.current.image.coupons[*].currentSegment.arrival"}]}
      ],
      "columns": [
        {"name": "AIRPORT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"airport": "$.iataCode"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (airport IATA code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"airport": "$.iataCode"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REASON_FOR_ISSUANCE",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the reasons for issuance codes used on coupons", "granularity": "1 reason code (RFIC)"},
      "merge": {
        "key-columns": ["REASON_FOR_ISSUANCE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.coupons[*].reasonForIssuance"}]}],
      "columns": [
        {"name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.code"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (reason code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.code"}]}},
        {"name": "REASON_FOR_ISSUANCE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.code"}]},
          "meta": {"description": {"value": "Label corresponding to the reason for issuance code", "rule": "replace"}, "example": {"value": "Air Transportation", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "REASON_FOR_ISSUANCE",
      "column-filler" : [
        {"dim-col" : "REASON_FOR_ISSUANCE_ID", "src-col" : "CODE"},
        {"dim-col" : "REASON_FOR_ISSUANCE_CODE", "src-col" : "CODE"},
        {"dim-col" : "REASON_FOR_ISSUANCE_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_COUPON_VOLUNTARY_INDICATOR",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the coupon voluntary indicators (manual operations) used on coupons", "granularity": "1 coupon voluntary indicator"},
      "merge": {
        "key-columns": ["COUPON_VOLUNTARY_INDICATOR_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.coupons[*].voluntaryIndicator"}]}],
      "columns": [
        {"name": "COUPON_VOLUNTARY_INDICATOR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (voluntary indicator code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "COUPON_VOLUNTARY_INDICATOR_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}},
        {"name": "COUPON_VOLUNTARY_INDICATOR_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]},
          "meta": {"description": {"value": "The label of the coupon voluntary indicator, as part of reference data", "rule": "replace"}, "example": {"value": "Schedule change", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "COUPON_VOLUNTARY_INDICATOR",
      "column-filler" : [
        {"dim-col" : "COUPON_VOLUNTARY_INDICATOR_ID", "src-col" : "CODE"},
        {"dim-col" : "COUPON_VOLUNTARY_INDICATOR_CODE", "src-col" : "CODE"},
        {"dim-col" : "COUPON_VOLUNTARY_INDICATOR_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_FORM_OF_PAYMENT_TYPE",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the categories of forms of payment, such as cash, credit card, cheque, etc. used for travel document payment or refund", "granularity": "1 form of payment category"},
      "merge": {
        "key-columns": ["FORM_OF_PAYMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.formsOfPayment[*].code"}]},
        {"blocks": [{"base": "$.mainResource.current.image.documentRefund.paymentDetails.code"}]}
      ],
      "columns": [
        {"name": "FORM_OF_PAYMENT_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (fop code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FORM_OF_PAYMENT_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}},
        {"name": "FORM_OF_PAYMENT_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]},
          "meta": {"description": {"value": "The label of the form of payment category, as part of reference data", "rule": "replace"}, "example": {"value": "Credit card", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "FORM_OF_PAYMENT_TYPE",
      "column-filler" : [
        {"dim-col" : "FORM_OF_PAYMENT_TYPE_ID", "src-col" : "CODE"},
        {"dim-col" : "FORM_OF_PAYMENT_TYPE_CODE", "src-col" : "CODE"},
        {"dim-col" : "FORM_OF_PAYMENT_TYPE_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_CARD_VENDOR",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the card vendors used on forms of payments of type credit card", "granularity": "1 card vendor"},
      "merge": {
        "key-columns": ["CARD_VENDOR_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.formsOfPayment[*].paymentCard.vendorCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image.documentRefund.paymentDetails.paymentCard.vendorCode"}]}
      ],
      "columns": [
        {"name": "CARD_VENDOR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (vendor code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CARD_VENDOR_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.value"}]}},
        {"name": "CARD_VENDOR_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]},
          "meta": {"description": {"value": "The name of the card vendor, as part of reference data", "rule": "replace"}, "example": {"value": "Visa", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "CARD_VENDOR",
      "column-filler" : [
        {"dim-col" : "CARD_VENDOR_ID", "src-col" : "CODE"},
        {"dim-col" : "CARD_VENDOR_CODE", "src-col" : "CODE"},
        {"dim-col" : "CARD_VENDOR_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_FARE_CALC_PRICING_INDICATOR",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the various fare calc pricing indicators", "granularity": "1 fare calc pricing indicator"},
      "merge": {
        "key-columns": ["FARE_CALC_PRICING_INDICATOR_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"price": "$.pricingConditions.fareCalculation.pricingIndicator"}]}
      ],
      "columns": [
        {"name": "FARE_CALC_PRICING_INDICATOR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"price": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FARE_CALC_PRICING_INDICATOR_CODE", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.value"}]}},
        {"name": "FARE_CALC_PRICING_INDICATOR_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.value"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "FARE_CALC_PRICING_INDICATOR",
      "column-filler" : [
        {"dim-col" : "FARE_CALC_PRICING_INDICATOR_ID", "src-col" : "CODE"},
        {"dim-col" : "FARE_CALC_PRICING_INDICATOR_CODE", "src-col" : "CODE"},
        {"dim-col" : "FARE_CALC_PRICING_INDICATOR_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_TAX_CODE",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the various tax codes", "granularity": "1 tax code"},
      "merge": {
        "key-columns": ["TAX_CODE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "trvDoc", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"tax": "$.price.taxes"}]}
        },
        { "name": "refund", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.documentRefund"},
          {"tax": "$.taxes"}]}
        },
        {
          "name": "obFee", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.documentRefund.obFees"},
          {"tax": "$.taxes"}]},
        },
        {
          "name": "fareComp", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.fareComponents"},
          {"tax": "$.price.taxes"}]}
        },
        {
          "name": "fltBnd", "rs": { "blocks": [
          {"base": "$.mainResource.current.image"},
          {"type": "$.pricingConditions.flightBounds"},
          {"tax": "$.price.taxes"}]}
        }
      ],
      "columns": [
        {"name": "TAX_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tax": "$.code"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TAX_CODE", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]}},
        {"name": "TAX_CODE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"tax": "$.code"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "TAX_CODE",
      "column-filler" : [
        {"dim-col" : "TAX_CODE_ID", "src-col" : "CODE"},
        {"dim-col" : "TAX_CODE", "src-col" : "CODE"},
        {"dim-col" : "TAX_CODE_LABEL", "src-col" : "NAME"}
      ]
      },
      {
        "data-source-key": "COUNTRY",
        "column-filler" : [
          {"dim-col" : "TAX_CODE_ID", "src-col" : "ISO2"},
          {"dim-col" : "TAX_CODE", "src-col" : "ISO2"},
          {"dim-col" : "TAX_CODE_LABEL", "src-col" : "LABEL"}
        ]
      }]
  },
  {
    "name": "DIM_PASSENGER_TYPE",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the different passenger types", "granularity": "1 passenger type"},
      "merge": {
        "key-columns": ["PASSENGER_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.traveler"}]}],
      "columns": [
        {"name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
        {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passengerTypeCode"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "PASSENGER_TYPE",
      "column-filler" : [
        {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
        {"dim-col" : "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
        {"dim-col" : "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_FOP_AUTHORIZATION_SOURCE",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the different form of payment authorization sources", "granularity": "1 FOP authorization source"},
      "merge": {
        "key-columns": ["FOP_AUTHORIZATION_SOURCE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "payment", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"fop": "$.formsOfPayment[*].authorization.sourceOfApprovalCode"}]}},
        {"name": "refund", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"fop": "$.documentRefund.paymentDetails.authorization.sourceOfApprovalCode"}]}}
      ],
      "columns": [
        {"name": "FOP_AUTHORIZATION_SOURCE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"fop":"$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FOP_AUTHORIZATION_SOURCE_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"fop":"$.value"}]}},
        {"name": "FOP_AUTHORIZATION_SOURCE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"fop":"$.value"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "FORM_OF_PAYMENT_APPROVAL_CODE",
      "column-filler" : [
        {"dim-col" : "FOP_AUTHORIZATION_SOURCE_ID", "src-col" : "CODE"},
        {"dim-col" : "FOP_AUTHORIZATION_SOURCE_CODE", "src-col" : "CODE"},
        {"dim-col" : "FOP_AUTHORIZATION_SOURCE_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_TRIGGER_EVENT",
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Lists the different trigger event types", "granularity": "1 trigger event type"},
      "merge": {
        "key-columns": ["TRIGGER_EVENT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image.latestEvent.triggerEventName"}]}],
      "columns": [
        {"name": "TRIGGER_EVENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TRIGGER_EVENT_NAME", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.value"}]}},
        {"name": "TRIGGER_EVENT_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.value"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "PASSENGER_TYPE",
      "column-filler" : [
        {"dim-col" : "TRIGGER_EVENT_ID", "src-col" : "CODE"},
        {"dim-col" : "TRIGGER_EVENT_NAME", "src-col" : "CODE"},
        {"dim-col" : "TRIGGER_EVENT_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "ASSO_FARE_COMPONENT_COUPON",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "ASSO_FARE_COMPONENT_COUPON_HISTO"
    }
  },
  {
    "name": "ASSO_FARE_COMPONENT_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Contains associations between fare components and coupons.", "granularity": "1 fare component and 1 coupon in 1 travel document"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FARE_COMPONENT_ID", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cartesian": [
            [{"fare": "$.pricingConditions.fareComponents[*]"}],
            [{"cpn": "$.coupons[*]"}]
          ]}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FARE_COMPONENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fare": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_FARE_COMPONENT_HISTO", "column": "FARE_COMPONENT_ID"}]},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "REFERENCE_KEY_FARE_COMPONENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"fare": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related fare component", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related coupon", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["FARE_COMPONENT_ID", "COUPON_ID", "VERSION"]
    }
  },
  {
    "name": "ASSO_FLIGHT_BOUND_COUPON",
    "table-selectors": ["TKTEMD_CORE"],
    "latest": {
      "histo-table-name": "ASSO_FLIGHT_BOUND_COUPON_HISTO"
    }
  },
  {
    "name": "ASSO_FLIGHT_BOUND_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["TKTEMD_CORE"],
    "mapping": {
      "description": {"description": "Contains associations between flight bounds and coupons.", "granularity": "1 flight bound and 1 coupon in 1 travel document"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_BOUND_ID", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          {"cartesian": [
            [{"flight": "$.pricingConditions.flightBounds[*]"}],
            [{"cpn": "$.coupons[*]"}]
          ]}
        ]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FLIGHT_BOUND_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flight": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_FLIGHT_BOUND_HISTO", "column": "FLIGHT_BOUND_ID"}]},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}]},
        {"name": "REFERENCE_KEY_FLIGHT_BOUND", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flight": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related flight bound", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]},
          "meta": {"description": {"value": "Functional key of the related coupon", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.primaryDocumentNumber"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "DOCUMENT_CREATION_DATE", "column-type": "dateColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"description": {"value": "Date/time UTC of the document issuance", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["FLIGHT_BOUND_ID", "COUPON_ID", "VERSION"]
    }
  },

  //// TKTEMD-PNR Internal associations
  {
    "name": "INTERNAL_ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO", //PNR -> TKT 1/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.TKT-PNR"}, {"corr": "$.correlations[*]"}]},
        {"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains PNR-TKT and PNR-EMD correlation information as seen by TKTEMD.",
        "granularity": "1 PNR-TRAVEL_DOCUMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_RESERVATION_TRAVEL_DOCUMENT_HISTO", //PNR -> TKT 1/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": []}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains PNR-TKT and PNR-EMD correlation information as seen by TKTEMD.",
        "granularity": "1 PNR-TRAVEL_DOCUMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO", //PNR -> TKT 2/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVELER_ID", "TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.TKT-PNR"}, {"corr": "$.correlations[*]"}, {"trvlr": "$.corrTktPnr.items[*].pnrTravelerId"}]},
        {"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}, {"trvlr": "$.corrEmdPnr.items[*].pnrTravelerId"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"trvlr": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_TRAVELER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"trvlr": "$.value"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains PNR-TKT and PNR-EMD correlation information as seen by TKTEMD.",
        "granularity": "1 TRAVELER-TRAVEL_DOCUMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVELER_TRAVEL_DOCUMENT_HISTO", //PNR -> TKT 2/5
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVELER_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVELER", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains PNR-TKT and PNR-EMD correlation information as seen by TKTEMD.",
        "granularity": "1 TRAVELER-TRAVEL_DOCUMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "tkt", "rs" :{"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.TKT-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrTktPnr.items[*]"}, {"cpn": "$.ticketCouponId"}]}},
        {"name": "emd", "rs" :{"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrEmdPnr.items[*]"}, {"cpn": "$.emdCouponId"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"items": "$.pnrAirSegmentId"}, {"items": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"items": "$.pnrAirSegmentId"}, {"items": "$.pnrTravelerId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.value"}]}},
        {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn",
          "sources": {"root-specific": [
            {"rs-name": "tkt", "literal": "TKT"},
            {"rs-name": "emd", "literal": "EMD"}
          ]}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains AIR_SEGMENT_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 AIR_SEGMENT_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {}},
        {"name": "AIR_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "TECH_RELATED_AIR_SEGMENT_IDENTIFIER_FOR_FILTERING_PURPOSE", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains AIR_SEGMENT_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 AIR_SEGMENT_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_SERVICE_PAX_COUPON_HISTO", // TODO : add filter to get only services that are subtype: SERVICE
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SERVICE_PAX_ID", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrEmdPnr.items[*]"}, {"cpn": "$.emdCouponId"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]}},
        {"name": "SERVICE_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"items": "$.pnrServiceId"}, {"items": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_SERVICE_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"items": "$.pnrServiceId"}, {"items": "$.pnrTravelerId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.value"}]}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SERVICE_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 SERVICE_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_SERVICE_PAX_COUPON_HISTO", // TODO : add filter to get only services that are subtype: SERVICE
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SERVICE_PAX_ID", "INTERNAL_CORR_ID_EMD", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrEmdPnr.items[*]"}, {"cpn": "$.emdCouponId"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "SERVICE_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_SERVICE_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER_EMD", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SERVICE_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 SERVICE_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO", // TODO : add filter to get only services that are subtype: SEATING
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEATING_PAX_ID", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrEmdPnr.items[*]"}, {"cpn": "$.emdCouponId"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]}},
        {"name": "SEATING_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"items": "$.pnrServiceId"}, {"items": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_SEATING_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"items": "$.pnrServiceId"}, {"items": "$.pnrTravelerId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.value"}]}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SEATING_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 SEATING_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO", // TODO : add filter to get only services that are subtype: SEATING
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEATING_PAX_ID", "INTERNAL_CORR_ID_EMD", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrEmdPnr.items[*]"}, {"cpn": "$.emdCouponId"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "SEATING_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_SEATING_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER_EMD", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SEATING_PAX-COUPON correlation information as seen by TKTEMD.",
        "granularity": "1 SEATING_PAX-COUPON",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_TRAVEL_DOCUMENT_SKD_FLIGHT_DATE_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "SKD_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.TKT-SKD"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 TRAVEL_DOCUMENT-FLIGHT_DATE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_TRAVEL_DOCUMENT_SKD_FLIGHT_DATE_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "SKD_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.soldSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.currentSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.usedSegment"}, {"shouldNotBeEmpty": "$.number"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 TRAVEL_DOCUMENT-FLIGHT_DATE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_COUPON_SKD_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "SKD_ACTIVE", "DIH_CORRELATION"],

    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.TKT-SKD"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrTktSkd.items[*]"},
          {"shouldNotBeEmpty": "$.flightSegmentId"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_SEGMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_COUPON_SKD_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "SKD_ACTIVE", "DAAS_CORRELATION"],

    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.soldSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.currentSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.usedSegment"}, {"shouldNotBeEmpty": "$.number"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrFlightSegmentId}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_SEGMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_COUPON_SKD_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "SKD_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.TKT-SKD"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrTktSkd.items[*]"},
          {"shouldNotBeEmpty": "$.partnershipFlightId"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"},{"item": "$.partnershipFlightId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"},{"item": "$.partnershipFlightId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_CODESHARE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_COUPON_SKD_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "SKD_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.soldSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.currentSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.usedSegment"}, {"shouldNotBeEmpty": "$.number"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrCodeshareFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrCodeshareFlightSegmentId}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_CODESHARE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE","TKTEMD_ACTIVE", "DIH_CORRELATION"],["DCSPAX_ACTIVE","TKTEMD_PASSIVE", "DIH_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["TRAVEL_DOCUMENT_ID", "PASSENGER_ID", "VERSION_PASSENGER", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "tkt", "rs": {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.TKT-DCSPAX"}, {"corr": "$.correlations[*]"}]}},
        {"name": "emd", "rs": {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-DCSPAX"}, {"corr": "$.correlations[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root2": "$.mainResource.current.image.creation.dateTime"}, {"root2": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION_PASSENGER", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX-TRAVEL_DOCUMENT correlation information as seen by TKTEMD.",
      "granularity": "1 PAX-TKT/EMD",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE","TKTEMD_ACTIVE", "DAAS_CORRELATION"],["DCSPAX_ACTIVE","TKTEMD_PASSIVE", "DAAS_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_CORR_ID", "PASSENGER_ID", "VERSION_PASSENGER", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "tkt", "rs": {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.TKT-DCSPAX"}, {"corr": "$.correlations[*]"}]}},
        {"name": "emd", "rs": {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-DCSPAX"}, {"corr": "$.correlations[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {},  "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION_PASSENGER", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX-TRAVEL_DOCUMENT correlation information as seen by TKTEMD.",
      "granularity": "1 PAX-TKT/EMD",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SEGMENT_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE", "DIH_CORRELATION"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "SEGMENT_DELIVERY_ID", "VERSION_PASSENGER", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "tkt", "rs": {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.TKT-DCSPAX"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxTkt.items[*]"}, {"segdel": "$.dcspaxSegmentId"}]}},
        {"name": "emd", "rs": {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-DCSPAX"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxEmd.items[*]"}, {"segdel": "$.dcsPassengerSegmentDeliveryId"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root2": "$.mainResource.current.image.creation.dateTime"}, {"root2": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {
          "name": "COUPON_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "tkt", "blocks": [{"item": "$.tktCouponId"}]},
              {"rs-name": "emd", "blocks": [{"item": "$.emdCouponId"}]}
            ]
          }, "expr": "hashM({0})"
        },
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {
          "name": "REFERENCE_KEY_COUPON", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "tkt", "blocks": [{"item": "$.tktCouponId"}]},
              {"rs-name": "emd", "blocks": [{"item": "$.emdCouponId"}]}
            ]
          }
        },
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.value"}]}},
        {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {
          "root-specific": [
            {"rs-name": "tkt","literal": "TKT"},
            {"rs-name": "emd","literal": "EMD"}
          ]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION_PASSENGER", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SEGMENT_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by TKTEMD.",
      "granularity": "1 PAX_SEG_DEL-TKT/EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SEGMENT_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE", "DAAS_CORRELATION"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_CORR_ID", "SEGMENT_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "tkt", "rs": {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.TKT-DCSPAX"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxTkt.items[*]"}, {"segdel": "$.dcspaxSegmentId"}]}},
        {"name": "emd", "rs": {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-DCSPAX"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxEmd.items[*]"}, {"segdel": "$.dcsPassengerSegmentDeliveryId"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SEGMENT_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by TKTEMD.",
      "granularity": "1 PAX_SEG_DEL-TKT/EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE", "DIH_CORRELATION"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "SERVICE_DELIVERY_ID", "VERSION_PASSENGER", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-DCSPAX"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxEmd.items[*]"}, {"serv": "$.dcsPassengerSegmentDeliveryId"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root2": "$.mainResource.current.image.creation.dateTime"}, {"root2": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"item": "$.emdCouponId"}]}, "expr": "hashM({0})"},
        {"name": "SERVICE_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"serv": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.emdCouponId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"serv": "$.value"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.fromVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION_PASSENGER", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SERVICE_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by EMD.",
      "granularity": "1 PAX_SERVICE_DEL-EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE", "DAAS_CORRELATION"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_CORR_ID", "SERVICE_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root2": "$"}, {"base": "$.correlatedResourcesCurrent.EMD-DCSPAX"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxEmd.items[*]"}, {"serv": "$.dcsPassengerSegmentDeliveryId"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "SERVICE_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SERVICE_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by EMD.",
      "granularity": "1 PAX_SERVICE_DEL-EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_FACT_ASSOCIATED_RESERVATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": [["PNR_ACTIVE", "TKTEMD_PASSIVE", "DAAS_CORRELATION"], ["PNR_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"]],
    "mapping": {
      "description": {
        "description": "Contains references of reservations/PNRs related to a given travel document, including the system code in which the related PNR was created.",
        "granularity": "1 associated reservation for 1 travel document",
        "subdomain": "Travel Document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "basePnr", "rs": {"blocks": [{"base": "$.mainResource.current.image"}]}},
        {"name": "associatedPnrs", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"assoRes": "$.associatedPnrs[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnr", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}]},
            {"rs-name": "associatedPnrs", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}]}
          ]}, "expr": "hashM({0})"
        },
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnr", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}]},
            {"rs-name": "associatedPnrs", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}]}
          ]}
        },
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-recordLoc", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-AB{0}23", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["INTERNAL_CORR_ID", "VERSION"]
    }
  },
  {
    "name": "INTERNAL_FACT_ASSOCIATED_RESERVATION_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": [["PNR_ACTIVE", "TKTEMD_PASSIVE", "DAAS_CORRELATION"], ["PNR_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"]],
    "mapping": {
      "description": {
        "description": "Contains references of reservations/PNRs related to a given travel document, including the system code in which the related PNR was created.",
        "granularity": "1 associated reservation for 1 travel document",
        "subdomain": "Travel Document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "basePnr", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}]}},
        {"name": "associatedPnrs", "rs": {"blocks": [
          {"base": "$.mainResource.current.image"},
          {
            "cartesian": [
              [{"cpn": "$.coupons[*]"}],
              [{"assoRes": "$.associatedPnrs[*]"}],
            ]
          }
        ]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnr", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}, {"cpn": "$.currentSegment.departure.iataCode"}, {"cpn": "$.currentSegment.arrival.iataCode"}, {"cpn": "$.currentSegment.departure.localDateTime"}]},
            {"rs-name": "associatedPnrs", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}, {"cpn": "$.currentSegment.departure.iataCode"}, {"cpn": "$.currentSegment.arrival.iataCode"}, {"cpn": "$.currentSegment.departure.localDateTime"}]}
          ]}, "expr": "hashM("${removeTimePart}")"
        },
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnr", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"base": "$.primaryDocumentNumber"}, {"cpn": "$.currentSegment.departure.iataCode"}, {"cpn": "$.currentSegment.arrival.iataCode"}, {"cpn": "$.currentSegment.departure.localDateTime"}]},
            {"rs-name": "associatedPnrs", "blocks": [{"assoRes": "$.reference"}, {"base": "$.primaryDocumentNumber"}, {"cpn": "$.currentSegment.departure.iataCode"}, {"cpn": "$.currentSegment.arrival.iataCode"}, {"cpn": "$.currentSegment.departure.localDateTime"}]}
          ]}, "expr": ${removeTimePart}
        },
        {"name": "INTERNAL_CORR_ID_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnr", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"cpn": "$.id"}]},
            {"rs-name": "associatedPnrs", "blocks": [{"assoRes": "$.reference"}, {"cpn": "$.id"}]}
          ]}, "expr": "hashM("${createInternalCorrIdEmd}")"
        },
        {"name": "INTERNAL_CORR_IDENTIFIER_EMD", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"root-specific": [
            {"rs-name": "basePnr", "blocks": [{"base": "$.validatingCarrierPnr.reference"}, {"cpn": "$.id"}]},
            {"rs-name": "associatedPnrs", "blocks": [{"assoRes": "$.reference"}, {"cpn": "$.id"}]}
          ]}, "expr": ${createInternalCorrIdEmd}
        },
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-recordLoc", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-AB{0}23", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]},
          "meta": {"description": {"value": "Functional key: docId-cpnSeqNum", "rule": "replace"}, "example": {"value": "*********0123-2020-01-31-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', DOCUMENT_CREATION_DATE)"],
      "primary-keys": ["INTERNAL_CORR_ID", "VERSION"]
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_TRAVEL_DOCUMENT_INV_FLIGHT_DATE_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.TKT-INV"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 TRAVEL_DOCUMENT-FLIGHT_DATE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_TRAVEL_DOCUMENT_INV_FLIGHT_DATE_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.soldSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.currentSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.usedSegment"}, {"shouldNotBeEmpty": "$.number"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-INV correlation information as seen by TKT/EMD.",
        "granularity": "1 TRAVEL_DOCUMENT-FLIGHT_DATE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_COUPON_INV_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"],

    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.TKT-INV"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvTkt.items[*]"},
          {"shouldNotBeEmpty": "$.inventorySegmentId"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-SKD correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_SEGMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_COUPON_INV_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"],

    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.soldSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.currentSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.usedSegment"}, {"shouldNotBeEmpty": "$.number"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"cpn": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrFlightSegmentId}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-INV correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_SEGMENT",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_COUPON_INV_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.TKT-INV"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvTkt.items[*]"},
          {"shouldNotBeEmpty": "$.inventoryPartnershipId"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.creation.dateTime"}, {"root1": "$.mainResource.current.image.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"},{"item": "$.inventoryPartnershipId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"},{"item": "$.inventoryPartnershipId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-INV correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_CODESHARE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_COUPON_INV_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "table-selectors": ["TKTEMD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.soldSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.currentSegment"}, {"shouldNotBeEmpty": "$.number"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cpn": "$.coupons[*]"}, {"seg": "$.usedSegment"}, {"shouldNotBeEmpty": "$.number"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.primaryDocumentNumber"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrCodeshareFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"cpn": "$.id"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.localDateTime"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrCodeshareFlightSegmentId}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.flightDesignator.carrierCode"}, {"seg": "$.operating.flightDesignator.flightNumber"}, {"seg": "$.operating.flightDesignator.operationalSuffix"}, {"seg": "$.departure.localDateTime"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.latestEvent.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-INV correlation information as seen by TKT/EMD.",
        "granularity": "1 COUPON-FLIGHT_CODESHARE",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_RA_TRAVEL_DOCUMENT", // TKTEMD-RA #1/2
    "table-selectors": [["TKTEMD_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TKTEMD_TRAVEL_DOCUMENT_ID", "RA_TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "TKTEMD_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RA_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TKTEMD_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_RA_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION_RA_TRAVEL_DOCUMENT", "column-type": "floatColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-RA correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  }
  {
    "name": "INTERNAL_PARTIAL_TKTEMD_RA_COUPON", // TKTEMD-RA #1/2
    "table-selectors": [["TKTEMD_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TKTEMD_COUPON_ID", "RA_COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "TKTEMD_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RA_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TKTEMD_COUPON", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_RA_COUPON", "column-type": "strColumn", "sources": {}},
        {"name": "TKTEMD_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RA_TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TKTEMD_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_RA_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION_RA_TRAVEL_DOCUMENT", "column-type": "floatColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKTEMD-RA correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
]
