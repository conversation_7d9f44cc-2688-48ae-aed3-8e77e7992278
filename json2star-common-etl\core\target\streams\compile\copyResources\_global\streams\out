[0m[[0m[0mdebug[0m] [0m[0mCopy resource mappings: [0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-databricks-sf-consistency.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/check-databricks-sf-consistency.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-no-null-columns.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/check-no-null-columns.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-purge.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/check-purge.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-snowflake-no-dupes-on-islatestversion-true.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/check-snowflake-no-dupes-on-islatestversion-true.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/check-tables-count.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/check-tables-count.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_copy_sf_tables.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_copy_sf_tables.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_copy_tables.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_copy_tables.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_create.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_db_create.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_delete.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_db_delete.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_sf_create.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_db_sf_create.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_sf_delete.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_db_sf_delete.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_db_validate.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_db_validate.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_jdf_init.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_jdf_init.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_purge.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_purge.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/json2star_remove_checkpoints.scala,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/json2star_remove_checkpoints.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m	(/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/main/resources/log4j2.properties,/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes/log4j2.properties)[0m
