{"defaultComment" : "PNR star schema, V2 (JSON-To-Star)",
"partition-spec" : {
  "key" : "PNR_CREATION_DATE",
  "column-name": "PART_PNR_CREATION_MONTH",
  "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
},
"tables": [
  {
    "name": "FACT_PRODUCT_PAX_HISTO",
    "mapping": {
      "description": {"description": "Contains information on a service booking (SSR or SVC) of a given passenger, such as service code, booking status and service freetext.", "granularity": "1 service booking for 1 passenger"},
      "merge": {
        "key-columns": ["PRODUCT_PAX_ITEN_SEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [
          {"base": "$.mainResource.current.image"},
          //cartesian product between three branches with
          {"cartesian": [
            [{"prod": "$.products[*]"}],
            [{"trv": "$.travelers[*]"}],
            [{"iten": "$.flightItinerary[*]"},{"segs": "$.flights[*].connectedFlights.flightSegments[*]"}]
          ]}
        ]}
      ],
      "columns": [
        {"name": "PRODUCT_PAX_ITEN_SEG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}, {"trv": "$.id"}, {"iten": "$.id"}, {"segs": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"prod": "$.id"}, {"trv": "$.id"}]},
          "meta": {"description": {"value": "Functional key: serviceId-paxId", "rule": "replace"}, "example": {"value": "ABCDEF-2019-10-05-OT-1-ABCDEF-2019-10-05-PT-1", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"trv": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.service.code"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_SERVICE"}]},
        {"name": "ITINERARY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"iten": "$.id"}]}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"segs": "$.id"}]}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.creation.dateTime"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]},
          "meta": {"description": {"value": "Envelope/version of the PNR message", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]},
          "meta": {"description": {"value": "Envelope/version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this envelope/version is the latest envelope/version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master" : {
          "pit-key": "PRODUCT_PAX_ITEN_SEG_ID",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_VERSION"
        }
      }}
  }]
}