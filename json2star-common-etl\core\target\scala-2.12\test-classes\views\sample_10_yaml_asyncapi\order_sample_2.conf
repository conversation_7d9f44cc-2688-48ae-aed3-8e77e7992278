"defaultComment" : "ORDERAGREEMENT star schema",
"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.data": "OrderOrderChangeEvent",
      "$.attachments": "attachments",
      "$.data.currentImage.orderItems.lifecycle": "OrderOrderLifecycle",
      // point to the right property of a Schema as mismatch between json/yaml paths (expected stakeholders.personalData.personalDataRef)
      "$.data.currentImage.stakeholders.personalDataRef": "OrderServiceFulfillmentStakeholderPersonalData.personalDataRef",
      // point to the right Schema as mismatch between json/yaml paths (expected stakeholders.seller.pointOfSale)
      "$.data.currentImage.stakeholders.pointOfBusiness": "OrderStakeholderPointOfBusiness",
      //  point to the right Schema as mismatch in asyncapi yaml - Leg.v1
      "$.data.currentImage.orderItems.services.context.soldProduct.productInstance.leg": "Leg\\.v1",
      "$.data.currentImage.orderItems.services.context.soldProduct.productInstance.leg.deis.value": "DEI\\.v2.\\value"
    }
  }
},
"tables": [
  {
    "name": "FACT_AGREEMENT_ITEM",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_AGREEMENT_ITEM_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATE_TIME_AGREEMENT)"]
    }
  },
  {
    "name": "FACT_AGREEMENT_ITEM_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to the agreement item", "granularity": "1 Agreement Item"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "VERSION_AGREEMENT"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"agree": "$.data.currentImage.agreements[*]"}, {"item": "$.items[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"agree": "$.lifecycle.creation.dateTime"}, {"agree": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AGREEMENT_ITEM_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"agree": "$.id"}, {"item": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"agree": "$.id"}, {"item": "$.id"}]},
          "meta": {"description": {"value": "Functional key: agreementId-itemId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AGREEMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"agree": "$.id"}]}},
        {"name": "AGREEMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"agree": "$.status"}]}},
        {"name": "AGREEMENT_ORIGIN", "column-type": "strColumn", "sources": {"blocks": [{"agree": "$.origin"}]}},
        {"name": "ORDER_ID", "column-type": "strColumn", "sources": {"blocks": [{"root": "$.data.currentImage.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_ORDER_HISTO", "column": "ORDER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.offerItemRef.offerRef.href"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "OFFER_STAKEHOLDER_ID", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.stakeholderPtrByOfferStakeholderId"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_STAKEHOLDER_HISTO", "column": "STAKEHOLDER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "SELLER_STAKEHOLDER_ID", "column-type": "strColumn", "sources": {"blocks": [{"agree": "$.sellerPtr"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_STAKEHOLDER_HISTO", "column": "STAKEHOLDER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_AGREEMENT", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"agree": "$.lifecycle.creation.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_AGREEMENT", "column-type": "intColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"agree": "$.lifecycle.dataVersion"}]},
          "meta": {"description": {"value": "Version of the Agreement", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"agree": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Agreement validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Agreement validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master" : {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION_AGREEMENT",
          "valid-from": "VALID_FROM",
          "valid-to": "VALID_TO",
          "is-last": "IS_LATEST_VERSION"
        }
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_AGREEMENT)"]
    }
  },
  {
    "name": "DIM_USER",
    "mapping": {
      "description": {"description": "Lists the users", "granularity": "1 user"},
      "merge": {
        "key-columns": ["USER_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"user": "$.data.currentImage.lifecycle.creation.user"}]},
        {"blocks": [{"user": "$.data.currentImage.lifecycle.lastUpdate.user"}]},
        {"blocks": [{"user": "$.data.currentImage.agreements[*].lifecycle.creation.user"}]},
        {"blocks": [{"user": "$.data.currentImage.agreements[*].lifecycle.lastUpdate.user"}]},
        {"blocks": [{"user": "$.data.currentImage.orderItems[*].services[*].lifecycle.creation.user"}]},
        {"blocks": [{"user": "$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user"}]},
        {"blocks": [{"user": "$.data.currentImage.payments[*].lifecycle.creation.user"}]},
        {"blocks": [{"user": "$.data.currentImage.payments[*].lifecycle.lastUpdate.user"}]}
      ],
      "columns": [
        {"name": "USER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"user": "$.organizationCode"}, {"user": "$.iataNumber"}]}, "expr": "hashS({0})"},
        {"name": "ORGANIZATION_CODE", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.organizationCode"}]}},
        {"name": "IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.iataNumber"}]}},
        {"name": "ORGANIZATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"user": "$.organizationCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_COMPANY", "column": "COMPANY_ID"}]}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_LOCATION",
    "mapping": {
      "description": {"description": "Lists the locations", "granularity": "1 location"},
      "merge": {
        "key-columns": ["LOCATION_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"loc": "$.data.currentImage.lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.agreements.lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.agreements.lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.orderItems[*].lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.payments[*].lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.payments[*].lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.stakeholders[*].pointOfBusiness.location"},{"country": "$.countries[*].code"}]},  // TO DO : check if this is present in the final feed
        {"blocks": [{"loc": "$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location"},{"country": "$.countries[*].code"}]}  // TO DO : check if this is present in the final feed
      ],
      "columns": [
        {"name": "LOCATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"loc": "$.iataCode"}]}, "expr": "hashS({0})"},
        {"name": "IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.iataCode"}]}},
        {"name": "ICAO_CODE", "column-type": "strColumn", "sources": {"literal": "UNKNOWN"}},
        {"name": "NAME", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.iataCode"}]}},
        {"name": "COUNTRY_ISO2_CODE", "column-type": "strColumn", "sources": {"blocks": [{"country": "$.value"}]}},
        {"name": "COUNTRY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"country": "$.value"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_COUNTRY", "column": "COUNTRY_ID"}]},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "LOCATION",
      "column-filler" : [
        {"dim-col" : "LOCATION_ID", "src-col" : "IATA_CODE"},
        {"dim-col" : "IATA_CODE", "src-col" : "IATA_CODE"},
        {"dim-col" : "ICAO_CODE", "src-col" : "ICAO_CODE"},
        {"dim-col" : "NAME", "src-col" : "NAME"},
        {"dim-col" : "COUNTRY_ISO2_CODE", "src-col" : "COUNTRY_ISO2_CODE"},
        {"dim-col" : "COUNTRY_ID", "src-col" : "COUNTRY_ISO2_CODE"}
      ]
    }]
  },
  {
    "name": "DIM_COUNTRY",
    "mapping": {
      "description": {"description": "Lists the countries", "granularity": "1 country"},
      "merge": {
        "key-columns": ["COUNTRY_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"loc": "$.data.currentImage.lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.agreements.lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.agreements.lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.orderItems[*].lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.payments[*].lifecycle.creation.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.payments[*].lifecycle.lastUpdate.location"}, {"country": "$.address.countryCode"}]},
        {"blocks": [{"loc": "$.data.currentImage.stakeholders.pointOfBusiness.location"},{"country": "$.countries[*].code"}]},  // TO DO : check if this is present in the final feed
        {"blocks": [{"loc": "$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location"},{"country": "$.countries[*].code"}]}  // TO DO : check if this is present in the final feed
      ],
      "columns": [
        {"name": "COUNTRY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"country": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "ISO2_CODE", "column-type": "strColumn", "sources": {"blocks": [{"country": "$.value"}]}},
        {"name": "ISO3_CODE", "column-type": "strColumn", "sources": {"literal" : "UNKNOWN"}},
        {"name": "NAME", "column-type": "strColumn", "sources": {"blocks": [{"country": "$.value"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "COUNTRY",
      "column-filler" : [
        {"dim-col" : "COUNTRY_ID", "src-col" : "ISO2_CODE"},
        {"dim-col" : "ISO2_CODE", "src-col" : "ISO2_CODE"},
        {"dim-col" : "ISO3_CODE", "src-col" : "ISO3_CODE"},
        {"dim-col" : "NAME", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_COMPANY",
    "mapping": {
      "description": {"description": "Lists the companies referenced in Order", "granularity": "1 company"},
      "merge": {
        "key-columns": ["COMPANY_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "org", "rs": {"blocks": [{"comp": "$.data.currentImage.lifecycle.creation.user"}]}},
        {"name": "org", "rs": {"blocks": [{"comp": "$.data.currentImage.lifecycle.lastUpdate.user"}]}},
        {"name": "com", "rs": {"blocks": [{"comp": "$.attachments[*].payload.offerData.offerSet.offerStakeholders[*]"}]}},
        {"name": "com", "rs": {"blocks": [{"comp": "$.attachments[*].payload.offerData.offerSet.offerStakeholders[*]"}]}},
        {"name": "com", "rs": {"blocks": [{"comp": "$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].airlineLevel"}]}},
        {"name": "com", "rs": {"blocks": [{"comp": "$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].allianceLevel"}]}}
      ],
      "columns": [
        {"name": "COMPANY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"comp": "$.company.name"}]}, "expr": "hashS({0})"},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.company.name"}]}},
        // for final mapping :
        //        {"name": "CODE", "column-type": "strColumn", "sources": {"root-specific": [
        //          {"rs-name": "org", "blocks": [{"comp": "$.organizationCode"}]},
        //          {"rs-name": "com", "blocks": [{"comp": "$.companyCode"}]}
        //        ]}},
        {"name": "NAME", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.company.name"}]}},
        {"name": "SUB_TYPE", "column-type": "strColumn", "sources": {"literal" : "UNKNOWN"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "COMPANY",
      "column-filler" : [
        {"dim-col" : "COMPANY_ID", "src-col" : "CODE"},
        {"dim-col" : "CODE", "src-col" : "CODE"},
        {"dim-col" : "NAME", "src-col" : "NAME"},
        {"dim-col" : "SUB_TYPE", "src-col" : "SUB_TYPE"}
      ]
    }]
  }
]