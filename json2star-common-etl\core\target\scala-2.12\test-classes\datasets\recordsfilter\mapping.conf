{"ruleset": {"mapping": {"record-filter": "$.mainResource.current.image.bagsGroup"}}, "tables": [{"name": "FACT_HISTO", "mapping": {"merge": {"key-columns": ["RESERVATION_ID", "ENVELOP_NB"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}], "columns": [{"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "ENVELOP_NB", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_ENVELOP", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "RESERVATION_ID", "pit-version": "ENVELOP_NB", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_ENVELOP"}}}}]}