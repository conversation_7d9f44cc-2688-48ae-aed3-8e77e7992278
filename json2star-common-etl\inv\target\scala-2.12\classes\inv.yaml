swagger: '2.0'
info:
  title: Dynamic Intelligence Hub API and Data Models - Flight Inventory
  description: >-
    The API to provide all information attached to a flight date on Inventory
    from Dynamic Intelligence Hub
  version: 1.1.3
  termsOfService: http://amadeus.com/todo/terms
  license:
    name: Proprietary
    url: http://amadeus.com/todo/licenses/LICENSE-1.0.html
paths:
  /inventory/processed-flights-feed:
    post:
      summary: Model for Flight Inventory Data push feed
      description: >-
        This is NOT a POST method, rather it is the definition of the online
        data push model
      tags:
        - Data push model (NOT a POST API)
      responses:
        default:
          description: The online data push schema
          schema:
            $ref: '#/definitions/FlightInventoryDataPush'
  /inventory/processed-flights-correlation-feed:
    post:
      summary: Model for Flight Inventory Correlation Data push feed
      description: >-
        This is NOT a POST method, rather it is the definition of the online
        correlation data push model
      tags:
        - Correlation data push model (NOT a POST API)
      responses:
        default:
          description: The online data push schema
          schema:
            $ref: '#/definitions/FlightInventoryCorrelationDataPush'
definitions:
  Included:
    type: object
    allOf:
      - $ref: '#/definitions/InventoryCorrelationsFeedData'
  FlightInventory:
    title: Flight Inventory
    type: object
    allOf:
      - $ref: '#/definitions/DatedFlight'
      - $ref: '#/definitions/CorrelationReferences'
  InventoryCorrelationsFeedData:
    type: object
    properties:
      correlationInventorySchedule:
        $ref: '#/definitions/CorrelationInventorySchedule'
      correlationInventoryPnr:
        $ref: '#/definitions/CorrelationInventoryPnr'
      correlationInventoryTicket:
        $ref: '#/definitions/CorrelationInventoryTicket'
      correlationInventoryDcsPassenger:
        $ref: '#/definitions/CorrelationInventoryDcsPassenger'
      dictionaries:
        $ref: '#/definitions/Dictionaries'
  Dictionaries:
    type: object
    properties:
      schedules:
        type: object
        description: 'Set of key/value pairs with scheduleId as key '
        additionalProperties:
          $ref: '#/definitions/Relationship'
      pnrs:
        type: object
        description: 'Set of key/value pairs with pnrId as key '
        additionalProperties:
          $ref: '#/definitions/Relationship'
      tickets:
        type: object
        description: Set of key/value pairs with ticketId as key
        additionalProperties:
          $ref: '#/definitions/Relationship'
      dcsPassengers:
        type: object
        description: Set of key/value pairs with dcsPassengerId as key
        additionalProperties:
          $ref: '#/definitions/Relationship'
      inventories:
        type: object
        description: Set of key/value pairs with inventoryId as key
        additionalProperties:
          $ref: '#/definitions/Relationship'
    example:
      schedules:
        6X-871-2018-12-17:
          type: dated-flight
          id: 6X-871-2018-12-17
          version: '1545129287'
          href: >-
            https://airlines.api.amadeus.com/v1/schedule/processed-flights/6X-835-2018-10-05
      pnrs:
        ORY4NY-2018-10-19:
          type: pnr
          id: ORY4NY-2018-10-19
          version: '1'
          href: >-
            https://airlines.api.amadeus.com/v1/reservation/processed-pnrs/ORY4NY-2018-10-19
        KBR849-2018-09-17:
          type: pnr
          id: KBR849-2018-09-17
          version: '1'
          href: >-
            https://airlines.api.amadeus.com/v1/reservation/processed-pnrs/KBR849-2018-09-17
      tickets:
        1728767935960-2018-08-21:
          type: air-travel-document
          id: 1728767935960-2018-08-21
          version: '1'
          href: >-
            https://airlines.api.amadeus.com/v1/ticketing/processed-tickets/1728767935960-2018-08-21
        1728767935961-2018-08-21:
          type: air-travel-document
          id: 1728767935961-2018-08-21
          version: '1'
          href: >-
            https://airlines.api.amadeus.com/v1/ticketing/processed-tickets/1728767935961-2018-08-21
      dcsPassengers:
        2501ADE0000001:
          type: dcs-passenger
          id: 2501ADE0000001
          version: '2019-10-05T10:00:00+00:00'
          href: >-
            http://airlines.api.amadeus.com/v1/departure-control/processed-dcs-passengers/2501ADE0000001
      inventories:
        6X-835-2018-09-20:
          type: dated-flight
          id: 6X-835-2018-09-20
          version: '1536835243'
          href: >-
            https://airlines.api.amadeus.com/v1/inventory/processed-flights/6X-835-2018-10-05
  Meta:
    type: object
    description: Technical information related to the feed
    properties:
      triggerEventLog:
        description: Information related to the initial event that trigger the process
        $ref: '#/definitions/EventLog'
      version:
        description: Version of the JSON feed produced
        type: string
        example: 1.1.2
    example:
      triggerEventLog:
        id: 46541dsfsSDRWFS54
        triggerEventName: DATA_INIT
      version: 1.1.2
  EventLog:
    type: object
    properties:
      id:
        description: Unique identifier of a JSON message
        type: string
        example: 46541dsfsSDRWFS54
      triggerEventName:
        description: >-
          Trigger of the message - set to 'DATA_INIT' for initialization
          messages
        type: string
        example: DATA_INIT
  DatedFlight:
    type: object
    properties:
      type:
        type: string
        description: Refers to Inventory flight
        example: dated-flight
      id:
        type: string
        description: id of the flight Inventory
        example: 6X-87-2018-09-20
      version:
        type: string
        description: Version to identify the inventory flight image.
        example: '1536835243'
      flightDesignator:
        description: >-
          Flight designator that conveys the carrier code, number and
          operational suffix of the flight
        $ref: '#/definitions/FlightDesignator'
      scheduledDepartureDate:
        type: string
        format: date
        description: >-
          The departure date of the flight date (in Local Time) in ISO format
          yyyy-mm-dd
        example: '2018-09-17'
      isCancelled:
        type: boolean
        description: Depicts if a flight is cancelled or active
        example: 'false'
      ongoingInventoryRebuild:
        type: boolean
        description: BRB-Flight is under inventory and seat rebuild
        example: 'false'
      systemFlags:
        type: array
        items:
          type: string
          description: DCS status of the flight.
          enum:
            - ACTIVE_FLIGHT_DCS
            - SENT_FLIGHT_TO_DCS
            - LINK_BROKEN
            - AUTOVMS_TRIGGERED
      characteristics:
        type: array
        items:
          type: string
          description: >-
            It indicates whether the flightdate is pseudo, hidden,
            international, domestic, ground handling.
          enum:
            - PSEUDO
            - HIDDEN
            - INTERNATIONAL
            - DOMESTIC
            - GROUND_HANDLING
          example: INTERNATIONAL
      inventoryFileSnapshotDateTime:
        type: string
        format: date-time
        description: >-
          This indicates the creation date and time stamp of the BIF feed file.
          in ISO format yyyy-mm-ddTHH:MM:SSZ
        example: '2017-12-06T11:00:00Z'
      route:
        type: string
        description: >-
          This indicates the complete route of the flight. If legs are
          A-B,B-C,C-D then routing order will be --> A-B-C-D
        example: BLR-CDG-JFK
      flixes:
        type: array
        items:
          type: object
          description: list of Flixes that were set on the Inventory flight
          properties:
            qualifier:
              type: string
              description: >-
                It depicts flight cancelled,Leg cancelled or flight disrupted
                using the flix qualifier codes.
              enum:
                - FX
                - LX
                - FD
              example: LX
            referenceLegId:
              type: string
              description: LegId on which the FLIX is imposed.
              example: HEL-CDG
      segments:
        description: >-
          It is an array that depicts inventory details of all the segments of
          the flight.
        type: array
        items:
          $ref: '#/definitions/Segment'
      legs:
        description: >-
          It is an array that depicts inventory details of all the legs of the
          flight.
        type: array
        items:
          $ref: '#/definitions/Leg'
  Segment:
    type: object
    properties:
      id:
        type: string
        description: id of the segment
        example: 2018-09-20-HEL-CDGM
      boardPointIataCode:
        type: string
        description: IATA code of the segment's departure airport
        example: GVA
      offPointIataCode:
        type: string
        description: IATA code of the segment's arrival airport
        example: YUL
      scheduledDepartureDateTime:
        type: string
        format: date-time
        description: >-
          scheduled departure date and time of the segment in local time, ISO
          format yyyy-mm-ddThh:mm:ss
        example: '2018-09-17T12:40:00'
      scheduledArrivalDateTime:
        type: string
        format: date-time
        description: >-
          scheduled arrival date and time of the segment in local time, ISO
          format yyyy-mm-ddThh:mm:ss
        example: '2018-09-17T14:45:00'
      segmentCounters:
        description: >-
          It provides the dcs counters, inventory controls and counters at each
          segment-cabin level.
        $ref: '#/definitions/SegmentCounters'
      bookingClassList:
        description: >-
          Information on all the booking classes of the segment-cabin, it also
          provides details at subclass level.
        type: array
        items:
          $ref: '#/definitions/BookingClass'
      partnerships:
        description: >-
          The codeshare details such as the operating or Makreting flights along
          with the codeshare agreement are conveyed in this section.
        type: object
        properties:
          operatingFlight:
            description: It is populated only when the main flight is a marketing flight
            type: object
            properties:
              id:
                type: string
                description: Operating flight id and .
                example: 7X-345-2018-05-11
              flightDesignator:
                $ref: '#/definitions/FlightDesignator'
          marketingFlights:
            description: >-
              it is populated only when the main flight is an operating flight.
              It is an array because there can be more than 1 carrier marketing
              the same operating flight.
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  description: Id of the Marketing flight
                  example: 7X-567-2019-12-11
                flightDesignator:
                  $ref: '#/definitions/FlightDesignator'
  Leg:
    type: object
    properties:
      id:
        type: string
        description: identifier of the leg
        example: HEL-CDG
      boardPointIataCode:
        type: string
        description: IATA code of the leg's departure airport
        example: GVA
      offPointIataCode:
        type: string
        description: IATA code of the leg's arrival airport
        example: YUL
      scheduledDepartureDateTime:
        type: string
        format: date-time
        description: >-
          scheduled departure date and time of the Leg in local time, ISO format
          yyyy-mm-ddThh:mm:ss
        example: '2018-09-17T12:40:00'
      scheduledArrivalDateTime:
        type: string
        format: date-time
        description: >-
          scheduled arrival date and time of the Leg in local time, ISO format
          yyyy-mm-ddThh:mm:ss.
        example: '2018-09-17T14:45:00'
      serviceType:
        type: string
        description: service type defined for this leg. Ex. J is for passenger service.
        enum:
          - UNKNOWN_SERVICE_TYPE
          - J
          - S
          - U
          - F
          - V
          - M
          - Q
          - G
          - B
          - A
          - R
          - C
          - O
          - H
          - L
          - P
          - T
          - K
          - D
          - E
          - W
          - X
          - I
          - 'N'
          - 'Y'
          - Z
        example: J
      aircraftEquipment:
        type: object
        description: information on the aircraft equipment
        properties:
          aircraftType:
            type: string
            description: Type of means of transport identification.
            example: '388'
      ssrQuotas:
        type: array
        description: Information on Special Service Requests (SSR).
        items:
          $ref: '#/definitions/SsrQuotas'
      legCounters:
        $ref: '#/definitions/LegCounters'
  FlightDesignator:
    type: object
    properties:
      carrierCode:
        type: string
        description: Two letter IATA standard carrier code
        example: 6X
      flightNumber:
        type: string
        description: 1-4 digit flight number
        example: '555'
      operationalSuffix:
        type: string
        description: operational suffix assigned to the flight
        example: A
      codeshareAgreement:
        type: string
        description: >-
          This indicates the type of codeshare agreement between marketing and
          operating flights. This field is only applicable under "partnerships".
        enum:
          - BLOCKSPACE
          - FREEFLOW
          - CAPPED_FREEFLOW
        example: BLOCKSPACE
  BookingClass:
    description: >-
      This model is called under bookingClassList which provides the information
      on all the booking classes of the segment-cabin.
    type: object
    properties:
      code:
        type: string
        description: booking class code
        example: M
      cabin:
        type: string
        description: cabin code (in inventory)
        example: 'Y'
      counters:
        description: >-
          Provides counters at booking class level such as dcs counters and
          partner dcs counters. Also provides counters at subclass level.
        $ref: '#/definitions/BookingClassCounters'
  SsrQuotas:
    type: object
    properties:
      counterCode:
        type: string
        description: >-
          Name of the quota counter which is SSR type. Ex: INFT - Infant BSCT -
          bassinet, VGML - veg meal etc.
        example: INFT
      quota:
        type: integer
        description: >-
          This indicates the SSR threshold i.e. maximum quantity that can be
          reached
        example: '5'
      availability:
        type: integer
        description: >-
          This indicates the number of SSR availability i.e.number of available
          SSRs
        example: '5'
      count:
        type: integer
        description: This indicates number of SSR bookings made.
        example: '0'
      cabinCode:
        type: string
        description: >-
          Reference of the leg cabin on which the quota applies. If no cabin,
          the quota applies on the whole Leg.
        example: J
      nestFamily:
        type: string
        description: >-
          Reference of the nest family if any. This means availability is shared
          with other quota of the same family. Ex. WCHC, WCHR, WCHS all these
          different wheel chair service belong to the same nest Family A.
        example: A
  Capacity:
    description: >-
      This will be present only for leg counters when referred from cabin
      counters.
    type: object
    properties:
      authorizationLevel:
        type: integer
        description: >-
          It denotes authorizationLevel (AU), the Authorisation Level represents
          the maximum number of bookings that can be made in the class or
          subclass for the flight leg.
        example: '25'
      effectiveCapacity:
        type: integer
        description: >-
          It denotes effectiveCapacity, this is the adjusted capacity due to
          cross cabin optimization.
        example: '21'
      operationalCapacity:
        type: integer
        description: >-
          It denotes operationalCapacity, this is the physical capacity of the
          plane
        example: '12'
      saleableCapacity:
        type: integer
        description: It dentoes the Saleable capacity assigned for the aircraft.
        example: '170'
  BookingClassCounters:
    description: >-
      It provides the information on all the booking classes of the
      segment-cabin
    type: object
    properties:
      subClassCounters:
        type: array
        items:
          $ref: '#/definitions/SubClassCounters'
      partnerDcsCounters:
        description: It provides the DCS counters of each marketing partner.
        type: array
        items:
          $ref: '#/definitions/PartnerDcsCounters'
      dcsCounters:
        $ref: '#/definitions/DcsCounters'
  SubClassCounters:
    description: >-
      It provides the counters and controls of each subcalss under a booking
      class.
    type: object
    properties:
      subClass:
        type: string
        description: Identification of the subclass with a letter+number/number code
        example: '0'
      inventoryCounters:
        $ref: '#/definitions/InventoryCounters'
      inventoryControls:
        $ref: '#/definitions/InventoryControls'
      availabilityCounters:
        $ref: '#/definitions/AvailabilityCounters'
  PartnerDcsCounters:
    description: It provides the DCS counters of marketing partner.
    type: object
    properties:
      dcsCounters:
        $ref: '#/definitions/DcsCounters'
      marketingFlight:
        type: object
        properties:
          id:
            type: string
            description: marketing flight id
            example: 7X-345-2018-05-11
          flightDesignator:
            $ref: '#/definitions/FlightDesignator'
  InventoryCounters:
    description: >-
      Inventory counters provides the count of various booking aspects such as
      bookings made, cancelled, waitlisted, group bookings etc.
    type: object
    properties:
      authorisation:
        type: integer
        description: >-
          AU- Authorization level. The maximum number of bookings that are
          authorised for sale in a class by a revenue management system. This is
          applicable to Subclass Counters.
        example: '12'
      assignedNamesInGroups:
        type: integer
        description: >-
          GAS- Assigned names in groups. This is applicable to Subclass
          Counters.
        example: '5'
      bookingCancellation:
        type: integer
        description: >-
          BCA- Booking Cancellations Counter. This is applicable to Subclass
          Counters.
        example: '8'
      bookings:
        type: integer
        description: >-
          Number of bookings made. Leg-Cabin Bookings Counter  0 if no bookings.
          This is applicable in Leg Cabin counters, Segment Cabin Counters and
          Subclass Counters.
        example: '35'
      groupBookings:
        type: integer
        description: >-
          Passengers who reserved as part of a group. This is applicable in Leg
          Cabin counters and also in Segment Subclass Counters.
        example: '10'
      pendingGroups:
        type: integer
        description: >-
          This figure counts committed group bookings with a pending Action
          Code, for a given segment-cabin-booking class. This is applicable to
          Subclass Counters.
        example: '1'
      waitlistGroups:
        type: integer
        description: >-
          A list of passengers as part of a group holding an unconfirmed
          booking. The status of a waitlisted passenger can be changed to
          confirmed during the waitlist clearance process, when space becomes
          available for example as a result of a cancellation. This is
          applicable to Subclass Counters.
        example: '5'
      groupCancellations:
        type: integer
        description: GBC- Group Cancellations. This is applicable to Subclass Counters.
        example: '2'
      expectedToBoard:
        type: number
        description: >-
          ETB- Expected To Board. A counter stored at leg-cabin level that
          represents how many customers are expected to show up at departure
          time. This is applicable in Leg Cabin counters, Segment Cabin Counters
          and Subclass Counters.This is an integer value. However, this value is
          parameter controlled depending on airline configurations and can be a
          decimal value.
        example: '32'
      regrade:
        type: integer
        description: >-
          Process of transferring a customer to a cabin that is different from
          the one in which the booking was made. This can take the form of an
          upgrade to a higher cabin or a downgrade to a lower cabin. This field
          is applicable only in Leg Cabin Counters.
        example: '4'
      staffStandby:
        type: integer
        description: >-
          A counter used to keep track of non-revenue passengers on standby.
          This figure counts committed bookings with a stand-by staff Action
          Code, including blockspace bookings, for a given segment-cabin-booking
          class. This is applicable to Segment Cabin Counters.
        example: '4'
      waitlist:
        type: integer
        description: >-
          A list of passengers holding an unconfirmed booking. This is
          applicable in Leg Cabin counters, Segment Cabin Counters and Subclass
          Counters.
        example: '3'
      ticketedBooking:
        type: integer
        description: TKP- Ticketed Booking. This is applicable to Subclass Counters.
        example: '3'
      totalNumberOfNegoBookings:
        type: integer
        description: >-
          NEG- Total number of nego bookings. This figure counts committed
          negotiated space bookings in a negotiated space block associated to
          'Nego Block' identifier. This is applicable to Subclass Counters.
        example: '2'
  AvailabilityCounters:
    description: >-
      It provides the availablity details at various projections such as net,
      gross etc. This will be present only for leg counters when referred from
      cabin counters.
    type: object
    properties:
      gross:
        type: number
        description: >-
          Represents the number of seats that would be left empty if the
          passengers currently booked and those remaining to book turn up as
          predicted by the overbooking factors on each class. This field is
          applicable only in Leg Cabin Counters. This is an integer value.
          However, this value is parameter controlled depending on airline
          configurations and can be a decimal value.
        example: '65'
      net:
        type: number
        description: >-
          Represents the number of seats that would be left empty on a flight if
          all the passengers currently booked and those forecasted to book (ie.
          protected by UPRs), turned up at the airport. This does not include
          expected cancellations. This field is applicable only in Leg Cabin
          Counters and Segment Subclass Counters. This is an integer value.
          However, this value is parameter controlled depending on airline
          configurations and can be a decimal value.
        example: '55'
      normal:
        type: number
        description: >-
          This indicates the normalAvailability(segment availability counter).
          This field is applicable only in Segment Subclass Counters. This is an
          integer value. However, this value is parameter controlled depending
          on airline configurations and can be a decimal value.
        example: '5'
  InventoryControls:
    description: >-
      It conveys the limit or restriction on various aspects such as maximum
      waitlist passengers allowed, number of unsold protection implied etc.
    type: object
    properties:
      min:
        type: integer
        description: >-
          Represents the minimum number of seats to be protected in this Booking
          class. It must be lower (not strictly) than the Protection applied on
          the same Booking class. The total of all MINs cannot exceed the
          capacity of a cabin. The MIN cannot be zero. This is applicable to
          Subclass Counters.
        example: '10'
      unsoldProtection:
        type: number
        description: >-
          A class UPR represents the number of seats that have been protected
          through a MIN and that have not yet been booked. A UPR is compared to
          the remaining capacity, increased by overbooking. Its relation with
          the MIN is as follows --> UPR = MIN – bookings already made in this
          class.This is applicable in Leg Cabin counters, Segment Cabin Counters
          and Subclass Counters. This is an integer value. However, this value
          is parameter controlled depending on airline configurations and can be
          a decimal value.
        example: '10.11'
      maximumWaitlistPercentage:
        type: integer
        description: >-
          MWP denotes Maximum Waitlist Percentage. This is applicable to
          Subclass Counters.
        example: '4'
      parentBookingClass:
        type: string
        description: Identification of the subclass with a letter+number code
        example: M
      inhibitWaitlist:
        type: integer
        description: IWL- Inhibit Waitlist. This is applicable to Subclass Counters.
        example: '2'
      inhibitWaitlistClearance:
        type: integer
        description: >-
          IWC- Inhibit Waitlist Clearance. Prevents from using Waitlist for this
          class. This is applicable to Subclass Counters.
        example: '1'
      segmentLimit:
        type: integer
        description: >-
          SLM denotes Segment limit, the maximum number of seats that may be
          made available in a given booking class. This is applicable to
          Subclass Counters.
        example: '2'
      staffStandbyMax:
        type: integer
        description: >-
          Maximum number of  non-revenue passengers on standby. This is
          applicable to Segment Cabin Counters.
        example: '2'
      noshowPercentage:
        type: integer
        description: >-
          The risk-adjusted percentage of noshows, that is, the percentage of
          bookings that haven't been cancelled but fail to appear at check-in.
          This is applicable to Subclass Counters.
        example: '6'
      overBookingPercentage:
        type: integer
        description: >-
          The overbooking percentage of a given leg cabin is the percentage of
          seats over the cabin's physical capacity that the airline is ready to
          sell. This percentage is applied so as to mitigate the loss of revenue
          due to potential no-shows and cancellations. This is applicable to
          Subclass Counters.
        example: '3'
      nettedProtection:
        type: integer
        description: >-
          PRO denotes Netted Protection. Subclass Netted Protection. This is
          applicable to Subclass Counters.
        example: '2'
      unbalanceAdjustment:
        type: integer
        description: >-
          This adjustment enables the user to give additional space to a cabin.
          When it is expected that some seats of a MIN block will not be sold,
          some space can be given back to the cabin due to this adjustment. This
          value is not balanced (only concerns one cabin) and is greater than 0.
          This is applicable to Leg Cabin Counters.
        example: '6'
      maxiRegradeAdjustment:
        type: integer
        description: >-
          The maximum number of seats which can be adjusted out of a cabin
          through a regrade adjustment. This is applicable to Leg Cabin
          Counters.
        example: '4'
      regradeAdjustment:
        type: integer
        description: >-
          Transfer of space from a higher cabin to a lower cabin. This
          adjustment is balanced, meaning that the sum over all the cabins of
          the regrade adjustments equals 0. This is applicable to Leg Cabin
          Counters.
        example: '4'
      waitListMax:
        type: integer
        description: >-
          A list of passengers holding an unconfirmed booking. This is
          applicable to both Segment cabin Counters and Subclass Counters
        example: '3'
      waitlistMaxPercentageOfAU:
        type: integer
        description: Defines the Waitlist via a percentage of the AU of the class.
        example: '4'
  DcsCounters:
    description: >-
      Boarded figures sent by the Departure Control System (DCS), after the
      leg/segment departure.
    type: object
    properties:
      dcsAdjustments:
        type: integer
        description: >-
          DAJ denotes DCS adjustements. DCS adjustments are composed of DCS
          regrade and Accepted stand-by counts. DCS adjustments = DCS regrade -
          Accepted stand-by. This field is applicable only in LegCounters.
        example: '1'
      acceptedStandBy:
        type: integer
        description: >-
          CSY denotes Accepted stand-by. The real number of stand-by customers
          accepted onto the flight during check in. This field is applicable
          only in LegCounters.
        example: '2'
      boardedPassengers:
        type: integer
        description: >-
          BOA denotes Boarded passengers. This is the total Passengers Boarded
          and includes Go Shows, Group Passengers Boarded, Commercial Standby
          Boarded, Staff     Standby Boarded and No Record Passengers who
          boarded.
        example: '28'
      commercialStandbyPassengersBoarded:
        type: integer
        description: >-
          CBO denotes Commercial stand-by boarded. A counter used to keep track
          of revenue passengers on standby who boarded.
        example: '10'
      commercialStandbyPassengersLeftAtGate:
        type: integer
        description: >-
          CNB denotes Commercial Standby passengers Left at gate. A counter used
          to keep track of revenue passengers on standby who did not board.
        example: '1'
      downgradesInto:
        type: integer
        description: >-
          DGI denotes Downgrades into. The passengers who were downgraded from
          higher classes into this class.
        example: '2'
      downgradesOutof:
        type: integer
        description: >-
          DGO denotes Downgrades out of. The passengers who were downgraded from
          this class into a lower class.
        example: '2'
      deniedBoardingPassengers:
        type: integer
        description: >-
          DNB denotes Denied Boarding passengers. The number of passengers who
          were denied boarding.
        example: '1'
      groupPassengersBoarded:
        type: integer
        description: >-
          GBO denotes Group passengers Boarded. Passengers who reserved as part
          of a group and who have boarded.
        example: '10'
      groupNoShows:
        type: integer
        description: >-
          GNS denotes Group No-shows. Group of passengers who had a confirmed
          booking but did not appear at check-in.
        example: '0'
      goShowPassengers:
        type: integer
        description: >-
          GOS denotes Go-show passengers. A passenger who arrived at the airport
          without a confirmed reservation and purchased a ticket just before
          boarding the aircraft.
        example: '4'
      noRec:
        type: integer
        description: >-
          NOR denotes No-rec. A passenger present at check-in with an apparently
          valid paper ticket or e-ticket that is not traceable in the computer
          system(s).
        example: '3'
      noShowPassengers:
        type: integer
        description: >-
          NOS denotes No-show passengers. A passenger who had a confirmed
          booking but did not appear at check-in.
        example: '4'
      offloadedPassengers:
        type: integer
        description: >-
          OFF denotes Offloaded Passengers. Total number of passengers
          offloaded.
        example: '2'
      upgradesInto:
        type: integer
        description: >-
          UGI denotes Upgrades into. The number of passengers that have been
          upgraded from lower classes into this class.
        example: '2'
      upgradesOutof:
        type: integer
        description: >-
          UGO denotes Upgrades out. The number of passengers that have been
          upgraded from this class into a higher class.
        example: '4'
      volunteersForOffloadPassengers:
        type: integer
        description: >-
          VOL denotes Volunteers for Offload passengers. Passengers who, even
          though they had a valid ticket, did not board the aircraft as there
          was not enough space. The number includes volunteer off-loads.
        example: '3'
      staffCounters:
        $ref: '#/definitions/StaffCounters'
  LegCounters:
    type: object
    properties:
      cabinCounters:
        type: array
        description: Counters per cabin
        items:
          $ref: '#/definitions/CabinCounters'
  SegmentCounters:
    type: object
    properties:
      cabinCounters:
        type: array
        description: Counters per cabin
        items:
          $ref: '#/definitions/CabinCounters'
  CabinCounters:
    type: object
    description: Information on all the cabins of the leg/Segment.
    properties:
      cabinCode:
        type: string
        description: denotes the cabin class code designator and not an RBD.
        example: 'Y'
      counters:
        $ref: '#/definitions/DcsCounters'
      inventoryCounters:
        $ref: '#/definitions/InventoryCounters'
      availabilityCounters:
        $ref: '#/definitions/AvailabilityCounters'
      inventoryControls:
        $ref: '#/definitions/InventoryControls'
      blockSpaces:
        description: >-
          This will be present only for leg counters when referred from cabin
          counters.
        type: array
        items:
          $ref: '#/definitions/BlockSpace'
      capacity:
        $ref: '#/definitions/Capacity'
  BlockSpace:
    description: Details for each leg-cabin blockspace.
    type: object
    properties:
      codeShare:
        type: integer
        description: S- Codeshare blockspace
        example: '1'
      marketingFlight:
        type: object
        properties:
          id:
            type: string
            description: >-
              Marketing flight id and it populated only when the main flight is
              an operating flight.
            example: 7X-567-2019-12-11
          flightDesignator:
            $ref: '#/definitions/FlightDesignator'
      protectedSeats:
        type: integer
        description: P- Protected seats (non commercial bookings/ZZ PNRs)
        example: '1'
      seatsBlocked:
        type: integer
        description: 'Z- Seats blocked for other reasons (broken seats, deadload etc.)'
        example: '3'
      allotment:
        type: integer
        description: A- Allotment (CTP/Customer Transfer Process is on-going)
        example: '0'
  StaffCounters:
    type: object
    description: Details for each kind of BlockSpace
    properties:
      staffStandbyPassengersBoarded:
        type: integer
        description: >-
          SBO denotes Staff standby passengers Boarded. A counter used to keep
          track of non-revenue passengers on standby who boarded.
        example: '2'
      acceptedStaffStandbyDowngradeInto:
        type: integer
        description: >-
          SDI denotes Accepted Staff Standby Downgrade Into. Staff Passengers on
          standby who were downgraded from higher classes into this class.
        example: '1'
      acceptedStaffStandbyDowngradeOutof:
        type: integer
        description: >-
          SDO denotes Accepted Staff Standby Downgrade Out Of. Staff Passengers
          on standby who were downgraded from this class into a lower class.
        example: '1'
      staffStandbyPassengersLeftAtGate:
        type: integer
        description: >-
          SNB denotes Staff standby passengers Left at gate. Staff Passengers on
          standby who did not board.
        example: '1'
      acceptedStaffStanbyUpgradeInto:
        type: integer
        description: >-
          SUI denotes Accepted Staff Standby Upgrade Into. Staff passengers who
          have been upgraded from lower classes into this class.
        example: '0'
      acceptedStaffStanbyUpgradeOutof:
        type: integer
        description: >-
          SUO denotes Accepted Staff Standby Upgrade Out Of. Staff passengers
          who have been upgraded from this class into a higher class.
        example: '0'
  CorrelationReferences:
    type: object
    properties:
      correlations:
        type: object
        description: >-
          Correlation structures are defined in the included section. This
          section provides links for all the correlation structures pertaining
          to the current datedFlightInventory record.
        properties:
          inventoryPnr:
            $ref: '#/definitions/Relationship'
          inventoryTicket:
            $ref: '#/definitions/Relationship'
          inventoryDcsPassenger:
            $ref: '#/definitions/Relationship'
          inventorySchedule:
            $ref: '#/definitions/Relationship'
        example:
          inventoryPnr:
            ref: included/correlationInventoryPnr/6X-834-2018-09-17
          inventoryTicket:
            ref: included/correlationInventoryTicket/6X-834-2018-09-17
          inventoryDcsPassenger:
            ref: included/correlationInventoryDcsPassenger/6X-834-2018-09-17
          inventorySchedule:
            ref: included/correlationInventorySchedule/6X-834-2018-09-17
  issueSource:
    description: an object containing references to the source of the error
    properties:
      pointer:
        description: >-
          a JSON Pointer [RFC6901] to the associated entity in the request
          document
        type: string
      parameters:
        description: a string indicating which URI query parameter caused the issue
        type: string
  CorrelationInventorySchedule:
    type: object
    description: Structure of correlation between an Inventory and a Schedule record
    properties:
      inventoryFlightId:
        type: string
        description: Id of the flight Inventory record which is correlating with Schedules.
      scheduleIds:
        type: array
        description: >-
          Id of the dateflight records correlated with the current flight
          Inventory.
        items:
          type: string
      correlatedData:
        type: object
        description: The sub-entity details which used for correalting the main records
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataInventorySchedule'
    example:
      inventoryFlightId: 6X-871-2018-09-17
      scheduleIds:
        - 6X-871-2018-12-17
      correlatedData:
        - 6X-871-2018-12-17:
            - inventorySegmentId: 2018-09-17-LHR-FCO
              scheduleSegmentId: 2018-12-17-LHR-FCO
              correlatedLegs:
                - inventoryLegId: GVA-YUL
                  scheduleLegId: GVA-YUL
  CorrelationInventoryPnr:
    type: object
    description: Structure of correlation between an Inventory and a Passenger Name Record.
    properties:
      inventoryFlightId:
        type: string
        description: Id of the flight Inventory record which is correalting with PNRs.
      pnrIds:
        type: array
        description: >-
          Identifying the PNR(s) correlated with the current flight Inventory -
          item format is record locator + creation date.
        example: '[QFBCSR-2018-05-12,KNGBAD-2018-06-16]'
        items:
          type: string
      correlatedData:
        type: object
        description: The sub-entity details which used for correalting the main records
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataInventoryPnr'
    example:
      inventoryFlightId: 6X-871-2018-09-20
      pnrIds:
        - QFBCSR-2018-05-12
        - KNGBAD-2018-06-16
      correlatedData:
        - QFBCSR-2018-05-12:
            - pnrAirSegmentId: QFBCSR-2018-05-12-ST-1
              inventorySegmentId: 2018-09-20-GVA-YUL
              inventoryPartnershipId: 7X-743-2018-05-12
        - KNGBAD-2018-06-16:
            - pnrAirSegmentId: KNGBAD-2018-06-16-ST-3
              inventorySegmentId: 2018-09-20-GVA-YUL
              inventoryPartnershipId: 7X-743-2018-05-12
  CorrelationInventoryTicket:
    type: object
    description: Structure of correlation between an Inventory and a ticket Record.
    properties:
      inventoryFlightId:
        type: string
        description: Id of the flight Inventory record which is correalting with Tickets.
      ticketIds:
        type: array
        description: >-
          Ids of the tkts correlated with the current flight Inventory, where
          tickt Id is ticket number + issuance date.
        items:
          type: string
      correlatedData:
        type: object
        description: The sub-entity details which used for correalting the main records
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataInventoryTicket'
    example:
      inventoryFlightId: 6X-871-2018-09-20
      ticketIds:
        - 1723214567845-2018-05-12
        - 1722541219542-2018-06-16
      correlatedData:
        - 1723214567845-2018-05-12:
            - ticketCouponId: '1'
              inventorySegmentId: 2018-09-20-GVA-YUL
              inventoryPartnershipId: 7X-743-2018-05-12
        - 1722541219542-2018-06-16:
            - ticketCouponId: '2'
              inventorySegmentId: 2018-09-20-GVA-YUL
              inventoryPartnershipId: 7X-743-2018-05-12
  CorrelationInventoryDcsPassenger:
    type: object
    description: Structure of correlation between an Inventory and a CM record
    properties:
      inventoryFlightId:
        type: string
        description: >-
          Id of the flight Inventory record which is correalting with
          DcsPassenger.
      dcsPassengerIds:
        type: array
        description: >-
          Ids the DcsPassenger records correlated with the current flight
          Inventory.
        items:
          type: string
      correlatedData:
        type: object
        description: The sub-entity details which used for correalting the main records
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataInventoryDcsPassenger'
    example:
      inventoryFlightId: 6X-871-2018-09-20
      dcsPassengerIds:
        - 2501ADE0000001
      correlatedData:
        - 2501ADE0000001:
            - inventorySegmentId: 2018-09-20-GVA-YUL
              dcsPassengerSegmentDeliveryId: 2401CA5500003OID
              inventoryPartnershipId: 7X-743-2018-09-20
              correlatedLegs:
                - inventoryLegId: GVA-YUL
                  dcsPassengerLegDeliveryId: 2401DA55000037F3-GVA
  CorrelatedDataInventorySchedule:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between a flight
      Inventoryand a datedFlight record
    properties:
      inventorySegmentId:
        type: string
        description: >-
          The Id of the flight Inventory segment that is invloved in
          correlation.
        example: 2018-12-11-HEL-CDG
      scheduleSegmentId:
        type: string
        description: The Id of the flight segment ID that is involved in correlation.
        example: 2018-12-11-HEL-CDG
      inventoryPartnershipIds:
        type: array
        items:
          type: string
          description: >-
            If the carrier is a codeshare then the operating/marketing
            partnership flight ID will be populated here. Since there can be
            more than one marketing flight for an operating flight, the field
            has been defined as array for schedule and inventory correlation.
          example:
            - 7X-9623-2018-09-17
            - 8X-765-2018-09-17
      correlatedLegs:
        type: array
        items:
          $ref: '#/definitions/CorrelatedInventoryScheduleLegData'
  CorrelatedDataInventoryPnr:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between a flight
      Inventory and a PNR
    properties:
      pnrAirSegmentId:
        type: string
        description: The Id of the PNR air segment that is involved in correlation.
        example: QFBCSR-2018-05-12-ST-1
      inventorySegmentId:
        type: string
        description: The Id of the flightInventory segment that is invloved in correlation.
        example: 2018-09-20-HEL-CDG
      inventoryPartnershipId:
        type: string
        description: >-
          If the carrier is a codeshare and the booking is linked to one of the
          codeshare parther, it is Id of that partner flight.
        example: 7X-9623-2018-09-17
  CorrelatedDataInventoryTicket:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between the
      Inventory and tkt
    properties:
      ticketCouponId:
        type: string
        description: The Id of the coupon that is involved in correlation.
        example: '1'
      inventorySegmentId:
        type: string
        description: >-
          The Id of the flight Inventory segment that is invloved in
          correlation.
        example: 2018-09-20-HEL-CDG
      inventoryPartnershipId:
        type: string
        description: >-
          If the carrier is a codeshare and the booking is linked to one of the
          codeshare parther, it is Id of that partner flight.
        example: 7X-9623-2018-09-17
  CorrelatedDataInventoryDcsPassenger:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between a dated
      flight and a CM Pax record
    properties:
      inventorySegmentId:
        type: string
        description: >-
          The Id of the flight Inventory segment that is invloved in         
          correlation.
        example: 2018-09-20-HEL-CDG
      dcsPassengerSegmentDeliveryId:
        type: string
        description: >-
          The Id of the segment delivery of Dcs Passenger that is involved in
          correlation.
        example: 2401CA5500003OID
      inventoryPartnershipId:
        type: string
        description: >-
          If the carrier is a codeshare and the booking is linked to one of the
          codeshare parther, it is Id of that partner flight.
        example: 7X-9623-2018-09-17
      correlatedLegs:
        type: array
        items:
          $ref: '#/definitions/CorrelatedInventoryDcsPassengerLegData'
  Relationship:
    type: object
    description: Details of a relationship
    properties:
      id:
        type: string
        description: Identifier of the related resource
        example: 2002CAE00036002D
      type:
        type: string
        description: The type of the related resource
        example: dcs-passenger
      ref:
        type: string
        description: >-
          The reference to the related resource if this latter exists in the
          same document
        example: included/correlationInventoryTicket/6X-9623-2018-09-17
      version:
        type: string
        description: Only populated for correlated resources defined in Dictionaries
        example: '1'
      href:
        description: The URI of the related resource
        type: string
        example: >-
          https://airlines.api.amadeus.com/v1/inventory/processed-flights/6X-9623-2018-09-17
      methods:
        $ref: '#/definitions/Methods'
  Methods:
    type: array
    description: Accepted Methods
    items:
      type: string
      enum:
        - GET
  CorrelatedInventoryScheduleLegData:
    type: object
    properties:
      inventoryLegId:
        type: string
        description: Id of the flight Inventory leg which is invloved in correlation.
        example: HEL-CDG
      scheduleLegId:
        type: string
        description: Id of the flight leg  involved in correlation.
        example: HEL-CDG
  CorrelatedInventoryDcsPassengerLegData:
    type: object
    properties:
      inventoryLegId:
        type: string
        description: Id of the flight Inventory leg which is invloved in correlation.
        example: HEL-CDG
      dcsPassengerLegDeliveryId:
        type: string
        description: Id of the leg delivery of Dcs Passenger involved in correlation.
        example: 2401DA55000037F3-LHR
  Events:
    type: object
    description: Structure of Dynamic Intelligence Hub functional events
    properties:
      recordDomain:
        type: string
        description: Functional domain of the record
        example: INVENTORY_SCHEDULE
      recordId:
        type: string
        description: Record identifier e.g. flightInventoryId
        example: 6X-871-2018-09-20
      originFeedTimeStamp:
        type: string
        description: Incoming PSS feed time stamp
        format: date-time
      events:
        type: array
        description: >-
          List of events that have been detected on the datedFlightInventory
          document
        items:
          type: object
          properties:
            origin:
              type: string
              description: Type of event e.g. 'COMPARISON' / 'TIME_INITIATED_EVENT'
              example: COMPARISON
            eventType:
              type: string
              description: >-
                In case of comparison events, type of operation notified by the
                event e.g. 'CREATION' / 'UPDATE' / 'DELETION'
              example: CREATED
              enum:
                - CREATED
                - UPDATED
                - DELETED
            currentPath:
              type: string
              description: >-
                String containing the JSON Pointer (see IETF RFC6901) that
                points to the data referenced by the Event in the latest version
                of the entity. It is only applicable for CREATED and UPDATED
                events.
              example: /correlationInventorySchedule/correlatedData/6X-12-2018-09-20
            previousPath:
              type: string
              description: >-
                String containing the JSON Pointer (see IETF RFC6901) that
                points to the data referenced by the Event in the previous
                version of the entity. It is only applicable for DELETED and
                UPDATED events.
              example: /correlationInventorySchedule/correlatedData/6X-12-2018-09-20
    example:
      recordDomain: INVENTORY_SCHEDULE
      recordId: 6X-12-2018-09-20
      events:
        - origin: COMPARISON
          eventType: UPDATED
          currentPath: /correlationInventorySchedule/correlatedData/6X-12-2018-09-20
          previousPath: /correlationInventorySchedule/correlatedData/6X-12-2018-09-20
        - origin: COMPARISON
          eventType: DELETED
          previousPath: /correlationInventorySchedule/correlatedData/6X-12-2018-09-20
        - origin: COMPARISON
          eventType: CREATED
          currentPath: /correlationInventorySchedule/correlatedData/6X-12-2018-09-20
  FlightInventoryDataPush:
    type: object
    description: The Online data push schema to be referenced in the main Swagger spec
    properties:
      meta:
        $ref: '#/definitions/Meta'
      inventoryProcessedFlight:
        $ref: '#/definitions/DatedFlight'
      events:
        $ref: '#/definitions/Events'
    example:
      meta:
        triggerEventLog:
          id: 46541dsfsSDRWFS54
          triggerEventName: DATA_INIT
        version: 1.1.2
      inventoryProcessedFlight:
        type: dated-flight
        id: 6X-871-A-2018-12-17
        version: '1549517963'
        flightDesignator:
          carrierCode: 6X
          flightNumber: '871'
          operationalSuffix: A
        scheduledDepartureDate: '2018-12-17'
        isCancelled: 'false'
        ongoingInventoryRebuild: 'false'
        systemFlags:
          - ACTIVE_FLIGHT_DCS
        characteristics:
          - INTERNATIONAL
        inventoryFileSnapshotDateTime: '2018-12-06T11:00:00'
        route: GVA-YUL
        flixes:
          - qualifier: LX
            referenceLegId: GVA-YUL
        segments:
          - id: 2018-12-17-GVA-YUL
            boardPointIataCode: GVA
            offPointIataCode: YUL
            scheduledDepartureDateTime: '2018-12-17T12:40:00'
            scheduledArrivalDateTime: '2018-12-17T17:45:00'
            segmentCounters:
              cabinCounters:
                - cabinCode: 'Y'
                  counters:
                    boardedPassengers: '40'
                    commercialStandbyPassengersBoarded: '20'
                    commercialStandbyPassengersLeftAtGate: '2'
                    deniedBoardingPassengers: '3'
                    downgradesInto: '2'
                    downgradesOutof: '2'
                    groupPassengersBoarded: '10'
                    groupNoShows: '2'
                    goShowPassengers: '1'
                    noRec: '2'
                    noShowPassengers: '2'
                    offloadedPassengers: '45'
                    upgradesInto: '3'
                    upgradesOutof: '5'
                    volunteersForOffloadPassengers: '3'
                    staffCounters:
                      staffStandbyPassengersBoarded: '1'
                      acceptedStaffStandbyDowngradeInto: '1'
                      acceptedStaffStandbyDowngradeOutof: '2'
                      staffStandbyPassengersLeftAtGate: '1'
                      acceptedStaffStanbyUpgradeInto: '1'
                      acceptedStaffStanbyUpgradeOutof: '0'
                  inventoryCounters:
                    bookings: '24'
                    expectedToBoard: '20'
                    staffStandby: '2'
                    waitlist: '12'
                  inventoryControls:
                    unsoldProtection: '10.11'
                    staffStandbyMax: '3'
                    waitListMax: '2'
                - cabinCode: J
                  counters:
                    boardedPassengers: '10'
                    commercialStandbyPassengersBoarded: '2'
                    commercialStandbyPassengersLeftAtGate: '2'
                    deniedBoardingPassengers: '3'
                    downgradesInto: '2'
                    downgradesOutof: '2'
                    groupPassengersBoarded: '9'
                    groupNoShows: '2'
                    goShowPassengers: '1'
                    noRec: '2'
                    noShowPassengers: '2'
                    offloadedPassengers: '25'
                    upgradesInto: '3'
                    upgradesOutof: '5'
                    volunteersForOffloadPassengers: '3'
                    staffCounters:
                      staffStandbyPassengersBoarded: '1'
                      acceptedStaffStandbyDowngradeInto: '1'
                      acceptedStaffStandbyDowngradeOutof: '2'
                      staffStandbyPassengersLeftAtGate: '1'
                      acceptedStaffStanbyUpgradeInto: '1'
                      acceptedStaffStanbyUpgradeOutof: '0'
                  inventoryCounters:
                    bookings: '24'
                    expectedToBoard: '4.5'
                    staffStandby: '2'
                    waitlist: '12'
                  inventoryControls:
                    unsoldProtection: '1'
                    staffStandbyMax: '3'
                    waitListMax: '2'
            bookingClassList:
              - code: M
                cabin: 'Y'
                counters:
                  subClassCounters:
                    - subClass: '0'
                      inventoryCounters:
                        bookings: '35'
                        expectedToBoard: '32'
                        waitlist: '3'
                        authorisation: '12'
                        assignedNamesInGroups: '5'
                        bookingCancellation: '8'
                        groupBookings: '10'
                        pendingGroups: '1'
                        waitlistGroups: '5'
                        groupCancellations: '2'
                        ticketedBooking: '3'
                        totalNumberofNegoBookings: '2'
                      availabilityCounters:
                        net: '55'
                        normal: '5'
                      inventoryControls:
                        unsoldProtection: '1'
                        waitListMax: '3'
                        min: '10'
                        maximumWaitlistPercentage: '4'
                        inhibitWaitlist: '2'
                        inhibitWaitlistClearance: '1'
                        segmentLimit: '2'
                        noshowPercentage: '6'
                        overBookingPercentage: '3'
                        nettedProtection: '2'
                        waitlistMaxPercentageOfAU: '1'
                  partnerDcsCounters:
                    - dcsCounters:
                        boardedPassengers: '28'
                        commercialStandbyPassengersBoarded: '10'
                        commercialStandbyPassengersLeftatGate: '1'
                        downgradesInto: '2'
                        downgradesOutof: '2'
                        deniedBoardingPassengers: '1'
                        groupPassengersBoarded: '10'
                        groupNoShows: '0'
                        goShowPassengers: '4'
                        noRec: '3'
                        noShowPassengers: '4'
                        offloadedPassengers: '2'
                        upgradesInto: '2'
                        upgradesOutof: '4'
                        volunteersforOffloadPassengers: '3'
                        staffCounters:
                          staffStandbyPassengersBoarded: '2'
                          acceptedStaffStandbyDowngradeInto: '1'
                          acceptedStaffStandbyDowngradeOutof: '1'
                          staffStandbyPassengersLeftatGate: '1'
                          acceptedStaffStanbyUpgradeInto: '1'
                          acceptedStaffStanbyUpgradeOutof: '0'
                      marketingFlight:
                        id: 7X-764-A-2018-12-17
                        flightDesignator:
                          carrierCode: 7X
                          flightNumber: '764'
                          operationalSuffix: A
                    - dcsCounters:
                        boardedPassengers: '28'
                        commercialStandbyPassengersBoarded: '10'
                        commercialStandbyPassengersLeftatGate: '1'
                        downgradesInto: '2'
                        downgradesOutof: '2'
                        deniedBoardingPassengers: '1'
                        groupPassengersBoarded: '10'
                        groupNoShows: '0'
                        goShowPassengers: '4'
                        noRec: '3'
                        noShowPassengers: '4'
                        offloadedPassengers: '2'
                        upgradesInto: '2'
                        upgradesOutof: '4'
                        volunteersforOffloadPassengers: '3'
                        staffCounters:
                          staffStandbyPassengersBoarded: '2'
                          acceptedStaffStandbyDowngradeInto: '1'
                          acceptedStaffStandbyDowngradeOutof: '1'
                          staffStandbyPassengersLeftatGate: '1'
                          acceptedStaffStanbyUpgradeInto: '1'
                          acceptedStaffStanbyUpgradeOutof: '0'
                      marketingFlight:
                        id: 8X-434-2018-12-17
                        flightDesignator:
                          carrierCode: 8X
                          flightNumber: '434'
                  dcsCounters:
                    boardedPassengers: '28'
                    commercialStandbyPassengersBoarded: '10'
                    commercialStandbyPassengersLeftatGate: '1'
                    downgradesInto: '2'
                    downgradesOutof: '2'
                    deniedBoardingPassengers: '1'
                    groupPassengersBoarded: '10'
                    groupNoShows: '0'
                    goShowPassengers: '4'
                    noRec: '3'
                    noShowPassengers: '4'
                    offloadedPassengers: '2'
                    upgradesInto: '2'
                    upgradesOutof: '4'
                    volunteersforOffloadPassengers: '3'
                    staffCounters:
                      staffStandbyPassengersBoarded: '2'
                      acceptedStaffStandbyDowngradeInto: '1'
                      acceptedStaffStandbyDowngradeOutof: '1'
                      staffStandbyPassengersLeftatGate: '1'
                      acceptedStaffStanbyUpgradeInto: '1'
                      acceptedStaffStanbyUpgradeOutof: '0'
              - code: K
                cabin: 'Y'
                counters:
                  subClassCounters:
                    - subClass: '0'
                      inventoryCounters:
                        bookings: '35'
                        expectedToBoard: '32'
                        waitlist: '3'
                        authorisation: '12'
                        assignedNamesInGroups: '5'
                        bookingCancellation: '8'
                        groupBookings: '10'
                        pendingGroups: '1'
                        waitlistGroups: '5'
                        groupCancellations: '2'
                        ticketedBooking: '3'
                        totalNumberofNegoBookings: '2'
                      availabilityCounters:
                        net: '54'
                        normal: '4.80'
                      inventoryControls:
                        unsoldProtection: '5.20'
                        waitListMax: '3'
                        min: '10'
                        maximumWaitlistPercentage: '4'
                        parentBookingClass: K
                        inhibitWaitlist: '2'
                        inhibitWaitlistClearance: '1'
                        segmentLimit: '2'
                        noshowPercentage: '6'
                        overBookingPercentage: '3'
                        nettedProtection: '2'
                        waitlistMaxPercentageOfAU: '1'
                  partnerDcsCounters:
                    - dcsCounters:
                        boardedPassengers: '28'
                        commercialStandbyPassengersBoarded: '10'
                        commercialStandbyPassengersLeftatGate: '1'
                        downgradesInto: '2'
                        downgradesOutof: '2'
                        deniedBoardingPassengers: '1'
                        groupPassengersBoarded: '10'
                        groupNoShows: '0'
                        goShowPassengers: '4'
                        noRec: '3'
                        noShowPassengers: '4'
                        offloadedPassengers: '2'
                        upgradesInto: '2'
                        upgradesOutof: '4'
                        volunteersforOffloadPassengers: '3'
                        staffCounters:
                          staffStandbyPassengersBoarded: '2'
                          acceptedStaffStandbyDowngradeInto: '1'
                          acceptedStaffStandbyDowngradeOutof: '1'
                          staffStandbyPassengersLeftatGate: '1'
                          acceptedStaffStanbyUpgradeInto: '1'
                          acceptedStaffStanbyUpgradeOutof: '0'
                      marketingFlight:
                        id: 7X-764-A-2018-12-17
                        flightDesignator:
                          carrierCode: 7X
                          flightNumber: '764'
                          operationalSuffix: A
                    - dcsCounters:
                        boardedPassengers: '28'
                        commercialStandbyPassengersBoarded: '10'
                        commercialStandbyPassengersLeftatGate: '1'
                        downgradesInto: '2'
                        downgradesOutof: '2'
                        deniedBoardingPassengers: '1'
                        groupPassengersBoarded: '10'
                        groupNoShows: '0'
                        goShowPassengers: '4'
                        noRec: '3'
                        noShowPassengers: '4'
                        offloadedPassengers: '2'
                        upgradesInto: '2'
                        upgradesOutof: '4'
                        volunteersforOffloadPassengers: '3'
                        staffCounters:
                          staffStandbyPassengersBoarded: '2'
                          acceptedStaffStandbyDowngradeInto: '1'
                          acceptedStaffStandbyDowngradeOutof: '1'
                          staffStandbyPassengersLeftatGate: '1'
                          acceptedStaffStanbyUpgradeInto: '1'
                          acceptedStaffStanbyUpgradeOutof: '0'
                      marketingFlight:
                        id: 8X-434-2018-12-17
                        flightDesignator:
                          carrierCode: 8X
                          flightNumber: '434'
                  dcsCounters:
                    boardedPassengers: '28'
                    commercialStandbyPassengersBoarded: '10'
                    commercialStandbyPassengersLeftatGate: '1'
                    downgradesInto: '2'
                    downgradesOutof: '2'
                    deniedBoardingPassengers: '1'
                    groupPassengersBoarded: '9'
                    groupNoShows: '0'
                    goShowPassengers: '2'
                    noRec: '3'
                    noShowPassengers: '3'
                    offloadedPassengers: '1'
                    upgradesInto: '1'
                    upgradesOutof: '3'
                    volunteersforOffloadPassengers: '3'
                    staffCounters:
                      staffStandbyPassengersBoarded: '2'
                      acceptedStaffStandbyDowngradeInto: '1'
                      acceptedStaffStandbyDowngradeOutof: '1'
                      staffStandbyPassengersLeftatGate: '1'
                      acceptedStaffStanbyUpgradeInto: '1'
                      acceptedStaffStanbyUpgradeOutof: '0'
              - code: P
                cabin: J
                counters:
                  subClassCounters:
                    - subClass: '0'
                      inventoryCounters:
                        bookings: '15'
                        expectedToBoard: '19'
                        waitlist: '2'
                        authorisation: '8'
                        assignedNamesInGroups: '6'
                        bookingCancellation: '5'
                        groupBookings: '6'
                        pendingGroups: '1'
                        waitlistGroups: '3'
                        groupCancellations: '1'
                        ticketedBooking: '2'
                        totalNumberofNegoBookings: '1'
                      availabilityCounters:
                        net: '27.4'
                        normal: '5'
                      inventoryControls:
                        unsoldProtection: '1'
                        waitListMax: '2'
                        min: '8'
                        maximumWaitlistPercentage: '3'
                        parentBookingClass: P
                        inhibitWaitlist: '2'
                        inhibitWaitlistClearance: '1'
                        segmentLimit: '2'
                        noshowPercentage: '2'
                        overBookingPercentage: '1'
                        nettedProtection: '2'
                        waitlistMaxPercentageOfAU: '1'
                  partnerDcsCounters:
                    - dcsCounters:
                        boardedPassengers: '18'
                        commercialStandbyPassengersBoarded: '2'
                        commercialStandbyPassengersLeftatGate: '1'
                        downgradesInto: '2'
                        downgradesOutof: '2'
                        deniedBoardingPassengers: '1'
                        groupPassengersBoarded: '6'
                        groupNoShows: '0'
                        goShowPassengers: '4'
                        noRec: '3'
                        noShowPassengers: '4'
                        offloadedPassengers: '2'
                        upgradesInto: '2'
                        upgradesOutof: '4'
                        volunteersforOffloadPassengers: '3'
                        staffCounters:
                          staffStandbyPassengersBoarded: '2'
                          acceptedStaffStandbyDowngradeInto: '1'
                          acceptedStaffStandbyDowngradeOutof: '1'
                          staffStandbyPassengersLeftatGate: '1'
                          acceptedStaffStanbyUpgradeInto: '1'
                          acceptedStaffStanbyUpgradeOutof: '0'
                      marketingFlight:
                        id: 7X-764-A-2018-12-17
                        flightDesignator:
                          carrierCode: 7X
                          flightNumber: '764'
                          operationalSuffix: A
                    - dcsCounters:
                        boardedPassengers: '28'
                        commercialStandbyPassengersBoarded: '10'
                        commercialStandbyPassengersLeftatGate: '1'
                        downgradesInto: '2'
                        downgradesOutof: '2'
                        deniedBoardingPassengers: '1'
                        groupPassengersBoarded: '10'
                        groupNoShows: '0'
                        goShowPassengers: '4'
                        noRec: '3'
                        noShowPassengers: '4'
                        offloadedPassengers: '2'
                        upgradesInto: '2'
                        upgradesOutof: '4'
                        volunteersforOffloadPassengers: '3'
                        staffCounters:
                          staffStandbyPassengersBoarded: '2'
                          acceptedStaffStandbyDowngradeInto: '1'
                          acceptedStaffStandbyDowngradeOutof: '1'
                          staffStandbyPassengersLeftatGate: '1'
                          acceptedStaffStanbyUpgradeInto: '1'
                          acceptedStaffStanbyUpgradeOutof: '0'
                      marketingFlight:
                        id: 8X-434-2018-12-17
                        flightDesignator:
                          carrierCode: 8X
                          flightNumber: '434'
                  dcsCounters:
                    boardedPassengers: '18'
                    commercialStandbyPassengersBoarded: '10'
                    commercialStandbyPassengersLeftatGate: '1'
                    downgradesInto: '2'
                    downgradesOutof: '2'
                    deniedBoardingPassengers: '1'
                    groupPassengersBoarded: '9'
                    groupNoShows: '0'
                    goShowPassengers: '2'
                    noRec: '3'
                    noShowPassengers: '3'
                    offloadedPassengers: '1'
                    upgradesInto: '1'
                    upgradesOutof: '3'
                    volunteersforOffloadPassengers: '3'
                    staffCounters:
                      staffStandbyPassengersBoarded: '2'
                      acceptedStaffStandbyDowngradeInto: '1'
                      acceptedStaffStandbyDowngradeOutof: '1'
                      staffStandbyPassengersLeftatGate: '1'
                      acceptedStaffStanbyUpgradeInto: '1'
                      acceptedStaffStanbyUpgradeOutof: '0'
            partnerships:
              marketingFlights:
                - id: 7X-764-A-2018-12-17
                  flightDesignator:
                    carrierCode: 7X
                    flightNumber: '764'
                    operationalSuffix: A
                    codeshareAgreement: BLOCKSPACE
                - id: 8X-434-2018-12-17
                  flightDesignator:
                    carrierCode: 8X
                    flightNumber: '434'
                    codeshareAgreement: BLOCKSPACE
        legs:
          - id: GVA-YUL
            boardPointIataCode: GVA
            offPointIataCode: YUL
            scheduledDepartureDateTime: '2018-12-17T12:40:00'
            scheduledArrivalDateTime: '2018-12-17T17:45:00'
            serviceType: J
            aircraftEquipment:
              aircraftType: '388'
            ssrQuotas:
              - counterCode: INFT
                quota: '5'
                availability: '5'
                count: '0'
                cabinCode: J
                nestFamily: A
            legCounters:
              cabinCounters:
                - cabinCode: 'Y'
                  counters:
                    dcsAdjustments: '1'
                    acceptedStandBy: '2'
                  inventoryCounters:
                    bookings: '35'
                    groupBookings: '10'
                    expectedToBoard: '32'
                    regrade: '4'
                    waitlist: '3'
                  availabilityCounters:
                    gross: '65'
                    net: '55'
                  inventoryControls:
                    unsoldProtection: '1'
                    unbalanceAdjustment: '6'
                    maxiRegradeAdjustment: '4'
                    regradeAdjustment: '0'
                  blockSpaces:
                    - codeShare: '1'
                      marketingFlight:
                        id: 7X-764-A-2018-10-03
                        flightDesignator:
                          carrierCode: 7X
                          flightNumber: '764'
                          operationalSuffix: A
                    - protectedSeats: '1'
                    - seatsBlocked: '3'
                    - allotment: '0'
                  capacity:
                    saleableCapacity: '58'
                    authorizationLevel: '25'
                    effectiveCapacity: '21'
                    operationalCapacity: '12'
                - cabinCode: J
                  counters:
                    dcsAdjustments: '1'
                    acceptedStandBy: '2'
                  inventoryCounters:
                    bookings: '15'
                    groupBookings: '5'
                    expectedToBoard: '12'
                    regrade: '1'
                    waitlist: '1'
                  availabilityCounters:
                    gross: '42'
                    net: '38'
                  inventoryControls:
                    unsoldProtection: '1'
                    unbalanceAdjustment: '3'
                    maxiRegradeAdjustment: '4'
                    regradeAdjustment: '0'
                  blockSpaces:
                    - codeShare: '1'
                      marketingFlight:
                        id: 7X-764-A-2018-10-03
                        flightDesignator:
                          carrierCode: 7X
                          flightNumber: '764'
                          operationalSuffix: A
                    - protectedSeats: '1'
                    - seatsBlocked: '2'
                    - allotment: '0'
                  capacity:
                    saleableCapacity: '40'
                    authorizationLevel: '15'
                    effectiveCapacity: '22'
                    operationalCapacity: '20'
      events:
        recordDomain: INVENTORY
        recordId: 6X-123-2019-03-12
        events:
          - origin: COMPARISON
            eventType: UPDATED
            currentPath: ''
            previousPath: ''
  FlightInventoryCorrelationDataPush:
    type: object
    description: The correlation data push schema to be referenced in the main Swagger spec
    properties:
      meta:
        $ref: '#/definitions/Meta'
      inventoryCorrelations:
        $ref: '#/definitions/InventoryCorrelationsFeedData'
      events:
        $ref: '#/definitions/Events'
