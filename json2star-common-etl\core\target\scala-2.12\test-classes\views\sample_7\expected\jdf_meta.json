{"version": "[VERSION]", "path": "output/path", "shard": "[SHARD]", "schemaName": "DEFAULT_DB.db", "status": "ACTIVATED", "tables": [{"name": "FACT_PASSENGER_HISTO_LIGHT", "type": "FACT", "description": "", "gdprZone": "red", "kind": "MATERIALIZED", "columns": [{"name": "PASSENGER_ID", "description": "Hash of Unique Customer Identifier (UCI)", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["hashM($.mainResource.current.image.id)", "hashM($.mainResource.current.image.segDel.id)"]}, {"name": "COLUMN_A", "description": "CUSTOM DESCRIPTION FOR A", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "CUSTOM PII TYPE FOR A,This is PII Type from DIH", "example": "CUSTOM EXAMPLE FOR A,This is Example from DIH", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.specialSeat", "$.mainResource.current.image.segDel.specialSeat"]}, {"name": "COLUMN_B", "description": "CUSTOM DESCRIPTION FOR B\nThis is Description from DIH", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "CUSTOM PII TYPE FOR B,This is PII Type from DIH", "example": "CUSTOM EXAMPLE FOR B", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.specialSeat", "$.mainResource.current.image.segDel.specialSeat"]}, {"name": "COLUMN_C", "description": "CUSTOM DESCRIPTION FOR C\nThis is Description from DIH", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "CUSTOM PII TYPE FOR C", "example": "CUSTOM EXAMPLE FOR C,This is Example from DIH", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.specialSeat", "$.mainResource.current.image.segDel.specialSeat"]}, {"name": "COLUMN_D", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "orange", "piiType": "", "example": "CUSTOM EXAMPLE FOR D", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.specialSeat[*].seatId", "$.mainResource.current.image.segDel.specialSeat[*].seatId"]}, {"name": "COLUMN_E", "description": "Hash of This is Description from DIH", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "This is PII Type from DIH", "fkRelationships": [], "sourcePaths": ["if(element_at(split($.mainResource.current.image.specialSeat,'-'),array_size(split($.mainResource.current.image.specialSeat,'-'))) == '_', NULL,hashM($.mainResource.current.image.specialSeat) )", "if(element_at(split($.mainResource.current.image.segDel.specialSeat,'-'),array_size(split($.mainResource.current.image.segDel.specialSeat,'-'))) == '_', NULL,hashM($.mainResource.current.image.segDel.specialSeat) )"]}, {"name": "COLUMN_F", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.mySpecialSeat", "$.mainResource.current.image.segDel.mySpecialSeat"]}, {"name": "COLUMN_G", "description": "This is Description from DIH but different path in JSON", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "This is PII Type from DIH but different path in JSON", "example": "This is Example from DIH but different path in JSON", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.mySecondSpecialSeat", "$.mainResource.current.image.segDel.mySecondSpecialSeat"]}, {"name": "RELATES_TO", "description": "", "type": "STRING", "primaryKey": false, "piiType": "", "example": "PASSENGER,SEGMENT_DELIVERY", "fkRelationships": []}]}, {"name": "DIM_PASSENGER", "type": "DIM", "description": "A DIM table", "gdprZone": "green", "kind": "MATERIALIZED", "columns": [{"name": "PASSENGER_TYPE_ID", "description": "Hash of the functional key (code)", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_PASSENGER_WITH_PREFILLER", "columnName": "PASSENGER_WITH_PREFILLER_ID"}], "sourcePaths": ["hashS($.mainResource.current.image.travelers[*].passengerTypeCode)"]}, {"name": "PASSENGER_TYPE_CODE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.travelers[*].passengerTypeCode"]}, {"name": "PASSENGER_TYPE_LABEL", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.travelers[*].passengerTypeCode"]}, {"name": "RECORD_SOURCE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "example": "FROM_FEED_DATA", "fkRelationships": []}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "DIM_PASSENGER_WITH_PREFILLER", "type": "DIM", "description": "A DIM table - This table is preloaded with static referential data coming from Amadeus", "gdprZone": "green", "kind": "MATERIALIZED", "columns": [{"name": "PASSENGER_TYPE_ID", "description": "Hash of the functional key (code)", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [{"tableName": "DIM_PASSENGER", "columnName": "PASSENGER_ID"}], "sourcePaths": ["hashS($.mainResource.current.image.travelers[*].passengerTypeCode)"]}, {"name": "PASSENGER_TYPE_CODE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.travelers[*].passengerTypeCode"]}, {"name": "PASSENGER_TYPE_LABEL", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": [], "sourcePaths": ["$.mainResource.current.image.travelers[*].passengerTypeCode"]}, {"name": "RECORD_SOURCE", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "example": "FROM_FEED_DATA", "fkRelationships": []}, {"name": "LOAD_DATE", "description": "Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}]}