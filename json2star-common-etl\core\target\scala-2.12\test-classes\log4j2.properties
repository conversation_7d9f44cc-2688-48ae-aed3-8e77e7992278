rootLogger.level = warn
rootLogger.appenderRef.stdout.ref = STDOUT

appender.console.type = Console
appender.console.name = STDOUT
appender.console.target = SYSTEM_OUT
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = %d{yy/MM/dd HH:mm:ss} %p %c{1}: %m%n%ex

# Settings to quiet third party logs that are too verbose
logger.jetty.name = org.sparkproject.jetty
logger.jetty.level = warn
logger.jetty2.name = org.sparkproject.jetty.util.component.AbstractLifeCycle
logger.jetty2.level = error
logger.repl1.name = org.apache.spark.repl.SparkIMain$exprTyper
logger.repl1.level = info
logger.repl2.name = org.apache.spark.repl.SparkILoop$SparkILoopInterpreter
logger.repl2.level = info

# Set the default spark-shell log level to WARN. When running the spark-shell, the
# log level for this class is used to overwrite the root logger's log level, so that
# the user can have different defaults for the shell and regular Spark apps.
logger.repl.name = org.apache.spark.repl.Main
logger.repl.level = warn

# SPARK-9183: Settings to avoid annoying messages when looking up nonexistent UDFs
# in SparkSQL with Hive support
logger.metastore.name = org.apache.hadoop.hive.metastore.RetryingHMSHandler
logger.metastore.level = fatal
logger.hive_functionregistry.name = org.apache.hadoop.hive.ql.exec.FunctionRegistry
logger.hive_functionregistry.level = error

# Parquet related logging
logger.parquet.name = org.apache.parquet.CorruptStatistics
logger.parquet.level = error
logger.parquet2.name = parquet.CorruptStatistics
logger.parquet2.level = error

# Set SimpleFunctionRegistry to error to avoid WARN in case of multiple udf registration (happening only in tests)
logger.udf.name = org.apache.spark.sql.catalyst.analysis.SimpleFunctionRegistry
logger.udf.level = error

logger.delta.name = io.delta
logger.delta.level = warn

logger.hadoop.name = org.apache.hadoop.util
logger.hadoop.level = error

logger.spark.name = org.apache.spark
logger.spark.level = warn

logger.sparkutils.name = org.apache.spark.util.Utils
logger.sparkutils.level = off

logger.sparksql.name = org.apache.spark.sql
logger.sparksql.level = off

logger.sparkexecutor.name = org.apache.spark.executor
logger.sparkexecutor.level = off

logger.sparkscheduler.name = org.apache.spark.scheduler
logger.sparkscheduler.level = off

logger.amadeus.name = com.amadeus
# Change here below to '... = trace' to see the good stuff
logger.amadeus.level = warn

logger.amadeusdihyaml.name = com.amadeus.airbi.json2star.common.views.generators
logger.amadeusdihyaml.level = error

logger.customoptimize.name = com.amadeus.airbi.json2star.common.optimize
logger.customoptimize.level = off

logger.microbatchexecution.name = org.apache.spark.sql.execution.streaming
logger.microbatchexecution.level = off
