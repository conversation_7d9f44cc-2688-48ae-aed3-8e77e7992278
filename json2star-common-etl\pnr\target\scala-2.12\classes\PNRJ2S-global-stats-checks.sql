-- MAGIC %md
-- MAGIC #Notebook Information
-- MAGIC
-- MAGIC Each cell starts by defining the values specific to the test. Then an INSERT INTO is run with a join between tables. For a test to pass, zero records are expected from the join.

-- COMMAND ----------

--The threshold value can be set individually for each validation test.
SET db.task = "PNRJ2S-global-stats-checks.sql";
SET db.validation_database = ${valDatabase};
SET db.validation_table = ${valTableName};
SET db.default_threshold = 0;
SET db.now_datetime = current_timestamp();
SET db.table_fields = (domain, domain_version, customer, phase, test_time, task, covered_functional_case,status,nb_failed_records,nb_total_records,fail_ratio,result_details);

-- COMMAND ----------

--test 1
--set variables for this test
SET db.threshold = 100;
SET db.func_case = "Test 1 - Check we have approximately the right number of PNR per day";
SET db.result_details = CONCAT('Less than ',${db.threshold},' reservations received per day. Avg reservations per day is ');

WITH avg_PNR AS (
  select avg(nb_resas_day) as avg_number
  from (
    select date_format(pnr_creation_date,'yyyy-MM-dd'), count(*) as nb_resas_day
    from $DB_NAME.fact_reservation
    group by date_format(pnr_creation_date, 'yyyy-MM-dd')
  )
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select "${DOMAIN}","${DOMAIN_VERSION}","${CUSTOMER}","${PHASE}",${db.now_datetime},${db.task},${db.func_case},
  (avg_number>${db.threshold}) as status,
  null,null,null,
  if(avg_number<${db.threshold},concat(${db.result_details},cast(avg_number as int)),NULL) as result_details
from avg_PNR;

-- COMMAND ----------

--test 2
--set variables for this test
SET db.threshold_min = 1;
SET db.threshold_max = 3;
SET db.func_case = "Test 2 - Check the average number of passenger by reservation";
SET db.result_details = CONCAT('The average number of passenger by resa is less than ',${db.threshold_min},' or higher than ',${db.threshold_max},'. Average number is ');

WITH avg_PAX AS (
  select avg(nb_pax_resas) as average_number_pax_by_resa
  from (
    select count(*) as nb_pax_resas
    from $DB_NAME.fact_traveler trav
      inner join $DB_NAME.fact_reservation res on trav.reservation_id = res.reservation_id
    group by trav.reservation_id
  )
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select "${DOMAIN}","${DOMAIN_VERSION}","${CUSTOMER}","${PHASE}",${db.now_datetime},${db.task},${db.func_case},
  ((average_number_pax_by_resa>${db.threshold_min}) AND (average_number_pax_by_resa<${db.threshold_max})) as status,
  null,null,null,
  if((average_number_pax_by_resa<${db.threshold_min}) OR (average_number_pax_by_resa>${db.threshold_max}),concat(${db.result_details},cast(average_number_pax_by_resa as int)),NULL) as result_details
from avg_PAX;

-- COMMAND ----------

--test 3
--set variables for this test
SET db.threshold_min = 1;
SET db.threshold_max = 3;
SET db.func_case = "Test 3 - Check the average number of segments by reservation";
SET db.result_details = CONCAT('The average number of segments by resa is less than ',${db.threshold_min},' or higher than ',${db.threshold_max},'. Average number is ');

WITH avg_seg AS (
  select avg(nb_seg_resas) as average_number_seg_by_resa
  from (
    select count(distinct seg.air_segment_id) as nb_seg_resas
    from $DB_NAME.fact_air_segment_pax seg
      inner join $DB_NAME.fact_reservation res on seg.reservation_id=res.reservation_id
    group by seg.reservation_id
  )
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select "${DOMAIN}","${DOMAIN_VERSION}","${CUSTOMER}","${PHASE}",${db.now_datetime},${db.task},${db.func_case},
  ((average_number_seg_by_resa>${db.threshold_min}) AND (average_number_seg_by_resa<${db.threshold_max})) as status,
  null,null,null,
  if((average_number_seg_by_resa<${db.threshold_min}) OR (average_number_seg_by_resa>${db.threshold_max}),concat(${db.result_details},cast(average_number_seg_by_resa as int)),NULL) as result_details
from avg_seg;

-- COMMAND ----------

--test 4
--set variables for this test
SET db.func_case = "Test 4 - GROUP Validation, expectation : some groups should exist without any name given";
SET db.result_details = 'No group without any name';

WITH num_groups AS (
  select count(*) as num_without_name
  from $DB_NAME.fact_reservation
  where group_size_taken = 0
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select "${DOMAIN}","${DOMAIN_VERSION}","${CUSTOMER}","${PHASE}",${db.now_datetime},${db.task},${db.func_case},
  (num_without_name!=0) as status,
  null,null,null,
  if(num_without_name==0,${db.result_details},NULL) as result_details
from num_groups;

-- COMMAND ----------

--test 5
--set variables for this test
SET db.threshold = 1; --threshold for number of days since last data
SET db.func_case = "Test 5 - Check we continuously receive data in star schema";
SET db.result_details = ' day(s) since last reservation data received';

WITH last_res AS (
  select min(datediff(current_timestamp(), to_timestamp(load_date))) as min_days
  from $DB_NAME.fact_reservation
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select "${DOMAIN}","${DOMAIN_VERSION}","${CUSTOMER}","${PHASE}",${db.now_datetime},${db.task},${db.func_case},
  (min_days<=${db.threshold}) as status,
  null,null,null,
  if(min_days>${db.threshold},CONCAT(min_days,${db.result_details}),NULL) as result_details
from last_res;
