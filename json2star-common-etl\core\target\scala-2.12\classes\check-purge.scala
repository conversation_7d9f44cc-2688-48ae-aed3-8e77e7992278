%scala
import com.amadeus.airbi.json2star.common.app.{PurgeApp, PurgeInternalContext}
import com.amadeus.airbi.json2star.common.validation.config.ValidationRecord
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

import java.time.format.DateTimeFormatter
import java.time.{LocalDate, OffsetDateTime}

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val phase = dbutils.widgets.get("phase")
val currentTimestamp = OffsetDateTime.now()
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last
val (appConfig, purgeParams, model, dryRun) =
  PurgeApp.buildConfigs(Array("--app-config-file", appConfigFile,
    "--dry-run", "false"))

val purgeCtxt: PurgeInternalContext = PurgeApp.buildPurgeInternalContext(
  spark,
  appConfig.common.outputDatabase,
  purgeParams,
  model,
  dryRun,
  currentTimestamp.format(DateTimeFormatter.ISO_LOCAL_DATE)
)
// run the validation statement
val diff: Long = PurgeApp.getDiffBtwOldestDataVsThreshold(purgeCtxt)

// create the validation record
val testRecord = Seq(
  ValidationRecord(
    appConfig.common.domain,
    appConfig.common.domainVersion,
    appConfig.common.shard,
    phase,
    currentTimestamp.toString,
    task,
    "Check there is no data eligible to purge left in the star schema",
    diff < 0, // if diff is negative, it means there is data older than the threshold
    0,
    0,
    0,
    s"The difference between the oldest latest version begin date and the threshold is $diff minutes"
  )
)

// write the validation record to the validation table
val df = testRecord.toDF()
df.write.insertInto(s"${valDatabase}.${valTableName}")