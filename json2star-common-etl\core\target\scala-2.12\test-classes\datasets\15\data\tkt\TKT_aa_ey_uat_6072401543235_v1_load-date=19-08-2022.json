﻿{
    "correlatedResourcesCurrent": {
        "TKT-PNR": {
            "correlations": [
                {
                    "corrTktPnr": {
                        "items": [
                            {
                                "pnrAirSegmentId": "NP84R4-2022-08-19-ST-2",
                                "pnrTicketingReferenceId": "NP84R4-2022-08-19-OT-19",
                                "pnrTravelerId": "NP84R4-2022-08-19-PT-2",
                                "ticketCouponId": "6072401543235-2022-08-19-2"
                            }
                        ]
                    },
                    "fromVersion": "0",
                    "toId": "NP84R4-2022-08-19",
                    "toVersion": "4"
                }
            ],
            "fromDomain": "TKT",
            "fromFullVersion": "1",
            "id": "6072401543235-2022-08-19",
            "isFullUpdate": true,
            "toDomain": "PNR",
            "version": "PNR-3"
        }
    },
    "correlatedResourcesPrevious": {
        "TKT-PNR": {
            "correlations": [
                {
                    "corrTktPnr": {
                        "items": [
                            {
                                "pnrAirSegmentId": "NP84R4-2022-08-19-ST-2",
                                "pnrTicketingReferenceId": "NP84R4-2022-08-19-OT-19",
                                "pnrTravelerId": "NP84R4-2022-08-19-PT-2",
                                "ticketCouponId": "6072401543235-2022-08-19-2"
                            }
                        ]
                    },
                    "fromVersion": "0",
                    "toId": "NP84R4-2022-08-19",
                    "toVersion": "4"
                }
            ],
            "fromDomain": "TKT",
            "fromFullVersion": "0",
            "id": "6072401543235-2022-08-19",
            "isFullUpdate": false,
            "toDomain": "PNR",
            "version": "PNR-2"
        }
    },
    "mainResource": {
        "current": {
            "correlations": [
                {
                    "name": "TKT-PNR",
                    "relation": {
                        "rel": "related"
                    }
                }
            ],
            "image": {
                "@type": "type.googleapis.com/com.amadeus.pulse.message.AirTravelDocument",
                "associatedPnrs": [
                    {
                        "creation": {
                            "pointOfSale": {
                                "office": {
                                    "systemCode": "EY"
                                }
                            }
                        },
                        "reference": "NP84R4"
                    },
                    {
                        "creation": {
                            "pointOfSale": {
                                "office": {
                                    "systemCode": "1A"
                                }
                            }
                        },
                        "reference": "NP84R4"
                    }
                ],
                "conjunctiveDocumentNumbers": [
                    "6072401543235"
                ],
                "coupons": [
                    {
                        "baggageAllowance": {
                            "weight": {
                                "amount": "30",
                                "unit": "KILOGRAMS"
                            }
                        },
                        "currentSegment": {
                            "arrival": {
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-05T21:35:00"
                            },
                            "bookingClass": {
                                "code": "Q"
                            },
                            "carrierCode": "EY",
                            "departure": {
                                "iataCode": "AUH",
                                "localDate": "2023-01-05",
                                "localDateTime": "2023-01-05T21:35:00"
                            },
                            "number": "391"
                        },
                        "documentNumber": "6072401543235",
                        "fareBasis": {
                            "fareBasisCode": "QLC2AE"
                        },
                        "id": "6072401543235-2022-08-19-1",
                        "isCodeshare": false,
                        "isFromConnection": true,
                        "isNonExchangeable": false,
                        "isNonRefundable": false,
                        "number": 1,
                        "reservationStatus": "CONFIRMED",
                        "revalidation": {
                            "dateTime": "2022-09-06T06:06:32.*********",
                            "pointOfSale": {
                                "office": {
                                    "agentType": "AIRLINE_AGENT",
                                    "iataNumber": "00145725"
                                }
                            }
                        },
                        "sequenceNumber": 1,
                        "soldSegment": {
                            "arrival": {
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-06T02:15:00"
                            },
                            "bookingClass": {
                                "code": "Q"
                            },
                            "carrierCode": "EY",
                            "departure": {
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-06T02:10:00"
                            },
                            "number": "399"
                        },
                        "status": "OPEN_FOR_USE",
                        "validityDates": {
                            "notValidAfterDate": "2023-01-06",
                            "notValidBeforeDate": "2023-01-06"
                        },
                        "voluntaryIndicator": "I"
                    },
                    {
                        "baggageAllowance": {
                            "weight": {
                                "amount": "30",
                                "unit": "KILOGRAMS"
                            }
                        },
                        "currentSegment": {
                            "arrival": {
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-26T05:35:00"
                            },
                            "bookingClass": {
                                "code": "Q"
                            },
                            "carrierCode": "EY",
                            "departure": {
                                "iataCode": "DOH",
                                "localDate": "2023-01-26",
                                "localDateTime": "2023-01-26T03:20:00"
                            },
                            "number": "390"
                        },
                        "documentNumber": "6072401543235",
                        "fareBasis": {
                            "fareBasisCode": "QLC2AE"
                        },
                        "id": "6072401543235-2022-08-19-2",
                        "isCodeshare": false,
                        "isFromConnection": true,
                        "isNonExchangeable": false,
                        "isNonRefundable": false,
                        "number": 2,
                        "reservationStatus": "CONFIRMED",
                        "revalidation": {
                            "pointOfSale": {}
                        },
                        "sequenceNumber": 2,
                        "soldSegment": {
                            "arrival": {
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-26T05:35:00"
                            },
                            "bookingClass": {
                                "code": "Q"
                            },
                            "carrierCode": "EY",
                            "departure": {
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-26T03:20:00"
                            },
                            "number": "390"
                        },
                        "status": "OPEN_FOR_USE",
                        "validityDates": {
                            "notValidAfterDate": "2023-01-26",
                            "notValidBeforeDate": "2023-01-26"
                        }
                    }
                ],
                "creation": {
                    "dateTime": "2022-08-19",
                    "pointOfSale": {
                        "login": {
                            "cityCode": "RKT",
                            "countryCode": "AE",
                            "currencyCode": "AED",
                            "dutyCode": "AS",
                            "initials": "WS",
                            "numericSign": "9999"
                        },
                        "office": {
                            "agentType": "AIRLINE_AGENT",
                            "iataNumber": "86493503",
                            "id": "RKTEY08WC",
                            "inHouseIdentification": "127399",
                            "systemCode": "1A"
                        }
                    }
                },
                "destinationCityIataCode": "AUH",
                "documentRefund": {},
                "documentType": "TICKET",
                "endorsementFreeFlow": "NON ENDO/ REF",
                "formsOfPayment": [
                    {
                        "code": "EBKW",
                        "displayedAmount": {
                            "amount": "2755",
                            "currency": "AED"
                        },
                        "fopIndicator": "NEW",
                        "freeText": "EBKWEYXXXXXXXXXXXX2178-AED2755*A",
                        "paymentLoyalty": {
                            "certificateNumber": "8823036238",
                            "membership": {
                                "activeTier": {
                                    "companyCode": "EY"
                                },
                                "id": "7600010016662178",
                                "membershipType": "INDIVIDUAL"
                            }
                        }
                    }
                ],
                "id": "6072401543235-2022-08-19",
                "issuanceType": "FIRST_ISSUE",
                "latestEvent": {
                    "dateTime": "2022-09-06T06:06:32.*********",
                    "id": "1",
                    "pointOfSale": {
                        "login": {
                            "cityCode": "AUH",
                            "countryCode": "AE",
                            "currencyCode": "AED",
                            "dutyCode": "SU",
                            "initials": "AA",
                            "numericSign": "0001"
                        },
                        "office": {
                            "agentType": "AIRLINE_AGENT",
                            "iataNumber": "00145725",
                            "inHouseIdentification": "273101",
                            "systemCode": "1A"
                        }
                    },
                    "triggerEventName": "139"
                },
                "numberOfBooklets": 1,
                "originCityIataCode": "AUH",
                "price": {
                    "currency": "AED",
                    "detailedPrices": [
                        {
                            "amount": "2755",
                            "currency": "AED",
                            "elementaryPriceType": "TotalFare"
                        },
                        {
                            "amount": "2100",
                            "currency": "AED",
                            "elementaryPriceType": "BaseFare"
                        }
                    ],
                    "taxes": [
                        {
                            "amount": "360",
                            "category": "NEW",
                            "code": "YQ",
                            "currency": "AED",
                            "nature": "AC",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "75",
                            "category": "NEW",
                            "code": "AE",
                            "currency": "AED",
                            "nature": "AD",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "35",
                            "category": "NEW",
                            "code": "F6",
                            "currency": "AED",
                            "nature": "TO",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "5",
                            "category": "NEW",
                            "code": "TP",
                            "currency": "AED",
                            "nature": "SE",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "10",
                            "category": "NEW",
                            "code": "ZR",
                            "currency": "AED",
                            "nature": "AP",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "70",
                            "category": "NEW",
                            "code": "G4",
                            "currency": "AED",
                            "nature": "AF",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "10",
                            "category": "NEW",
                            "code": "PZ",
                            "currency": "AED",
                            "nature": "AV",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "70",
                            "category": "NEW",
                            "code": "QA",
                            "currency": "AED",
                            "nature": "AP",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "10",
                            "category": "NEW",
                            "code": "R9",
                            "currency": "AED",
                            "nature": "SE",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        }
                    ],
                    "total": "2755",
                    "totalTaxes": "655"
                },
                "pricingConditions": {
                    "fareCalculation": {
                        "pricingIndicator": "0",
                        "text": "AUH EY DOH Q50.00 260.02EY AUH260.02NUC570.04END ROE3.672750"
                    },
                    "isInternationalSale": true,
                    "isNonEndorsable": true,
                    "isNonExchangeable": false,
                    "isNonRefundable": false,
                    "isPenaltyRestriction": false
                },
                "primaryDocumentNumber": "6072401543235",
                "traveler": {
                    "name": {
                        "firstName": "KARL MR",
                        "lastName": "GREEN"
                    },
                    "passengerTypeCode": "ADT"
                },
                "validatingCarrierCode": "EY",
                "validatingCarrierPnr": {
                    "reference": "NP84R4"
                },
                "version": "1",
                "void": {}
            }
        },
        "id": "6072401543235-2022-08-19",
        "previous": {
            "correlations": [
                {
                    "name": "TKT-PNR",
                    "relation": {
                        "rel": "related"
                    }
                }
            ],
            "image": {
                "@type": "type.googleapis.com/com.amadeus.pulse.message.AirTravelDocument",
                "associatedPnrs": [
                    {
                        "creation": {
                            "pointOfSale": {
                                "office": {
                                    "systemCode": "EY"
                                }
                            }
                        },
                        "reference": "NP84R4"
                    },
                    {
                        "creation": {
                            "pointOfSale": {
                                "office": {
                                    "systemCode": "1A"
                                }
                            }
                        },
                        "reference": "NP84R4"
                    }
                ],
                "conjunctiveDocumentNumbers": [
                    "6072401543235"
                ],
                "coupons": [
                    {
                        "baggageAllowance": {
                            "weight": {
                                "amount": "30",
                                "unit": "KILOGRAMS"
                            }
                        },
                        "currentSegment": {
                            "arrival": {
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-06T02:15:00"
                            },
                            "bookingClass": {
                                "code": "Q"
                            },
                            "carrierCode": "EY",
                            "departure": {
                                "iataCode": "AUH",
                                "localDate": "2023-01-06",
                                "localDateTime": "2023-01-06T02:10:00"
                            },
                            "number": "399"
                        },
                        "documentNumber": "6072401543235",
                        "fareBasis": {
                            "fareBasisCode": "QLC2AE"
                        },
                        "id": "6072401543235-2022-08-19-1",
                        "isCodeshare": false,
                        "isFromConnection": true,
                        "isNonExchangeable": false,
                        "isNonRefundable": false,
                        "number": 1,
                        "reservationStatus": "CONFIRMED",
                        "sequenceNumber": 1,
                        "soldSegment": {
                            "arrival": {
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-06T02:15:00"
                            },
                            "bookingClass": {
                                "code": "Q"
                            },
                            "carrierCode": "EY",
                            "departure": {
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-06T02:10:00"
                            },
                            "number": "399"
                        },
                        "status": "OPEN_FOR_USE",
                        "validityDates": {
                            "notValidAfterDate": "2023-01-06",
                            "notValidBeforeDate": "2023-01-06"
                        }
                    },
                    {
                        "baggageAllowance": {
                            "weight": {
                                "amount": "30",
                                "unit": "KILOGRAMS"
                            }
                        },
                        "currentSegment": {
                            "arrival": {
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-26T05:35:00"
                            },
                            "bookingClass": {
                                "code": "Q"
                            },
                            "carrierCode": "EY",
                            "departure": {
                                "iataCode": "DOH",
                                "localDate": "2023-01-26",
                                "localDateTime": "2023-01-26T03:20:00"
                            },
                            "number": "390"
                        },
                        "documentNumber": "6072401543235",
                        "fareBasis": {
                            "fareBasisCode": "QLC2AE"
                        },
                        "id": "6072401543235-2022-08-19-2",
                        "isCodeshare": false,
                        "isFromConnection": true,
                        "isNonExchangeable": false,
                        "isNonRefundable": false,
                        "number": 2,
                        "reservationStatus": "CONFIRMED",
                        "sequenceNumber": 2,
                        "soldSegment": {
                            "arrival": {
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-26T05:35:00"
                            },
                            "bookingClass": {
                                "code": "Q"
                            },
                            "carrierCode": "EY",
                            "departure": {
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-26T03:20:00"
                            },
                            "number": "390"
                        },
                        "status": "OPEN_FOR_USE",
                        "validityDates": {
                            "notValidAfterDate": "2023-01-26",
                            "notValidBeforeDate": "2023-01-26"
                        }
                    }
                ],
                "creation": {
                    "dateTime": "2022-08-19",
                    "pointOfSale": {
                        "login": {
                            "cityCode": "RKT",
                            "countryCode": "AE",
                            "currencyCode": "AED",
                            "dutyCode": "AS",
                            "initials": "WS",
                            "numericSign": "9999"
                        },
                        "office": {
                            "agentType": "AIRLINE_AGENT",
                            "iataNumber": "86493503",
                            "id": "RKTEY08WC",
                            "inHouseIdentification": "127399",
                            "systemCode": "1A"
                        }
                    }
                },
                "destinationCityIataCode": "AUH",
                "documentRefund": {},
                "documentType": "TICKET",
                "endorsementFreeFlow": "NON ENDO/ REF",
                "formsOfPayment": [
                    {
                        "code": "EBKW",
                        "displayedAmount": {
                            "amount": "2755",
                            "currency": "AED"
                        },
                        "fopIndicator": "NEW",
                        "freeText": "EBKWEYXXXXXXXXXXXX2178-AED2755*A",
                        "paymentLoyalty": {
                            "certificateNumber": "8823036238",
                            "membership": {
                                "activeTier": {
                                    "companyCode": "EY"
                                },
                                "id": "7600010016662178",
                                "membershipType": "INDIVIDUAL"
                            }
                        }
                    }
                ],
                "id": "6072401543235-2022-08-19",
                "issuanceType": "FIRST_ISSUE",
                "latestEvent": {
                    "dateTime": "2022-08-19T14:14:30.*********",
                    "id": "0",
                    "pointOfSale": {
                        "login": {
                            "cityCode": "RKT",
                            "countryCode": "AE",
                            "currencyCode": "AED",
                            "dutyCode": "AS",
                            "initials": "WS",
                            "numericSign": "9999"
                        },
                        "office": {
                            "agentType": "AIRLINE_AGENT",
                            "iataNumber": "86493503",
                            "id": "RKTEY08WC",
                            "inHouseIdentification": "127399",
                            "systemCode": "1A"
                        }
                    },
                    "triggerEventName": "130"
                },
                "numberOfBooklets": 1,
                "originCityIataCode": "AUH",
                "price": {
                    "currency": "AED",
                    "detailedPrices": [
                        {
                            "amount": "2755",
                            "currency": "AED",
                            "elementaryPriceType": "TotalFare"
                        },
                        {
                            "amount": "2100",
                            "currency": "AED",
                            "elementaryPriceType": "BaseFare"
                        }
                    ],
                    "taxes": [
                        {
                            "amount": "360",
                            "category": "NEW",
                            "code": "YQ",
                            "currency": "AED",
                            "nature": "AC",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "75",
                            "category": "NEW",
                            "code": "AE",
                            "currency": "AED",
                            "nature": "AD",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "35",
                            "category": "NEW",
                            "code": "F6",
                            "currency": "AED",
                            "nature": "TO",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "5",
                            "category": "NEW",
                            "code": "TP",
                            "currency": "AED",
                            "nature": "SE",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "10",
                            "category": "NEW",
                            "code": "ZR",
                            "currency": "AED",
                            "nature": "AP",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "70",
                            "category": "NEW",
                            "code": "G4",
                            "currency": "AED",
                            "nature": "AF",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "10",
                            "category": "NEW",
                            "code": "PZ",
                            "currency": "AED",
                            "nature": "AV",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "70",
                            "category": "NEW",
                            "code": "QA",
                            "currency": "AED",
                            "nature": "AP",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        },
                        {
                            "amount": "10",
                            "category": "NEW",
                            "code": "R9",
                            "currency": "AED",
                            "nature": "SE",
                            "ticketReportedStatuses": [
                                "REPORTED",
                                "DISPLAYED"
                            ]
                        }
                    ],
                    "total": "2755",
                    "totalTaxes": "655"
                },
                "pricingConditions": {
                    "fareCalculation": {
                        "pricingIndicator": "0",
                        "text": "AUH EY DOH Q50.00 260.02EY AUH260.02NUC570.04END ROE3.672750"
                    },
                    "isInternationalSale": true,
                    "isNonEndorsable": true,
                    "isNonExchangeable": false,
                    "isNonRefundable": false,
                    "isPenaltyRestriction": false
                },
                "primaryDocumentNumber": "6072401543235",
                "traveler": {
                    "name": {
                        "firstName": "KARL MR",
                        "lastName": "GREEN"
                    },
                    "passengerTypeCode": "ADT"
                },
                "validatingCarrierCode": "EY",
                "validatingCarrierPnr": {
                    "reference": "NP84R4"
                },
                "version": "0"
            }
        },
        "type": "com.amadeus.pulse.message.AirTravelDocument"
    }
}