{"defaultComment": "A comment here", "partition-spec": {"key": "DATE_BEGIN", "column-name": "PART_DATE_BEGIN_MONTH", "expr": "date_format(DATE_BEGIN, \"yyyy-MM\")"}, "tables": [{"name": "FACT_PASSENGER_HISTO", "mapping": {"merge": {"key-columns": ["PASSENGER_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}], "columns": [{"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}}, {"name": "CPR_FEED_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.cprFeedType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}}, {"name": "ETAG", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.etag"}]}}, {"name": "GROUP_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.groupName"}]}}, {"name": "IS_MASTER_RECORD", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isMasterRecord"}]}}, {"name": "IS_SAME_PHYSICAL_CUSTOMER", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isSamePhysicalCustomer"}]}}, {"name": "IS_SYSTEM_MARKED_SPC", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isSystemMarkedSPC"}]}}, {"name": "BIRTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.passenger.dateOfBirth"}]}}, {"name": "BIRTH_PLACE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.placeOfBirth"}]}}, {"name": "PASSENGER_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.passengerType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}}, {"name": "GENDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.gender"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_GENDER"}]}}, {"name": "NATIONALITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.nationality"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_COUNTRY"}]}}, {"name": "SPECIAL_SEAT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.specialSeat"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}}, {"name": "FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.firstName"}]}}, {"name": "LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.lastName"}]}}, {"name": "TITLE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.title"}]}}, {"name": "RESIDENCE_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.countryOfResidence"}]}}, {"name": "AGE", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.passenger.age"}]}}, {"name": "STAFF_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.category"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}}, {"name": "STAFF_COMPANY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.companyCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}}, {"name": "STAFF_COMPANY_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.companyName"}]}}, {"name": "STAFF_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.id"}]}}, {"name": "STAFF_BOOKING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.idType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}}, {"name": "STAFF_JOINING_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.staff.joiningDate"}]}}, {"name": "STAFF_RELATIONSHIP", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.relationshipType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}}, {"name": "STAFF_RETIREMENT_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.staff.retirementDate"}]}}, {"name": "STAFF_TRANSFER_DAYS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.staff.transferDays"}]}}, {"name": "STAFF_TRANSFERS_DURING_DAYS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.staff.transfersDuringDay"}]}}, {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}}, {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "PASSENGER_ID", "pit-version": "VERSION", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_VERSION"}}}}, {"name": "ASSO_RESERVATION_DCS_PASSENGER_HISTO", "mapping": {"merge": {"key-columns": ["RESERVATION_ID", "PASSENGER_ID", "VERSION_RESERVATION", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-PNR"}, {"corr": "$.correlations[*]"}]}], "columns": [{"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"}, {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}}, {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}}, {"name": "VERSION_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toVersion"}]}}, {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.fromFullVersion"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"corr": "$.mainResource.current.image.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "secondary-pit-table"}}, "description": {"description": "It contains DCSPAX-PNR correlation information as seen by DCSPAX.", "granularity": "1 PAX-PNR", "links": ["???"]}}, {"name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO", "mapping": {"merge": {"key-columns": ["AIR_SEGMENT_PAX_ID", "SEGMENT_DELIVERY_ID", "VERSION_RESERVATION", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrDcspaxPnr.items[*]"}]}], "columns": [{"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.pnrAirSegmentId"}, {"items": "$.pnrTravelerId"}]}, "expr": "hashM({0})"}, {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"items": "$.dcsPassengerSegmentDeliveryId"}]}, "expr": "hashM({0})"}, {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}}, {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {"blocks": [{"items": "$.pnrAirSegmentId"}, {"items": "$.pnrTravelerId"}]}}, {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}}, {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"items": "$.dcsPassengerSegmentDeliveryId"}]}}, {"name": "VERSION_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toVersion"}]}}, {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.fromFullVersion"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.fromVersion"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}, {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}], "pit": {"type": "secondary-pit-table"}}, "description": {"description": "It contains DCSPAX_SEGDEL-PNR_AIR_SEG_PAX correlation information as seen by DCSPAX.", "granularity": "1 PAX_SEG_DEL-PNR_AIR_SEGMENT_PAX", "links": ["???"]}}]}