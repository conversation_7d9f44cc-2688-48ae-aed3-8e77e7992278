{
  "tables": [
    {
        "name": "FACT_RESERVATION_HISTO",
        "zorder-columns" : ["PNR_CREATION_DATE", "RECORD_LOCATOR"], // two columns for z-order for testing purposes
        "mapping" : {
          "merge": {
            "key-columns": ["RESERVATION_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
            {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
            {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reference"}]}},
            {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "NIP", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.nip"}]}},
            {"name": "GROUP_SIZE", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.size"}]}},
            {"name": "GROUP_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.group.name"}]}},
            {"name": "GROUP_SIZE_TAKEN", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.sizeTaken"}]}},
            {"name": "POINT_OF_SALE_OWNER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.owner.office.id"}, {"base":"$.owner.office.iataNumber"}, {"base":"$.owner.office.systemCode"}, {"base":"$.owner.office.agentType"}, {"base":"$.owner.login.cityCode"}, {"base":"$.owner.login.countryCode"}, {"base":"$.owner.login.numericSign"}, {"base":"$.owner.login.initials"}, {"base":"$.owner.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "POINT_OF_SALE_CREATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}, {"base": "$.creation.pointOfSale.office.iataNumber"}, {"base": "$.creation.pointOfSale.office.systemCode"}, {"base": "$.creation.pointOfSale.office.agentType"}, {"base": "$.creation.pointOfSale.login.cityCode"}, {"base": "$.creation.pointOfSale.login.countryCode"}, {"base": "$.creation.pointOfSale.login.numericSign"}, {"base": "$.creation.pointOfSale.login.initials"}, {"base": "$.creation.pointOfSale.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "POINT_OF_SALE_LAST_UPDATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.lastModification.pointOfSale.office.id"}, {"base": "$.lastModification.pointOfSale.office.iataNumber"}, {"base": "$.lastModification.pointOfSale.office.systemCode"}, {"base": "$.lastModification.pointOfSale.office.agentType"}, {"base": "$.lastModification.pointOfSale.login.cityCode"}, {"base": "$.lastModification.pointOfSale.login.countryCode"}, {"base": "$.lastModification.pointOfSale.login.numericSign"}, {"base": "$.lastModification.pointOfSale.login.initials"}, {"base": "$.lastModification.pointOfSale.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master": {
              "pit-key": "RESERVATION_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          }
        }
    },
    {
      "name": "FACT_RESERVATION",
      "zorder-columns" : ["PNR_CREATION_DATE"], // wrongly defining a z-order column on a view
      "latest": { "histo-table-name": "FACT_RESERVATION_HISTO" }
    },
    {
      "name": "FACT_AIR_SEGMENT_PAX_HISTO",
      "mapping": {
        "merge": {
          "key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"},
          {"tr": "$.travelers[*]"}
        ]}],
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr":  "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
          {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.id"}]}, "expr": "hashM({0})"},
          {
            "name": "SEGMENT_SCHEDULE_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [
              {"prod": "$.airSegment.marketing.flightDesignator.carrierCode"},
              {"prod": "$.airSegment.operating.flightDesignator.carrierCode"},
              {"prod": "$.airSegment.departure.iataCode"},
              {"prod": "$.airSegment.arrival.iataCode"},
              {"prod": "$.airSegment.marketing.flightDesignator.flightNumber"},
              {"prod": "$.airSegment.marketing.flightDesignator.operationalSuffix"},
              {"prod": "$.airSegment.operating.flightDesignator.flightNumber"},
              {"prod": "$.airSegment.operating.flightDesignator.operationalSuffix"},
              {"prod": "$.airSegment.departure.localDateTime"},
              {"prod": "$.airSegment.arrival.localDateTime"},
              {"prod": "$.airSegment.departure.terminal"},
              {"prod": "$.airSegment.arrival.terminal"},
              {"prod": "$.airSegment.operating.codeshareAgreement"},
              {"prod": "$.airSegment.aircraft.aircraftType"}
            ]},
            "expr": "hashM({0})"
          },
          {"name": "CABIN_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.cabin.code"}]}, "expr": "hashXS({0})"},
          {"name": "BOOKING_CLASS_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.code"}]}, "expr": "hashXS({0})"},
          {"name": "BOOKING_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.bookingStatusCode"}]}, "expr": "hashXS({0})"},
          {
            "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [
              {"prod": "$.airSegment.creation.pointOfSale.office.id"},
              {"prod": "$.airSegment.creation.pointOfSale.office.iataNumber"},
              {"prod": "$.airSegment.creation.pointOfSale.office.systemCode"},
              {"prod": "$.airSegment.creation.pointOfSale.office.agentType"},
              {"prod": "$.airSegment.creation.pointOfSale.login.cityCode"},
              {"prod": "$.airSegment.creation.pointOfSale.login.countryCode"},
              {"prod": "$.airSegment.creation.pointOfSale.login.numericSign"},
              {"prod": "$.airSegment.creation.pointOfSale.login.initials"},
              {"prod": "$.airSegment.creation.pointOfSale.login.dutyCode"}
            ]},
            "expr": "hashM({0})"
          },
          { // ADDED one to showcase access to different json sections
            "name": "FIRST_NAME", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"}]},
            "has-variable": true
          },
          {"name": "CABIN_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.cabin.code"}]}},
          {"name": "BOOKING_CLASS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.code"}]}},
          {"name": "BOOKING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.bookingStatusCode"}]}},
          {"name": "IS_INFO", "column-type": "booleanColumn", "sources": {"blocks": [{"prod": "$.airSegment.isInformational"}]}},
          {"name": "PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}]}}, // ADDED one for filtering the delivery
          {
            "name": "PDI_NOREC", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isNoRec"}]},
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_GOSHOW", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isGoShow"}]},
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_DISTRIBUTION_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].distributionId"}]},
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "PDI_TRANSFER_FLAG", "column-type": "strColumn",

            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].transferFlag"}]},
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {"name": "DEPARTURE_DATE_TIME", "column-type": "timestampColumn", "sources": {"blocks": [{"prod": "$.airSegment.departure.localDateTime"}]}},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.reference"}]}},
          {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.creation.dateTime"}]}},
          {"name": "VERSION", "column-type": "intColumn","sources": {"blocks": [{"root" : "$.mainResource.current.image.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      }
    }
  ],
  "links": [ ]
}