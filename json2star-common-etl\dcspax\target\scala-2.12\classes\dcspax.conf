versionExprVal: "bigint({0})"
versionTypeVal: "longColumn"
customer : "{CUSTOMER}"
//To create flightSegment REFERENCE_KEY with the departure date_time with format yyyy-MM-dd
//with {0} like 2005C08700090AE7-AA-3599-_-CMH-ORD-2022-09-10T16:24:24.592Z
//and expected output like 2005C08700090AE7-AA-3599-_-CMH-ORD-2022-09-10
flightSegmentRefKeyExpr: " substring_index({0}, '-',6) || '-' || date_format(substring({0}, position('-', {0}, length(substring_index({0}, '-',6)))+1),\"yyyy-MM-dd\")"
// in case of fk_id created from multiple fields, check if end point Id is not null - ex : ARRIVAL_GATES_ID
hashMIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) )"
hashSIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashS({0}) )"
createConsolidatedDocumentNumberReferenceKey: "if(split_part({0},'-',2) == '_', concat_ws('-',split_part({0},'-',1),split_part({0},'-',3)), concat_ws('-',split_part({0},'-',1),split_part({0},'-',2)))"
createConsolidatedCouponReferenceKey: "if(split_part({0},'-',2) == '_', concat_ws('-',split_part({0},'-',1),split_part({0},'-',3),split_part({0},'-',4)), concat_ws('-',split_part({0},'-',1),split_part({0},'-',2),split_part({0},'-',3)))"

// - CORR FLIGHT_DATE_ID
// Inputs: [1-3] Marketing Flight Part - [4-6] Operating Flight Part - [7-9] Departure Date
// Rules: take the {CUSTOMER} Flight Part
getCorrFlightDateId: "regexp_replace(concat_ws('-', if(split_part({0}, '-', 4) == '{CUSTOMER}', slice(split({0}, '-'), 4, 3), slice(split({0}, '-'), 1, 3)), slice(split({0}, '-'), 7, 3)), '-_|T..:..:..Z', '')"

// - CORR FLIGHT_SEGMENT_ID
// Inputs: [1-3] Marketing Flight Part - [4-6] Operating Flight Part - [7-12] Departure Date *2 - [13-14] Departure and Arrival Airport
// Rules: take the {CUSTOMER} Flight Part
getCorrFlightSegmentId: "regexp_replace(concat_ws('-', if(split_part({0}, '-', 1) == '{CUSTOMER}', slice(split({0}, '-'), 1, 3), slice(split({0}, '-'), 4, 3)), slice(split({0}, '-'), 7, 8)), '-_|T..:..:..Z', '')"

// - CORR CODESHARE_FLIGHT_SEGMENT_ID
// Inputs: [1-6] Marketing Flight Date Part - [7-12] Operating Flight Date Part - [13-15] Departure Date - [16-17] Departure and Arrival Airport
// Rules: - in case Marketing Carrier is not equal to Operating Carrier (codeshare), put the {CUSTOMER} Flight Date Part at the beginning and add the non-{CUSTOMER} Flight Date Part at the end
//        - in case Marketing Carrier is equal to Operating Carrier (prime), invalidate the ID (null)
getCorrCodeshareFlightSegmentId: "if(split_part({0}, '-', 1) != split_part({0}, '-', 7), regexp_replace(concat_ws('-', if(split_part({0}, '-', 1) == '{CUSTOMER}', slice(split({0}, '-'), 1, 6), slice(split({0}, '-'), 7, 6)), slice(split({0}, '-'), 13, 5), if(split_part({0}, '-', 1) != '{CUSTOMER}', slice(split({0}, '-'), 1, 6), slice(split({0}, '-'), 7, 6))), '-_|T..:..:..Z', ''), null)"

// - CORR FLIGHT_LEG_ID (same logic as CORR FLIGHT_SEGMENT_ID)
// Inputs: [1-3] Marketing Flight Part - [4-6] Operating Flight Part - [7-12] Departure Date *2 - [13-14] Departure and Arrival Airport
// Rules: take the {CUSTOMER} Flight Part
getCorrFlightLegId: "regexp_replace(concat_ws('-', if(split_part({0}, '-', 1) == '{CUSTOMER}', slice(split({0}, '-'), 1, 3), slice(split({0}, '-'), 4, 3)), slice(split({0}, '-'), 7, 8)), '-_|T..:..:..Z', '')"

"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.mainResource.current.image": "DcsPassengerPush.processedDcsPassenger",
      "$.mainResource.current.image.lastModification": "DcsPassengerPush.lastModification",
      "$.mainResource.current.image.version": "DcsPassengerPush.processedDcsPassenger.etag",
      "$.mainResource.current.image.lastModification.user.officeId": "DcsPassengerPush.lastModification.user.officeID",
      "$.mainResource.current.image.segmentDeliveries.associatedAirTravelDocuments.coupons": "DcsPassengerPush.processedDcsPassenger.segmentDeliveries.associatedAirTravelDocuments.airCoupons",
      "$.mainResource.current.image.passenger.passengerType": "DcsPassengerPush.processedDcsPassenger.passenger.flightPassengerType",
      "$.mainResource.current.image.lastModification.user.workstation": "DcsPassengerPush.lastModification.user.workStation",
      "$.mainResource.current.image.services.nIP": "DcsPassengerPush.processedDcsPassenger.services.numberInParty",
      "$.mainResource.current.image.segmentDeliveries.services.nIP": "DcsPassengerPush.processedDcsPassenger.segmentDeliveries.services.numberInParty",
      "$.mainResource.current.image.segmentDeliveries.legDeliveries.seating.chargeableSeat.nIP": "DcsPassengerPush.processedDcsPassenger.segmentDeliveries.legDeliveries.seating.chargeableSeat.numberInParty"
    }
  }
},
"tables": [
  {
    "name": "FACT_PASSENGER",
    "zorder-columns": ["INTERNAL_ZORDER"], // IMPORTANT: it should be one of the first N columns, where N is delta.dataSkippingNumIndexedCols
    "latest": {
      "histo-table-name": "FACT_PASSENGER_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_PASSENGER_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Passenger",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains various passenger personal information: name, pax type, nationality, staff information if applicable, ...",
        "granularity": "1 passenger in DCS"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{ "blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.id"}]}
          , "meta": {"description": {"value": "Functional key: UCI", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passenger.name.firstName"}]}
          , "meta": {"gdpr-zone": "red"}},
        {"name": "LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passenger.name.lastName"}]}
          , "meta": {"gdpr-zone": "red"}},
        {"name": "TITLE", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passenger.name.title"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "GENDER", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passenger.gender"}]}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "BIRTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base":"$.passenger.dateOfBirth"}]}
          , "meta": {"gdpr-zone": "red"}},
        {"name": "AGE", "column-type": "intColumn", "sources": {"blocks": [{"base":"$.passenger.age"}]}
          , "meta": {"gdpr-zone": "red"}},
        {"name": "PASSENGER_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passenger.passengerType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "GROUP_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.groupName"}]}
          , "meta": {"gdpr-zone": "red"}},
        {"name": "SPECIAL_SEAT", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.passenger.specialSeat"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "STAFF_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.staff.category"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "STAFF_COMPANY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.staff.companyCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "STAFF_COMPANY_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.staff.companyName"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "STAFF_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.staff.id"}]}
			, "meta": {"gdpr-zone": "orange"}},
        {"name": "STAFF_BOOKING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.staff.idType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "STAFF_RELATIONSHIP", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.staff.relationshipType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "STAFF_JOINING_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base":"$.staff.joiningDate"}]}
          , "meta": {"gdpr-zone": "red"}},
        {"name": "STAFF_RETIREMENT_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base":"$.staff.retirementDate"}]}
          , "meta": {"gdpr-zone": "red"}},
        {"name": "IS_MASTER_RECORD", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.isMasterRecord"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "IS_SAME_PHYSICAL_CUSTOMER", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.isSamePhysicalCustomer"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "IS_SYSTEM_MARKED_SPC", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.isSystemMarkedSPC"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "CPR_FEED_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.cprFeedType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base":"$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base":"$.version"}]},"expr" : ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master" : {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_VERSION"
        }
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CONTACT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_CONTACT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CONTACT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Passenger",
    "mapping": {
      "description": {
        "description": "Contains contact details of the passenger (address, phone, email) relative to a passenger or a specific flight segment.",
        "granularity": "1 contact element for 1 passenger or 1 segment delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "CONTACT_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "pax", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"contact": "$.passenger.contacts[*]"}]}},
        { "name": "segDel", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"contact": "$.contacts[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "CONTACT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "pax","blocks": [ {"base": "$.id"}, {"contact": "$.id"}, {"contact": "$.purpose"}]},
              {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"contact": "$.id"}, {"contact": "$.purpose"}]}
            ]
          }, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "pax","blocks": [ {"base": "$.id"}, {"contact": "$.id"}, {"contact": "$.purpose"}]},
              {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"contact": "$.id"}, {"contact": "$.purpose"}]}
            ]
          }
          , "meta": {"description": {"value": "Functional key: UCI-contact-purpose, UCI-UPI-contact-purpose", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "ADDRESS_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.address.category"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ADDRESS_CITY", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.address.cityName"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ADDRESS_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.address.countryCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_COUNTRY"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ADDRESS_POSTAL_CODE", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.address.postalCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ADDRESS_STATE", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.address.stateCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ADDRESS_LINES", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.address.lines[*]"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "ADDRESSEE_FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.addresseeName.firstName"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "PHONE", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.phone.text"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "PURPOSES", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.purpose[*]"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "IS_DECLINED", "column-type": "booleanColumn", "sources": {"blocks": [{"contact": "$.isDeclined"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"contact": "$.carrierCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "RELATES_TO", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "pax","literal": "PASSENGER"},
              {"rs-name": "segDel","literal": "SEGMENT_DELIVERY"}
            ]
          }
          , "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_MEMBERSHIP",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_MEMBERSHIP_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_MEMBERSHIP_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Passenger",
    "mapping": {
      "description": {
        "description": "Contains information on loyalty membership of the passenger: number, airline/alliance tiers, priority code, ...",
        "granularity": "1 loyalty membership for 1 segment delivery, 1 service delivery or 1 passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "MEMBERSHIP_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "pax", "rs" :  { "blocks": [ {"base": "$.mainResource.current.image"}, {"freq": "$.frequentFlyer[*]"}]}},
        { "name": "seg", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"freq": "$.frequentFlyer[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "pax", "blocks": [ {"base": "$.id"}, {"freq": "$.id"}]},
              {"rs-name": "seg", "blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"freq": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "pax","blocks": [ {"base": "$.id"}, {"freq": "$.id"}]},
              {"rs-name": "seg","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"freq": "$.id"}]}
            ]
          }
          , "meta": {"description": {"value": "Functional key: UCI-frequent flyer, UCI-UPI-frequent flyer", "rule": "replace"}, "gdpr-zone": "red"}
        },
        {
          "name": "MEMBERSHIP_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.frequentFlyerNumber"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "APPLICABLE_AIRLINE", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.applicableAirlineCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "AIRLINE_TIER_CODE", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.airlineLevel.code"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "AIRLINE_TIER_NAME", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.airlineLevel.name"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "AIRLINE_TIER_PRIORITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.airlineLevel.priorityCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ALLIANCE", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.allianceLevel.companyCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ALLIANCE_TIER_CODE", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.allianceLevel.code"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ALLIANCE_TIER_NAME", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.allianceLevel.name"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ALLIANCE_TIER_PRIORITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.allianceLevel.priorityCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.serviceCode"}]}, "create-fk": {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "expr": "hashS({0})"
          , "fk": [{"table": "DIM_SERVICE", "column": "SERVICE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CONFIRMATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"freq": "$.confirmationStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_MEMBERSHIP_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RELATES_TO", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "pax","literal": "PASSENGER"},
              {"rs-name": "seg","literal": "SEGMENT_DELIVERY"}
            ]
          }
          , "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "seg","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "AIRLINE_TIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"freq": "$.airlineLevel.companyCode"}, {"freq": "$.airlineLevel.code"}, {"freq": "$.airlineLevel.name"}, {"freq": "$.airlineLevel.priorityCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_MEMBERSHIP_TIER", "column": "MEMBERSHIP_TIER_ID"}]
        },
        {
          "name": "ALLIANCE_TIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"freq": "$.allianceLevel.companyCode"}, {"freq": "$.allianceLevel.code"}, {"freq": "$.allianceLevel.name"}, {"freq": "$.allianceLevel.priorityCode"}]}, "expr": "hashM({0})",
          "fk": [{"table": "DIM_MEMBERSHIP_TIER", "column": "MEMBERSHIP_TIER_ID"}]
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_IDENTITY_DOCUMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_IDENTITY_DOCUMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_IDENTITY_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Passenger",
    "mapping": {
      "description": {
        "description": "Contains details about the passenger's identity document: personal information, issuance, validity, ...",
        "granularity": "1 identity document for 1 passenger or 1 regulatory document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "IDENTITY_DOCUMENT_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "segRegDoc", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"regDoc": "$.regulatoryDocuments[*]"}, {"doc": "$.document"}]}},
        {"name": "regDoc", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"regDoc": "$.regulatoryDocuments[*]"}, {"doc": "$.document"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "IDENTITY_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "segRegDoc", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
              {"rs-name": "regDoc", "blocks": [{"base": "$.id"}, {"regDoc": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]}
            ]
          }, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "segRegDoc", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
              {"rs-name": "regDoc", "blocks": [{"base": "$.id"}, {"regDoc": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]}
            ]
          }
          , "meta": {"description": {"value": "Functional key: UCI-UPI-regulatory doc-country-number, UCI-regulatory doc-country-number", "rule": "replace"}, "gdpr-zone": "red"}
        },
        {
          "name": "NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.number"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.documentType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_IDENTITY_DOCUMENT_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.name.firstName"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.name.lastName"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "EXPIRY_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"doc": "$.expiryDate"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ISSUANCE_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"doc": "$.issuanceDate"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ISSUANCE_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.issuanceCountry"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_COUNTRY"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ISSUANCE_LOCATION", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.issuanceLocation"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RELATES_TO", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segRegDoc", "literal": "SEGMENT_REGULATORY_DOCUMENT"},
              {"rs-name": "regDoc", "literal": "REGULATORY_DOCUMENT"}
            ]
          }
          , "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segRegDoc", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REGULATORY_DOCUMENT_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segRegDoc", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}]},
              {"rs-name": "regDoc", "blocks": [{"base": "$.id"}, {"regDoc": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_REGULATORY_DOCUMENT_HISTO", "column": "REGULATORY_DOCUMENT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_REGULATORY_DOCUMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_REGULATORY_DOCUMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_REGULATORY_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment Delivery",
    "mapping": {
      "description": {
        "description": "Contains information on regulatory documents (which are based on identity documents) for the passenger on a flight segment: entry port, input device information, ...",
        "granularity": "1 regulatory document for 1 segment delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "REGULATORY_DOCUMENT_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "segDel", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"regDoc": "$.regulatoryDocuments[*]"}]}},
        {"name": "pax", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"regDoc": "$.regulatoryDocuments[*]"}, {"doc": "$.document"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "REGULATORY_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "segDel", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}]},
              {"rs-name": "pax", "blocks": [{"base": "$.id"}, {"regDoc": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "segDel", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}]},
              {"rs-name": "pax", "blocks": [{"base": "$.id"}, {"regDoc": "$.id"}]}
            ]
          }
          , "meta": {"description": {"value": "Functional key: UCI-UPI-regulatory document", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "VALID_FOR_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"regDoc": "$.validForCarrier"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "IS_CARRIED", "column-type": "booleanColumn", "sources": {"blocks": [{"regDoc": "$.isCarried"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORDED_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"regDoc": "$.recordedDocumentType"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "INPUT_ENTRY_METHOD", "column-type": "strColumn", "sources": {"blocks": [{"regDoc": "$.inputSource.entryMethod"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RELATES_TO", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segDel", "literal": "SEGMENT_DELIVERY"},
              {"rs-name": "pax", "literal": "PASSENGER"}
            ]
          }
          , "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segDel", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_REVENUE_INTEGRITY_CHECK",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_REVENUE_INTEGRITY_CHECK_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_REVENUE_INTEGRITY_CHECK_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment Delivery",
    "mapping": {
      "description": {
        "description": "Contains the list of revenue integrity checks performed on a flight segment",
        "granularity": "1 revenue integrity check for 1 segment delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "REVENUE_INTEGRITY_CHECK_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"ric": "$.revenueIntegrity.checks[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "REVENUE_INTEGRITY_CHECK_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"ric": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"ric": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: UCI-UPI-check", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "CHECK_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"ric": "$.id"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CHECK_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"ric": "$.checkType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REVENUE_INTEGRITY_CHECK_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CHECK_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"ric": "$.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REVENUE_INTEGRITY_CHECK_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_REVENUE_INTEGRITY_COMMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_REVENUE_INTEGRITY_COMMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_REVENUE_INTEGRITY_COMMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment Delivery",
    "mapping": {
      "description": {
        "description": "Contains the list of revenue integrity comments on a flight segment",
        "granularity": "1 revenue integrity comment for 1 segment delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "REVENUE_INTEGRITY_COMMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"ric": "$.revenueIntegrity.comments[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "REVENUE_INTEGRITY_COMMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"ric": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"ric": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: UCI-UPI-comment", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "COMMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"ric": "$.id"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"ric": "$.commentType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REVENUE_INTEGRITY_COMMENT_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "COMMENT_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"ric": "$.description"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SEGMENT_DELIVERY",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SEGMENT_DELIVERY_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SEGMENT_DELIVERY_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment Delivery",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information on the flight segments delivered to the passenger: voluntary denied boarding, downgrade, disruption information, staff information, ...",
        "granularity": "1 segment delivery for 1 passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"seg": "$.segment"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: UCI-UPI", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "UPI", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.id"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.cabin"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.class"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BOOKING_CLASS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOOKING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BOOKING_STATUS"}]}
          , "meta": {"description": {"value": "Booking status of the segment", "rule": "replace"}, "example": {"value": "HK", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "BOARD_POINT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.departure.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OFF_POINT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.arrival.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.number"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.suffix"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.operating.carrierCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.operating.number"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.operating.suffix"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.departure.at"}]}
          , "meta": {"description": {"value": "The scheduled departure date time of the flight segment, in UTC", "rule": "replace"}, "example": {"value": "2018-09-14T08:10:00Z", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.arrival.at"}]}
          , "meta": {"description": {"value": "The scheduled arrival date time of the flight segment, in UTC", "rule": "replace"}, "example": {"value": "2018-09-14T08:10:00Z", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "NATIONALITY", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.nationality"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "VOLUNTEER_DENIED_BOARDING", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.volunteerDeniedBoarding"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "VOLUNTEER_DOWNGRADE", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.volunteerDowngrade"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DISRUPTION_ORIGINAL_DESTINATION", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.passengerDisruption.originalDestination"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DISRUPTION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.passengerDisruption.productState.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DISRUPTION_REASONS", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.passengerDisruption.productState.reason[*]"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashM({0})", "fk": [{"table": "DIM_DISRUPTION_REASONS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STAFF_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.staff.category"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_STAFF_CATEGORY"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STAFF_COMPANY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.staff.companyCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STAFF_COMPANY_NAME", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.staff.companyName"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STAFF_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.staff.id"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "STAFF_BOOKING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.staff.idType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_STAFF_BOOKING_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STAFF_RELATIONSHIP", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.staff.relationshipType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_STAFF_RELATIONSHIP"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STAFF_JOINING_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"segdel":"$.staff.joiningDate"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "STAFF_RETIREMENT_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"segdel":"$.staff.retirementDate"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "DCS_PRODUCT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.dcsProductType"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ASSO_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.onwardId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_INFORMATIVE_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.informativeOnwardId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_ACCEPTED_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.acceptedOnwardId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_ALTERNATE_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.alternateOnwardId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_DISRUPTED_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.disruptedOnwardId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_MISCONNECTED_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.misconnectedOnwardId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_TCI_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.tciOnwardId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_INFANT_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.infantId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_CBBG_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.cbbgId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_EXST_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel":"$.associatedPassengerSegments.exstId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "UCI_BEFORE_MERGE", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.uniqueCustomerIdentifier"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID_BEFORE_MERGE", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.uniqueCustomerIdentifier"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR_BEFORE_MERGE", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        }
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.at"}]}, "expr": "hashM("${flightSegmentRefKeyExpr}")"
          , "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SERVICE_DELIVERY",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SERVICE_DELIVERY_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SERVICE_DELIVERY_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Service Delivery",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information on services delivered to the passenger: service type and code, service provider, status, chargeable document, reason for issuance, waiver, ...",
        "granularity": "1 service delivery for 1 passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SERVICE_DELIVERY_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "pax", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"serv": "$.services[*]"}]}},
        {"name": "segDel", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"serv": "$.services[*]"}]}},
        {"name": "segLegSeat", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"serv": "$.seating.chargeableSeat"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "SERVICE_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "pax", "blocks": [{"base": "$.id"}, {"serv": "$.id"}]},
              {"rs-name": "segDel", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"serv": "$.id"}]},
              {"rs-name": "segLegSeat", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "pax", "blocks": [{"base": "$.id"}, {"serv": "$.id"}]},
              {"rs-name": "segDel", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"serv": "$.id"}]},
              {"rs-name": "segLegSeat", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}]}
            ]
          }
          , "meta": {"description": {"value": "Functional key: UCI-service, UCI-UPI-service, UCI-UPI-leg-seat", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "SERVICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.id"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "CHARGEABLE_SERVICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.chargeableServiceId"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.code"}]}, "create-fk": {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "expr": "hashS({0})"
          , "fk": [{"table": "DIM_SERVICE", "column": "SERVICE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.subType"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_SERVICE_SUBTYPE", "column": "SERVICE_SUBTYPE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.description"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "NUMBER_IN_PARTY", "column-type": "intColumn", "sources": {"blocks": [{"serv": "$.nIP"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOOKING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.statusCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BOOKING_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PAYMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.paymentStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SERVICE_PROVIDER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.serviceProvider.code"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "IS_ISSUANCE_REQUIRED", "column-type": "booleanColumn", "sources": {"blocks": [{"serv": "$.isIssuanceRequired"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CHARGEABLE_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.chargeableServiceDocument.documentType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CHARGEABLE_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.chargeableServiceDocument.number"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "CHARGEABLE_DOCUMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.chargeableServiceDocument.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.priceCategory.code"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REASON_FOR_ISSUANCE_SUBCODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.priceCategory.subCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "WAIVER_AUTHORIZER", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.waiver.authoriser"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashM({0})"}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "WAIVER_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.waiver.reasonCode"}]}
          , "create-fk": {"name": "WAIVER_REASON_ID", "column-type": "binaryStrColumn", "expr": "hashM({0})", "fk": [{"table": "DIM_WAIVER_REASON", "column": "WAIVER_REASON_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "WAIVER_REASON_TEXT", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.waiver.reasonText"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "RELATES_TO", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "pax", "literal": "PASSENGER"},
              {"rs-name": "segDel", "literal": "SEGMENT_DELIVERY"},
              {"rs-name": "segLegSeat", "literal": "LEG_DELIVERY_SEATING"}
            ]
          }
          , "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segLegSeat", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}]},
              {"rs-name": "segDel", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segLegSeat", "blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}
            ]
          }, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_LEG_DELIVERY_HISTO", "column": "LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn",
          "sources": {"blocks": [{"serv": "$.priceCategory.code"}]}, "expr": "hashS({0})"
          , "fk": [{"table": "DIM_REASON_FOR_ISSUANCE", "column": "REASON_FOR_ISSUANCE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_LEG_DELIVERY",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_LEG_DELIVERY_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_LEG_DELIVERY_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg Delivery",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains details about the flight legs delivered to a passenger: checkin/acceptance, boarding behavior, onload, regrade, seating, ... ",
        "granularity": "1 leg delivery for 1 segment delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: UCI-UPI-leg", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.departure.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.arrival.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.operatingFlight.carrierCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.operatingFlight.number"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.operatingFlight.suffix"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_DATE_LOCAL", "column-type": "dateColumn", "sources": {"blocks": [{"legdel": "$.departure.at"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.travelCabinCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.acceptance.acceptanceType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_ACCEPTANCE_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_CHANNEL", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.acceptance.channel"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_ACCEPTANCE_CHANNEL"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.acceptance.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_ACCEPTANCE_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_CANCELLATION_REASON", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.acceptance.cancellationReason"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "ACCEPTANCE_FORCE_REASON", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.acceptance.forceAcceptanceReason"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "ACCEPTANCE_STANDBY_REASON", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.acceptance.standbyReason"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_IS_ADVANCE_ACCEPTED", "column-type": "booleanColumn", "sources": {"blocks": [{"legdel": "$.acceptance.isAdvanceAccepted"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_IS_FORCE_ACCEPTED", "column-type": "booleanColumn", "sources": {"blocks": [{"legdel": "$.acceptance.isForceAccepted"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_IS_FROZEN", "column-type": "booleanColumn", "sources": {"blocks": [{"legdel": "$.acceptance.isFrozen"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_PHYSICAL_LOCATION", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.acceptance.physicalAcceptanceLocation"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_ACCEPTANCE_PHYSICAL_LOCATION"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_SECURITY_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.acceptance.securityNumber"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "BOARDING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.boardingStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BOARDING_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_ZONE", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.boardingZone"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_WARNING", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.warning"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_ERROR", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.error"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_PRINT_CHANNEL", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.boardingPassPrint.channel"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BOARDING_PRINT_CHANNEL"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_PRINT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.boardingPassPrint.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BOARDING_PRINT_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_LOG_DEVICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.trackingLog.deviceId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_LOG_DEVICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.trackingLog.referenceDeviceType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_DEVICE_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_LOG_TIMESTAMP_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"legdel": "$.boarding.trackingLog.localDateTime"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_LOG_TIMESTAMP_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"legdel": "$.boarding.trackingLog.utcDateTime"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_LOG_SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.boarding.trackingLog.logSource"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_LOG_SOURCE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ONLOAD_CABIN", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.onload.cabinCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ONLOAD_PRIORITY", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.onload.priority"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ONLOAD_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.onload.status"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ONLOAD_EDIT_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.onload.edit.reasonCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ONLOAD_EDIT_REASON_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.onload.edit.reasonFreeText"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "REGRADE_PROPOSED_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeProposed.regradeType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGRADE_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REGRADE_PROPOSED_CABIN", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeProposed.cabinCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REGRADE_PROPOSED_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeProposed.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGRADE_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REGRADE_PROPOSED_PRIORITY", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeProposed.priority"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REGRADE_PROPOSED_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeProposed.reasonCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGRADE_REASON"}]}
          , "meta": {"description": {"value": "The reason code for the proposed regrade", "rule": "replace"}, "example": {"value": "OVER_SOLD_CURRENT_FLIGHT", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REGRADE_PROPOSED_REASON_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeProposed.reasonFreeText"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "REGRADE_PROPOSED_AUTHORIZER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeProposed.authoriserReference"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "REGRADE_DELIVERED_CABIN", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeDelivered.cabinCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REGRADE_DELIVERED_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeDelivered.reasonCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGRADE_REASON"}]}
          , "meta": {"description": {"value": "The reason code for the delivered regrade", "rule": "replace"}, "example": {"value": "OVER_SOLD_CURRENT_FLIGHT", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REGRADE_DELIVERED_REASON_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeDelivered.reasonFreeText"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "REGRADE_DELIVERED_AUTHORIZER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.regradeDelivered.authoriserReference"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "SEATING_SEAT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.seating.seat.number"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SEATING_SEAT_CHARACTERISTICS", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.seating.seat.characteristicsCodes[*]"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_SEAT_CHARACTERISTICS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SEATING_IS_CHARGEABLE_SEAT", "column-type": "booleanColumn", "sources": {"blocks": [{"legdel": "$.seating.isChargeableSeat"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SEATING_EMERGENCY_EXIT_SUITABILITY", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.seating.emergencyExitSuitability"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SEATING_PREFERENCES_SEAT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.seating.seatPreferences.number"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SEATING_PREFERENCES_SEAT_CHARACS", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.seating.seatPreferences.characteristicsCodes[*]"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_SEAT_CHARACTERISTICS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.segment.id"}, {"legdel": "$.departure.iataCode"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_LEG_DELIVERY_COMMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_LEG_DELIVERY_COMMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_LEG_DELIVERY_COMMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg Delivery",
    "mapping": {
      "description": {
        "description": "Contains the list of comments for a flight leg delivery, including priority flag, usage and status",
        "granularity": "1 comment for 1 leg delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "LEG_DELIVERY_COMMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"com": "$.comments[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "LEG_DELIVERY_COMMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"com": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"com": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: UCI-UPI-leg-comment", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.text"}]}
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "PRIORITY", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.priority"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_DELIVERY_COMMENT_PRIORITY"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "USAGE", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.usage"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_DELIVERY_COMMENT_USAGE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DELIVERY_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"com": "$.delivery.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_DELIVERY_COMMENT_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_LEG_DELIVERY_HISTO", "column": "LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_COMPENSATION_VOUCHER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COMPENSATION_VOUCHER_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_COMPENSATION_VOUCHER_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg Delivery",
    "mapping": {
      "description": {
        "description": "Contains detailed information on the compensation and the corresponding voucher: status, authorization, voucher number, ...",
        "granularity": "1 compensation voucher for 1 leg delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COMPENSATION_VOUCHER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "name": "vouch", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"comp": "$.compensations[*]"}, {"voucher": "$.vouchers[*]"}]}},
        { "name": "compensation", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"comp": "$.compensations[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "COMPENSATION_VOUCHER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {
          "root-specific": [
            {"rs-name": "vouch","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"comp": "$.id"}, {"voucher": "$.id"}]},
            {"rs-name": "compensation","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"comp": "$.id"}]}
          ]
        }, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {
          "root-specific": [
            {"rs-name": "vouch","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"comp": "$.id"}, {"voucher": "$.id"}]},
            {"rs-name": "compensation","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"comp": "$.id"}]}
          ]
        }
          , "meta": {"description": {"value": "Functional key: UCI-UPI-leg-compensation(-voucher)", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "COMPENSATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.id"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "COMPENSATION_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.categoryDescription"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "COMPENSATION_AUTH_AUTHORIZER", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.authorisation.authorizer"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "COMPENSATION_AUTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"comp": "$.authorisation.date"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "COMPENSATION_AUTH_QUANTITY", "column-type": "intColumn", "sources": {"blocks": [{"comp": "$.authorisation.quantity"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "COMPENSATION_AUTH_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.authorisation.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_COMPENSATION_AUTHORIZATION_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "COMPENSATION_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.reason.code"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_COMPENSATION_REASON"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "COMPENSATION_REASON_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.reason.label"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "VOUCHER_IDENTIFIER", "column-type": "strColumn", "sources": {
          "root-specific": [{"rs-name": "vouch", "blocks": [{"voucher": "$.id"}]}]
        }
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "VOUCHER_DESCRIPTION", "column-type": "strColumn", "sources": {
          "root-specific": [{"rs-name": "vouch", "blocks": [{"voucher": "$.description"}]}]
        }
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "VOUCHER_COMMENT", "column-type": "strColumn", "sources": {
          "root-specific": [{"rs-name": "vouch", "blocks": [{"voucher": "$.comment"}]}]
        }
          , "meta": {"gdpr-zone": "red"}
        },
        {
          "name": "VOUCHER_TYPE", "column-type": "strColumn", "sources": {
          "root-specific": [{"rs-name": "vouch", "blocks": [{"voucher": "$.voucherType"}]}]
        }, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "VOUCHER_STATUS", "column-type": "strColumn", "sources": {
          "root-specific": [{"rs-name": "vouch", "blocks": [{"voucher": "$.status"}]}]
        }, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "VOUCHER_PROVIDER", "column-type": "strColumn", "sources": {
          "root-specific": [{"rs-name": "vouch", "blocks": [{"voucher": "$.providerCode"}]}]
        }
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "VOUCHER_IS_REPRINTABLE", "column-type": "booleanColumn", "sources": {
          "root-specific": [{"rs-name": "vouch", "blocks": [{"voucher": "$.isReprintable"}]}]
        }
          , "meta": {"example": {"value": "TRUE", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VOUCHER_PRINT_STATUS", "column-type": "strColumn", "sources": {
          "root-specific": [{"rs-name": "vouch", "blocks": [{"voucher": "$.printStatus"}]}]
        }, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_LEG_DELIVERY_HISTO", "column": "LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_REGULATORY_CHECK",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_REGULATORY_CHECK_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_REGULATORY_CHECK_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg Delivery",
    "mapping": {
      "description": {
        "description": "Contains the list of regulatory checks performed on a flight leg, with the regulatory program, check name and time, and status ",
        "granularity": "1 regulatory check for 1 leg delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "REGULATORY_CHECK_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"check": "$.regulatoryChecks[*]"}, {"status": "$.statuses[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "REGULATORY_CHECK_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"check": "$.id"}, {"status": "$.id"}, {"check": "$.regulatoryProgram.name"}, {"check": "$.regulatoryProgram.countryCode"}, {"status": "$.statusType"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"check": "$.id"}, {"status": "$.id"}, {"check": "$.regulatoryProgram.name"}, {"check": "$.regulatoryProgram.countryCode"}, {"status": "$.statusType"}]}
          , "meta": {"description": {"value": "Functional key: UCI-UPI-leg-check-check status-program-program country-check status type", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "REGULATORY_PROGRAM_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"check": "$.regulatoryProgram.countryCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_COUNTRY"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REGULATORY_PROGRAM_NAME", "column-type": "strColumn", "sources": {"blocks": [{"check": "$.regulatoryProgram.name"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGULATORY_PROGRAM"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CHECK_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"check": "$.id"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ASSESSMENT_TIME", "column-type": "timestampColumn", "sources": {"blocks": [{"check": "$.assesmentTime"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STATUS_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.id"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STATUS_IS_VERIFIED_IDENTIFIER", "column-type": "booleanColumn", "sources": {"blocks": [{"status": "$.isVerifiedId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.statusCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STATUS_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.statusType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"
          , "fk": [{"table": "DIM_REGULATORY_CHECK_STATUS_TYPE", "column": "REGULATORY_CHECK_STATUS_TYPE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STATUS_MESSAGE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.message.code"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGULATORY_CHECK_COMMENT_CODE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STATUS_MESSAGE_COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.message.commentType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGULATORY_CHECK_COMMENT_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "STATUS_MESSAGE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.message.description"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OVERRIDE_MESSAGE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.override.code"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGULATORY_CHECK_COMMENT_CODE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OVERRIDE_MESSAGE_COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.override.commentType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_REGULATORY_CHECK_COMMENT_TYPE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OVERRIDE_MESSAGE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.override.description"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_LEG_DELIVERY_HISTO", "column": "LEG_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
        {
          "name": "STATUS_ID", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.statusCode"}]}, "expr": "hashS({0})"
          , "fk": [{"table": "DIM_REGULATORY_CHECK_STATUS", "column": "REGULATORY_CHECK_STATUS_ID"}]
          , "meta": {"gdpr-zone": "green"}
        },
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_TRANSFER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_TRANSFER_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_TRANSFER_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Flight",
    "mapping": {
      "description": {
        "description": "Contains information on itinerary changes, i.e. replacing flight segments by other flight segments: replacement reason, and references to the involved flight segments",
        "granularity": "1 flight transfer for 1 passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_TRANSFER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"ftrans": "$.flightTransfers[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "FLIGHT_TRANSFER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"ftrans": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"ftrans": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: UCI-transfer", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "TRANSFER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"ftrans": "$.id"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "DISRUPTION_TRANSFER_REASON", "column-type": "strColumn", "sources": {"blocks": [{"ftrans": "$.disruptionTransferReason"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"description": {"value": "Reason for the flight transfer in case of a disruption", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"ftrans": "$.subType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_TRANSFER_SUBTYPE"}]}
          , "meta": {"description": {"value": "Subtype of the flight transfer", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATA_TRANSFER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"ftrans": "$.dataTransferStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"description": {"value": "Status of data transfer", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DCS_TRANSFER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"ftrans": "$.dcsTransferStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"description": {"value": "Status of DCS transfer", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Flight",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information about a flight segment: board/off points, marketing/operating flight information, departure/arrival times, cabin and booking class, ...",
        "granularity": "1 flight segment for 1 flight transfer or 1 segment delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_SEGMENT_ID", "RELATES_TO", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "segDel", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"middle": "$.segmentDeliveries[*]"}, {"seg": "$.segment"}]}},
        {"name": "fromFlt", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"middle": "$.flightTransfers[*]"}, {"seg": "$.fromSegments[*]"}]}},
        {"name": "toFlt", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"middle": "$.flightTransfers[*]"}, {"seg": "$.toSegments[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.at"}]}, "expr": "hashM("${flightSegmentRefKeyExpr}")"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.at"}]}, "expr": ${flightSegmentRefKeyExpr}
          , "meta": {"description": {"value": "Functional key: UCI-carrier-flight number-suffix-board-off-departure date", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "BOARD_POINT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.departure.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OFF_POINT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.arrival.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.number"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.suffix"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.operating.carrierCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.operating.number"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.operating.suffix"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_DEPARTURE_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"seg": "$.departure.at"}]}, "expr": "date_format({0},\"yyyy-MM-dd\")"
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_ARRIVAL_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"seg": "$.arrival.at"}]}, "expr": "date_format({0},\"yyyy-MM-dd\")"
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "RELATES_TO", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segDel", "literal": "SEGMENT_DELIVERY"},
              {"rs-name": "fromFlt", "literal": "TRANSFER_FROM"},
              {"rs-name": "toFlt", "literal": "TRANSFER_TO"}
            ]
          }
          , "meta": {"description": {"value": "Indicates to which parent fact table the record belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_TRAVEL_DOCUMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_TRAVEL_DOCUMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_TRAVEL_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Ticketing",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains high-level information on ticket and/or EMDs associated to the DCS passenger: document numbers, association status, ...",
        "granularity": "1 ticket or EMD for 1 passenger"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "TRAVEL_DOCUMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { // shouldNotBeEmpty : if the travel document have no id, don t generate records in this table
          "blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}, {"id": "$.primaryDocumentNumber"}]
        },
        {
          "blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedAirTravelDocuments[*]"}, {"atd": "$.associatedDocuments[*]"}, {"id": "$.primaryDocumentNumber"}]
        },
        {
          "blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.previousTicket"}, {"id": "$.primaryDocumentNumber"}]
        },
        {
          "blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.previousTicket"}, {"atd": "$.associatedDocuments[*]"}, {"id": "$.primaryDocumentNumber"}]
        },
        {
          "blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"srvd": "$.legDeliveries[*].seating.chargeableSeat.chargeableServiceDocument"}, {"id": "$.number"}]
        }
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"atd": "$.id"}, {"id": "$.value"}]}, "expr": hashM(${createConsolidatedDocumentNumberReferenceKey})
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"atd": "$.id"}, {"id": "$.value"}]}, "expr": ${createConsolidatedDocumentNumberReferenceKey}
          , "meta": {"description": {"value": "Functional key: travel document number", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"id": "$.value"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "CONJUNCTIVE_DOCUMENT_NUMBERS", "column-type": "strColumn", "sources": {"blocks": [{"atd": "$.conjunctiveDocumentNumbers[*]"}]}
          , "meta": {"description": {"value": "Indicates the list of conjunctive ticket numbers, if any.", "rule": "replace"}, "example": {"value": "1234567890124, 1234567890125", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"atd": "$.documentType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ASSOCIATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"atd": "$.associationStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_TRAVEL_DOCUMENT_ASSOCIATION_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SOURCE_SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_COUPON",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_COUPON_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_COUPON_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Ticketing",
    "mapping": {
      "description": {
        "description": "Contains information on the coupons associated to a travel document: status, reason for issuance, ...",
        "granularity": "1 coupon for 1 travel document"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}, {"coupon": "$.coupons[*]"}, {"shouldNotBeEmpty": "$.number"}]},
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedAirTravelDocuments[*]"}, {"atd": "$.associatedDocuments[*]"}, {"coupon": "$.coupons[*]"}, {"shouldNotBeEmpty": "$.number"}]},
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.previousTicket"}, {"coupon": "$.coupons[*]"}, {"shouldNotBeEmpty": "$.number"}]},
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.previousTicket"}, {"atd": "$.associatedDocuments[*]"}, {"coupon": "$.coupons[*]"}, {"shouldNotBeEmpty": "$.number"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"coupon": "$.id"}, {"atd": "$.primaryDocumentNumber"}, {"coupon": "$.number"}]}, "expr": hashM(${createConsolidatedCouponReferenceKey})
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"coupon": "$.id"}, {"atd": "$.primaryDocumentNumber"}, {"coupon": "$.number"}]}, "expr": ${createConsolidatedCouponReferenceKey}
          , "meta": {"description": {"value": "Functional key: UCI-coupon", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"coupon": "$.number"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [{"coupon": "$.status"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_COUPON_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"atd": "$.id"}, {"atd": "$.primaryDocumentNumber"}]}, "expr": hashM(${createConsolidatedDocumentNumberReferenceKey})
          , "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SYSTEM_USER_ACTION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SYSTEM_USER_ACTION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SYSTEM_USER_ACTION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "System User Action",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Captures information on the system user and the performed action: trigger event, office id, user id, workstation",
        "granularity": "1 user information for 1 passenger",
        "subdomain": "System User Action",
        "subdomain-main-table": "true"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SYSTEM_USER_ACTION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"modif": "$.lastModification"}, {"user": "$.user"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "SYSTEM_USER_ACTION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: UCI", "rule": "replace"}, "gdpr-zone": "orange"}
        },
        {
          "name": "TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"modif": "$.triggerEventName"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "USER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.id"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.officeId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.id"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_LOC_CITY", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.cityCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_LOC_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.airportCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_LOC_TERMINAL", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.terminalId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_LOC_AREA", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.areaCode"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_LOC_BUILDING", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.buildingId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRIGGER_EVENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"modif": "$.triggerEventName"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_LOC_AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.airportCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_LOC_TERMINAL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.airportCode"}, {"user": "$.workstation.fullLocation.terminalId"}]}, "expr": ${hashSIdCheckEndNotNull}, "fk": [{"table": "DIM_AIRPORT_TERMINAL"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "WORKSTATION_LOC_AREA_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.areaCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT_AREA"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "DIM_AIRLINE",
    "mapping": {
      "description": {"description": "Lists the airlines used as marketing/operating/validating carriers on flight segments, service providers, or for loyalty programs. Contains alliances (prefixed by *).", "granularity": "1 airline"},
      "merge": {
        "key-columns": ["AIRLINE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"companyCode": "$.mainResource.current.image.staff.companyCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].staff.companyCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].validatingCarrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.passenger.contacts[*].carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].contacts[*].carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].segment.carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.flightTransfers[*].toSegments[*].carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].segment.operating.carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].operating.carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.flightTransfers[*].toSegments[*].operating.carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].operatingFlight.carrierCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].validForCarrier"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.regulatoryDocuments[*].validForCarrier"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.services[*].serviceProvider.code"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].services[*].serviceProvider.code"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.chargeableSeat.serviceProvider.code"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.frequentFlyer[*].applicableAirlineCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].applicableAirlineCode.code"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.frequentFlyer[*].allianceLevel.companyCode"}]},
        {"blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].allianceLevel.companyCode"}]}

      ],
      "columns": [
        {
          "name": "AIRLINE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"companyCode": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of the airline IATA code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "AIRLINE_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"companyCode": "$.value"}]}
          , "meta": {"gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT",
    "mapping": {
      "description": {"description": "Lists the airports used in departure/arrival airports of flight segments or legs", "granularity": "1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].segment.arrival.iataCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].arrival.iataCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.flightTransfers[*].toSegments[*].arrival.iataCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].segment.departure.iataCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].departure.iataCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.flightTransfers[*].toSegments[*].departure.iataCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].arrival.iataCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].departure.iataCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.trackingLog.trackedLocation.airportCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.lastModification.user.workstation.fullLocation.airportCode"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].passengerDisruption.originalDestination"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].presentedAtPort"}]},
        {"blocks": [{"airportCode": "$.mainResource.current.image.regulatoryDocuments[*].presentedAtPort"}]}
      ],
      "columns": [
        {"name": "AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"airportCode": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"airportCode": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT_TERMINAL",
    "mapping": {
      "description": {"description": "Lists the airport terminals used in departure/arrival of flight legs", "granularity": "1 terminal in 1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_TERMINAL_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"loc": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.trackingLog.trackedLocation"}, {"termId" : "$.terminalId"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.lastModification.user.workstation.fullLocation"}, {"termId" : "$.terminalId"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.lastModification.user.workstation.fullLocation"}, {"termId" : "$.terminalId"}]}
      ],
      "columns": [
        {
          "name": "AIRPORT_TERMINAL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"loc": "$.airportCode"}, {"loc": "$.terminalId"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of the terminal (airport-terminal)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.airportCode"}]}},
        {"name": "TERMINAL", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.terminalId"}]}},
        {
          "name": "AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"loc": "$.airportCode"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_TRIGGER_EVENT",
    "mapping": {
      "description": {"description": "Lists the different trigger events which updated the DCS passenger", "granularity": "1 event"},
      "merge": {
        "key-columns": ["TRIGGER_EVENT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"event": "$.mainResource.current.image.lastModification"}]}
      ],
      "columns": [
        {
          "name": "TRIGGER_EVENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"event": "$.triggerEventName"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of the triggering event", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"event": "$.triggerEventName"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT_AREA",
    "mapping": {
      "description": {"description": "Lists the airport areas/zones used in flight legs", "granularity": "1 area in 1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_AREA_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"event": "$.mainResource.current.image.lastModification.user.workstation.fullLocation"},{"dim" : "$.areaCode"}]}
      ],
      "columns": [
        {
          "name": "AIRPORT_AREA_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"event": "$.areaCode"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of area code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "AIRPORT_AREA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"event": "$.areaCode"}]}
          , "meta": {"description": {"value": "Category of the location", "rule": "replace"}, "example": {"value": "CKI", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "AIRPORT_AREA_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"event": "$.areaCode"}]}
          , "meta": {"description": {"value": "Label corresponding to the airport area code", "rule": "replace"}, "example": {"value": "Airport Check In", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "AIRPORT_AREA",
      "column-filler" : [
        {"dim-col" : "AIRPORT_AREA_ID", "src-col" : "CODE"},
        {"dim-col" : "AIRPORT_AREA_CODE", "src-col" : "CODE"},
        {"dim-col" : "AIRPORT_AREA_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_BOOKING_STATUS",
    "mapping": {
      "description": {"description": "Lists the booking statuses for segment or service deliveries", "granularity": "1 booking status"},
      "merge": {
        "key-columns": ["BOOKING_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].segment.status"}]},
        {"blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].status"}]},
        {"blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].toSegments[*].status"}]},
        {"blocks": [{"status": "$.mainResource.current.image.services[*].statusCode"}]},
        {"blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].services[*].statusCode"}]},
        {"blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.statusCode"}]},
        {"blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.seatProductStatus"}]}
      ],
      "columns": [

        {"name": "BOOKING_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "BOOKING_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}},
        {"name": "BOOKING_STATUS_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the booking status code", "rule": "replace"}, "example": {"value": "Holding Confirmed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BOOKING_MACRO_STATUS", "column-type": "strColumn", "sources": {"literal": "UNKNOWN"}
          , "meta": {"description": {"value": "Macro Status corresponding to the booking status code", "rule": "replace"}, "example": {"value": "Confirmed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BOOKING_STATUS_TYPE", "column-type": "strColumn", "sources": {"literal": "UNKNOWN"}
          , "meta": {"description": {"value": "Status type corresponding to the booking status code", "rule": "replace"}, "example": {"value": "Status, Action", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "BOOKING_STATUS",
      "column-filler" : [
        {"dim-col" : "BOOKING_STATUS_ID", "src-col" : "CODE"},
        {"dim-col" : "BOOKING_STATUS_CODE", "src-col" : "CODE"},
        {"dim-col" : "BOOKING_STATUS_LABEL", "src-col" : "LABEL"},
        {"dim-col" :  "BOOKING_MACRO_STATUS", "src-col" : "MACRO_STATUS"},
        {"dim-col" :  "BOOKING_STATUS_TYPE", "src-col" : "TYPE"}
      ]
    }]
  },
  {
    "name": "DIM_BOOKING_CLASS",
    "mapping": {
      "description": {"description": "Lists the booking classes for segments deliveries", "granularity": "1 booking class"},
      "merge": {
        "key-columns": ["BOOKING_CLASS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"class": "$.mainResource.current.image.segmentDeliveries[*].segment.class"}]},
        {"blocks": [{"class": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].class"}]},
        {"blocks": [{"class": "$.mainResource.current.image.flightTransfers[*].toSegments[*].class"}]}
      ],
      "columns": [

        {"name": "BOOKING_CLASS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"class": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "BOOKING_CLASS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_CABIN",
    "mapping": {
      "description": {"description": "Lists the cabins of segment and leg deliveries", "granularity": "1 cabin"},
      "merge": {
        "key-columns": ["CABIN_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].segment.cabin"}]},
        {"blocks": [{"cabin": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].cabin"}]},
        {"blocks": [{"cabin": "$.mainResource.current.image.flightTransfers[*].toSegments[*].cabin"}]},
        {"blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].travelCabinCode"}]},
        {"blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].onload.cabinCode"}]},
        {"blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].onload.edit.cabinCode"}]},
        {"blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.cabinCode"}]},
        {"blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.cabinCode"}]}
      ],
      "columns": [

        {"name": "CABIN_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cabin": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "CABIN_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cabin": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DCS_TRANSFER_STATUS",
    "mapping": {
      "description": {"description": "Lists the DCS transfer statuses which can occur in a flight transfer", "granularity": "1 DCS transfer status"},
      "merge": {
        "key-columns": ["DCS_TRANSFER_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].dcsTransferStatus"}]}
      ],
      "columns": [
        {
          "name": "DCS_TRANSFER_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of DCS transfer status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DCS_TRANSFER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}
          , "meta": {"description": {"value": "Status of DCS transfer", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DATA_TRANSFER_STATUS",
    "mapping": {
      "description": {"description": "Lists the data transfer statuses which can occur in a flight transfer", "granularity": "1 data transfer status"},
      "merge": {
        "key-columns": ["DATA_TRANSFER_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].dataTransferStatus"}]}
      ],
      "columns": [
        {
          "name": "DATA_TRANSFER_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of data transfer status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATA_TRANSFER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}
          , "meta": {"description": {"value": "Status of data transfer", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DISRUPTION_TRANSFER_REASON",
    "mapping": {
      "description": {"description": "Lists the transfer reasons which justify a flight transfer", "granularity": "1 disruption reason"},
      "merge": {
        "key-columns": ["DISRUPTION_TRANSFER_REASON_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].disruptionTransferReason"}]}
      ],
      "columns": [
        {
          "name": "DISRUPTION_TRANSFER_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of disruption transfer reason", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DISRUPTION_TRANSFER_REASON", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}
          , "meta": {"description": {"value": "Reason for the flight transfer in case of a disruption", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_TRANSFER_SUBTYPE",
    "mapping": {
      "description": {"description": "Lists the subtypes of a flight transfer", "granularity": "1 transfer subtype"},
      "merge": {
        "key-columns": ["TRANSFER_SUBTYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].subType"}]}
      ],
      "columns": [
        {
          "name": "TRANSFER_SUBTYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of transfer subtype", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRANSFER_SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}
          , "meta": {"description": {"value": "Subtype of the flight transfer", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_MEMBERSHIP_TIER",
    "mapping": {
      "description": {"description": "Lists the loyalty membership tiers for airlines and alliances", "granularity": "1 membership tier for 1 airline"},
      "merge": {
        "key-columns": ["MEMBERSHIP_TIER_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].airlineLevel"}]},
        {"blocks": [{"base": "$.mainResource.current.image.frequentFlyer[*].airlineLevel"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].allianceLevel"}]},
        {"blocks": [{"base": "$.mainResource.current.image.frequentFlyer[*].allianceLevel"}]}
      ],
      "columns": [

        {
          "name": "MEMBERSHIP_TIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.companyCode"}, {"base": "$.code"}, {"base": "$.name"}, {"base": "$.priorityCode"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of membership tier (company/alliance-level-priority)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "TIER_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.code"}]}},
        {"name": "TIER_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.name"}]}},
        {"name": "PRIORITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.priorityCode"}]}},
        {"name": "COMPANY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.companyCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_MEMBERSHIP_STATUS",
    "mapping": {
      "description": {"description": "Lists the loyalty membership statuses", "granularity": "1 membership status"},
      "merge": {
        "key-columns": ["MEMBERSHIP_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"class": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].confirmationStatus"}]},
        {"blocks": [{"class": "$.mainResource.current.image.frequentFlyer[*].confirmationStatus"}]}
      ],
      "columns": [

        {
          "name": "MEMBERSHIP_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"class": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of Frequent flyer profile status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "MEMBERSHIP_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_IDENTITY_DOCUMENT_TYPE",
    "mapping": {
      "description": {"description": "Lists the types of identity documents", "granularity": "1 identity document type"},
      "merge": {
        "key-columns": ["IDENTITY_DOCUMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"status": "$.mainResource.current.image.regulatoryDocuments[*].document.documentType"}]},
        {"blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document.documentType"}]},
        {"blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].services[*].document.documentType"}]},
        {"blocks": [{"status": "$.mainResource.current.image.services[*].document.documentType"}]},
        {"blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.document.documentType"}]}
      ],
      "columns": [
        {"name": "IDENTITY_DOCUMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "IDENTITY_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_COUNTRY",
    "mapping": {
      "description": {"description": "Lists the countries used for nationalities, regulatory/identity documents and regulatory checks", "granularity": "1 country"},
      "merge": {
        "key-columns": ["COUNTRY_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"country": "$.mainResource.current.image.passenger.contacts[*].address.countryCode"}]},
        {"blocks": [{"country": "$.mainResource.current.image.passenger.nationality"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].contacts[*].address.countryCode"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document.issuanceCountry"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document.countryOfPortOfEntry"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document.countryOfRegistration"}]},
        {"blocks": [{"country": "$.mainResource.current.image.regulatoryDocuments[*].document.countryOfPortOfEntry"}]},
        {"blocks": [{"country": "$.mainResource.current.image.regulatoryDocuments[*].document.countryOfRegistration"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].services[*].document.issuanceCountry"}]},
        {"blocks": [{"country": "$.mainResource.current.image.services[*].document.issuanceCountry"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.document.issuanceCountry"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].regulatoryProgram.countryCode"}]},
        {"blocks": [{"country": "$.mainResource.current.image.services[*].address.countryCode"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].services[*].address.countryCode"}]},
        {"blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.address.countryCode"}]}
      ],
      "columns": [

        {"name": "COUNTRY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"country": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "COUNTRY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"country": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_SPECIAL_SEAT",
    "mapping": {
      "description": {"description": "Lists the special seat services (CBBG, EXST) used for passengers", "granularity": "1 special seat service"},
      "merge": {
        "key-columns": ["SPECIAL_SEAT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.passenger.specialSeat"}]}
      ],
      "columns": [

        {"name": "SPECIAL_SEAT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "SPECIAL_SEAT_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_PASSENGER_TYPE",
    "mapping": {
      "description": {"description": "Lists the passenger types", "granularity": "1 passenger type"},
      "merge": {
        "key-columns": ["PASSENGER_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.passenger.passengerType"}]}
      ],
      "columns": [

        {
          "name": "PASSENGER_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of passenger type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}},
        {"name": "PASSENGER_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the passenger type code", "rule": "replace"}, "example": {"value": "Adult", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "PASSENGER_TYPE",
      "column-filler" : [
        {"dim-col" : "PASSENGER_TYPE_ID", "src-col" : "CODE"},
        {"dim-col" : "PASSENGER_TYPE_CODE", "src-col" : "CODE"},
        {"dim-col" : "PASSENGER_TYPE_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_CPR_FEED_TYPE",
    "mapping": {
      "description": {"description": "Lists the RES-DCS synchronization types used for passengers", "granularity": "1 CPR feed type"},
      "merge": {
        "key-columns": ["CPR_FEED_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.cprFeedType"}]}
      ],
      "columns": [

        {"name": "CPR_FEED_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "CPR_FEED_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_STAFF_RELATIONSHIP",
    "mapping": {
      "description": {"description": "Lists the relationship types between a passenger and a staff, used for passengers or segment deliveries", "granularity": "1 staff relationship"},
      "merge": {
        "key-columns": ["STAFF_RELATIONSHIP_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.staff.relationshipType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].staff.relationshipType"}]}
      ],
      "columns": [

        {"name": "STAFF_RELATIONSHIP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "STAFF_RELATIONSHIP", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_STAFF_BOOKING_TYPE",
    "mapping": {
      "description": {"description": "Lists the staff booking types (e.g. N2, S1, ...) used for passengers or segment deliveries", "granularity": "1 staff booking type"},
      "merge": {
        "key-columns": ["STAFF_BOOKING_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.staff.idType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].staff.idType"}]}
      ],
      "columns": [

        {"name": "STAFF_BOOKING_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "STAFF_BOOKING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_STAFF_CATEGORY",
    "mapping": {
      "description": {"description": "Lists the staff categories used for passengers or segment deliveries", "granularity": "1 staff category"},
      "merge": {
        "key-columns": ["STAFF_CATEGORY_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.staff.category"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].staff.category"}]}
      ],
      "columns": [

        {"name": "STAFF_CATEGORY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "STAFF_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_SERVICE",
    "mapping": {
      "description": {"description": "Lists ths service codes used for service deliveries and loyalty memberships", "granularity": "1 service"},
      "merge": {
        "key-columns": ["SERVICE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*]"},{"service": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image.services[*]"},{"service": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat"},{"service": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image.frequentFlyer[*]"},{"service": "$.serviceCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*]"},{"service": "$.serviceCode"}]}
      ],
      "columns": [
        {
          "name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"service": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of service code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"service": "$.value"}]}},
        {"name": "SERVICE_SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.subType"}]}},
        {"name": "SERVICE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"service": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the service code", "rule": "replace"}, "example": {"value": "Excess baggage", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "SSR_CODE",
      "column-filler" : [
        {"dim-col" : "SERVICE_ID", "src-col" : "CODE"},
        {"dim-col" : "SERVICE_CODE", "src-col" : "CODE"},
        {"dim-col" : "SERVICE_SUBTYPE", "src-col" : "SERVICE_SUBTYPE"},
        {"dim-col" : "SERVICE_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_SERVICE_SUBTYPE",
    "mapping": {
      "description": {"description": "Lists ths service subtypes (SSR, SVC) used for service deliveries", "granularity": "1 service subtype"},
      "merge": {
        "key-columns": ["SERVICE_SUBTYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].subType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.services[*].subType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.subType"}]}
      ],
      "columns": [
        {
          "name": "SERVICE_SUBTYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of service subtype", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "SERVICE_SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_PAYMENT_STATUS",
    "mapping": {
      "description": {"description": "Lists the payment statuses used for service deliveries", "granularity": "1 payment status"},
      "merge": {
        "key-columns": ["PAYMENT_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].paymentStatus"}]},
        {"blocks": [{"base": "$.mainResource.current.image.services[*].paymentStatus"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.paymentStatus"}]}
      ],
      "columns": [
        {
          "name": "PAYMENT_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of payment status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "PAYMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_CHARGEABLE_DOCUMENT_TYPE",
    "mapping": {
      "description": {"description": "Lists the types of chargeable documents used for service deliveries", "granularity": "1 document type"},
      "merge": {
        "key-columns": ["CHARGEABLE_DOCUMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].chargeableServiceDocument.documentType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.services[*].chargeableServiceDocument.documentType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.chargeableSeat.chargeableServiceDocument.documentType"}]}
      ],
      "columns": [
        {
          "name": "CHARGEABLE_DOCUMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of document type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "CHARGEABLE_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_CHARGEABLE_DOCUMENT_STATUS",
    "mapping": {
      "description": {"description": "Lists the statuses of chargeable documents used for service deliveries", "granularity": "1 document status"},
      "merge": {
        "key-columns": ["CHARGEABLE_DOCUMENT_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].chargeableServiceDocument.status"}]},
        {"blocks": [{"base": "$.mainResource.current.image.services[*].chargeableServiceDocument.status"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.chargeableServiceDocument.status"}]}
      ],
      "columns": [
        {
          "name": "CHARGEABLE_DOCUMENT_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of document status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "CHARGEABLE_DOCUMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_WAIVER_AUTHORIZER",
    "mapping": {
      "description": {"description": "Lists the waiver authorizers used for service deliveries", "granularity": "1 waiver authorizer"},
      "merge": {
        "key-columns": ["WAIVER_AUTHORIZER_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].waiver.authoriser"}]},
        {"blocks": [{"base": "$.mainResource.current.image.services[*].waiver.authoriser"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.chargeableSeat.waiver.authoriser"}]}
      ],
      "columns": [
        {"name": "WAIVER_AUTHORIZER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "WAIVER_AUTHORIZER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_WAIVER_REASON",
    "mapping": {
      "description": {"description": "Lists the waiver reasons used for service deliveries", "granularity": "1 waiver reason"},
      "merge": {
        "key-columns": ["WAIVER_REASON_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].waiver.reasonCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image.services[*].waiver.reasonCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.waiver.reasonCode"}]}
      ],
      "columns": [
        {"name": "WAIVER_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "WAIVER_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REASON_FOR_ISSUANCE",
    "mapping": {
      "description": {"description": "Lists the RFIC used for service deliveries and coupons", "granularity": "1 RFIC"},
      "merge": {
        "key-columns": ["REASON_FOR_ISSUANCE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].priceCategory"},{"rfi": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image.services[*].priceCategory"},{"rfi": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.priceCategory"},{"rfi": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].coupons[*]"},{"rfi": "$.code"}]},
      ],
      "columns": [
        {
          "name": "REASON_FOR_ISSUANCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"rfi": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of reason of issuance code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"rfi": "$.value"}]}},
        {"name": "REASON_FOR_ISSUANCE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"rfi": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the reason for issuance code", "rule": "replace"}, "example": {"value": "Financial Impact", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "REASON_FOR_ISSUANCE",
      "column-filler" : [
        {"dim-col" : "REASON_FOR_ISSUANCE_ID", "src-col" : "CODE"},
        {"dim-col" : "REASON_FOR_ISSUANCE_CODE", "src-col" : "CODE"},
        {"dim-col" : "REASON_FOR_ISSUANCE_LABEL", "src-col" : "NAME"}
      ]
    }]
  },
  {
    "name": "DIM_COUPON_STATUS",
    "mapping": {
      "description": {"description": "Lists the coupon statuses used in coupons", "granularity": "1 coupon status"},
      "merge": {
        "key-columns": ["COUPON_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].coupons[*].status"}]}
      ],
      "columns": [
        {
          "name": "COUPON_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of coupon status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "COUPON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}
          , "meta": {"description": {"value": "Coupon operational status. It follows IATA PADIS Code List for data element 4405", "rule": "replace"}, "example": {"value": "CHECKED_IN", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DOCUMENT_TYPE",
    "mapping": {
      "description": {"description": "Lists the document types (ticket, EMD) used in travel documents", "granularity": "1 document type"},
      "merge": {
        "key-columns": ["DOCUMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].documentType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].associatedDocuments[*].documentType"}]}
      ],
      "columns": [
        {"name": "DOCUMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_TRAVEL_DOCUMENT_ASSOCIATION_STATUS",
    "mapping": {
      "description": {"description": "Lists the association statuses between two travel documents", "granularity": "1 association status"},
      "merge": {
        "key-columns": ["TRAVEL_DOCUMENT_ASSOCIATION_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].associationStatus"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].associatedDocuments[*].associationStatus"}]}
      ],
      "columns": [
        {"name": "TRAVEL_DOCUMENT_ASSOCIATION_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "TRAVEL_DOCUMENT_ASSOCIATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_VOLUNTEER_DENIED_BOARDING",
    "mapping": {
      "description": {"description": "Lists the qualifiers for voluntary denied boarding used in segment deliveries", "granularity": "1 volunteer denied boarding qualifier"},
      "merge": {
        "key-columns": ["VOLUNTEER_DENIED_BOARDING_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].volunteerDeniedBoarding"}]}
      ],
      "columns": [
        {
          "name": "VOLUNTEER_DENIED_BOARDING_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of volunteer denied boarding", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "VOLUNTEER_DENIED_BOARDING", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_VOLUNTEER_DOWNGRADE",
    "mapping": {
      "description": {"description": "Lists the qualifiers for voluntary downgrades used in segment deliveries", "granularity": "1 volunteer downgrade qualifier"},
      "merge": {
        "key-columns": ["VOLUNTEER_DOWNGRADE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].volunteerDowngrade"}]}
      ],
      "columns": [
        {
          "name": "VOLUNTEER_DOWNGRADE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of volunteer downgrade", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "VOLUNTEER_DOWNGRADE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DISRUPTION_STATUS",
    "mapping": {
      "description": {"description": "Lists the disruption statuses used in segment deliveries", "granularity": "1 disruption status"},
      "merge": {
        "key-columns": ["DISRUPTION_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].passengerDisruption.productState.status"}]}
      ],
      "columns": [
        {
          "name": "DISRUPTION_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of disruption status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DISRUPTION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DISRUPTION_REASONS",
    "mapping": {
      "description": {"description": "Lists the disruption reasons used in segment deliveries, can contain multiple values as concatenated list", "granularity": "1 disruption reasons (potentially concatenated values)"},
      "merge": {
        "key-columns": ["DISRUPTION_REASONS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].passengerDisruption.productState"}]}
      ],
      "columns": [
        {
          "name": "DISRUPTION_REASONS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.reason[*]"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of disruption reasons", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DISRUPTION_REASONS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reason[*]"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REVENUE_INTEGRITY_CHECK_TYPE",
    "mapping": {
      "description": {"description": "Lists the types of revenue integrity checks", "granularity": "1 RI check type"},
      "merge": {
        "key-columns": ["REVENUE_INTEGRITY_CHECK_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].revenueIntegrity.checks[*].checkType"}]}

      ],
      "columns": [
        {"name": "REVENUE_INTEGRITY_CHECK_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "REVENUE_INTEGRITY_CHECK_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REVENUE_INTEGRITY_CHECK_STATUS",
    "mapping": {
      "description": {"description": "Lists the statuses of revenue integrity checks", "granularity": "1 RI check status"},
      "merge": {
        "key-columns": ["REVENUE_INTEGRITY_CHECK_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].revenueIntegrity.checks[*].status"}]}

      ],
      "columns": [
        {"name": "REVENUE_INTEGRITY_CHECK_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "REVENUE_INTEGRITY_CHECK_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REVENUE_INTEGRITY_COMMENT_TYPE",
    "mapping": {
      "description": {"description": "Lists the types of revenue integrity check comments", "granularity": "1 RI comment type"},
      "merge": {
        "key-columns": ["REVENUE_INTEGRITY_COMMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].revenueIntegrity.comments[*].commentType"}]}

      ],
      "columns": [
        {
          "name": "REVENUE_INTEGRITY_COMMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of comment type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REVENUE_INTEGRITY_COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DELIVERY_COMMENT_PRIORITY",
    "mapping": {
      "description": {"description": "Lists the priority levels of delivery comments", "granularity": "1 delivery priority"},
      "merge": {
        "key-columns": ["DELIVERY_COMMENT_PRIORITY_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].comments[*].priority"}]}

      ],
      "columns": [
        {
          "name": "DELIVERY_COMMENT_PRIORITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of the priority of the comment", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DELIVERY_COMMENT_PRIORITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DELIVERY_COMMENT_USAGE",
    "mapping": {
      "description": {"description": "Lists the usages of delivery comments", "granularity": "1 delivery usage"},
      "merge": {
        "key-columns": ["DELIVERY_COMMENT_USAGE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].comments[*].usage"}]}

      ],
      "columns": [
        {
          "name": "DELIVERY_COMMENT_USAGE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of comment usage", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DELIVERY_COMMENT_USAGE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DELIVERY_COMMENT_STATUS",
    "mapping": {
      "description": {"description": "Lists the statuses of delivery comments", "granularity": "1 delivery status"},
      "merge": {
        "key-columns": ["COMMENT_DELIVERY_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].comments[*].delivery.status"}]}

      ],
      "columns": [
        {
          "name": "COMMENT_DELIVERY_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of comment delivery status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "COMMENT_DELIVERY_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_VOUCHER_TYPE",
    "mapping": {
      "description": {"description": "Lists the types of compensation vouchers", "granularity": "1 voucher type"},
      "merge": {
        "key-columns": ["VOUCHER_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].vouchers[*].voucherType"}]}

      ],
      "columns": [
        {
          "name": "VOUCHER_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of voucher type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "VOUCHER_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_VOUCHER_STATUS",
    "mapping": {
      "description": {"description": "Lists the statuses of compensation vouchers", "granularity": "1 voucher status"},
      "merge": {
        "key-columns": ["VOUCHER_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].vouchers[*].status"}]}

      ],
      "columns": [
        {
          "name": "VOUCHER_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of voucher status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "VOUCHER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_VOUCHER_PRINT_STATUS",
    "mapping": {
      "description": {"description": "Lists the print statuses of compensation vouchers", "granularity": "1 voucher print status"},
      "merge": {
        "key-columns": ["VOUCHER_PRINT_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].vouchers[*].printStatus"}]}

      ],
      "columns": [
        {
          "name": "VOUCHER_PRINT_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of printing status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "VOUCHER_PRINT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_COMPENSATION_CATEGORY",
    "mapping": {
      "description": {"description": "Lists the categories of compensations", "granularity": "1 compensation category"},
      "merge": {
        "key-columns": ["COMPENSATION_CATEGORY_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].categoryDescription"}]}

      ],
      "columns": [
        {
          "name": "COMPENSATION_CATEGORY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of compensation category", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "COMPENSATION_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_COMPENSATION_AUTHORIZATION_STATUS",
    "mapping": {
      "description": {"description": "Lists the authorization statuses of compensations", "granularity": "1 authorization status"},
      "merge": {
        "key-columns": ["COMPENSATION_AUTHORIZATION_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].authorisation.status"}]}

      ],
      "columns": [
        {
          "name": "COMPENSATION_AUTHORIZATION_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of authorization status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "COMPENSATION_AUTHORIZATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_COMPENSATION_REASON",
    "mapping": {
      "description": {"description": "Lists the reasons of compensations", "granularity": "1 compensation reason"},
      "merge": {
        "key-columns": ["COMPENSATION_REASON_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].reason.code"}]}

      ],
      "columns": [
        {
          "name": "COMPENSATION_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of compendation reason", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "COMPENSATION_REASON", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REGULATORY_PROGRAM",
    "mapping": {
      "description": {"description": "Lists the regulatory programs in which regulatory checks are performed", "granularity": "1 regulatory program"},
      "merge": {
        "key-columns": ["REGULATORY_PROGRAM_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].regulatoryProgram.name"}]}

      ],
      "columns": [
        {
          "name": "REGULATORY_PROGRAM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of regulatory program name", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REGULATORY_PROGRAM", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REGULATORY_CHECK_STATUS",
    "mapping": {
      "description": {"description": "Lists the status codes of regulatory checks", "granularity": "1 regulatory check code"},
      "merge": {
        "key-columns": ["REGULATORY_CHECK_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].statusCode"}]}

      ],
      "columns": [
        {
          "name": "REGULATORY_CHECK_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of status code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REGULATORY_CHECK_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}},
        {"name": "REGULATORY_CHECK_STATUS_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the regulatory check status code", "rule": "replace"}, "example": {"value": "AQQ Prechecked", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "REGULATORY_CHECK_STATUS",
      "column-filler" : [
        {"dim-col" : "REGULATORY_CHECK_STATUS_ID", "src-col" : "CODE"},
        {"dim-col" : "REGULATORY_CHECK_STATUS_CODE", "src-col" : "CODE"},
        {"dim-col" : "REGULATORY_CHECK_STATUS_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_REGULATORY_CHECK_STATUS_TYPE",
    "mapping": {
      "description": {"description": "Lists the status types of regulatory checks", "granularity": "1 regulatory check type"},
      "merge": {
        "key-columns": ["REGULATORY_CHECK_STATUS_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].statusType"}]}

      ],
      "columns": [
        {
          "name": "REGULATORY_CHECK_STATUS_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of status type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REGULATORY_CHECK_STATUS_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REGULATORY_CHECK_COMMENT_CODE",
    "mapping": {
      "description": {"description": "Lists the codes of regulatory check comments", "granularity": "1 regulatory comment code"},
      "merge": {
        "key-columns": ["REGULATORY_CHECK_COMMENT_CODE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].message.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].override.code"}]}

      ],
      "columns": [
        {
          "name": "REGULATORY_CHECK_COMMENT_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of comment code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REGULATORY_CHECK_COMMENT_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REGULATORY_CHECK_COMMENT_TYPE",
    "mapping": {
      "description": {"description": "Lists the types of regulatory check comments", "granularity": "1 regulatory comment type"},
      "merge": {
        "key-columns": ["REGULATORY_CHECK_COMMENT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].message.commentType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].override.commentType"}]}

      ],
      "columns": [
        {
          "name": "REGULATORY_CHECK_COMMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of comment type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REGULATORY_CHECK_COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REGRADE_REASON",
    "mapping": {
      "description": {"description": "Lists the regrade reasons used on leg deliveries", "granularity": "1 regrade reason"},
      "merge": {
        "key-columns": ["REGRADE_REASON_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.reasonCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.reasonCode"}]}

      ],
      "columns": [
        {
          "name": "REGRADE_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of regrade reason", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REGRADE_REASON_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}
          , "meta": {"description": {"value": "The reason code for regrade", "rule": "replace"}, "example": {"value": "OVER_SOLD_CURRENT_FLIGHT", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REGRADE_STATUS",
    "mapping": {
      "description": {"description": "Lists the regrade status used on leg deliveries", "granularity": "1 regrade status"},
      "merge": {
        "key-columns": ["REGRADE_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.status"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.status"}]}

      ],
      "columns": [
        {
          "name": "REGRADE_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of regrade status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REGRADE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_REGRADE_TYPE",
    "mapping": {
      "description": {"description": "Lists the regrade types used on leg deliveries", "granularity": "1 regrade type"},
      "merge": {
        "key-columns": ["REGRADE_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.regradeType"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.regradeType"}]}

      ],
      "columns": [
        {
          "name": "REGRADE_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of regrade type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "REGRADE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_SEAT_CHARACTERISTICS",
    "mapping": {
      "description": {"description": "Lists the seat characteristics used on leg deliveries, can contain multiple values as concatenated list.", "granularity": "1 seat characteristics (potentially concatenated values)"},
      "merge": {
        "key-columns": ["SEAT_CHARACTERISTICS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.seat"}]},
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.seatPreferences"}]}

      ],
      "columns": [
        {
          "name": "SEAT_CHARACTERISTICS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.characteristicsCodes[*]"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of seat characteristics (can be a list)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "SEAT_CHARACTERISTICS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.characteristicsCodes[*]"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_ACCEPTANCE_TYPE",
    "mapping": {
      "description": {"description": "Lists the acceptance types used on leg deliveries", "granularity": "1 acceptance type"},
      "merge": {
        "key-columns": ["ACCEPTANCE_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.acceptanceType"}]}
      ],
      "columns": [
        {"name": "ACCEPTANCE_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "ACCEPTANCE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_ACCEPTANCE_CHANNEL",
    "mapping": {
      "description": {"description": "Lists the acceptance channels used on leg deliveries", "granularity": "1 acceptance channel"},
      "merge": {
        "key-columns": ["ACCEPTANCE_CHANNEL_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.channel"}]}
      ],
      "columns": [
        {"name": "ACCEPTANCE_CHANNEL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "ACCEPTANCE_CHANNEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_ACCEPTANCE_STATUS",
    "mapping": {
      "description": {"description": "Lists the acceptance statuses used on leg deliveries", "granularity": "1 acceptance status"},
      "merge": {
        "key-columns": ["ACCEPTANCE_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.status"}]}
      ],
      "columns": [
        {"name": "ACCEPTANCE_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "ACCEPTANCE_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_ACCEPTANCE_PHYSICAL_LOCATION",
    "mapping": {
      "description": {"description": "Lists the physical locations of acceptance used on leg deliveries", "granularity": "1 acceptance location"},
      "merge": {
        "key-columns": ["ACCEPTANCE_PHYSICAL_LOCATION_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.physicalAcceptanceLocation"}]}
      ],
      "columns": [
        {
          "name": "ACCEPTANCE_PHYSICAL_LOCATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of acceptance location", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "ACCEPTANCE_PHYSICAL_LOCATION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_BOARDING_PRINT_CHANNEL",
    "mapping": {
      "description": {"description": "Lists the print channels of boarding passes used on leg deliveries", "granularity": "1 print channel"},
      "merge": {
        "key-columns": ["BOARDING_PRINT_CHANNEL_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.boardingPassPrint.channel"}]}
      ],
      "columns": [
        {
          "name": "BOARDING_PRINT_CHANNEL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of print channel", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "BOARDING_PRINT_CHANNEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_BOARDING_STATUS",
    "mapping": {
      "description": {"description": "Lists the boarding statuses used on leg deliveries", "granularity": "1 boarding status"},
      "merge": {
        "key-columns": ["BOARDING_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.boardingStatus"}]}
      ],
      "columns": [
        {
          "name": "BOARDING_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of boarding status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "BOARDING_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_BOARDING_PRINT_STATUS",
    "mapping": {
      "description": {"description": "Lists the print statuses of boarding passes used on leg deliveries", "granularity": "1 print status"},
      "merge": {
        "key-columns": ["BOARDING_PRINT_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.boardingPassPrint.status"}]}
      ],
      "columns": [
        {
          "name": "BOARDING_PRINT_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of print status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "BOARDING_PRINT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DEVICE_TYPE",
    "mapping": {
      "description": {"description": "Lists the device types of boarding logs used on leg deliveries", "granularity": "1 device type"},
      "merge": {
        "key-columns": ["DEVICE_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.trackingLog.referenceDeviceType"}]}
      ],
      "columns": [
        {
          "name": "DEVICE_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of device type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DEVICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_LOG_SOURCE",
    "mapping": {
      "description": {"description": "Lists the log sources of boarding logs used on leg deliveries", "granularity": "1 log source"},
      "merge": {
        "key-columns": ["LOG_SOURCE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.trackingLog.logSource"}]}
      ],
      "columns": [
        {
          "name": "LOG_SOURCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of log source", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "LOG_SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "ASSO_FLIGHT_TRANSFER_FLIGHT_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "ASSO_FLIGHT_TRANSFER_FLIGHT_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_FLIGHT_TRANSFER_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains the relationships between flight transfers (itinerary changes) and the involved flight segments.", "granularity": "1 relationship between 1 flight transfer and 1 flight segment"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_SEGMENT_ID", "FLIGHT_TRANSFER_ID", "ASSO_TYPE", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "fromFlt", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"ftrans": "$.flightTransfers[*]"}, {"seg": "$.fromSegments[*]"}]}},
        {"name": "toFlt", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"ftrans": "$.flightTransfers[*]"}, {"seg": "$.toSegments[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "FLIGHT_TRANSFER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"ftrans": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_FLIGHT_TRANSFER_HISTO", "column": "FLIGHT_TRANSFER_ID"}]
        },
        {
          "name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}, {"seg": "$.departure.at"}]}, "expr": "hashM("${flightSegmentRefKeyExpr}")",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}]
        },
        {
          "name": "ASSO_TYPE", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "fromFlt", "literal": "TRANSFER_FROM"},
              {"rs-name": "toFlt", "literal": "TRANSFER_TO"}
            ]
          }
          , "meta": {"description": {"value": "Indicates the role of the related flight segment in the flight transfer (part of "from", part of "to")", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_PASSENGER_ASSOCIATION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "ASSO_PASSENGER_ASSOCIATION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_PASSENGER_ASSOCIATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains the relationships between two DCS passengers (e.g. parent/infant).", "granularity": "1 relationship between 2 passengers"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "LINK_ID", "PASSENGER_ASSO_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"plink": "$.passengerLinks[*]"}, {"coll": "$.collection[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "LINK_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"plink": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hashed primary key (link identifier)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ASSO_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"coll": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
        },
        {
          "name": "PASSENGER_ASSO_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"coll": "$.id"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "ASSO_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"coll": "$.type"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_ASSOCIATION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "ASSO_TRAVEL_DOCUMENT_ASSOCIATION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_TRAVEL_DOCUMENT_ASSOCIATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains the relationships between two travel documents (e.g. ticket associated to EMD-A).", "granularity": "1 relationship between 2 travel documents"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "TRAVEL_DOCUMENT_FROM_ID", "TRAVEL_DOCUMENT_TO_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}, {"asso": "$.associatedDocuments[*]"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.previousTicket"}, {"asso": "$.associatedDocuments"}]},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "TRAVEL_DOCUMENT_FROM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"atd": "$.id"}, {"atd": "$.primaryDocumentNumber"}]},  "expr": hashM(${createConsolidatedDocumentNumberReferenceKey}),
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]
        },
        {
          "name": "TRAVEL_DOCUMENT_TO_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"asso": "$.id"}, {"asso": "$.primaryDocumentNumber"}]},  "expr": hashM(${createConsolidatedDocumentNumberReferenceKey}),
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]
        },
        {
          "name": "ASSOCIATION_STATUS_FROM", "column-type": "strColumn", "sources": {"blocks": [{"atd": "$.associationStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_TRAVEL_DOCUMENT_ASSOCIATION_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ASSOCIATION_STATUS_TO", "column-type": "strColumn", "sources": {"blocks": [{"asso": "$.associationStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_TRAVEL_DOCUMENT_ASSOCIATION_STATUS"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_TRAVEL_DOCUMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "ASSO_SEGMENT_DELIVERY_TRAVEL_DOCUMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_TRAVEL_DOCUMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains the relationships between a segment delivery and the travel document covering it.", "granularity": "1 relationship between 1 segment delivery and 1 travel document"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "TRAVEL_DOCUMENT_ID", "SEGMENT_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {// shouldNotBeEmpty : if the travel document have no id, don t generate records in this table
          "blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}, {"shouldNotBeEmpty": "$.id"}]
        },
        {// shouldNotBeEmpty : if the travel document have no id, don t generate records in this table
          "blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedAirTravelDocuments[*]"}, {"atd": "$.associatedDocuments[*]"}, {"shouldNotBeEmpty": "$.id"}]
        }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"atd": "$.id"}, {"atd": "$.primaryDocumentNumber"}]}, "expr": hashM(${createConsolidatedDocumentNumberReferenceKey})
          , "fk": [{"table": "FACT_TRAVEL_DOCUMENT_HISTO", "column": "TRAVEL_DOCUMENT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_ASSOCIATION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "ASSO_SEGMENT_DELIVERY_ASSOCIATION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_SEGMENT_DELIVERY_ASSOCIATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains the relationships between two segment deliveries, and indicating the precise type of relationship (e.g. onward, disruption, infant, cabin baggage, ...)", "granularity": "1 relationship between 2 segment deliveries"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "SEGMENT_DELIVERY_FROM_ID", "SEGMENT_DELIVERY_TO_ID", "ASSO_TYPE", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "accOnw", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.acceptedOnwardId"}]}},
        {"name": "altOwn", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.alternateOnwardId"}]}},
        {"name": "cbbg", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.cbbgId"}]}},
        {"name": "disOnw", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.disruptedOnwardId"}]}},
        {"name": "exst", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.exstId"}]}},
        {"name": "inft", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.infantId"}]}},
        {"name": "infoOnw", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.informativeOnwardId"}]}},
        {"name": "misOnw", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.misconnectedOnwardId"}]}},
        {"name": "onw", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.onwardId"}]}},
        {"name": "tciOnw", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.tciOnwardId"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {
          "name": "SEGMENT_DELIVERY_FROM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
        },
        {
          "name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_PASSENGER_HISTO", "column": "PASSENGER_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}
          , "meta": {"gdpr-zone": "orange"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the DCS Passenger message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SEGMENT_DELIVERY_TO_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "accOnw", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "altOwn", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "cbbg", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "disOnw", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "exst", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "inft", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "infoOnw", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "misOnw", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "onw", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]},
              {"rs-name": "tciOnw", "blocks": [{"base": "$.id"}, {"asso": "$.value"}]}
            ]
          }, "expr": "hashM({0})"
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"},
          "fk": [{"table": "FACT_SEGMENT_DELIVERY_HISTO", "column": "SEGMENT_DELIVERY_ID"}]
        },
        {
          "name": "ASSO_TYPE", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "accOnw", "literal": "ACCEPTED_ONWARD"},
              {"rs-name": "altOwn", "literal": "ALTERNATE_ONWARD"},
              {"rs-name": "cbbg", "literal": "CBBG"},
              {"rs-name": "disOnw", "literal": "DISRUPTED_ONWARD"},
              {"rs-name": "exst", "literal": "EXST"},
              {"rs-name": "inft", "literal": "INFANT"},
              {"rs-name": "infoOnw", "literal": "INFORMATIVE_ONWARD"},
              {"rs-name": "misOnw", "literal": "MISCONNECTED_ONWARD"},
              {"rs-name": "onw", "literal": "ONWARD"},
              {"rs-name": "tciOnw", "literal": "TCI_ONWARD"}
            ]
          }
          , "meta": {"description": {"value": "Indicates the type of relationship between the two segment deliveries", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "INTERNAL_ASSO_RESERVATION_DCS_PASSENGER_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE","PNR_ACTIVE","DIH_CORRELATION"],["DCSPAX_ACTIVE","PNR_PASSIVE"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "PASSENGER_ID", "VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-PNR"}, {"corr": "$.correlations[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX-PNR correlation information as seen by DCSPAX.",
      "granularity": "1 PAX-PNR",
      "links": ["???"]
    }

  },
  {
    "name": "INTERNAL_ASSO_RESERVATION_DCS_PASSENGER_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE","PNR_ACTIVE","DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "PASSENGER_ID", "VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ], //{"base": "$.recordLocator"},{"base": "$.passenger.name.firstName"},{"base": "$.passenger.name.lastName"}
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "CORR_PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX-PNR correlation information as seen by DCSPAX.",
      "granularity": "1 PAX-PNR",
      "links": ["???"]
    }

  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE","PNR_ACTIVE","DIH_CORRELATION"],["DCSPAX_ACTIVE","PNR_PASSIVE"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "SEGMENT_DELIVERY_ID", "VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-PNR"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxPnr.items[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"item": "$.pnrAirSegmentId"}, {"item": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPassengerSegmentDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.pnrAirSegmentId"}, {"item": "$.pnrTravelerId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPassengerSegmentDeliveryId"}]}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SEGDEL-PNR_AIR_SEG_PAX correlation information as seen by DCSPAX.",
      "granularity": "1 PAX_SEG_DEL-PNR_AIR_SEGMENT_PAX",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO",
    "table-selectors": ["DCSPAX_ACTIVE","PNR_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "CORR_SEGMENT_DELIVERY_ID", "VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "CORR_SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_CORR_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SEGDEL-PNR_AIR_SEG_PAX correlation information as seen by DCSPAX.",
      "granularity": "1 PAX_SEG_DEL-PNR_AIR_SEGMENT_PAX",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "PASSENGER_ID", "VERSION_TRAVEL_DOCUMENT", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-TKT"}, {"corr": "$.correlations[*]"}]},
        {"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-EMD"}, {"corr": "$.correlations[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX-TRAVEL_DOCUMENTS correlation information as seen by DCSPAX.",
      "granularity": "1 PAX-TKT/EMD",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_DCS_PASSENGER_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["DCSPAX_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "PASSENGER_ID", "VERSION_TRAVEL_DOCUMENT", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "atd1", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}, {"shouldNotBeEmpty": "$.primaryDocumentNumber"}]}},
        {"name": "atd2", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedAirTravelDocuments[*]"}, {"atd": "$.associatedDocuments[*]"}, {"shouldNotBeEmpty": "$.primaryDocumentNumber"}]}},
        {"name": "atd3", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.previousTicket"}, {"shouldNotBeEmpty": "$.primaryDocumentNumber"}]}},
        {"name": "atd4", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.previousTicket"}, {"atd": "$.associatedDocuments[*]"}, {"shouldNotBeEmpty": "$.primaryDocumentNumber"}]}},
        {"name": "srv", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"srvd": "$.legDeliveries[*].seating.chargeableSeat.chargeableServiceDocument"}, {"shouldNotBeEmpty": "$.number"}]}}
      ]
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "atd1", "blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]},
              {"rs-name": "atd2", "blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]},
              {"rs-name": "atd3", "blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]},
              {"rs-name": "atd4", "blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]},
              {"rs-name": "srv", "blocks": [{"srvd": "$.number"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]}
            ]
          }, "expr": "hashM({0})"
        },
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "atd1", "blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]},
              {"rs-name": "atd2", "blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]},
              {"rs-name": "atd3", "blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]},
              {"rs-name": "atd4", "blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]},
              {"rs-name": "srv", "blocks": [{"srvd": "$.number"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]}
            ]
          }
        },
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX-TRAVEL_DOCUMENTS correlation information as seen by DCSPAX.",
      "granularity": "1 PAX-TKT/EMD",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SEGMENT_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "SEGMENT_DELIVERY_ID", "VERSION_TRAVEL_DOCUMENT", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "tkt", "rs": {"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-TKT"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxTkt.items[*]"}, {"segdel": "$.dcspaxSegmentId"}]}},
        {"name": "emd", "rs": {"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-EMD"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxEmd.items[*]"}, {"segdel": "$.dcsPassengerSegmentDeliveryId"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {
          "name": "COUPON_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "tkt", "blocks": [{"item": "$.tktCouponId"}]},
              {"rs-name": "emd", "blocks": [{"item": "$.emdCouponId"}]}
            ]
          }, "expr": "hashM({0})"
        },
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {
          "name": "REFERENCE_KEY_COUPON", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "tkt", "blocks": [{"item": "$.tktCouponId"}]},
              {"rs-name": "emd", "blocks": [{"item": "$.emdCouponId"}]}
            ]
          }
        },
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.value"}]}},
        {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {
          "root-specific": [
            {"rs-name": "tkt","literal": "TKT"},
            {"rs-name": "emd","literal": "EMD"}
          ]}},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SEGMENT_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by DCSPAX.",
      "granularity": "1 PAX_SEG_DEL-TKT/EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SEGMENT_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["DCSPAX_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "SEGMENT_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}, {"coupon": "$.coupons[*]"}, {"shouldNotBeEmpty": "$.number"}]},
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedAirTravelDocuments[*]"}, {"atd": "$.associatedDocuments[*]"}, {"coupon": "$.coupons[*]"}, {"shouldNotBeEmpty": "$.number"}]},
        { "blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.previousTicket"}, {"shouldNotBeEmpty": "$.primaryDocumentNumber"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.previousTicket"}, {"atd": "$.associatedDocuments[*]"}, {"shouldNotBeEmpty": "$.primaryDocumentNumber"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]}, "expr": "hashM({0})"},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SEGMENT_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by DCSPAX.",
      "granularity": "1 PAX_SEG_DEL-TKT/EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_PASSIVE"],["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "SERVICE_DELIVERY_ID", "VERSION_TRAVEL_DOCUMENT", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.correlatedResourcesCurrent.DCSPAX-EMD"}, {"corr": "$.correlations[*]"}, {"item": "$.corrDcspaxEmd.items[*]"}, {"serv": "$.dcsPassengerSegmentDeliveryId"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"item": "$.emdCouponId"}]}, "expr": "hashM({0})"},
        {"name": "SERVICE_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"serv": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.emdCouponId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"serv": "$.value"}]}},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SERVICE_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by DCSPAX.",
      "granularity": "1 PAX_SERVICE_DEL-EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO",
    "table-selectors": [["DCSPAX_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["DCSPAX_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "SERVICE_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedAirTravelDocuments[*]"}, {"atd": "$.associatedDocuments[?(@.documentType == 'EMD_ASSOCIATED')]"}, {"coupon": "$.coupons[*]"}, {"shouldNotBeEmpty": "$.number"}]},
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.previousTicket"}, {"atd": "$.associatedDocuments[?(@.documentType == 'EMD_ASSOCIATED')]"}, {"coupon": "$.coupons[*]"}, {"shouldNotBeEmpty": "$.number"}]}
      ],
    "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]}, "expr": "hashM({0})"},
        {"name": "SERVICE_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"atd": "$.primaryDocumentNumber"}, {"segdel": "$.segment.departure.iataCode"}, {"segdel": "$.segment.arrival.iataCode"}, {"segdel": "$.legDeliveries[0].departure.at"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"segdel": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSPAX_SERVICE_DELIVERY-TRAVEL_DOCUMENT_COUPON correlation information as seen by DCSPAX.",
      "granularity": "1 PAX_SERVICE_DEL-EMD_CPN",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSPAX_DCSBAG",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE","DIH_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "BAG_GROUP_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-DCSBAG"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX-DCSBAG correlation information.",
        "granularity": "1 PAX-SEG",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSPAX_DCSBAG",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "BAG_GROUP_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-DCSBAG"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX-DCSBAG correlation information.",
        "granularity": "1 PAX-SEG",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_OWNER_PASSENGER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_ID", "PASSENGER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.DCSPAX-DCSBAG"},
          {"corr": "$.correlations[*]"}
          {"item": "$.corrDcspaxDcsbag.items[*]"},
          {"leg": "$.correlatedLegs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"},{"leg": "$.bagId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "RESPONSIBLE_PASSENGER_ID", "column-type": "binaryStrColumn",  "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"},{"leg": "$.bagId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_RESPONSIBLE_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "gdpr-zone": "green"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_SEGMENT_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX_SEG_DEL-BAG_LEG_DEL",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_OWNER_PASSENGER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.mainResource.current.image"},
          {"bag": "$.bags[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "PASSENGER_OWNER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn",  "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER_OWNER", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green", "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green", "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "gdpr-zone": "green", "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_SEGMENT_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX_SEG_DEL-BAG_LEG_DEL",
        "links": ["???"]
      }
    }
  },

  {
    "name": "INTERNAL_PARTIAL_CORR_DCSPAX_SEGMENT_DEL_DCSBAG_LEG_DEL",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE","DIH_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "BAG_LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-DCSBAG"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxDcsbag.items[*]"},
          {"leg": "$.correlatedLegs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPassengerSegmentDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"leg": "$.bagId"}, {"leg": "$.bagLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"},{"leg": "$.bagId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPassengerSegmentDeliveryId"}]}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"leg": "$.bagId"}, {"leg": "$.bagLegDeliveryId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"},{"leg": "$.bagId"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_SEGMENT_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX-SEG",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSPAX_SEGMENT_DEL_DCSBAG_LEG_DEL",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "BAG_LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-DCSBAG"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxDcsbag.items[*]"},
          {"legs": "$.correlatedLegs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESPONSIBLE_PASSENGER_ID", "column-type": "binaryStrColumn",  "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER_OWNER", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_SEGMENT_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX-SEG",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSPAX_LEG_DEL_DCSBAG_LEG_DEL",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE","DIH_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "LEG_DELIVERY_ID", "BAG_LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-DCSBAG"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxDcsbag.items[*]"},
          {"leg": "$.correlatedLegs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPassengerSegmentDeliveryId"}, {"leg": "$.dcsPassengerLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"leg": "$.bagId"}, {"leg": "$.bagLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"},{"leg": "$.bagId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPassengerSegmentDeliveryId"}, {"leg": "$.dcsPassengerLegDeliveryId"}]}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"leg": "$.bagId"}, {"leg": "$.bagLegDeliveryId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"},{"leg": "$.bagId"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_LEG_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX-SEG",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSPAX_LEG_DEL_DCSBAG_LEG_DEL",
    "table-selectors": ["DCSPAX_ACTIVE", "DCSBAG_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "BAG_LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-DCSBAG"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxDcsbag.items[*]"},
          {"legs": "$.correlatedLegs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESPONSIBLE_PASSENGER_ID", "column-type": "binaryStrColumn",  "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER_OWNER", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_LEG_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX-SEG",
        "links": ["???"]
      }
    }
  },
  {

    "name": "INTERNAL_PARTIAL_CORR_DCS_PASSENGER_FLIGHT_DATE",  // PAX-SKD #1/4
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE","DIH_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-SKD"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {

    "name": "INTERNAL_PARTIAL_CORR_DCS_PASSENGER_FLIGHT_DATE",  // PAX-SKD #1/4
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base":  "$.mainResource.current.image"},
          {"corr": "$.segmentDeliveries[*]"},
          {"seg": "$.segment"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}, {"seg": "$.departure.at"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}, {"seg": "$.departure.at"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr" : ${getCorrFlightSegmentId}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-DCSPAX correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSPAX_SEGMENT_DEL_SKD_FLIGHT_SEGMENT",  // PAX->SKD #2/4
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE","DIH_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-SKD"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"shouldNotBeEmpty": "$.dcsPaxSegmentDeliveryId"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPaxSegmentDeliveryId"}]}, "expr":  "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}]}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPaxSegmentDeliveryId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSPAX_SEGMENT_DEL_SKD_FLIGHT_SEGMENT",  // PAX->SKD #2/4
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base":  "$.mainResource.current.image"},
          {"corr": "$.segmentDeliveries[*]"},
          {"seg": "$.segment"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"corr": "$.id"}]}, "expr":  "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}, {"seg": "$.departure.at"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"corr": "$.id"}]}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}, {"seg": "$.departure.at"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr" : ${getCorrFlightSegmentId}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}]}, "expr" : ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {

    "name": "INTERNAL_PARTIAL_CORR_SEGMENT_DEL_CODESHARE_FLIGHT_SEGMENT",   // PAX->SKD #3/4
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-SKD"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"shouldNorBeEmpty" : "$.partnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPaxSegmentDeliveryId"}]}, "expr":  "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}, {"item": "$.partnershipFlightId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPaxSegmentDeliveryId"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}, {"item": "$.partnershipFlightId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {

    "name": "INTERNAL_PARTIAL_CORR_SEGMENT_DEL_CODESHARE_FLIGHT_SEGMENT",   // PAX->SKD #3/4
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.mainResource.current.image"},
          {"corr": "$.segmentDeliveries[*]"},
          {"seg": "$.segment"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"corr": "$.id"}]}, "expr":  "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.at"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}, {"seg": "$.departure.at"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrCodeshareFlightSegmentId}")"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"corr": "$.id"}]}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.departure.at"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}, {"seg": "$.departure.at"}, {"seg": "$.departure.iataCode"}, {"seg": "$.arrival.iataCode"}]}, "expr": ${getCorrCodeshareFlightSegmentId}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}]}, "expr": ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.carrierCode"}, {"seg": "$.number"}, {"seg": "$.suffix"}, {"seg": "$.operating.carrierCode"}, {"seg": "$.operating.number"}, {"seg": "$.operating.suffix"}, {"seg": "$.departure.at"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {

    "name": "INTERNAL_PARTIAL_CORR_LEG_DEL_FLIGHT_LEG",   // PAX->SKD #4/4
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "LEG_DELIVERY_ID", "FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.DCSPAX-SKD"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"legs": "$.correlatedLegs[*]"},
          {"shouldNotBeEmpty" : "$.dcsPaxLegDeliveryId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPaxSegmentDeliveryId"}, {"legs": "$.dcsPaxLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"legs": "$.flightLegId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_LEG_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPaxSegmentDeliveryId"}, {"legs": "$.dcsPaxLegDeliveryId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_LEG", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"legs": "$.flightLegId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {

    "name": "INTERNAL_PARTIAL_CORR_LEG_DEL_FLIGHT_LEG",   // PAX->SKD #4/4
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "LEG_DELIVERY_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}]}],

        "blocks": [
          {"base":  "$.mainResource.current.image"},
          {"corr": "$.segmentDeliveries[*]"},
          {"legdel": "$.legDeliveries[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"corr": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"legdel": "$.operatingFlight.carrierCode"}, {"legdel": "$.operatingFlight.number"}, {"legdel": "$.operatingFlight.suffix"}, {"corr": "$.segment.carrierCode"}, {"corr": "$.segment.number"}, {"corr": "$.segment.suffix"}, {"legdel": "$.departure.at"}, {"legdel": "$.departure.iataCode"}, {"legdel": "$.arrival.iataCode"}]}, "expr": "hashM("${getCorrFlightLegId}")"},
        {"name": "REFERENCE_KEY_LEG_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"corr": "$.id"}, {"legdel": "$.id"}]}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"legdel": "$.operatingFlight.carrierCode"}, {"legdel": "$.operatingFlight.number"}, {"legdel": "$.operatingFlight.suffix"}, {"corr": "$.segment.carrierCode"}, {"corr": "$.segment.number"}, {"corr": "$.segment.suffix"}, {"legdel": "$.departure.at"}, {"legdel": "$.departure.iataCode"}, {"legdel": "$.arrival.iataCode"}]}, "expr": ${getCorrFlightLegId}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.segment.carrierCode"}, {"corr": "$.segment.number"}, {"corr": "$.segment.suffix"}, {"corr": "$.segment.operating.carrierCode"}, {"corr": "$.segment.operating.number"}, {"corr": "$.segment.operating.suffix"}, {"corr": "$.segment.departure.at"}]}, "expr" : ${getCorrFlightDateId}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.segment.carrierCode"}, {"corr": "$.segment.number"}, {"corr": "$.segment.suffix"}, {"corr": "$.segment.operating.carrierCode"}, {"corr": "$.segment.operating.number"}, {"corr": "$.segment.operating.suffix"}, {"corr": "$.segment.departure.at"}]}, "expr": "hashM("${getCorrFlightDateId}")"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {

    "name": "INTERNAL_FACT_RESERVATION_IDS_ONLY",   // PAX->SKD #4/4
    "table-selectors": ["DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID","CORR_PASSENGER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "adtchd", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"pax": "$.passenger[?('INFANT' != @.passengerType)]"}]}}
        {"name": "inf", "rs" :{"blocks": [{"base": "$.mainResource.current.image"}, {"pax": "$.passenger[?('INFANT' == @.passengerType)]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "CORR_PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [
          {"rs-name": "adtchd", "blocks": [{"base": "$.id"}]},
          {"rs-name": "inf", "blocks": [{"base": "$.recordLocator"},{"pax": "$.name.firstName"},{"pax": "$.name.lastName"}]}
        ]}, "expr": "hashM({0})"},
        {"name": "CORR_PASSENGER_IDENTIFIER", "column-type": "strColumn", "sources":  {"root-specific": [
          {"rs-name": "adtchd", "blocks": [{"base": "$.id"}]},
          {"rs-name": "inf", "blocks": [{"base": "$.recordLocator"},{"pax": "$.name.firstName"},{"pax": "$.name.lastName"}]}
        ]}},
        {"name": "REFERENCE_KEY", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {

    "name": "INTERNAL_FACT_AIR_SEG_PAX_IDS_ONLY",   // PAX->SKD #4/4
    "table-selectors": ["DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID","CORR_SEGMENT_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "adtchd", "rs" :{"blocks": [{"base": "$.mainResource.current.image"},
          {
            "cartesian": [
              [{"pax": "$.passenger[?('INFANT' != @.passengerType)]"}],
              [{"segDel": "$.segmentDeliveries[*]"}]
            ]
          }]}},
        {"name": "inf", "rs" :{"blocks": [{"base": "$.mainResource.current.image"},
          {
            "cartesian": [
              [{"pax": "$.passenger[?('INFANT' == @.passengerType)]"}],
              [{"segDel": "$.segmentDeliveries[*]"}]
            ]
          }]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segDel" : "$.id"}]}, "expr": "hashM({0})"},
        {"name": "CORR_SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [
          {"rs-name": "adtchd", "blocks": [{"base": "$.id"}, {"segDel" : "$.segment.id"}]},
          {"rs-name": "inf", "blocks": [{"base": "$.recordLocator"},{"pax": "$.name.firstName"},{"pax": "$.name.lastName"},{"segDel" : "$.segment.id"}]}
        ]}, "expr": "hashM({0})"},
        {"name": "CORR_SEGMENT_DELIVERY_IDENTIFIER", "column-type": "strColumn", "sources": {"root-specific": [
          {"rs-name": "adtchd", "blocks": [{"base": "$.id"}, {"segDel" : "$.segment.id"}]},
          {"rs-name": "inf", "blocks": [{"base": "$.recordLocator"},{"pax": "$.name.firstName"},{"pax": "$.name.lastName"},{"segDel" : "$.segment.id"}]}
        ]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "CORR_PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [
          {"rs-name": "adtchd", "blocks": [{"base": "$.id"}]},
          {"rs-name": "inf", "blocks": [{"base": "$.recordLocator"},{"pax": "$.name.firstName"},{"pax": "$.name.lastName"}]}
        ]}, "expr": "hashM({0})"},
        {"name": "CORR_PASSENGER_IDENTIFIER", "column-type": "strColumn", "sources":  {"root-specific": [
          {"rs-name": "adtchd", "blocks": [{"base": "$.id"}]},
          {"rs-name": "inf", "blocks": [{"base": "$.recordLocator"},{"pax": "$.name.firstName"},{"pax": "$.name.lastName"}]}
        ]}},
        {"name": "REFERENCE_KEY", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"segDel" : "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  }
]

