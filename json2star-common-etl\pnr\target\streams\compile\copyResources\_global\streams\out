[debug] Copy resource mappings: 
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\src\main\resources\pnr.conf,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\target\scala-2.12\classes\pnr.conf)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\src\main\resources\pnr.yaml,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\target\scala-2.12\classes\pnr.yaml)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\src\main\resources\PNRJ2S-functional-checks.scala,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\target\scala-2.12\classes\PNRJ2S-functional-checks.scala)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\src\main\resources\PNRJ2S-global-stats-checks.sql,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\target\scala-2.12\classes\PNRJ2S-global-stats-checks.sql)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\src\main\resources\pnrmock.conf,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\pnr\target\scala-2.12\classes\pnrmock.conf)
