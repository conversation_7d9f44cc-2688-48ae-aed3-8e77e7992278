{
  "defaultComment" : "A coment here",
  "partition-spec" : {
    "key" : "PNR_CREATION_DATE",
    "column-name": "PART_PNR_CREATION_MONTH",
    "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
  },
  "tables": [
    {
      "name": "FACT_RESERVATION_HISTO", // table with multiple simple root source
      "mapping" : {
        "merge": {
          "key-columns": ["RESERVATION_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}, {"blocks": [{"base":"$.mainResource.current.image.dummy"}]}],
        "columns": [
          {"name": "RESERVATION_ID", "is-mandatory": "true", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "VERSION", "is-mandatory": "true", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "RESERVATION_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR, version"
        }
      },
    },
    {
      "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_COUPON_HISTO", //PNR-TKTEMD 3/5
      "zorder-columns": ["INTERNAL_ZORDER"],
      "table-selectors": ["PNR_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],
      "mapping": {
        "merge": {
          "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {"name": "basePnr", "rs": {"blocks": [
            {"base": "$.mainResource.current.image"},  // {"doc": "$.documents[?(@.documentType in ['ETICKET','EMD','EMD_ASSOCIATED'])]"}
            {
              "cartesian": [
                [{"ref": "$.ticketingReferences[*]"},
                  {"cartesian": [
                    [{"doc": "$.documents[*]"}],
                    [{"prd": "$.products[*]"}, {"shouldNotBeEmpty2": "$.id"}]
                  ]}
                ]
              ]
            }
          ]}},
          {"name": "associatedPnrs", "rs": {"blocks": [
            {"base": "$.mainResource.current.image"},
            {
              "cartesian": [
                [{"assPnr": "$.associatedPnrs[*]"}, {"creation": "$.creation"}],
                [{"ref": "$.ticketingReferences[*]"},
                  {"cartesian": [
                    [{"doc": "$.documents[*]"}],
                    [{"prd": "$.products[*]"}, {"shouldNotBeEmpty2": "$.id"}]
                  ]}
                ]
              ]
            }
          ]}},
          {"name": "transferredPnrs", "rs": {"blocks": [
            {"base": "$.mainResource.current.image"},
            {
              "cartesian": [
                [{"trans": "$.flightTransfer"}, {"assPnr": "$.associatedOrder"}, {"creation": "$.creation"}],
                [{"ref": "$.ticketingReferences[*]"},
                  {"cartesian": [
                    [{"doc": "$.documents[*]"}],
                    [{"prd": "$.products[*]"}, {"shouldNotBeEmpty2": "$.id"}]
                  ]}
                ]
              ]
            }
          ]}}
        ]
        "columns": [
          {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]}},
          {"name": "TICKET_PRODUCT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"prd": "$.id"}]}},
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
            "sources": {"blocks": [
              {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].id"},
              {"ref": "$.traveler.id"}
            ]}
            , "expr": "hashM({0})", "has-variable": true},
          {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
            "sources": {"root-specific": [
              {"rs-name": "basePnr", "blocks": [{"base": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.localDateTime"}]},
              {"rs-name": "associatedPnrs", "blocks": [{"assPnr": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.localDateTime"}]},
              {"rs-name": "transferredPnrs", "blocks": [{"assPnr": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.localDateTime"}]}
            ]}, "has-variable": true
          },
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].id"}, {"ref": "$.traveler.id"}]}, "has-variable": true},
          {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true",
            "sources": {"root-specific": [
              {"rs-name": "basePnr", "blocks": [{"base": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.localDateTime"}]},
              {"rs-name": "associatedPnrs", "blocks": [{"assPnr": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.localDateTime"}]},
              {"rs-name": "transferredPnrs", "blocks": [{"assPnr": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.subType=='AIR' && (@.id=='{TICKET_PRODUCT_IDENTIFIER}' || @.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}']))].airSegment.departure.localDateTime"}]}
            ]}, "has-variable": true
          },
          {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"doc": "$.documentType"}]}},
          {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains AIR_SEGMENT_PAX-COUPON correlation information as seen by PNR.",
          "granularity": "1 AIR_SEGMENT_PAX-COUPON",
          "links": ["???"]
        }
      }
    }
  ]
}