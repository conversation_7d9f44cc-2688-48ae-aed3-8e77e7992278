model-conf-file = /some/file

stream {
    sink {
      checkpoint-location = "target/test/resources/checkpoints/"
      trigger = "once"
      delta-options = {}
    }
}

spark-conf {
  "spark.app.name" = Foo bar
  "spark.driver.memory" = 2g
}

common {
    domain = "DOMAIN"
    output-database = "input_VaultPipelineIntegration" // should be overwritten in each test
    domain-version = "1_0_0"
    output-path = "/tmp/json2star/output/path"
    shard = "6X"
    home-weight-unit = "KILOGRAMS"
}

cloud-files-conf {
  use-notifications = true
  backfill-interval = 1 day
  include-existing-files = false
}

workspace-secrets {
  workspace-url = { scope: "az-db-dummy", key: "dummy-url"}
  workspace-token = { scope: "az-db-dummy", key: "dummy-token"}
}