{"tables": [{"name": "FACT_TABLE1", "zorder-columns": ["INTERNAL_ZORDER"], "latest": {"histo-table-name": "FACT_TABLE1_HISTO"}, "table-snowflake": {"cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]}}, {"name": "FACT_TABLE1_HISTO", "mapping": {"merge": {"key-columns": ["RESERVATION_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}], "columns": [{"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "RESERVATION_ID", "pit-version": "VERSION", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_VERSION"}}}}]}