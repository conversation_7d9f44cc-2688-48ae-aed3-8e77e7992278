/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scala-lang/scala-library/2.12.15/scala-library-2.12.15.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-core_2.12/3.4.1/spark-core_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-sql_2.12/3.4.1/spark-sql_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-avro_2.12/3.4.1/spark-avro_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/typelevel/cats-kernel_2.12/2.10.0/cats-kernel_2.12-2.10.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/delta/delta-core_2.12/2.4.0/delta-core_2.12-2.4.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/pureconfig/pureconfig_2.12/0.17.6/pureconfig_2.12-0.17.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/typesafe/scala-logging/scala-logging_2.12/3.9.5/scala-logging_2.12-3.9.5.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/mvn-production/com/amadeus/airbi/dih-datalake-common_2.12/4.2.8/dih-datalake-common_2.12-4.2.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/mvn-production/com/amadeus/airbi/dih-datalake-common-spark_2.12/4.2.8/dih-datalake-common-spark_2.12-4.2.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/databricks/dbutils-api_2.12/0.0.6/dbutils-api_2.12-0.0.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/snowflake/spark-snowflake_2.12/2.15.0-spark_3.4/spark-snowflake_2.12-2.15.0-spark_3.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/rogach/scallop_2.12/5.1.0/scallop_2.12-5.1.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/pureconfig/pureconfig-yaml_2.12/0.17.6/pureconfig-yaml_2.12-0.17.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/azure/azure-identity/1.13.2/azure-identity-1.13.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/databricks/databricks-sdk-java/0.16.0/databricks-sdk-java-0.16.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-reports-coupons-api_2.12/3.5.6/ti-reports-coupons-api_2.12-3.5.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/sourceforge/plantuml/plantuml/1.2024.7/plantuml-1.2024.7.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/avro/avro/1.11.1/avro-1.11.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/avro/avro-mapred/1.11.1/avro-mapred-1.11.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/twitter/chill_2.12/0.10.0/chill_2.12-0.10.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/twitter/chill-java/0.10.0/chill-java-0.10.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/xbean/xbean-asm9-shaded/4.22/xbean-asm9-shaded-4.22.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/hadoop/hadoop-client-api/3.3.4/hadoop-client-api-3.3.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/hadoop/hadoop-client-runtime/3.3.4/hadoop-client-runtime-3.3.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-launcher_2.12/3.4.1/spark-launcher_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-kvstore_2.12/3.4.1/spark-kvstore_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-network-common_2.12/3.4.1/spark-network-common_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-network-shuffle_2.12/3.4.1/spark-network-shuffle_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-unsafe_2.12/3.4.1/spark-unsafe_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/javax/activation/activation/1.1.1/activation-1.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/curator/curator-recipes/2.13.0/curator-recipes-2.13.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/zookeeper/zookeeper/3.6.3/zookeeper-3.6.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/jakarta/servlet/jakarta.servlet-api/4.0.3/jakarta.servlet-api-4.0.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/commons/commons-compress/1.22/commons-compress-1.22.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/commons/commons-text/1.10.0/commons-text-1.10.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/commons-io/commons-io/2.13.0/commons-io-2.13.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/slf4j/slf4j-api/2.0.11/slf4j-api-2.0.11.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/slf4j/jul-to-slf4j/2.0.6/jul-to-slf4j-2.0.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/slf4j/jcl-over-slf4j/2.0.6/jcl-over-slf4j-2.0.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/logging/log4j/log4j-slf4j2-impl/2.19.0/log4j-slf4j2-impl-2.19.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/logging/log4j/log4j-api/2.19.0/log4j-api-2.19.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/logging/log4j/log4j-core/2.19.0/log4j-core-2.19.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/logging/log4j/log4j-1.2-api/2.19.0/log4j-1.2-api-2.19.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/ning/compress-lzf/1.1.2/compress-lzf-1.1.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/xerial/snappy/snappy-java/1.1.10.1/snappy-java-1.1.10.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/luben/zstd-jni/1.5.2-5/zstd-jni-1.5.2-5.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/roaringbitmap/RoaringBitmap/0.9.38/RoaringBitmap-0.9.38.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scala-lang/modules/scala-xml_2.12/2.1.0/scala-xml_2.12-2.1.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scala-lang/scala-reflect/2.12.15/scala-reflect-2.12.15.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/json4s/json4s-jackson_2.12/3.7.0-M11/json4s-jackson_2.12-3.7.0-M11.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/jersey/core/jersey-client/2.36/jersey-client-2.36.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/jersey/core/jersey-common/2.36/jersey-common-2.36.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/jersey/core/jersey-server/2.36/jersey-server-2.36.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/jersey/containers/jersey-container-servlet/2.36/jersey-container-servlet-2.36.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/jersey/containers/jersey-container-servlet-core/2.36/jersey-container-servlet-core-2.36.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/jersey/inject/jersey-hk2/2.36/jersey-hk2-2.36.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-all/4.1.87.Final/netty-all-4.1.87.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport-native-epoll/4.1.110.Final/netty-transport-native-epoll-4.1.110.Final-linux-x86_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport-native-epoll/4.1.110.Final/netty-transport-native-epoll-4.1.110.Final-linux-aarch_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport-native-kqueue/4.1.110.Final/netty-transport-native-kqueue-4.1.110.Final-osx-aarch_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport-native-kqueue/4.1.110.Final/netty-transport-native-kqueue-4.1.110.Final-osx-x86_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/clearspring/analytics/stream/2.9.6/stream-2.9.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/dropwizard/metrics/metrics-core/4.2.15/metrics-core-4.2.15.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/dropwizard/metrics/metrics-jvm/4.2.15/metrics-jvm-4.2.15.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/dropwizard/metrics/metrics-json/4.2.15/metrics-json-4.2.15.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/dropwizard/metrics/metrics-graphite/4.2.15/metrics-graphite-4.2.15.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/dropwizard/metrics/metrics-jmx/4.2.15/metrics-jmx-4.2.15.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/fasterxml/jackson/core/jackson-databind/2.17.2/jackson-databind-2.17.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/fasterxml/jackson/module/jackson-module-scala_2.12/2.14.2/jackson-module-scala_2.12-2.14.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/ivy/ivy/2.5.1/ivy-2.5.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/oro/oro/2.0.8/oro-2.0.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/razorvine/pickle/1.3/pickle-1.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/sf/py4j/py4j/0.10.9.7/py4j-0.10.9.7.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-tags_2.12/3.4.1/spark-tags_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/commons/commons-crypto/1.1.0/commons-crypto-1.1.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/rocksdb/rocksdbjni/7.9.2/rocksdbjni-7.9.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/univocity/univocity-parsers/2.9.1/univocity-parsers-2.9.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-sketch_2.12/3.4.1/spark-sketch_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/spark/spark-catalyst_2.12/3.4.1/spark-catalyst_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/orc/orc-core/1.8.4/orc-core-1.8.4-shaded-protobuf.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/orc/orc-mapreduce/1.8.4/orc-mapreduce-1.8.4-shaded-protobuf.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/hive/hive-storage-api/2.8.1/hive-storage-api-2.8.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/parquet/parquet-column/1.12.3/parquet-column-1.12.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/parquet/parquet-hadoop/1.12.3/parquet-hadoop-1.12.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/protobuf/protobuf-java/3.19.3/protobuf-java-3.19.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/tukaani/xz/1.9/xz-1.9.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/delta/delta-storage/2.4.0/delta-storage-2.4.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/antlr/antlr4-runtime/4.9.3/antlr4-runtime-4.9.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/pureconfig/pureconfig-core_2.12/0.17.6/pureconfig-core_2.12-0.17.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/pureconfig/pureconfig-generic_2.12/0.17.6/pureconfig-generic_2.12-0.17.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/snowflake/snowflake-ingest-sdk/0.10.8/snowflake-ingest-sdk-0.10.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/snowflake/snowflake-jdbc/3.14.4/snowflake-jdbc-3.14.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/azure/azure-core/1.51.0/azure-core-1.51.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/azure/azure-core-http-netty/1.15.3/azure-core-http-netty-1.15.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/azure/azure-json/1.2.0/azure-json-1.2.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/microsoft/azure/msal4j/1.16.2/msal4j-1.16.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/microsoft/azure/msal4j-persistence-extension/1.3.0/msal4j-persistence-extension-1.3.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/java/dev/jna/jna-platform/5.13.0/jna-platform-5.13.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/ini4j/ini4j/0.5.4/ini4j-0.5.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/auth/google-auth-library-oauth2-http/1.20.0/google-auth-library-oauth2-http-1.20.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-models-refdata_2.12/4.5.2/ti-models-refdata_2.12-4.5.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-models-ticketing_2.12/8.4.0/ti-models-ticketing_2.12-8.4.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-ich-exchange-rates-api_2.12/4.0.8/ti-ich-exchange-rates-api_2.12-4.0.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/julien-truffaut/monocle-core_2.12/2.1.0/monocle-core_2.12-2.1.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/julien-truffaut/monocle-macro_2.12/2.1.0/monocle-macro_2.12-2.1.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-models-sql_2.12/5.3.3/ti-models-sql_2.12-5.3.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/typelevel/cats-core_2.12/2.1.1/cats-core_2.12-2.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/typelevel/cats-collections-core_2.12/0.9.0/cats-collections-core_2.12-0.9.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scilab/forge/jlatexmath/1.0.7/jlatexmath-1.0.7.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/elk/org.eclipse.elk.core/0.9.1/org.eclipse.elk.core-0.9.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/elk/org.eclipse.elk.alg.layered/0.9.1/org.eclipse.elk.alg.layered-0.9.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/elk/org.eclipse.elk.alg.mrtree/0.9.1/org.eclipse.elk.alg.mrtree-0.9.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/fasterxml/jackson/core/jackson-core/2.17.2/jackson-core-2.17.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/avro/avro-ipc/1.11.1/avro-ipc-1.11.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/esotericsoftware/kryo-shaded/4.0.2/kryo-shaded-4.0.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/fusesource/leveldbjni/leveldbjni-all/1.8/leveldbjni-all-1.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/fasterxml/jackson/core/jackson-annotations/2.17.2/jackson-annotations-2.17.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/crypto/tink/tink/1.7.0/tink-1.7.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/curator/curator-framework/2.13.0/curator-framework-2.13.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/zookeeper/zookeeper-jute/3.6.3/zookeeper-jute-3.6.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/yetus/audience-annotations/0.13.0/audience-annotations-0.13.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/roaringbitmap/shims/0.9.38/shims-0.9.38.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/json4s/json4s-core_2.12/3.7.0-M11/json4s-core_2.12-3.7.0-M11.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/jakarta/ws/rs/jakarta.ws.rs-api/2.1.6/jakarta.ws.rs-api-2.1.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/hk2/external/jakarta.inject/2.6.1/jakarta.inject-2.6.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/hk2/osgi-resource-locator/1.0.3/osgi-resource-locator-1.0.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/hk2/hk2-locator/2.6.1/hk2-locator-2.6.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/javassist/javassist/3.25.0-GA/javassist-3.25.0-GA.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-buffer/4.1.110.Final/netty-buffer-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-codec/4.1.110.Final/netty-codec-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-codec-http/4.1.110.Final/netty-codec-http-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-codec-http2/4.1.110.Final/netty-codec-http2-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-codec-socks/4.1.110.Final/netty-codec-socks-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-common/4.1.110.Final/netty-common-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-handler/4.1.110.Final/netty-handler-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport-native-unix-common/4.1.110.Final/netty-transport-native-unix-common-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-handler-proxy/4.1.110.Final/netty-handler-proxy-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-resolver/4.1.110.Final/netty-resolver-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport/4.1.110.Final/netty-transport-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport-classes-epoll/4.1.110.Final/netty-transport-classes-epoll-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport-classes-kqueue/4.1.110.Final/netty-transport-classes-kqueue-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/thoughtworks/paranamer/paranamer/2.8/paranamer-2.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scala-lang/modules/scala-parser-combinators_2.12/2.1.1/scala-parser-combinators_2.12-2.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/codehaus/janino/janino/3.1.9/janino-3.1.9.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/codehaus/janino/commons-compiler/3.1.9/commons-compiler-3.1.9.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/arrow/arrow-vector/11.0.0/arrow-vector-11.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/arrow/arrow-memory-netty/11.0.0/arrow-memory-netty-11.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/orc/orc-shims/1.8.4/orc-shims-1.8.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/airlift/aircompressor/0.21/aircompressor-0.21.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/threeten/threeten-extra/1.7.1/threeten-extra-1.7.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/parquet/parquet-common/1.12.3/parquet-common-1.12.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/parquet/parquet-encoding/1.12.3/parquet-encoding-1.12.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/parquet/parquet-format-structures/1.12.3/parquet-format-structures-1.12.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/parquet/parquet-jackson/1.12.3/parquet-jackson-1.12.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/typesafe/config/1.4.3/config-1.4.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/pureconfig/pureconfig-generic-base_2.12/0.17.6/pureconfig-generic-base_2.12-0.17.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/chuusai/shapeless_2.12/2.3.10/shapeless_2.12-2.3.10.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/azure/azure-xml/1.1.0/azure-xml-1.1.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.17.2/jackson-datatype-jsr310-2.17.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/projectreactor/reactor-core/3.4.38/reactor-core-3.4.38.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-tcnative-boringssl-static/2.0.65.Final/netty-tcnative-boringssl-static-2.0.65.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/projectreactor/netty/reactor-netty-http/1.0.45/reactor-netty-http-1.0.45.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/nimbusds/oauth2-oidc-sdk/11.9.1/oauth2-oidc-sdk-11.9.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/auto/value/auto-value-annotations/1.10.4/auto-value-annotations-1.10.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/auth/google-auth-library-credentials/1.20.0/google-auth-library-credentials-1.20.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/http-client/google-http-client/1.43.3/google-http-client-1.43.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/http-client/google-http-client-gson/1.43.3/google-http-client-gson-1.43.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/guava/guava/32.0.0-android/guava-32.0.0-android.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-scalaxb-abr_2.12/1.0.2/ti-scalaxb-abr_2.12-1.0.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-typeclasses_2.12/1.6.0/ti-typeclasses_2.12-1.6.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-gdpr-annotations_2.12/3.4.1/ti-gdpr-annotations_2.12-3.4.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-models-edifact_2.12/2.0.22/ti-models-edifact_2.12-2.0.22.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/github/chronoscala/chronoscala_2.12/2.0.10/chronoscala_2.12-2.0.10.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-scalaxb-apra_2.12/2.4.0/ti-scalaxb-apra_2.12-2.4.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/sweet-delights/delightful-parsing_2.12/0.8.0/delightful-parsing_2.12-0.8.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/typelevel/cats-free_2.12/2.1.1/cats-free_2.12-2.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-io_2.12/4.0.0/ti-io_2.12-4.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-models-common_2.12/3.5.0/ti-models-common_2.12-3.5.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-anonymization_2.12/1.9.0/ti-anonymization_2.12-1.9.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/typelevel/cats-macros_2.12/2.1.1/cats-macros_2.12-2.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/typelevel/algebra_2.12/2.0.0/algebra_2.12-2.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scilab/forge/jlatexmath-font-greek/1.0.7/jlatexmath-font-greek-1.0.7.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scilab/forge/jlatexmath-font-cyrillic/1.0.7/jlatexmath-font-cyrillic-1.0.7.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/elk/org.eclipse.elk.graph/0.9.1/org.eclipse.elk.graph-0.9.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/emf/org.eclipse.emf.ecore.xmi/2.12.0/org.eclipse.emf.ecore.xmi-2.12.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/elk/org.eclipse.elk.alg.common/0.9.1/org.eclipse.elk.alg.common-0.9.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/esotericsoftware/minlog/1.3.0/minlog-1.3.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/objenesis/objenesis/2.5.1/objenesis-2.5.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/curator/curator-client/2.13.0/curator-client-2.13.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/json4s/json4s-ast_2.12/3.7.0-M11/json4s-ast_2.12-3.7.0-M11.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/json4s/json4s-scalap_2.12/3.7.0-M11/json4s-scalap_2.12-3.7.0-M11.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/hk2/external/aopalliance-repackaged/2.6.1/aopalliance-repackaged-2.6.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/hk2/hk2-api/2.6.1/hk2-api-2.6.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/glassfish/hk2/hk2-utils/2.6.1/hk2-utils-2.6.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/arrow/arrow-format/11.0.0/arrow-format-11.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/arrow/arrow-memory-core/11.0.0/arrow-memory-core-11.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/flatbuffers/flatbuffers-java/1.12.0/flatbuffers-java-1.12.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/ow2/asm/asm/9.3/asm-9.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-tcnative-classes/2.0.65.Final/netty-tcnative-classes-2.0.65.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-tcnative-boringssl-static/2.0.65.Final/netty-tcnative-boringssl-static-2.0.65.Final-linux-x86_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-tcnative-boringssl-static/2.0.65.Final/netty-tcnative-boringssl-static-2.0.65.Final-linux-aarch_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-tcnative-boringssl-static/2.0.65.Final/netty-tcnative-boringssl-static-2.0.65.Final-osx-x86_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-tcnative-boringssl-static/2.0.65.Final/netty-tcnative-boringssl-static-2.0.65.Final-osx-aarch_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-tcnative-boringssl-static/2.0.65.Final/netty-tcnative-boringssl-static-2.0.65.Final-windows-x86_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-resolver-dns/4.1.109.Final/netty-resolver-dns-4.1.109.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-resolver-dns-native-macos/4.1.109.Final/netty-resolver-dns-native-macos-4.1.109.Final-osx-x86_64.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/projectreactor/netty/reactor-netty-core/1.0.45/reactor-netty-core-1.0.45.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/stephenc/jcip/jcip-annotations/1.0-1/jcip-annotations-1.0-1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/nimbusds/content-type/2.3/content-type-2.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/nimbusds/lang-tag/1.7/lang-tag-1.7.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/nimbusds/nimbus-jose-jwt/9.37.3/nimbus-jose-jwt-9.37.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/checkerframework/checker-qual/3.33.0/checker-qual-3.33.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-buildinfo_2.12/2.0.1/ti-buildinfo_2.12-2.0.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-scalaxb-edifact_2.12/0.2.3/ti-scalaxb-edifact_2.12-0.2.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/poi/poi/4.1.2/poi-4.1.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/sweet-delights/delightful-typeclasses_2.12/0.1.1/delightful-typeclasses_2.12-0.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-mvn-production/com/amadeus/ti/EdifactParser/4.1.4/EdifactParser-4.1.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/ti/ti-models-macros_2.12/3.0.1/ti-models-macros_2.12-3.0.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/gdpr/anonymization-pii-logic_2.12/*******/anonymization-pii-logic_2.12-*******.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/emf/org.eclipse.emf.common/2.12.0/org.eclipse.emf.common-2.12.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/emf/org.eclipse.emf.ecore/2.12.0/org.eclipse.emf.ecore-2.12.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-codec-dns/4.1.109.Final/netty-codec-dns-4.1.109.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-resolver-dns-classes-macos/4.1.109.Final/netty-resolver-dns-classes-macos-4.1.109.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/grpc/grpc-context/1.27.2/grpc-context-1.27.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/siemens/ct/exi/exificient/1.0.4/exificient-1.0.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/ati-sbt-production/com/amadeus/gdpr/anonymization-methods_2.12/*******/anonymization-methods_2.12-*******.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/netty/netty-transport-native-epoll/4.1.110.Final/netty-transport-native-epoll-4.1.110.Final.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/siemens/ct/exi/exificient-core/1.0.4/exificient-core-1.0.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/siemens/ct/exi/exificient-grammars/1.0.4/exificient-grammars-1.0.4.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/javax/xml/bind/jaxb-api/2.2.11/jaxb-api-2.2.11.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/sun/xml/bind/jaxb-core/2.2.11/jaxb-core-2.2.11.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/sun/xml/bind/jaxb-impl/2.2.11/jaxb-impl-2.2.11.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/joda-time/joda-time/2.10.2/joda-time-2.10.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/googlecode/libphonenumber/libphonenumber/8.10.16/libphonenumber-8.10.16.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/xerces/xercesImpl/2.12.0/xercesImpl-2.12.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.jar
