[debug] Created transactional ClassFileManager with tempDir = C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\core\target\scala-2.12\classes.bak
[debug] About to delete class files:
[debug] We backup class files:
[debug] Created transactional ClassFileManager with tempDir = C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\core\target\scala-2.12\classes.bak
[debug] About to delete class files:
[debug] We backup class files:
[debug] Rolling back changes to class files.
[debug] Removing generated classes:
[debug] Restoring class files: 
[debug] Removing the temporary directory used for backing up class files: C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\core\target\scala-2.12\classes.bak
