[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mAbout to delete class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mWe backup class files:[0m
[0m[[0m[0mdebug[0m] [0m[0mRolling back changes to class files.[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving generated classes:[0m
[0m[[0m[0mdebug[0m] [0m[0mRestoring class files: [0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes.bak[0m
