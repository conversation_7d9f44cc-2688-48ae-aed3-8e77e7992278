applyDecimal: "element_at(split({0},'-'),1)/(10*element_at(split({0},'-'),2))"

"defaultComment" : "ORDEROFFER star schema",
"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.data": "OrderOrderChangeEvent",
      "$.attachments": "attachments",
      "$.data.currentImage.orderItems.lifecycle": "OrderOrderLifecycle",
      // point to the right property of a Schema as mismatch between json/yaml paths (expected stakeholders.personalData.personalDataRef)
      "$.data.currentImage.stakeholders.personalDataRef": "OrderServiceFulfillmentStakeholderPersonalData.personalDataRef",
      // point to the right Schema as mismatch between json/yaml paths (expected stakeholders.seller.pointOfSale)
      "$.data.currentImage.stakeholders.pointOfBusiness": "OrderStakeholderPointOfBusiness",
      //  point to the right Schema as mismatch in asyncapi yaml - Leg.v1
      "$.data.currentImage.orderItems.services.context.soldProduct.productInstance.leg": "Leg\\.v1",
      "$.data.currentImage.orderItems.services.context.soldProduct.productInstance.leg.deis.value": "DEI\\.v2.\\value"
    }
  }
},
"tables": [
  {
    "name": "FACT_OFFER",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_OFFER_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATE_TIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information related to the offer selected", "granularity": "1 OFFER"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "VERSION_OFFER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"att": "$.attachments[*]"}, {"offer": "$.payload.offerData.offer"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}, {"offer": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.id"}]},
          "meta": {"description": {"value": "Functional key: offerId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_SET_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"att": "$.payload.offerData.offerSet.id"}]},
          "meta": {"gdpr-zone": "orange"}},
        {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.status"}]}},
        {"name": "CREATION_DATETIME_OFFER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_OFFER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.version"}]},
          "meta": {"description": {"value": "Version of the offer", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Offer validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Offer validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master" : {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION_OFFER",
          "valid-from": "VALID_FROM",
          "valid-to": "VALID_TO",
          "is-last": "IS_LATEST_VERSION"
        }
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_OFFER_ITEM_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information about the selected offer items.", "granularity": "1 OFFER ITEM"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "OFFER_ITEM_ID", "VERSION_OFFER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"att": "$.attachments[*]"}, {"offer": "$.payload.offerData.offer"}, {"item": "$.offerItems[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}, {"offer": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_ITEM_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}]},
          "meta": {"description": {"value": "Functional key: offerItemId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_OFFER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_OFFER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.version"}]},
          "meta": {"description": {"value": "Version of the offer", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Offer validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Offer validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },

  {
    "name": "FACT_OFFER_ITEM_PRICE",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_OFFER_ITEM_PRICE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_PRICE_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information about the selected offer item pricing.", "granularity": "1 OFFER ITEM PRICE ELEMENT"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "OFFER_ITEM_PRICE_ID", "VERSION_OFFER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"att": "$.attachments[*]"}, {"offer": "$.payload.offerData.offer"}, {"item": "$.offerItems[*]"}, {"price": "$.priceInformation.amountsCollections[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}, {"offer": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_ITEM_PRICE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"price": "$.type"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"price": "$.type"}]},
          "meta": {"description": {"value": "Functional key: offerItemId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "PRICE_TYPE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"price": "$.type"}]}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.totalAmount.type"}]}},
        {"name": "AMOUNT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"price": "$.totalAmount.value"}, {"price": "$.totalAmount.decimalPlaces"}]}, "expr": ${applyDecimal}},
        {"name": "AMOUNT_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"price": "$.totalAmount.currency"}]}},
        {"name": "AMOUNT_VALUE", "column-type": "floatColumn", "sources": {}},
        {"name": "AMOUNT_CURRENCY", "column-type": "strColumn", "sources": {}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "strColumn", "sources": {}},
        {"name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_OFFER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_OFFER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.version"}]},
          "meta": {"description": {"value": "Version of the offer", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Offer validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Offer validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "AMOUNT_VALUE_ORIGINAL", "src-unit-col": "AMOUNT_CURRENCY_ORIGINAL", "src-date-col" : "CREATION_DATETIME_OFFER", "dst-col": "AMOUNT_VALUE", "dst-unit-col": "AMOUNT_CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"},
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_PRICE_COMPOSITION",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_PRICE_COMPOSITION_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information about the selected offer item pricing composition.", "granularity": "1 OFFER ITEM PRICE COMPOSITION ELEMENT"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "OFFER_ITEM_PRICE_COMPOSITION_ID", "VERSION_OFFER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"att": "$.attachments[*]"}, {"offer": "$.payload.offerData.offer"}, {"item": "$.offerItems[*]"}, {"price": "$.priceInformation.amountsCollections[*]"}, {"comp": "$.totalAmount.composition[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}, {"offer": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_ITEM_PRICE_COMPOSITION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"price": "$.type"}, {"comp": "$.type"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"price": "$.type"}, {"comp": "$.type"}]},
          "meta": {"description": {"value": "Functional key: offerItemId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "AMOUNT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.type"}]}},
        {"name": "AMOUNT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"comp": "$.value"}, {"comp": "$.decimalPlaces"}]}, "expr": ${applyDecimal}},
        {"name": "AMOUNT_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"comp": "$.currency"}]}},
        {"name": "AMOUNT_VALUE", "column-type": "floatColumn", "sources": {}},
        {"name": "AMOUNT_CURRENCY", "column-type": "strColumn", "sources": {}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "strColumn", "sources": {}},

        {"name": "OFFER_ITEM_PRICE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"price": "$.type"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_OFFER_ITEM_PRICE_HISTO", "column": "OFFER_ITEM_PRICE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_OFFER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_OFFER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.version"}]},
          "meta": {"description": {"value": "Version of the offer", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Offer validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Offer validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "AMOUNT_VALUE_ORIGINAL", "src-unit-col": "AMOUNT_CURRENCY_ORIGINAL", "src-date-col" : "CREATION_DATETIME_OFFER", "dst-col": "AMOUNT_VALUE", "dst-unit-col": "AMOUNT_CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"},
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_RETAILING",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_OFFER_ITEM_RETAILING_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_RETAILING_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information about the selected offer item retailing.", "granularity": "1 OFFER ITEM RETAILING ELEMENT"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "OFFER_ITEM_RETAILING_ID", "VERSION_OFFER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"att": "$.attachments[*]"}, {"offer": "$.payload.offerData.offer"}, {"item": "$.offerItems[*]"}, {"ret": "$.retailing.branding.fareFamilies[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}, {"offer": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_ITEM_RETAILING_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"ret": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"ret": "$.id"}]},
          "meta": {"description": {"value": "Functional key: offerItemRetailingId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"ret": "$.id"}]}},
        {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"ret": "$.code"}]}},
        {"name": "OWNER", "column-type": "strColumn", "sources": {"blocks": [{"ret": "$.owner"}]}},
        {"name": "RANKING", "column-type": "strColumn", "sources": {"blocks": [{"ret": "$.ranking"}]}},
        {"name": "NAME", "column-type": "strColumn", "sources": {"blocks": [{"ret": "$.name"}]}},
        {"name": "COMMERCIAL_FARE_FAMILY", "column-type": "strColumn", "sources": {"blocks": [{"ret": "$.commercialFareFamily"}]}},
        {"name": "FARE_FAMILY_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"ret": "$.fareFamilyDescription"}]}},
        {"name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_OFFER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_OFFER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.version"}]},
          "meta": {"description": {"value": "Version of the offer", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Offer validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Offer validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_OFFICE",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_OFFER_ITEM_OFFICE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_OFFICE_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information about the selected offer item offices.", "granularity": "1 OFFER ITEM OFFICE"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "OFFER_ITEM_OFFICE_ID", "VERSION_OFFER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"att": "$.attachments[*]"}, {"offer": "$.payload.offerData.offer"}, {"item": "$.offerItems[*]"}, {"off": "$.offices[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}, {"offer": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_ITEM_OFFICE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"off": "$.officeDetails.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"off": "$.officeDetails.id"}]},
          "meta": {"description": {"value": "Functional key: offerItemRetailingId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"off": "$.officeDetails.id"}]}},
        {"name": "IATA_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"off": "$.officeDetails.IATANumber"}]}},
        {"name": "OFFICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"off": "$.officeType"}]}},
        {"name": "COUNTRY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"off": "$.countryCode"}]}},
        {"name": "CITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"off": "$.trueCityCode"}]}},
        {"name": "IATA_LINE_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"off": "$.iataLineNumber"}]}},
        {"name": "ERSP_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"off": "$.erspNumber"}]}},
        {"name": "ARC_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"off": "$.arcNumber"}]}},
        {"name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_OFFER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_OFFER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.version"}]},
          "meta": {"description": {"value": "Version of the offer", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Offer validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Offer validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_AIR_BOUNDS",
    "zorder-column": "INTERNAL_ZORDER",
    "latest": {
      "histo-table-name": "FACT_OFFER_ITEM_AIR_BOUNDS_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },
  {
    "name": "FACT_OFFER_ITEM_AIR_BOUNDS_HISTO",
    "zorder-column": "INTERNAL_ZORDER",
    "mapping": {
      "description": {"description": "Contains information about the selected offer item air bounds.", "granularity": "1 OFFER ITEM AIR BOUND"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "OFFER_ITEM_AIR_BOUNDS_ID", "VERSION_OFFER"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"att": "$.attachments[*]"}, {"offer": "$.payload.offerData.offer"}, {"item": "$.offerItems[*]"}, {"air": "$.airBounds[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}, {"offer": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OFFER_ITEM_AIR_BOUNDS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"air": "$.id"}]}, "expr": "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.id"}, {"air": "$.id"}]},
          "meta": {"description": {"value": "Functional key: offerItemRetailingId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "BOARD_POINT", "column-type": "strColumn", "sources": {"blocks": [{"air": "$.boardPointIataCode"}]}},
        {"name": "OFF_POINT", "column-type": "strColumn", "sources": {"blocks": [{"air": "$.offPointIataCode"}]}},
        {"name": "ELAPSED_TRAVELING_TIME", "column-type": "strColumn", "sources": {"blocks": [{"air": "$.elapsedTravelingTime"}]}},
        {"name": "BOUND_POSITION", "column-type": "strColumn", "sources": {"blocks": [{"air": "$.boundPosition"}]}},
        {"name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.id"}]}, "expr": "hashM({0})",
          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CREATION_DATETIME_OFFER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}]},
          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION_OFFER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.version"}]},
          "meta": {"description": {"value": "Version of the offer", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.lastUpdate.dateTime"}]},
          "meta": {"description": {"value": "Offer validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Offer validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
    }
  },

  //  {
  //    "name": "FACT_OFFER_CONSTRUCTION_STEP",
  //    "zorder-column": "INTERNAL_ZORDER",
  //    "latest": {
  //      "histo-table-name": "FACT_OFFER_CONSTRUCTION_STEP_HISTO"
  //    },
  //    "table-snowflake": {
  //      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
  //    }
  //  },
  //  {
  //    "name": "FACT_OFFER_CONSTRUCTION_STEP_HISTO",
  //    "zorder-column": "INTERNAL_ZORDER",
  //    "mapping": {
  //      "description": {"description": "Contains information related to offer construction steps", "granularity": "1 OFFER Construction Step"},
  //      "merge": {
  //        "key-columns": ["INTERNAL_ZORDER", "OFFER_CONSTRUCTION_STEP_ID", "VERSION_OFFER"],
  //        "if-dupe-take-higher": ["LOAD_DATE"]
  //      },
  //      "root-sources": [{"blocks": [{"att": "$.attachments[*]"}, {"offer": "$.payload.offerData.offer"}, {"cstep": "$.construction.constructionSteps[*]"}]}],
  //      "columns": [
  //        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}, {"offer": "$.id"}]},
  //          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
  //        {"name": "OFFER_CONSTRUCTION_STEP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.id"}, {"cstep": "$.constructionProcess"}]}, "expr": "hashM({0})",
  //          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
  //        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.id"}, {"cstep": "$.constructionProcess"}]},
  //          "meta": {"description": {"value": "Functional key: offerConstructionStepId", "rule": "replace"}, "gdpr-zone": "orange"}},
  //        {"name": "PROCESS", "column-type": "strColumn", "sources": {"blocks": [{"cstep": "$.constructionProcess"}]},
  //          "meta": {"gdpr-zone": "green"}},
  //        {"name": "DATA_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"cstep": "$.constructionData.constructionDataType"}]},
  //          "meta": {"gdpr-zone": "green"}},
  //        {"name": "CREATION_METHOD", "column-type": "strColumn", "sources": {"blocks": [{"cstep": "$.constructionData.creationMethod"}]},
  //          "meta": {"gdpr-zone": "green"}},
  //        {"name": "OFFER_ID", "column-type": "strColumn", "sources": {"blocks": [{"offer": "$.id"}]}, "expr": "hashM({0})",
  //          "fk": [{"table": "FACT_OFFER_HISTO", "column": "OFFER_ID"}], "meta": {"gdpr-zone": "green"}},
  //        {"name": "CREATION_DATETIME_OFFER", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.creation.dateTime"}]},
  //          "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
  //        {"name": "VERSION_OFFER", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.version"}]},
  //          "meta": {"description": {"value": "Version of the offer", "rule": "replace"}, "gdpr-zone": "green"}},
  //        {"name": "VALID_FROM", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"offer": "$.lifecycle.lastUpdate.dateTime"}]},
  //          "meta": {"description": {"value": "Offer validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
  //        {"name": "VALID_TO", "column-type": "timestampColumn", "sources": {},
  //          "meta": {"description": {"value": "Offer validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
  //        {"name": "IS_LATEST_VERSION", "column-type": "booleanColumn", "sources": {},
  //          "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
  //      ],
  //      "pit": {
  //        "type": "secondary-pit-table"
  //      }
  //    },
  //    "table-snowflake": {
  //      "cluster-by": ["date_trunc('WEEK', CREATION_DATETIME_OFFER)"]
  //    }
  //  }
]