  "tables": [
    {
        "name": "FACT_RESERVATION_HISTO",
        "mapping": {
          "merge": {
            "key-columns": ["RESERVATION_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [
            {
              "blocks": [
                {"base": "$.mainResource.current"},
                {"cartesian":
                  [
                    [{"cart1": "$.image"}],
                    [{"cart2": "$.toto"}]
                  ]
                }
              ]
            }
          ],
          "columns": [
            { "name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})" },
            {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
            {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]}},
            {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "RESERVATION_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          }
        }
    }
]

