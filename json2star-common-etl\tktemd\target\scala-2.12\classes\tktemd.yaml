swagger: '2.0'
################################################################################
#                              API Information                                 #
################################################################################
info:
  version: 4.3.0
  title: Amadeus Dynamic Intelligence Hub API
  description: >-
    This document describes DIH output for
    1. Amadeus REST/JSON API endpoint to retrieve a ticket or EMD from Dynamic Intelligence Hub
    2. Travel Document Data Push Feed
  termsOfService: http://amadeus.com/todo/terms
  license:
    name: Proprietary
    url: http://amadeus.com/todo/licenses/LICENSE-1.0.html
################################################################################
#                  Host, Base Path, Schemes and Content Types                  #
################################################################################
host: pulse-api-tkt-pulse-qa.router.openshift.int-a.pulse-tst1.*************.nip.io
basePath: /
schemes:
  - https
consumes:
  - application/json
produces:
  - application/vnd.amadeus+json

################################################################################
#                                  Security                                    #
################################################################################
securityDefinitions:
  basicAuth:
    type: basic
    description: HTTP Basic Authentication. Works 'HTTPS'.

################################################################################
#                                   Parameters                                 #
################################################################################
parameters:
  id:
    name: fid
    description: Contains the TKT/EMD suffixed by its creation date, which enable to uniquely identify a TravelDocument on DIH. Format is "TICKET_NUMBER-YYYY-MM-DD".
    in: path
    required: true
    type: string

################################################################################
#                                    Paths                                     #
################################################################################
paths:
########## DIH ticketing APIs definition ##########
  /ticketing/processed-travel-documents/{fid}:
    get:
      tags:
        - TravelDocumentAPI
      operationId: getProcessedTravelDocumentByFid
      summary: >-
        Get the DIH processed-travel-document resource corresponding to the requested fid
        It can either be an electronic ticket or an Electronic Miscellaneous Document (EMD)
      description: Returns the DIH TKT/EMD payload matching the requested fid, as well as its correlations with other domains
      security:
        - basicAuth: []
      consumes: []
      produces: []
      parameters:
        - $ref: '#/parameters/id'
      responses:
        '200':
          description: Successful response, processed-travel-document resource found
          schema:
            $ref: '#/definitions/travelDocumentApiResponse'
        '400':
          description: Bad request
          schema:
            $ref: '#/definitions/400Errors'
        '401':
          description: Unauthorized
          schema:
            $ref: '#/definitions/401Errors'
        '404':
          description: Not found
          schema:
            $ref: '#/definitions/404Errors'
        '500':
          description: Internal error
          schema:
            $ref: '#/definitions/500Errors'
            
########## DIH ticketing feeds definition ##########
  /ticketing/ticket-feed:
    post:
      tags:
        - FEED
      summary: Ticket Data Push
      description: >-
        Ticket feed generated by the Online Data Push Micro-Service of Agility Platform
      responses:
        default:
          description: >-
            Payload identical to the API GET /ticketing/tickets output except
            that it includes the 'events' structure. Please be aware this is NOT
            a POST API, rather this explains the format of the data pushed out
            by Amadeus.
          schema:
            $ref: '#/definitions/ticketDataPush'
  /ticketing/emd-feed:
    post:
      tags:
        - FEED
      summary: EMD Data Push
      description: >-
        EMD feed generated by the Online Data Push Micro-Service of Agility Platform
      responses:
        default:
          description: >-
            Payload identical to the API GET /ticketing/tickets output except
            that it includes the 'events' structure. Please be aware this is NOT
            a POST API, rather this explains the format of the data pushed out
            by Amadeus.
          schema:
            $ref: '#/definitions/emdDataPush'
  /ticketing/ticketing-correlations-feed:
    post:
      tags:
        - FEED
      summary: Ticket Correlation Data Push
      description: >-
        Ticket & EMD correlation feed generated by the Online Data Push Micro-Service of Agility Platform
      responses:
        default:
          description: >-
            Model for the Ticket and EMD correlations data push feed. Please be
            aware this is NOT a POST API, rather this explains the format of the
            data pushed out by Amadeus.
          schema:
            $ref: '#/definitions/ticketingCorrelationsPush'
definitions:
  ########## Payloads ##########
  ticketDataPush:
    type: object
    properties:
      meta:
        $ref: '#/definitions/Meta'
      processedTicket:
        $ref: '#/definitions/AirTravelDocument'
      previousRecord:
        description: Representation of the previous version of the record in the form of JSON Patch as defined by RFC 6902 (see https://tools.ietf.org/html/rfc6902). The actual JSON of this previous record needs to be built using the latest version and applying the JSON patch.
        type: array
        items:
          $ref: '#/definitions/PatchOperation'
      events:
        $ref: '#/definitions/Events'
  emdDataPush:
    type: object
    properties:
      meta:
        $ref: '#/definitions/Meta'
      processedEmd:
        $ref: '#/definitions/AirTravelDocument'
      previousRecord:
        description: Representation of the previous version of the record in the form of JSON Patch as defined by RFC 6902 (see https://tools.ietf.org/html/rfc6902). The actual JSON of this previous record needs to be built using the latest version and applying the JSON patch.
        type: array
        items:
          $ref: '#/definitions/PatchOperation'
      events:
        $ref: '#/definitions/Events'
  ticketingCorrelationsPush:
    type: object
    properties:
      meta:
        $ref: '#/definitions/Meta'
      ticketingCorrelations:
        type: object
        properties:
          correlationTicketPnr:
            $ref: '#/definitions/CorrelationTicketPnr'
          correlationTicketDcsPassenger:
            $ref: '#/definitions/CorrelationTicketDcsPassenger'
          correlationTicketSchedule:
            $ref: '#/definitions/CorrelationTicketSchedule'
          correlationTicketInventory:
            $ref: '#/definitions/CorrelationTicketInventory'
          correlationTicketMembership:
            $ref: '#/definitions/correlationTicketMembership'
          correlationEmdPnr:
            $ref: '#/definitions/CorrelationEmdPnr'
          correlationEmdDcsPassenger:
            $ref: '#/definitions/CorrelationEmdDcsPassenger'
          correlationEmdMembership:
            $ref: '#/definitions/correlationEmdMembership'
          dictionaries:
            $ref: '#/definitions/Dictionaries'
      events:
        $ref: '#/definitions/Events'
  travelDocumentApiResponse:
    type: object
    properties:
      meta:
        $ref: '#/definitions/Meta'
      data:
        $ref: '#/definitions/TravelDocumentResponseData'
      included:
        $ref: '#/definitions/Included'
      dictionaries:
        $ref: '#/definitions/Dictionaries'
   ########## Data models ##########
  TravelDocumentResponseData:
    type: object
    allOf:
      - $ref: '#/definitions/AirTravelDocument'
      - $ref: '#/definitions/CorrelationReferences'
  CorrelationReferences:
    type: object
    properties:
      correlations:
        type: object
        description: >-
          Correlation resources are defined in the "included" node.
          This node provides links to all correlations resources associated with the current processed-travel-document resource [TKT/EMD].
        properties:
          ticketPnr:
            $ref: '#/definitions/Relationship'
          ticketDcsPassenger:
            $ref: '#/definitions/Relationship'
          ticketSchedule:
            $ref: '#/definitions/Relationship'
          ticketInventory:
            $ref: '#/definitions/Relationship'
          ticketMembership:
            $ref: '#/definitions/Relationship'
          emdPnr:
            $ref: '#/definitions/Relationship'
          emdDcsPassenger:
            $ref: '#/definitions/Relationship'
          emdMembership:
            $ref: '#/definitions/Relationship'
        example:
          ticketPnr:
            ref: 'included/correlationTicketPnr/1722500003153-2018-10-18'
          ticketDcsPassenger:
            ref: 'included/correlationTicketDcsPassenger/1722500003153-2018-10-18'
          ticketSchedule:
            ref: 'included/correlationTicketSchedule/1722500003153-2018-10-18'
          ticketInventory:
            ref: 'included/correlationTicketInventory/1722500003153-2018-10-18'
          ticketMembership:
            ref: 'included/correlationTicketMembership/1722500003153-2018-10-18'
          emdPnr:
            ref: 'included/correlationEmdPnr/1722500009467-2019-10-11'
          emdDcsPassenger:
            ref: 'included/correlationEmdDcsPassenger/1722500009467-2019-10-11'
          emdMembership:
            ref: 'included/correlationEmdMembership/1722500009467-2019-10-11'
  Included:
    type: object
    properties:
      correlationTicketPnr:
        type: object
        description: Correlation resource - Ticket correlation with PNR
        additionalProperties:
          $ref: '#/definitions/CorrelationTicketPnr'
      correlationTicketDcsPassenger:
        type: object
        description: >-
          Correlation resource - Ticket correlation with DCS Passenger
        additionalProperties:
          $ref: '#/definitions/CorrelationTicketDcsPassenger'
      correlationTicketSchedule:
        type: object
        description: Correlation resource - Ticket correlation with Schedule
        additionalProperties:
          $ref: '#/definitions/CorrelationTicketSchedule'
      correlationTicketInventory:
        type: object
        description: Correlation resource - Ticket correlation with Inventory
        additionalProperties:
          $ref: '#/definitions/CorrelationTicketInventory'
      correlationTicketMembership:
        type: object
        description: Correlation resource - Ticket correlation with Loyalty Membership
        additionalProperties:
          $ref: '#/definitions/CorrelationTicketMembership'
      correlationEmdPnr:
        type: object
        description: >-
          Correlation resource - EMD correlation with Passenger Name Record
        additionalProperties:
          $ref: '#/definitions/CorrelationEmdPnr'
      correlationEmdDcsPassenger:
        type: object
        description: >-
          Correlation resource - EMD correlation with DCS Passenger
        additionalProperties:
          $ref: '#/definitions/CorrelationEmdDcsPassenger'
      correlationEmdMembership:
        type: object
        description: >-
          Correlation resource - EMD correlation with Loyalty Membership
        additionalProperties:
          $ref: '#/definitions/CorrelationEmdMembership'
  Dictionaries:
    type: object
    properties:
      currencyConversionLookupRates:
        type: object
        description: |
              - Multiple base (many to one)
              - Used to define rate conversion when multiple currency possible
              - origin_amount = reverse_rate x target_amount
              - target_amount = rate x origin_amount
              - Example:
                      currencyConversionLookupRates: {
                          USD: {
                              rate: "0.80",
                              target: EUR
                          },
                          GBP: {
                              rate: "1.14",
                              target: EUR
                          }
                      }
        additionalProperties:
          $ref: '#/definitions/CurrencyConversionLookupRates'
      tickets:
        type: object
        description: >-
          Set of key/value pairs with ticketId as key i.e. primary ticket number
          + issuance date - key example '1721234567890-2018-05-15'
        additionalProperties:
          $ref: '#/definitions/Relationship'
      emds:
        type: object
        description: >-
          Set of key/value pairs with emdId as key i.e. primary EMD number +
          issuance date - key example '1721234567891-2018-05-15'
        additionalProperties:
          $ref: '#/definitions/Relationship'
      pnrs:
        type: object
        description: >-
          Set of key/value pairs with pnrId as key i.e. record locator + creation date - key example 'ABCDEF-2018-05-15'
        additionalProperties:
          $ref: '#/definitions/Relationship'
      dcsPassengers:
        type: object
        description: >-
          Set of key/value pairs with dcsPassengerId as key i.e. CPR UCI - key example '2501ADE0000001'
        additionalProperties:
          $ref: '#/definitions/Relationship'
      schedules:
        type: object
        description: >-
          Set of key/value pairs with scheduleId as key i.e. carrier code + flight number + flight date + operational suffix - key example
          '6X-123-2018-10-05-A'
        additionalProperties:
          $ref: '#/definitions/Relationship'
      inventories:
        type: object
        description: >-
          Set of key/value pairs with inventoryId as key [generated] - key example 6X12355515101900000NULL'
        additionalProperties:
          $ref: '#/definitions/Relationship'
      memberships:
        type: object
        description: >-
          Set of key/value pairs with membershipId as key i.e. carrier code + frequent flyer id - key example '6X-1234567890'
        additionalProperties:
          $ref: '#/definitions/Relationship'
    example:
      tickets:
        '1722500003153-2018-10-18':
          type: 'air-travel-document'
          id: '1721234567890-2019-10-05'
          version: '1'
          href: 'http://airlines.api.amadeus.com/v1/ticketing/processed-tickets/1721234567890-2019-10-05'
      emds:
        '1722500009467-2019-10-11':
          type: 'air-travel-document'
          id: '1725555555555-2019-10-05'
          version: '1'
          href: 'http://airlines.api.amadeus.com/v1/ticketing/processed-emds/1725555555555-2019-10-05'
      pnrs:
        'ABC123-2018-10-05':
          type: 'pnr'
          id: 'ABC123-2019-10-05'
          version: '1'
          href: 'http://airlines.api.amadeus.com/v1/reservation/processed-pnrs/1721234567890-2019-10-05'
      dcsPassengers:
        '2501ADE0000001':
          type: 'dcs-passenger'
          id: '2501ADE0000001'
          version: '2019-10-05T10:00:00+00:00'
          href: 'http://airlines.api.amadeus.com/v1/departure-control/processed-dcs-passengers/2501ADE0000001'
      schedules:
        '6X-123-2019-10-05':
          type: 'dated-flight'
          id: '6X-123-2019-10-05'
          version: '1536835243'
          href: 'http://airlines.api.amadeus.com/v1/schedule/processed-flights/6X-123-2019-10-05'
      inventories:
        '6X12355515101900000NULL':
          type: 'dated-flight'
          id: '6X12355515101900000NULL'
          version: '1536835243'
          href: 'http://airlines.api.amadeus.com/v1/inventory/processed-flights/6X12355515101900000NULL'
      memberships:
        '6X-1234567890':
          type: 'membership'
          id: '6X-1234567890'
          version: '1'
  ########## Entities definition ##########
  Events:
    type: object
    description: Structure of Dynamic Intelligence Hub functional events
    properties:
      recordDomain:
        type: string
        description: Functional domain of the record
        example: TICKET_DCSPASSENGER
      recordId:
        type: string
        description: Ticket ID or EMD ID
        example: 0142100147468-2018-10-18
      originFeedTimeStamp:
        type: string
        description: Incoming PSS feed time stamp - only populated for ticket & emd feeds
      events:
        type: array
        description: List of events that have been detected on the DCS Passenger document
        items:
          type: object
          properties:
            origin:
              type: string
              description: Type of event e.g. 'COMPARISON' / 'TIME_INITIATED_EVENT'
              example: COMPARISON
            eventType:
              type: string
              description: >-
                In case of comparison events, type of operation notified by the event
              example: CREATED
              enum:
              - CREATED
              - UPDATED
              - DELETED
            currentPath:
              type: string
              description: >-
                String containing the JSON Pointer (see IETF RFC6901) that points to the data referenced by the
                Event in the latest version of the entity. It is only applicable for CREATED and UPDATED events.
              example: ''
            previousPath:
              type: string
              description: >-
                String containing the JSON Pointer (see IETF RFC6901) that points to the data referenced by the
                Event in the previous version of the entity. It is only applicable for DELETED and UPDATED events.
              example: ''
    example:
      recordDomain: 'TICKET_DCSPASSENGER'
      recordId: '0142100147468-2018-10-18'
      events:
      - origin: 'COMPARISON'
        eventType: 'UPDATED'
        currentPath: '/ticketingCorrelations/correlationTicketDcsPassenger/correlatedData/2501ADE000000001'
        previousPath: '/ticketingCorrelations/correlationTicketDcsPassenger/correlatedData/2501ADE000000001'
  Meta:
    type: object
    description: Technical information related to the feed
    properties:
      triggerEventLog:
        description: information related to the initial event that trigger the process
        $ref: '#/definitions/EventLog'
      version:
        description: Version of the JSON feed produced
        type: string
        example: '4.3.0'
    example:
      triggerEventLog:
        id: '46541dsfsSDRWFS54'
        triggerEventName: 'DATA_INIT'
      version: '4.3.0'
  AirTravelDocument:
    title: AirTravelDocument
    type: object
    properties:
      id:
        type: string
        description: id of ticket or EMD.
        example: '0142100147468-2018-10-18'
      version:
        type: string
        description: An identifier to track the sequential flow of messages. Epoch format.
        example: '1586867381'
      creation:
        description: Indicates the creation DateTime and the point of sale of the entity.
        $ref: '#/definitions/EventLog'
      latestEvent:
        description: >-
          Indicates the last updated DateTime.
          Indicate the last point of sale of the entity.   
          Indicate code to depict message function.
        $ref: '#/definitions/EventLog'
      documentType:
        type: string
        description: Type of document
        enum:
        - TICKET
        - EMD_ASSOCIATED
        - EMD_STANDALONE
        example: 'TICKET'
      primaryDocumentNumber:
        type: string
        description: Ticket primary number or EMD primary number
        example: '0142100147468'
      conjunctiveDocumentNumbers:
        type: array
        items:
          type: string
          description: The list of conjunctive document numbers.
        example: '0142100147468'         
      associatedDocuments:
        description: List of Associated Ticket or Associated EMD for the current document,e.g. EMD-A associated to an ETKT.
        type: array
        items:
          properties:
            documentType:
              type: string
              description: Type of document
              enum:
              - TICKET
              - EMD_ASSOCIATED
              - EMD_STANDALONE
              example: 'EMD_ASSOCIATED'
            documentNumber:
              type: string
              description: >-
                Identifier of the travel document prefixed by its owner code [NALC - 3 digits].
                Can either be a primary or a conjunctive document number.
              example: '0142100147468'
      originalDocuments:
        description: The ticket(s) against which this ticket was exchanged and the original local DateTime of the document issuance.
        type: array
        items:
          properties:
            documentType:
              type: string
              description: Type of document
              enum:
              - TICKET
              - EMD_ASSOCIATED
              - EMD_STANDALONE
              example: 'TICKET'
            documentNumber:
              type: string
              description: >-
                Identifier of the travel document prefixed by its owner code [NALC - 3 digits].
                Can either be a primary or a conjunctive document number.
              example: '0142100147468'
            creation:
              description: Indicates the creation DateTime of the entity.
              $ref: '#/definitions/EventLog'
      numberOfBooklets:
        type: number
        description: Number of conjunctive tickets/EMDs in the document.
        example: '1'
      issuanceType:
        type: string
        description: Indicates the type of transaction.
        enum:
        - FIRST_ISSUE
        - REISSUE
        example: 'FIRST_ISSUE'
      originalIssueFreeFlow:
        type: string
        description: Original issue information in free text. Not formatted on ETS.
        example: 'Freetext'
      endorsementFreeFlow:
        type: string
        description: Used to identify any restrictions, airline comments, or rules that may apply to the ticket/EMD
        example: 'FARE RESTRICTION MAY APPLY -BG 6X'
      validatingCarrierCode:
        type: string
        description: Ticket validating carrier code
        example: '6X'  
      validatingCarrierPnr:
        description: >-
          Validating carrier record locator and owner of the record locator 
          (Validating carrier code/Ticketing Online Feed user code if not validating/booking GDS code)
        $ref: '#/definitions/AssociatedPnr'
      associatedPnrs:
        type: array
        items:
            $ref: '#/definitions/AssociatedPnr'
      void:
        description: Is only filled when a ticket status becomes void (DateTime information in ISO6081 format)
        $ref: '#/definitions/EventLog'
      originCityIataCode:
        type: string
        description: origin city code
        example: 'PAR'
      destinationCityIataCode:
        type: string
        description: destination city code
        example: 'LON'
      documentRefund:
        $ref: '#/definitions/TicketRefund'
      traveler:
        description: Traveler information.
        $ref: '#/definitions/Stakeholder'
      formsOfIdentification:
        type: array
        items:
          $ref: '#/definitions/FormOfIdentification'
      pricingConditions:
        $ref: '#/definitions/PricingConditions'
      formsOfPayment:
        type: array
        items:
          $ref: '#/definitions/DisplayedFormOfPayment'
      quotationOverrides:
        description: >-
          The aim of the override element is to enable an agent to override
          quotation data and to keep track of all the changes along with the
          reason for the changes. Overrides are applied on quotation documents
          [TST] and are automatically populated into the travel document at
          issuance. To create an override  element, an agent needs to provide
          one override type and one reason code at the same time.
        type: array
        items:
          $ref: '#/definitions/EventLogOverride'
      price:
          $ref: '#/definitions/Price'
      coupons:
        type: array
        items:
          $ref: '#/definitions/Coupon'
      dictionaries: 
          $ref: '#/definitions/Dictionaries'
  CorrelationTicketPnr:
    type: object
    description: Structure of correlation between a ticket and a Passenger Name Record.
    properties:
      ticketId:
        type: string
        description: current ticket number + issuance date
        example: '1722500003153-2018-06-15'
      pnrIds:
        type: array
        description: >-
          Identifying the PNR(s) correlated with the current ticket - item format is record locator + creation date
        example: 'ABCDEF-2018-06-15'
        items:
          type: string
      correlatedData:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataTicketPnr'
    example:
      ticketId: '1722500003153-2018-06-15'
      pnrIds:
        - 'ABCDEF-2018-06-15'
      correlatedData:
        - 'ABCDEF-2018-06-15':
          - ticketCouponId: '1722500003153-2018-06-15-1'
            pnrTravelerId: 'ABCDEF-2018-06-15-PT-1'
            pnrAirSegmentId: 'ABCDEF-2018-06-15-ST-4'
            pnrTicketingReferenceId: 'ABCDEF-2018-06-15-OT-19'
  CorrelationTicketDcsPassenger:
    type: object
    description: Structure of correlation between a ticket and a Customer Product Record.
    properties:
      ticketId:
        type: string
        description: current ticket number + issuance date
        example: '1722500003153-2018-06-15'
      dcsPassengerIds:
        type: array
        description: >-
          Identifying the CPR(s) correlated with the current ticket - item corresponds to CPR UCI
        example:
          - '2501ADE0000001'
        items:
          type: string
      correlatedData:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataTicketDcsPassenger'
    example:
      ticketId: '1722500003153-2018-06-15'
      dcsPassengerIds:
        - '2501ADE0000001'
      correlatedData:
        - '2501ADE0000001':
          - ticketCouponId: '1722500003153-2018-06-15-1'
            dcsPassengerSegmentDeliveryId: '2401CA5500003OID'
  CorrelationTicketSchedule:
    type: object
    description: >-
      Structure of correlation between a ticket and a Schedule Dated Flight record.
    properties:
      ticketId:
        type: string
        description: current ticket number + issuance date
        example: '1722500003153-2018-06-15'
      scheduleIds:
        type: array
        description: >-
          Identifying the schedule flight(s) correlated with the current ticket
          - item is dated flight ID i.e. carrier code + flight number + flight
          date + operational suffix
        example:
          - '6X-123-2018-10-05'
        items:
          type: string
      correlatedData:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataTicketSchedule'
    example:
      ticketId: '1722500003153-2018-06-15'
      scheduleIds:
        - '6X-123-2018-10-05'
      correlatedData:
        - '6X-123-2018-10-05':
          - ticketCouponId: '1722500003153-2018-06-15-1'
            scheduleSegmentId: '6X-123-2018-10-05-CDGYUL'
            schedulePartnershipId: 'BA-435-2018-05-25'
  CorrelationTicketInventory:
    type: object
    description: >-
      Structure of correlation between a ticket and an Inventory Dated Flight record.
    properties:
      ticketId:
        type: string
        description: current ticket number + issuance date
        example: '1722500003153-2018-06-15'
      inventoryIds:
        type: array
        description: >-
          Identifying the inventory flight(s) correlated with the current ticket
          - item is dated flight ID i.e. carrier code + flight number + flight
          date + operational suffix.
        example:
          - 6X12355515101900000NULL'
        items:
          type: string
      correlatedData:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataTicketInventory'
    example:
      ticketId: '1722500003153-2018-06-15'
      inventoryIds:
        - '6X12355515101900000NULL'
      correlatedData:
        - '6X12355515101900000NULL':
          - ticketCouponId: '1722500003153-2018-06-15-1'
            inventorySegmentId: '6X12355515101900000NULLCDGYULO'
            inventoryPartnershipId: '6X78955515101900000NULL'
  CorrelationTicketMembership:
    type: object
    description: >-
      Structure of correlation between a ticket and a Loyalty Membership.
    properties:
      ticketId:
        type: string
        description: current ticket number + issuance date
        example: '1722500003153-2018-06-15'
      membershipId:
        type: string
        description: >-
          Identifying the Loyalty Membership correlated with the current ticket. 
          Format is carrier code (owner of the program) + frequent flyer id (within the loyalty program)
        example:
          - 6X-1234567890'
      correlatedData:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataTicketMembership'
    example:
      ticketId: '1722500003153-2018-06-15'
      membershipId:
        - '6X-1234567890'
      correlatedData:
        - '6X-1234567890':
          - ticketCouponId: '1722500003153-2018-06-15-1'
            ticketCouponStatus: 'OPEN_FOR_USE'
          - ticketCouponId: '1722500003153-2018-06-15-3'
            ticketCouponStatus: 'REFUNDED'
  CorrelationEmdPnr:
    type: object
    description: >-
      Structure of correlation between an Electronic Miscellaneous Document and a Passenger Name Record.
    properties:
      emdId:
        type: string
        description: current EMD number + issuance date
        example: '1722500003207-2018-06-15'
      pnrIds:
        type: array
        description: >-
          Identifying the PNR(s) correlated with the current EMD - item format is record locator + creation date
        example:
          - 'ABCDEF-2018-06-15'
        items:
          type: string
      correlatedData:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataEmdPnr'
    example:
      emdId: '1722500003207-2018-06-15'
      pnrIds:
        - 'ABCDEF-2018-06-15'
      correlatedData:
        - 'ABCDEF-2018-06-15':
          - emdCouponId: '1722500003207-2018-06-15-1'
            pnrTravelerId: 'ABCDEF-2018-06-15-PT-1'
            pnrServiceId: 'ABCDEF-2018-06-15-OT-10'
            pnrAirSegmentId: 'ABCDEF-2018-06-15-ST-4'
            pnrTicketingReferenceId: 'ABCDEF-2018-06-15-OT-19'
  CorrelationEmdDcsPassenger:
    type: object
    description: >-
      Structure of correlation between an Electronic Miscellaneous Document and a Customer Product Record.
    properties:
      emdId:
        type: string
        description: Current EMD number + issuance date
        example: '1722500003207-2018-06-15'
      dcsPassengerIds:
        type: array
        description: >-
          Identifying the CPR(s) correlated with the current EMD - item corresponds to CPR UCI
        example: '2501ADE0000001'
        items:
          type: string
      correlatedData:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataEmdDcsPassenger'
    example:
      emdId: '1722500003207-2018-06-15'
      dcsPassengerIds:
        - '2501ADE0000001'
      correlatedData:
        - '2501ADE0000001':
          - emdCouponId: '1722500003207-2018-06-15-1'
            dcsPassengerServiceId: '1000000000B54A31'
            dcsPassengerSegmentDeliveryId: '2401CA5500003OID'
  CorrelationEmdMembership:
    type: object
    description: >-
      Structure of correlation between an Electronic Miscellaneous Document and a Loyalty Membership.
    properties:
      emdId:
        type: string
        description: Current EMD number + issuance date
        example: '1722500003207-2018-06-15'
      membershipId:
        type: string
        description: >-
          Identifying the Loyalty Membership correlated with the current ticket. 
          Format is carrier code (owner of the program) + frequent flyer id (within the loyalty program)
        example:
          - 6X-1234567890'
      correlatedData:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/CorrelatedDataEmdMembership'
    example:
      emdId: '1722500003207-2018-06-15'
      membershipId:
        - '6X-1234567890'
      correlatedData:
        - '6X-1234567890':
          - emdCouponId: '1722500003207-2018-06-15-1'
            ticketCouponStatus: 'OPEN_FOR_USE'
          - emdCouponId: '1722500003207-2018-06-15-2'
            ticketCouponStatus: 'REFUNDED'
  CorrelatedDataTicketPnr:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between a ticket and a PNR
    properties:
      ticketCouponId:
        type: string
        example: '1722500003153-2018-06-15-1'
      pnrTravelerId:
        type: string
        example: 'ABCDEF-2018-06-15-PT-1'
      pnrAirSegmentId:
        type: string
        example: 'ABCDEF-2018-06-15-ST-4'
      pnrTicketingReferenceId:
        type: string
        example: 'ABCDEF-2018-06-15-OT-10'
  CorrelatedDataTicketDcsPassenger:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between a ticket and a CPR
    properties:
      ticketCouponId:
        type: string
        example: '1722500003153-2018-06-15-1'
      dcsPassengerSegmentDeliveryId:
        type: string
        example: '2401CA5500003OID'
  CorrelatedDataTicketSchedule:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between a ticket and a Schedule flight record
    properties:
      ticketCouponId:
        type: string
        example: '1722500003153-2018-06-15-1'
      scheduleSegmentId:
        type: string
        example: '6X-123-2018-10-05-CDGYUL'
      schedulePartnershipId:
        type: string
        example: 'BA-435-2018-05-25'
  CorrelatedDataTicketInventory:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between a ticket and an Inventory flight record
    properties:
      ticketCouponId:
        type: string
        example: '1722500003153-2018-06-15-1'
      inventorySegmentId:
        type: string
        example: '6X56755525101900000NULLYULCDGO'
      inventoryPartnershipId:
        type: string
        example: 6X78955515101900000NULL
  CorrelatedDataTicketMembership:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between a ticket and a Loyalty Membership
    properties:
      ticketCouponId:
        type: string
        example: '1722500003153-2018-06-15-1'
      ticketCouponStatus:
        type: string
        example: 'READY_FOR_USE'
  CorrelatedDataEmdPnr:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between an EMD and a PNR
    properties:
      emdCouponId:
        type: string
        example: '1722500003207-2018-06-15-1'
      pnrTravelerId:
        type: string
        example: 'ABCDEF-2018-06-15-PT-1'
      pnrServiceId:
        type: string
        example: 'ABCDEF-2018-06-15-OT-10'
      pnrAirSegmentId:
        type: string
        example: 'ABCDEF-2018-06-15-ST-4'
      pnrTicketingReferenceId:
        type: string
        example: 'ABCDEF-2018-06-15-OT-10'
  CorrelatedDataEmdDcsPassenger:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between an EMD and a CPR
    properties:
      emdCouponId:
        type: string
        example: '1722500003207-2018-06-15-1'
      dcsPassengerServiceId:
        type: string
        example: '1000000000B54A31'
      dcsPassengerSegmentDeliveryId:
        type: string
        example: '2401CA5500003OID'
  CorrelatedDataEmdMembership:
    type: object
    description: >-
      Set of data field identifiers defining the correlation between an EMD and a Loyalty Membership
    properties:
      emdCouponId:
        type: string
        example: '1722500003207-2018-06-15-1'
      emdCouponStatus:
        type: string
        example: 'READY_FOR_USE'
  Relationship:
    type: object
    properties:
      type:
        type: string
      id:
        type: string
      ref:
        type: string
      version:
        type: string
        description: Only populated for correlated resources defined in Dictionaries
      href:
        description: Only populated for correlated resources defined in Dictionaries
        type: string
  CurrencyConversionLookupRates:
    description: "Conversion from provider's currency to requested currency"
    type: object
    properties:
      rate:
        description: The conversion factor to apply against the source currency to obtain the requested currency
        type: string
      target:
        description: Requested currency code
        type: string
        pattern: '[A-Z]{3}'
  DisplayedFormOfPayment:
    type: object
    description: >-
      Contains the Forms Of Payment that have been used to pay for the products 
      issued in the current travel document [flights for tickets, ancillary services/upgrade services/fees for EMDs].
    properties:
      code:
        type: string
        description: >-
          Form Of Payment category as defined in IATA PADIS code list for data
          element 9888. Examples - CA for Cash, CC for Credit Card, CK for
          Check, MS for Miscellaneous, EF for Electronic Funds transfer.
        example: 'CC'
      fopIndicator:
        type: string
        description: >-
          Used to specify what the FOP has been used for, in case of
          exchanges. In case of a first-issue document, it takes the value [NEW].
          In case of a single exchange, the FOP used to settle the
          original document takes the value [ORIGINAL], while the
          FOP used to settle the additional collection of the exchange i.e.
          the new ticket, takes the value [NEW].
          In case of multiple exchanges, the same principle applies, and the FOP used to settle
          the last ticket to exchange takes the value [OLD].
          This field follows IATA PADIS Code List for data element 9988.
        enum: 
          - NEW
          - OLD
          - ORIGINAL
        example: 'NEW'
      displayedAmount:
        $ref: '#/definitions/ElementaryPrice'
      freeText:
        type: string
        description: Free text as entered in the order via the FP element
        example: 'Form of payment free text'
      paymentCard:
        $ref: '#/definitions/PaymentCard'
      paymentLoyalty:
        $ref: '#/definitions/PaymentLoyalty'        
      authorization:
        $ref: '#/definitions/Authorization'
  Tax:
    type: object
    properties:
      amount:
        type: string
        description: Defines amount with decimal separator.
      currency :
        type : string
        description: >- 
          Defines a monetary unit. It is a three alpha code. Example: EUR for Euros, USD for US dollar, etc.
      code:
        type: string
        description: International Standards Organization (ISO) Tax code.
      nature:
        type: string
        description: Filling on SSP side.
      category:
        type: string
        description: >-
          Used to specify if the tax is New, Old or Refundable.
        enum: 
        - NEW
        - OLD
        - REFUNDABLE
      isPaidInLoyaltyRewards:
        type: boolean
        description: It is true when tax is paid in frequent flyer points. The Default value is false.
      ticketReportedStatuses:
        description: >-
          DISPLAYED Taxes displayed on the ticket according the IATA standard
          REPORTED Taxes displayed at ticket level with additional business codes specifics to AMADEUS
          EQUIVALENT_MONETARY_VALUE It is applicable only when isPaidInLoyaltyRewards = true, this is the corresponding monetary amount in a specific currency of a tax paid with LoyaltyRewards.
        type: array
        items:
          type: string 
          enum: 
          - DISPLAYED
          - REPORTED
          - EQUIVALENT_MONETARY_VALUE
  PriceCategory:
    type: object
    description: >-
      AN EMD IS CATEGORISED BASED ON ITS REASON FOR ISSUANCE CODE (RFIC), WHICH
      DEFINES THE GROUP OF SERVICES IT BELONGS TO. THERE CAN ONLY BE ONE RFIC
      CODE PER EMD. SOME CODES ARE DEFINED BY IATA, HOWEVER, OTHERS CAN BE
      DEFINED BY INDIVIDUAL AIRLINES.  AN EMD, AND EACH RFIC, CAN HAVE MULTIPLE
      REASON FOR ISSUANCE SUB-CODES (RFISC). THERE IS ONE RFISC IN EACH EMD
      COUPON AND THEY ARE AIRLINE-SPECIFIC.
    properties:
      code:
        type: string
        description: >-
          The reason for issuance code (RFIC) chargeability indicator defined for the sellable object
        example: 'E'
      subCode:
        type: string
        description: >-
          The reason for issurance sub code (RFISC) chargeability indicator defined for the sellable object
        example: '0BX'
      description:
        type: string
        description: description of reason for issuance sub code
        example: 'Lounge Access'
  PointOfSale:
      type: object
      properties:
        office:
          type: object
          properties:
            id:
              type: string
              description: An identifier for a corporate user of a computer reservation system or global distribution system, typically a travel agency, also known as office ID.
              example: 'NCE1A0980'
            inHouseIdentification:
              type: string
              description: AMID- office id in Amadeus
              example: '000000'
            iataNumber:
              type: string
              description: IATA assigned agency number
              example: '00106137'
            agentType:
              type: string
              description: The issuing channel.  AIRLINE_AGENT, TRAVEL_AGENT, WEB, NON_IATA_AGENT
              example: 'AIRLINE_AGENT'
            systemCode:
              type: string
              description: 2-3 character airline/CRS code of the system that originates the current transaction or the issuance.
              example: '1A'
        login:
          type: object
          properties:
            initials:
              type: string
              description: agent initials
              example: 'JS'
            dutyCode:
              type: string
              description: duty code of the agent
              example: 'SU'
            numericSign:
              type: string
              description: Authorization code of the agent
              example: '2416'
            currencyCode:
              type: string
              description: ISO currency code of the agent
              example: 'CAD'
            countryCode:
              type: string
              description: ISO country code of the agent
              example: 'CA'
            cityCode:
              type: string
              description: city code of the issuing agent/system
              example: 'YUL'
  FrequentFlyer:
    type: object
    properties:
      applicableAirlineCode:
        type: string
        description: Code of the airline for which frequent traveller data has been added.
        example: '6X'
      frequentFlyerNumber:
        type: string
        description: A code to identify a frequent traveller - Loyalty card number
        example: '6X090807061234'
  ElementaryPrice:
    type: object
    properties:
      amount:
        type: string
        description: >-
          Amount of the fare. could be alpha numeric. Ex- 500.20 or 514.13A, 'A' signifies additional collection.
        example: '4871.81'
      currency:
        type: string
        description: Currency type of the fare.
        example: 'CAD'
      elementaryPriceType:
        type: string
        description: 'Defines the price type, e.g. for base fare, total...'
        example: ''
  PaymentCard:
    type: object
    properties:
      expiryDate:
        type: string
        description: Expiration date of the card - format is month & year - MMYY
        example: '0318'
      maskedCardNumber:
        type: string
        description: Masked credit card number
        example: '****************'
      vendorCode:
        type: string
        description: >-
          Vendor code in case the FOP is a credit card - designates the card
          brand. For instance, VI for Visa, CA for MasterCard, etc.
        example: 'CA'
      holderName:
        type: string
        description: Name of credit card holder.
        example: 'SMITH'
  Authorization:
    type: object
    properties:
      approvalCode:
        type: string
        description: >-
          A series of characters assigned by the applicable credit card
          company's authorization system to confirm the approval of a credit
          sale transaction, with a maximum of 8 digits
        example: '38562'
      sourceOfApprovalCode:
        type: string
        description: >-
          Approval source of the authorization when the FOP is a credit card.
          Can be seen in the corresponding FP elements of the order. Possible
          values are A - Automatic Approval Code M - Manual Approval Code F -
          Automatic Capture and Approval Code B - Manual Capture and Approval
          Code
        example: 'A'
      extendedPaymentCode:
        type: string
        description: >-
          A code to indicate the number of months over which the customer wishes
          to pay where the credit card issuer permits.
        example: '11'
  FormOfIdentification:
    type: object
    description: Alternative means of identifying stakeholders.
    properties:    
      identificationType:
        type: string
        description: Indicates type of passenger identification number
        enum:
        - CREDIT_CARD
        - DRIVERS_LICENSE
        - FREQUENT_FLYER
        - PASSPORT
        - NATIONAL_IDENTITY_CARD
        - BOOKING_CONFIRMATION
        - TICKET
        - OTHER_ID
      number:
        type: string
        description: The actual form of identification number.
        example: "*********"
  FlightSegment:
    description: >-
        Defining a flight segment, including both Operating and Marketing details when applicable.
    type: object
    properties:
      bookingClass:
        $ref: '#/definitions/BookingClass'
      departure:
        $ref: '#/definitions/FlightEndPoint'
      arrival:
        $ref: '#/definitions/FlightEndPoint'
      carrierCode:
        type: string
        description: The airline / carrier code
        minLength: 1
        maxLength: 2
        example: '6X'
      number:
        type: string
        description: The flight number as assigned by the carrier.
        minLength: 1
        maxLength: 4
        example: '123'
      suffix:
        type: string
        description: The flight number suffix as assigned by the carrier.
        minLength: 1
        maxLength: 4
        example: 'C'
      operating:
        $ref: '#/definitions/OperatingFlight'
  FlightEndPoint:
    type: object
    description: Departure or arrival information
    properties:
      iataCode:
        type: string
        description: IATA Airport code
        example: 'LHR'
      localDateTime:
        type: string
        description: 'Local date and time with the following format \ yyyy-mm-ddThh:mm:ss'
        example: '2018-10-03T09:35:42.000'
  OperatingFlight:
    type: object
    description: Information about the marketing flight
    properties:
      flightDesignator:
        $ref: '#/definitions/FlightDesignator' 
  FlightDesignator:
    type: object
    properties:
      carrierCode:
        type: string
        description: Two letter IATA standard carrier code
        minLength: 1
        maxLength: 2
        example: 6X
      flightNumber:
        type: string
        description: The flight number as assigned by the carrier - 1-4 digit number
        minLength: 1
        maxLength: 4
        example: '555'
      operationalSuffix:
        type: string
        description: The flight number suffix as assigned by the carrier - 1 char
        example: A
  EventLog:
    type: object
    description: Contains the when/how/who of a particular change.
    properties:
      id:
        type: string
        description: Identifier of the change
        example: "64GqpZfbcAW61PtL"
      triggerEventName:
        description: >-
          Trigger of the message - set to 'DATA_INIT' for initialization messages
          Description of the overriding use case (promo-code discount, merchandising discounts, manual agent overrides...)
        type: string
        example: 'DATA_INIT'
      dateTime:
        type: string
        description: Date/Time in UTC of the change.
        example: '2018-10-03T09:35:42.000Z'
      pointOfSale:
        $ref: '#/definitions/PointOfSale'
  EventLogOverride:   
    allOf:
      - $ref: '#/definitions/EventLog'
      - properties:
          changeLog:
            type: array
            description: Contains the old/new quotations values following an Override (promo / discount) change.
            items:
              properties:
                logType:
                  type: string
                  description: Defines the type of log.
                  example: 'EntityChangeLog'
                oldEntity:
                  description: Quotations values before override.
                  $ref: '#/definitions/Price'
                newEntity:
                  description: Current quotations valueq after override.
                  $ref: '#/definitions/Price'
  PatchOperation:
    description: A single JSON Patch operation as defined by RFC 6902 (see https://tools.ietf.org/html/rfc6902)
    type: object
    required:
     - "op"
     - "path"
    properties:
      op:
        type: string
        description: The operation to be performed
        enum:
        - "add"
        - "remove"
        - "replace"
        - "move"
        - "copy"
        - "test"
      path:
        type: string
        description: A JSON Pointer as defined by RFC 6901 (see https://tools.ietf.org/html/rfc6901)
      value:
        type: object
        description: The value to be used within the operations
      from:
        type: string
        description: A JSON Pointer as defined by RFC 6901 (see https://tools.ietf.org/html/rfc6901)
  AssociatedPnr:
    type: object
    description: Describes the association between the current reservation and another one.
    properties:
      reference:
        type: string
        description: Record locator [Amadeus or OA] with which the current reservation is associated - in case of a codeshare association, it enables to identify the operating PNR
        example: 'JKL789'
      creation:
        $ref: '#/definitions/EventLog'
        description: Creation date and system code [GDS/CRS] from which originates the associated reference
        example:
          date: '2019-10-05'
          pointOfSale:
            office:
              systemCode: '1S'
  TicketRefund:
    type: object
    description: >-
      A ticket refund contains refund information in case of an eticket or EMD.
      Field farePaid indicates the amount of the original/equivalent fare issued in the
      current travel document.
      Field fareUsed is the non-refundable portion of fare applicable to the
      used coupons of the current travel document [for example, flown
      coupons & exchanged coupons are considered as used]. 
      Field fareRefund is the fare to be refunded for relative document.
      FARE PAID - FARE USED= FARE REFUND 
      Fields refundTotal indicates the Total Refunded amount
      for the current travel document - it equals the sum of refunded fare
      amount and refunded tax amount, minus penalties & fees. 
      Field penalty indicates the amount of the cancellation penalty that has been
      incurred and deduced from the Total Refund amount. 
      Field noShowFee is the fee amount incurred because the refunded travel document
      contained at least one coupon flagged with the No Show indicator -
      this fee has been deduced from the Total Refunded amount.
    properties:
      farePaid:
        $ref: '#/definitions/ElementaryPrice'
      fareUsed:
        $ref: '#/definitions/ElementaryPrice'
      fareRefund:
        $ref: '#/definitions/ElementaryPrice'
      refundTotal:
        $ref: '#/definitions/ElementaryPrice'
      cancellationPenalty:
        $ref: '#/definitions/ElementaryPrice'
      noShowFee:
        $ref: '#/definitions/ElementaryPrice'
      totalObFee:
        $ref: '#/definitions/ElementaryPrice'
      obFees:
        type: array
        items: 
          $ref: '#/definitions/Fee'
      totalTax:
        $ref: '#/definitions/ElementaryPrice'
      taxes:
        type: array
        items:
          $ref: '#/definitions/Tax'
      paymentDetails:
        type: array
        items:
          $ref: '#/definitions/DisplayedFormOfPayment'
      refundRemark:
        type: string
        description: Refund remark
  Fee:
    type: object
    description: >-
      Structure to describe a Fee applied to provide a service or a product to the client.
      Each  type of  fees  is  assigned  a  code,  a 3-digit  subcode  and  a commercial name.
    properties:
      code:
        type: string
        description: Indicates the type of fee. Ex- "OB"
      subCode:
        type: string
        description: >-
          Identify uniquely the OB fee associated to the TST-
          1. Automated ticketing subcode (Txx) 
          2. Non-automated ticketing subcodes (Rxx)
      amount:
        type: string
        description: Defines amount with decimal separator.
      currency :
        type : string
        description: >-
          Defines a monetary unit. It is a three alpha code. Example: EUR for Euros, USD for US dollar, etc.
      taxes:
        type: array
        items:
          $ref: '#/definitions/Tax'
  Stakeholder:
    type: object
    description: Definition of the traveler.
    properties:
      name:
        description: Names of the stakeholder.
        $ref: '#/definitions/Name'
      contact:
        description: Contact associated with the traveler.
        $ref: '#/definitions/Contact'
      passengerTypeCode:
        description: >-
          3-characters code defining the passenger type - possible values: ADT, CHD, INS, INF, UNA
        type: string
        maxLength: 3
        example: 'ADT'  
  Name:
    type: object
    description: Description of the name of a physical person.
    properties:
      firstName:
        type: string
        example: 'JAMES'
      lastName:
        type: string
        example: 'CARTER'        
  Contact:
      description: Represents a contact
      type: object
      properties:
        phone:
          $ref: '#/definitions/Phone'
        email:
          $ref: '#/definitions/Email'
  Phone:
    type: object
    description: Phone information.
    properties:
      number:
        type: string
        description: >-
          Phone number. Composed of digits only. The number of digits depends on
          the country.
        pattern: '[0-9]{1,15}'
        example: '0033612345678'
  Email:
    type: object
    description: Email information.
    properties:
      address:
        type: string
        description: Email address
        example: '<EMAIL>'
  PricingConditions:
    type: object
    description: >-
      Provides the context under which a product has been priced.
      Those conditions are dictated at pricing and can affect the subsequent issuance behaviour in terms of eligibility and processing. 
    properties:
      fareCalculation:
        $ref: '#/definitions/FareCalculation'
      negoFareContract:
        $ref: '#/definitions/NegoFareContract'
      isInternationalSale:
        type: boolean
        description: International fare applied for the given itinerary
      isDomesticSale:
        type: boolean
        description: Domestic fare applied for the given itinerary
      isNonExchangeable:
        type: boolean
        description: Indicates that the fare does not permit exchange operation - false as default value
        example: false
      isNonRefundable:
        type: boolean
        description: Indicates that the fare does not permit refund operation - false as default value
        example: true
      isNonEndorsable:
        type: boolean
        description: Indicates that the fare cannot be endorsed - false as default value
        example: false
      isPenaltyRestriction:
        type: boolean
        description: Penalty restriction indicator signifies if any penalty applies or not for example in case of cancellation. Default value is false
        example: false
      fareComponents:
        type: array
        items :
            $ref: '#/definitions/FareComponent'
      flightBounds:
        type: array
        items :
            $ref: '#/definitions/FareComponent'        
  FareComponent:
    type: object
    description: Fare Component is a basic item that composes Fare Calculation of the base fare on a Fare quote.
    properties:
      id:
        type: integer
        description: Indicates the identifier of the FareComponent
      couponRefs:
        type: array
        items:
          $ref: '#/definitions/Relationship'
        description: used to identify the Coupons related to the component
      startCityCode:
        $ref: '#/definitions/Location'
        description: Defines the starting city of the Fare Component
      endCityCode:
        $ref: '#/definitions/Location'
        description: Defines the ending city of the Fare Component    
      price:
        $ref: '#/definitions/Price'        
  Location:
      type: object
      description: Description of a particular point or place in physical space
      properties:
        iataCode:
          type: string
          description: IATA location code
  FareCalculation:
    type: object
    description: Provides details on the type of pricing applied to the product.
    properties:
      pricingIndicator:
        type: string
        description: >-
          The Fare Calculation Pricing Indicator provides a granular view of the pricing mode that has been used during the pricing.
          Thanks to the FCPI, the travel agency, the airline or any user can know what pricing combinations have been used at pricing time. 
          0: Automatically priced
          1: Manually created or updated TST.
          2: No fare:  free or charter entered in the TST.
          3: Pricing by fare basis.
          4: Manual manipulation of taxes and/or fees at pricing or issuance time.
          5: The system did not price according to the entered passenger type.
          6: For US only: Exchange ticket request
          7: Bulk fare ticket request
          8: Fare price override at issuance time
          9: Inclusive tour ticket request
          A: SATA fare used.
          B: Amount discount override applied to fare base at pricing time.
          C: Amount or percent discount override applied to total fare combined with segment selection at pricing time.
          D: Amount or percent discount override applied to base fare combined with segment selection at pricing time.
          E: Percentage discount override at pricing time.
          F: Private fares have been used at pricing time.
          G: Dynamic discounted fare used at pricing time.
          H: HIP may apply. The system has priced but is unable to check a higher intermediate point.
          I: Override Fare Calculation by M/IT at pricing time.
          J: Override fare diagnostic entry at pricing time.
          K: Override Fare Calculation by M/BT at pricing time
          L: Booking date override
          M: Negotiated rates have been used at pricing time. Agent/consolidator is a fare updater.
          N: Negotiated rates have been used at pricing time. Airline is a fare updater.
          O: Past date TST override at issuance time.
          P: Lowest possible fare override at pricing time.
          Q: Depends on TSTIndicator. Either Manually stored endorsement before pricing or Manually added a tour code (FT element) on a private or public fare
          R: Validating carrier override at issuance time.
          S: Booking class override used on negotiated fares.
          T: Amount discount override applied to Total fare at pricing time.
          U: Stopover/transfer override is used in the pricing entry.
          V: Pricing override used with a pricing past date.
          W: Booking class override used on non-negotiated fares.
          Z: Net fare field manually updated (not used in US market)
        example: '0'
      text:
        type: string
        description: Content of the fare calculation.
        example: 'PAR 6X YTO289.85SKWI5LGT 6X PAR289.85SKWI5LGT NUC579.70END ROE0.862490'
  NegoFareContract:
    type: object
    description: >-
      Nature and reference of the commercial agreement by which the airline provides incentives to the selling travel agency for the quoted negotiated fare.
      This specific type of fare is used in conjunction with tours, referring to Inclusive Tour fares and Bulk Tour fares.
      The agent incentive on the IT/BT fare notably depends on the commission element entered in PNR.
    properties:
      incentiveScheme:
        type: string
        description: Nature of the agreement between the airline and the selling travel agency
        enum:
          - NET_REMIT
          - INCLUSIVE_TOUR
          - BULK_TOUR
          - INCLUSIVE_TOUR_WITH_NET_REMIT
          - BULK_TOUR_WITH_NET_REMIT
          - FLEXIBLE_COMMISSION
      tourCode:
        type: string
        description: >-
          Used when a published tour or a special negotiated fare is sold in conjunction with the ticket
        example: 'TOUR99'
  Price:
    type: object
    description: Price valuation information
    properties:
      currency:
        type: string
        description: currency Code apply to all elements of the price
      total:
        type: string
        description : Total = base + totalTaxes
      detailedPrices:
         type: array
         items: 
           $ref: '#/definitions/ElementaryPrice'
      totalTaxes:
        type: string
      taxes:
        description: Additional charge for external actors (government, airport...)
        type: array
        items:
          $ref: '#/definitions/Tax' 
      discounts:
        type: array
        description: Exhaustive list of applied discounts
        items: 
          $ref: '#/definitions/Discount'
      commissions:
        description: Commissions details
        type: array
        items: 
          $ref: '#/definitions/Commission'
  Discount:
    type: object
    description: Deduction from the price amount, made in advance of its payment.
    properties:
      id:
        type: string
        description: In case of promocode override - Corporate code used to access the 'discounted' private fare within Fare Quote process
        example: 'OWNTPM20'
      code:
        type: string
        description: In case of promocode override - Identifier provided by the customer to obtain the discount
        example: '87TVEFN1'
      voucherStatus:
        description: |
            APPLIED  Voucher has been issued / re-issued
            READY_TO_REFUND Voucher is released following a refund / exchange without benefit re-applied 
            FULLY_CONSUMED  Voucher has been consumed following a flown coupon
        type: string
        enum:
        - APPLIED
        - READY_TO_REFUND
        - FULLY_CONSUMED 
        example: 'APPLIED'        
  Commission:
    type: object
    properties:
      amount:
        $ref: '#/definitions/ElementaryPrice'
      percentage:
        type: number
        description: Percentage in case of a rate commission - can be decimal.
  PaymentLoyalty:
    type: object
    properties:
      membership:
        $ref: "#/definitions/Membership"
        description: Loyalty profile used for award payment i.e. miles redemption/upgrade [for FOP codes 'FFP'/'FFU']
      certificateNumber:
        type: string
        description: >-
          Identifies an optional pool of miles provided by the airline owning the loyalty program
          that can be requested as input to the redemption request [FQTR/FQTU insertion]
  Membership:
      type: object
      properties:
        id:
          type: string
          description: Identifies the customer profile i.e. either Frequent Flyer Number or Corporate Recognition Number
          example: '1234567890'
        membershipType:
          type: string
          description: Nature of the membership
          enum:
            - INDIVIDUAL
          example: 'INDIVIDUAL'
        activeTier:
          $ref: '#/definitions/Tier'
  Tier:
    type: object
    description: Description of a Tier in the context of the loyalty program - applicable to both Frequent Flyer profiles & Corporate profiles.
    properties:
      customerValue:
        type: integer
        description: >-
          Ranging from 0 to 9999, it measures the value of the customer [frequent flyer/corporation] for the airline depending on its profile, its travel history, potential and other internal factors.
        example: 5000
      companyCode:
        type: string
        description: Alliance code or airline code of the loyalty program
        example: '6X'        
  Coupon:
    type: object
    description: >-
          A coupon refers to a product that has been issued in the current
          travel document. In tickets, a coupon refers to a flight segment.
          An airline ticket portion that bears the notion "good for passage",
          or in the case of an electronic ticket, the electronic coupon indicates the
          particular places a passenger is entitled to be carried.
          A single ticket/document number may contain up to 4 segments.
          For tickets/documents with more than 4 segments of travel, a conjunction
          ticket is required to continue the itinerary.
          In EMDs, a coupon can refer to an ancillary service, an upgrade, a fee or a residual value.
          * soldSegment - Original image of the coupon at first issuance of the
          document. 
          * currentSegment - Latest image of the coupon in case it has
          been subject to modification[s] such as revalidation e.g. change of
          flight number. 
          * usedSegment - Image of the coupon when its status is
          considered as used i.e. either flown [B] or exchanged [E], refunded
          [RF], printed [PR], print exchange [PE], converted to Flight
          Interruption Manifest [G], closed [CLO], voided [V].
    properties:
      id:
        type: string
        description: Coupon id
        example: '0142100147468-2018-10-18-1'
      number:
        type: number
        description: >-
          The coupon number inside this conjunctive ticket should be the
          same as ”Coupon Number” in case of non conjunctive tickets
          (from1 to 4).
        example: 1
      sequenceNumber:
        type: number
        description: >-
          The coupon sequence number over ticket(s). In the case of
          conjunctive tickets it defines the coupon sequence number over
          all conjunctive tickets and in this case it can be greater than
          four.
        example: 1
      documentNumber:
        type: string
        description: >-
          A single ticket/document number may contain up to 4 segments.
          For tickets/documents with more than 4 segments of travel, a
          conjunction ticket is required to continue the itinerary.
          Conjunctive tickets/EMDs (i.e. for an ETKT, a conjunctive ticket
          is issued when the ticket has more than 4 coupons)
        example: '0142100147468'
      associatedDocuments:
        type: array
        items:
          properties:
            documentType:
              type: string
              description: Type of document
              enum:
              - TICKET
              - EMD_STANDALONE
              - EMD_ASSOCIATED
            documentNumber:
              type: string
              description: Defines the number of the travel document
              example: '1724546546479'
            couponNumbers:
              type: array
              items:
                type: integer
                description: List of coupon numbers that has been issued in the travel document.
              example: 1
      monetaryValue:
        $ref: '#/definitions/ElementaryPrice'
      status:
        type: string
        description: >-
          Coupon operational status. It follows IATA PADIS Code List for data element 4405
        enum:
        - EXCHANGED
        - FLOWN
        - EXCHANGED_TO_FIM
        - COUPON_NOTIFICATION
        - OPEN_FOR_USE
        - REFUNDED
        - SUSPENDED
        - VOID
        - CLOSED
        - REVOKED
        - PRINTED
        - PRINT_EXCHANGE
        - NOT_AVAILABLE
        - BOARDED
        - IRREGULAR_OPERATIONS
        - ORIGINAL_ISSUE
        - CHECKED_IN
        - AIRPORT_CONTROL
        - UNAVAILABLE
        - REFUND_TAXES_AND_FEES
        - PAPER_TICKET
        example: 'EXCHANGED'
      reservationStatus:
        type: string
        description: Reservation status.
        enum:
        - REQUESTED
        - STANDBY
        - NO_SEAT
        - CONFIRMED
        - WAITLISTED
        - SPACE_AVAILABLE
        example: 'REQUESTED'
      fareBasis:
         $ref: '#/definitions/FareBasis'
      fareFamily:
         $ref: '#/definitions/FareFamily'
      reasonForIssuance:
        $ref: '#/definitions/PriceCategory'
      settlementAuthorizationCode:
        type: string
        description: >-
          Contains the code present in the coupon that authenticates the
          settlement of all monetary charges of the coupon.
        example: '1326459788'
      isNonRefundable:
        type: boolean
        description: >-
          Non refundable indicator signifying that the document being
          refundable or not. Default value is false
        example: false
      isNonExchangeable:
        type: boolean
        description: >-
          Non exchangeable indicator signifying that the document being
          exchanged is exchangeable or not.ex:You can only exchange an EMD
          if none of the EMD coupons have the coupon indicator set to
          non-exchangeable. Default value is false
        example: false
      isFromConnection:
        type: boolean
        description: Indicates whether the flight segment priced is departing from a connection with the previous flight
        example: false
      voluntaryIndicator:
        type: string
        description: >-
          Coupon Involuntary Indicator indicates if any manual operations
          have been performed on the coupon. When an Involuntary change is
          performed on a coupon IATA Ticketing handbook requires one to
          enter an endorsement in the FE field.
          * 'W' - Weather/ATC delay
          * 'U' - Involuntary upgrade
          * 'D' - Diversion
          * 'F' - Strike/labor
          * 'IG' - Involuntary Grade Change
          * 'I' - Involuntary (no reason given)
          * 'O' - Oversale
          * 'S' - Schedule change
          * 'V' - Voluntary (customer requested)
          * 'SO' - Flight cancelled
          * '727' - Involuntary downgrade - passenger compensated
          * '728' - Involuntary downgrade - passenger not compensated
        example: 'S'
      frequentFlyer:
        $ref: '#/definitions/FrequentFlyer'
      isCodeshare:
        type: boolean
        description: >-
          Depicts whether the coupon has codeshare flight or not. 
          Default value is false
        example: true
      serviceRemark:
        type: string
        description: EMD related service remarks (free text information)
        example: 'Service remark freetext'
      validityDates:
        type: object
        properties:
          notValidBeforeDate:
            type: string
            format: date
            description: Not valid before date(GMT) in format ISO 8601 YYYY-MM-DD
            example: '2018-10-03T09:35:42.000Z'
          notValidAfterDate:
            type: string
            format: date
            description: 'Not valid after date(GMT) in ISO 8601format,YYYY-MM-DD'
            example: '2018-10-29T09:35:42.000Z'
      baggageAllowance:
        $ref: '#/definitions/BaggageAllowance'
      soldSegment:
        $ref: '#/definitions/FlightSegment'
      usedSegment:
        $ref: '#/definitions/FlightSegment'
      currentSegment:
        $ref: '#/definitions/FlightSegment'
      revalidation:
        description: Revalidating originator and date at which the coupon was revalidated
        $ref: '#/definitions/EventLog'
  FareBasis:
    type: object
    description: >- 
      Composed of codes used by airlines to identify the type of a fare proposed to the booker and to allow airline staff 
      and travel agents to find the rules applicable to that fare.
    properties:
      fareBasisCode:
        type: string
        description: Defines the code of the fare delivered to the booker - up to 6 alphanumerical chars
        example: 'TTI'
  FareFamily:
    type: object
    description: >- 
      A method of identifying multiple fare classes in a single entry.
    properties:
      code:
        type: string
        description: Defines the code of Fare Family
        example: 'ELIGHT'
      owner:
        type: string
        description: Defines the company owner of Fare Family
        example: '8X'
  BaggageAllowance:
    type: object
    properties:
      excessRate:
        $ref: '#/definitions/ElementaryPrice'
      quantity:
        type: integer
        description: Total number of units
      weight:
        $ref: '#/definitions/Weight'
  Weight:
    type: object
    description: An object representing weight and unit.
    properties:
      amount:
        type: string
        description: Defines the weight value with the specified unit with decimal position.
        example: '125,5'
      unit:
        type: string
        description: The unit of measurement used for the weight value.
        enum:
          - KILOGRAMS
          - POUNDS
  BookingClass:
    type: object
    description: Booking class description
    properties:
      code:
        type: string
        description: code identifying the booking class
        example: 'Y'
##
  400Errors:
    type: object
    properties:
      error:
        type: array
        items:
          properties:
            status:
              description: the HTTP status code applicable to this error.
              type: string
            code:
              description: an application-specific error code.
              type: number
            title:
              description: a short summary of the error.
              type: string
            detail:
              description: explanation of the error.
              type: string
            source:
              $ref: '#/definitions/IssueSource'
          example:
            status: '400'
            code: 00008
            title: BAD REQUEST
            detail: Client passed invalid data
  401Errors:
    type: object
    properties:
      error:
        type: array
        items:
          properties:
            status:
              description: the HTTP status code applicable to this error.
              type: string
            code:
              description: an application-specific error code.
              type: number
            title:
              description: a short summary of the error.
              type: string
            detail:
              description: explanation of the error.
              type: string
            source:
              $ref: '#/definitions/IssueSource'
          example:
            status: '401'
            code: 13614
            title: UNAUTHORIZED
            detail: Client not authorized to access this resource
  404Errors:
    type: object
    properties:
      error:
        type: array
        items:
          properties:
            status:
              description: the HTTP status code applicable to this error.
              type: string
            code:
              description: an application-specific error code.
              type: number
            title:
              description: a short summary of the error.
              type: string
            detail:
              description: explanation of the error.
              type: string
            source:
              $ref: '#/definitions/IssueSource'
          example:
            status: '404'
            code: 1797
            title: NOT FOUND
            detail: Client requested resource that does not exist
  500Errors:
    type: object
    properties:
      error:
        type: array
        items:
          properties:
            status:
              description: the HTTP status code applicable to this error.
              type: string
            code:
              description: an application-specific error code.
              type: number
            title:
              description: a short summary of the error.
              type: string
            detail:
              description: explanation of the error.
              type: string
            source:
              $ref: '#/definitions/IssueSource'
          example:
            status: '500'
            code: 727
            title: INTERNAL_SERVER_ERROR
            detail: >-
              Server was unable to process request due to unexpected problem
              that cannot be corrected by client
  IssueSource:
    description: an object containing references to the source of the error
    properties:
      pointer:
        description: A JSON Pointer [RFC6901] to the associated entity in the request document
        type: string
      parameter:
        description: A string indicating which URI query parameter caused the issue
        type: string