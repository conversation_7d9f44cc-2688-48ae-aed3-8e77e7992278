
{
  "tables": [
    {
      "name": "ASSO_AIR_SEGMENT_PAX_COUPON_HISTO",
      "merge": {
        "key-columns": ["UNUSED_FOR_NOW"],
        "if-dupe-take-higher": ["UNUSED_FOR_NOW"]
      },
      "root-sources": [{"blocks": [{"base": ""}]}],
      "columns": [
        // will be generated
      ],
      "correlation": {
        "domain-a": {
          "name": "PNR",
          "partial": {
            "table": "PNR_TKT_PARTIAL_CORR_PIT",
            "start-date": "DATE_BEGIN",
            "end-date": "DATE_END",
            "partial-corr-version": "VERSION",
            "partial-corr-key": "AIR_SEGMENT_PAX_ID",
            "partial-corr-secondary-key": "COUPON_ID",
            "is-last": "IS_LAST_VERSION"
          },
          "pit": {
            "table": "FACT_RESERVATION_HISTO",
            "is-last": "IS_LAST_VERSION",
            "pit-version": "VERSION"
          }
        },
        "domain-b": {
          "name": "TKTEMD",
          "partial": {
            "table": "TKT_PNR_PARTIAL_CORR_PIT",
            "start-date": "DATE_BEGIN",
            "end-date": "DATE_END",
            "partial-corr-version": "VERSION",
            "partial-corr-key": "COUPON_ID",
            "partial-corr-secondary-key": "AIR_SEGMENT_PAX_ID",
            "is-last": "IS_LAST_VERSION"
          },
          "pit": {
            "table": "FACT_TRAVEL_DOCUMENT_HISTO",
            "pit-version": "VERSION",
            "is-last": "IS_LAST_VERSION"
          }
        },
        "target": {
          "domain-a-key": {"name": "AIR_SEGMENT_PAX_ID", "fk": [{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO"}]},
          "domain-b-key": {"name": "COUPON_ID", "fk": [{"schema": "TKT", "table": "FACT_COUPON_HISTO"}]},
          "domain-a-version": {"name": "VERSION_PNR", "fk": [{"schema": "PNR", "table": "FACT_AIR_SEGMENT_PAX_HISTO", "column": "VERSION"}]},
          "domain-b-version": {"name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKT", "table": "FACT_COUPON_HISTO", "column": "VERSION"}]},
          "asso-attributes": [{
            "name": "REFERENCE_KEY_TRAVEL_DOC", "meta": {
              "description": {"value": "DUMMY desc", "rule": "replace"},
              "example": {"value": "DUMMY example", "rule": "concat"},
              "pii-type": {"value": "DUMMY pii type", "rule": "concat"},
              "gdpr-zone": "red"
            }},
          {
            "name": "REFERENCE_KEY_PNR", "meta": {
              "description": {"value": "DUMMY desc", "rule": "replace"},
              "example": {"value": "DUMMY example", "rule": "concat"},
              "pii-type": {"value": "DUMMY pii type", "rule": "concat"},
              "gdpr-zone": "red"
            }
          },
            {
              "name": "REFERENCE_KEY_AIR_SEGMENT_PAX_ID", "meta": {
              "description": {"value": "DUMMY desc", "rule": "replace"},
              "example": {"value": "DUMMY example", "rule": "concat"},
              "pii-type": {"value": "DUMMY pii type", "rule": "concat"},
              "gdpr-zone": "red"
            }
            },
            {
              "name": "REFERENCE_KEY_COUPON_ID", "meta": {
              "description": {"value": "DUMMY desc", "rule": "replace"},
              "example": {"value": "DUMMY example", "rule": "concat"},
              "pii-type": {"value": "DUMMY pii type", "rule": "concat"},
              "gdpr-zone": "red"
            }
            }
          ],
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "is-last": "IS_LAST_VERSION",
          "if-dupe-take-highest": ["DATE_BEGIN"]
        },
        "correlation-field-a-to-b": "TRAVEL_DOCUMENT_ID",
        "correlation-field-b-to-a": "RESERVATION_ID"
      }
    }
  ]
}
