import java.time.OffsetDateTime
import scala.collection.mutable.ListBuffer
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import com.amadeus.airbi.json2star.common.validation.config.{ValidationRecord,ValidationConf}
import com.amadeus.airbi.json2star.common.config.SnowflakeParams
import org.apache.spark.sql.functions.col


import net.snowflake.spark.snowflake.Utils


case class MyQuery(
                    name: String,
                    query: String
                  )

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last
val vConfig = ValidationConf.apply(dbutils)
spark.catalog.setCurrentDatabase(vConfig.appConfig.common.outputDatabase)

val SNOWFLAKE_SOURCE_NAME = "net.snowflake.spark.snowflake"

val phase = vConfig.phase
val customer = vConfig.appConfig.common.shard
val domain = vConfig.appConfig.common.domain
val domainVersion = vConfig.appConfig.common.domainVersion
val validationDb = vConfig.validationDatabase
val validationTable = vConfig.validationTable

val snowflakeParams = vConfig.appConfig.snowflakeParams match {
  case None => throw new IllegalArgumentException(s"AppConfigFile has missing parameters for snowflake")
  case Some(s) => s
}

val sfOptions = snowflakeParams.getSnowflakeOptions(dbutils)
val sfDatabase = sfOptions("sfDatabase")
val sfSchema = sfOptions("sfSchema").toUpperCase

val sfListTables = sqlContext.read
  .format(SNOWFLAKE_SOURCE_NAME)
  .options(sfOptions)
  .option(
    "query",
    s"""
    select table_name
    from ${sfDatabase}.information_schema.tables
    where table_schema='${sfSchema}';
    """
  )
  .load()
  .filter(!col("table_name").startsWith("STAGING") and !col("table_name").startsWith("INTERNAL")).map(r => r.getString(0))

val dbListTables = spark.catalog
  .listTables()
  .select("name").map(r => r.getString(0).toUpperCase).filter(!col("value").startsWith("INTERNAL") and !col("value").startsWith("METADATA"))

val missingTables = dbListTables.except(sfListTables).union(sfListTables.except(dbListTables))

val commonTables =  dbListTables.intersect(sfListTables)

val resultsByTable = commonTables.collect
  .map(r => {
    val name = r
    val q = s"""select count(*) as A_COUNT from (${sfSchema}.${name}) """

    // Run the query in SF
    val cpt = Utils.runQuery(sfOptions, q)
    cpt.next

    //run in spark
    val count = spark.sql("SELECT count(*) as count FROM " + name + ";").select("count").first().getLong(0)

    (name, cpt.getLong("A_COUNT"), count)
  })


val listFailingTables = resultsByTable.filter(r => r._2 != r._3 )
val failTableCount = listFailingTables.size + missingTables.count()
val totalTableCount = resultsByTable.size + missingTables.count()

val testRecord = ValidationRecord(
  domain,
  domainVersion,
  customer,
  phase,
  currentTimestamp,
  task,
  s"Test 1/1 - Check, in SnowFlake ${sfSchema}, tables not consistent with databricks tables from ${vConfig.appConfig.common.outputDatabase}",
  failTableCount == 0,
  failTableCount,
  totalTableCount,
  failTableCount.toFloat / totalTableCount,
  missingTables.map(r => r + " is missing").collect().mkString(", ") ++ listFailingTables.map(r => r._1 + " => sf : "  +r._2 +" upstream : "  +r._3).mkString(", ")
)

val df = Seq(testRecord).toDF()
df.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
  .write
  .insertInto(s"${validationDb}.${validationTable}")