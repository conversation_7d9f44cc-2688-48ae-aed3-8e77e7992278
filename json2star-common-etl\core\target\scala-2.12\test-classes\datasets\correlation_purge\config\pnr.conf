"tables": [
{
  "name": "FACT_RESERVATION_HISTO",
  "mapping": {
    "merge": {
      "key-columns": ["RESERVATION_ID", "VERSION"],
      "if-dupe-take-higher": ["LOAD_DATE"]
    },
    "root-sources": [{ "blocks": [{"base": "$.mainResource.current.image"}]}],
    "columns": [
      {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
      {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
      {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]}},
      {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}},
      {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
      {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
      {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
    ],
    "pit": {
      "type": "master-pit-table",
      "master" : {
        "pit-key": "RESERVATION_ID",
        "pit-version": "VERSION",
        "valid-from": "DATE_BEGIN",
        "valid-to": "DATE_END",
        "is-last": "IS_LAST_VERSION"
      }
    },
    "description": {
      "description": "It contains information related to the booking on PNR-level.",
      "granularity": "1 PNR"
    }
  }
},
{
  "name": "FACT_AIR_SEGMENT_PAX_HISTO",
  "mapping": {
    "merge": {
      "key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"],
      "if-dupe-take-higher": ["LOAD_DATE"]
    },
    "root-sources": [{ "blocks": [
      {"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"},
      {"tr": "$.travelers[*]"}
    ]}],
    "columns": [
      {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr":  "hashM({0})"},
      {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
      {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root" : "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
      {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"root" : "$.mainResource.current.image.reference"}]}},
      {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"root" : "$.mainResource.current.image.version"}]}},
      {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.lastModification.dateTime"}]}},
      {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
      {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
    ],
    "pit": {
      "type": "secondary-pit-table"
    },
    "description": {
      "description": "It contains information related to a Passenger-Segment, with DCS delivery info.",
      "granularity": "1 PAX-SEG"
    }
  }
},
{
  "name": "PNR_TKT_PARTIAL_CORR_PIT",
  "mapping": {
    "merge": {
      "key-columns": ["RESERVATION_ID", "TRAVEL_DOCUMENT_ID", "AIR_SEGMENT_PAX_ID", "COUPON_ID", "VERSION"],
      "if-dupe-take-higher": ["LOAD_DATE"]
    },
    "root-sources": [{ "blocks": [
      {"corr": "$.correlatedResourcesCurrent.PNR-TKT.correlations[*]"},
      {"coupon": "$.corrTktPnr.items[*]"}
    ]}],
    "columns": [
      {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
      {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
      {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
      {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
      {"name": "REFERENCE_KEY_TRAVEL_DOC", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"corr": "$.toId"}]}},
      {"name": "REFERENCE_KEY_PNR", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}},
      {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}},
      {"name": "REFERENCE_KEY_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}},
      {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}},
      {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
      {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
      {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
    ],
    "pit": {
      "type": "secondary-pit-table"
    },
    "description": {
      "description": "It contains PNR-TKT correlation information as seen by PNR.",
      "granularity": "1 PAX-SEG",
      "links": ["???"]
    }
  }
}
]

