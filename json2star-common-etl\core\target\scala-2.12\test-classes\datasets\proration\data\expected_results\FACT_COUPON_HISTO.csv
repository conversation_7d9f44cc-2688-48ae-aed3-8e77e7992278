INTERNAL_ZORDER,<PERSON><PERSON><PERSON>_ID,REF<PERSON>EN<PERSON>_KEY,<PERSON><PERSON>ON_DOCUMENT_NUMBER,<PERSON><PERSON><PERSON>_NUMBER,SEQUENCE_NUMBER,RESERVATION_STATUS,<PERSON><PERSON><PERSON>_STATUS,CURRENT_DEPARTURE_AIRPORT,CURRENT_ARRIVAL_AIRPORT,CURRENT_DEPARTURE_DATETIME_LOCAL,CURRENT_ARRIVAL_DATETIME_LOCAL,CURRENT_MKT_CARRIER,CURRENT_MKT_FLIGHT_NUMBER,CURRENT_MKT_OPERATIONAL_SUFFIX,CURRENT_MKT_BOOKING_CLASS,CURRENT_OPE_CARRIER,CURRENT_OPE_FLIGHT_NUMBER,CURRENT_OPE_OPERATIONAL_SUFFIX,SOLD_DEPARTURE_AIRPORT,SOLD_ARRIVAL_AIRPORT,SOLD_DEPARTURE_DATETIME_LOCAL,<PERSON><PERSON><PERSON>_<PERSON>RIVAL_DATETIME_LOCAL,<PERSON>OL<PERSON>_MK<PERSON>_CARRIER,SOLD_MKT_FLIGHT_NUMBER,SOLD_MKT_OPERATIONAL_SUFFIX,SOLD_MKT_BOOKING_CLASS,SOLD_OPE_CARRIER,SOLD_OPE_FLIGHT_NUMBER,SOLD_OPE_OPERATIONAL_SUFFIX,USED_DEPARTURE_AIRPORT,USED_ARRIVAL_AIRPORT,USED_DEPARTURE_DATETIME_LOCAL,USED_ARRIVAL_DATETIME_LOCAL,USED_MKT_CARRIER,USED_MKT_FLIGHT_NUMBER,USED_MKT_OPERATIONAL_SUFFIX,USED_MKT_BOOKING_CLASS,USED_OPE_CARRIER,USED_OPE_FLIGHT_NUMBER,USED_OPE_OPERATIONAL_SUFFIX,FARE_BASIS_CODE,FARE_FAMILY_CODE,FARE_FAMILY_OWNER,ESTIMATED_PRORATED_FARE,ESTIMATED_PRORATED_FARE_CURRENCY,ESTIMATED_PRORATED_FARE_ORIGINAL,ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL,ESTIMATED_PRORATED_FARE_ALGORITHM,PRORATION_EXCHANGE_RATE_DATE_NEEDED,PRORATION_EXCHANGE_RATE_DATE_TAKEN,INTERNAL_PRORATION_METADATA,REASON_FOR_ISSUANCE_CODE,REASON_FOR_ISSUANCE_SUB_CODE,REASON_FOR_ISSUANCE_DESCRIPTION,SERVICE_REMARK,IS_CODESHARE,IS_NON_EXCHANGEABLE,IS_NON_REFUNDABLE,IS_FROM_CONNECTION,BAGGAGE_ALLOWANCE_WEIGHT_VALUE,BAGGAGE_ALLOWANCE_WEIGHT_UNIT,BAGGAGE_ALLOWANCE_WEIGHT_VALUE_ORIGINAL,BAGGAGE_ALLOWANCE_WEIGHT_UNIT_ORIGINAL,BAGGAGE_ALLOWANCE_QUANTITY,BAGGAGE_EXCESS_RATE,BAGGAGE_EXCESS_RATE_CURRENCY,BAGGAGE_EXCESS_RATE_ORIGINAL,BAGGAGE_EXCESS_RATE_CURRENCY_ORIGINAL,BAGGAGE_EXCHANGE_RATE_DATE_NEEDED,BAGGAGE_EXCHANGE_RATE_DATE_TAKEN,REVALIDATION_DATETIME_UTC,REVALIDATION_OFFICE_IDENTIFIER,REVALIDATION_OFFICE_IATA_NUMBER,REVALIDATION_OFFICE_IN_HOUSE_IDENTIFICATION,REVALIDATION_OFFICE_SYSTEM_CODE,REVALIDATION_OFFICE_AGENT_TYPE,NOT_VALID_BEFORE_DATE_UTC,NOT_VALID_AFTER_DATE_UTC,VOLUNTARY_INDICATOR,SETTLEMENT_AUTHORIZATION_CODE,TRAVEL_DOCUMENT_ID,CURRENT_DEPARTURE_AIRPORT_ID,CURRENT_ARRIVAL_AIRPORT_ID,SOLD_DEPARTURE_AIRPORT_ID,SOLD_ARRIVAL_AIRPORT_ID,USED_DEPARTURE_AIRPORT_ID,USED_ARRIVAL_AIRPORT_ID,REASON_FOR_ISSUANCE_ID,MEMBERSHIP_ID,REVALIDATION_POINT_OF_SALE_ID,VOLUNTARY_INDICATOR_ID,DOCUMENT_NUMBER,DOCUMENT_CREATION_DATE,VERSION,DATE_BEGIN,DATE_END,IS_LAST_VERSION,LOAD_DATE
2023-01-25-2321187251970,hashM(2321187251970-2023-01-25-1),2321187251970-2023-01-25-1,,,1,,,,,,,,,,,,,,EWR,BOS,2023-04-05T12:00:00.000000Z,2023-04-05T18:45:00.000000Z,MH,105,,V,,,,,,,,,,,,,,,,,,,,64.19,USD,FARE CALC,2023-01-25,,"{\"input\":{\"fareCalcLine\":\"EWR B6 BOS64.19UL07AE2C 9K ACK147.81MIPX 9K BOS157.12MIP B6 DCA Q8.79 219.89VH03AE6U USD597.80END ZP EWR4.00BOS0.00ACK0.00BOS4.00XF EWR4.5BOS4.5XT8.00ZP9.00XF\",\"issueDate\":[2023,1,25],\"paymentCurrency\":\"USD\",\"paymentTotal\":\"647.80\",\"paymentTotalTaxes\":\"50.00\",\"itinerary\":[{\"sequenceNumber\":1,\"departureAirport\":\"EWR\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":2,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"ACK\"},{\"sequenceNumber\":3,\"departureAirport\":\"ACK\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":4,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"DCA\"}]},\"tktStatus\":\"FARE CALC-ALL\",\"exceptionMsg\":null,\"inputStep\":null,\"fclStep\":null,\"dstStep\":null}",,,,,,,,,,,,,,,,,,2023-01-25,,,,,,,,,,,,hashM(2321187251970-2023-01-25),,,hashS(EWR),hashS(BOS),,,,,,,2321187251970,2023-01-25,0,2023-05-03T10:37:05.568997Z,,true,2024-01-10T00:00:00.000000Z
2023-01-25-2321187251970,hashM(2321187251970-2023-01-25-2),2321187251970-2023-01-25-2,,,2,,,,,,,,,,,,,,BOS,ACK,2023-04-05T12:00:00.000000Z,2023-04-05T18:45:00.000000Z,ACK,105,,V,,,,,,,,,,,,,,,,,,,,147.81,USD,FARE CALC,2023-01-25,,"{\"input\":{\"fareCalcLine\":\"EWR B6 BOS64.19UL07AE2C 9K ACK147.81MIPX 9K BOS157.12MIP B6 DCA Q8.79 219.89VH03AE6U USD597.80END ZP EWR4.00BOS0.00ACK0.00BOS4.00XF EWR4.5BOS4.5XT8.00ZP9.00XF\",\"issueDate\":[2023,1,25],\"paymentCurrency\":\"USD\",\"paymentTotal\":\"647.80\",\"paymentTotalTaxes\":\"50.00\",\"itinerary\":[{\"sequenceNumber\":1,\"departureAirport\":\"EWR\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":2,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"ACK\"},{\"sequenceNumber\":3,\"departureAirport\":\"ACK\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":4,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"DCA\"}]},\"tktStatus\":\"FARE CALC-ALL\",\"exceptionMsg\":null,\"inputStep\":null,\"fclStep\":null,\"dstStep\":null}",,,,,,,,,,,,,,,,,,2023-01-25,,,,,,,,,,,,hashM(2321187251970-2023-01-25),,,hashS(BOS),hashS(ACK),,,,,,,2321187251970,2023-01-25,0,2023-05-03T10:37:05.568997Z,,true,2024-01-10T00:00:00.000000Z
2023-01-25-2321187251970,hashM(2321187251970-2023-01-25-3),2321187251970-2023-01-25-3,,,3,,,,,,,,,,,,,,ACK,BOS,2023-04-05T12:00:00.000000Z,2023-04-05T18:45:00.000000Z,MH,105,,V,,,,,,,,,,,,,,,,,,,,157.12,USD,FARE CALC,2023-01-25,,"{\"input\":{\"fareCalcLine\":\"EWR B6 BOS64.19UL07AE2C 9K ACK147.81MIPX 9K BOS157.12MIP B6 DCA Q8.79 219.89VH03AE6U USD597.80END ZP EWR4.00BOS0.00ACK0.00BOS4.00XF EWR4.5BOS4.5XT8.00ZP9.00XF\",\"issueDate\":[2023,1,25],\"paymentCurrency\":\"USD\",\"paymentTotal\":\"647.80\",\"paymentTotalTaxes\":\"50.00\",\"itinerary\":[{\"sequenceNumber\":1,\"departureAirport\":\"EWR\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":2,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"ACK\"},{\"sequenceNumber\":3,\"departureAirport\":\"ACK\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":4,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"DCA\"}]},\"tktStatus\":\"FARE CALC-ALL\",\"exceptionMsg\":null,\"inputStep\":null,\"fclStep\":null,\"dstStep\":null}",,,,,,,,,,,,,,,,,,2023-01-25,,,,,,,,,,,,hashM(2321187251970-2023-01-25),,,hashS(ACK),hashS(BOS),,,,,,,2321187251970,2023-01-25,0,2023-05-03T10:37:05.568997Z,,true,2024-01-10T00:00:00.000000Z
2023-01-25-2321187251970,hashM(2321187251970-2023-01-25-4),2321187251970-2023-01-25-4,,,4,,,,,,,,,,,,,,BOS,DCA,2023-04-05T12:00:00.000000Z,2023-04-05T18:45:00.000000Z,MH,105,,V,,,,,,,,,,,,,,,,,,,,228.68,USD,FARE CALC,2023-01-25,,"{\"input\":{\"fareCalcLine\":\"EWR B6 BOS64.19UL07AE2C 9K ACK147.81MIPX 9K BOS157.12MIP B6 DCA Q8.79 219.89VH03AE6U USD597.80END ZP EWR4.00BOS0.00ACK0.00BOS4.00XF EWR4.5BOS4.5XT8.00ZP9.00XF\",\"issueDate\":[2023,1,25],\"paymentCurrency\":\"USD\",\"paymentTotal\":\"647.80\",\"paymentTotalTaxes\":\"50.00\",\"itinerary\":[{\"sequenceNumber\":1,\"departureAirport\":\"EWR\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":2,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"ACK\"},{\"sequenceNumber\":3,\"departureAirport\":\"ACK\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":4,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"DCA\"}]},\"tktStatus\":\"FARE CALC-ALL\",\"exceptionMsg\":null,\"inputStep\":null,\"fclStep\":null,\"dstStep\":null}",,,,,,,,,,,,,,,,,,2023-01-25,,,,,,,,,,,,hashM(2321187251970-2023-01-25),,,hashS(BOS),hashS(DCA),,,,,,,2321187251970,2023-01-25,0,2023-05-03T10:37:05.568997Z,,true,2024-01-10T00:00:00.000000Z
2023-01-25-3321187252000,hashM(3321187252000-2023-01-25-1),3321187252000-2023-01-25-1,,,1,,,,,,,,,,,,,,EWR,BOS,2023-04-05T12:00:00.000000Z,2023-04-05T18:45:00.000000Z,MH,105,,V,,,,,,,,,,,,,,,,,,,,153.34,USD,DISTANCE,2023-01-25,,"{\"input\":{\"fareCalcLine\":\"\",\"issueDate\":[2023,1,25],\"paymentCurrency\":\"USD\",\"paymentTotal\":\"647.80\",\"paymentTotalTaxes\":\"50.00\",\"itinerary\":[{\"sequenceNumber\":1,\"departureAirport\":\"EWR\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":2,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"ACK\"},{\"sequenceNumber\":3,\"departureAirport\":\"ACK\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":4,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"DCA\"}]},\"tktStatus\":\"DISTANCE-ALL\",\"exceptionMsg\":null,\"inputStep\":null,\"fclStep\":{\"value\":\"FARE_CALC-input-empty\",\"details\":\"ERROR: fare calc is empty\"},\"dstStep\":{\"value\":\"DISTANCE-proration-success\",\"details\":\"SUCCESS: check details|ratio = 0.26|remainingAmount = 597.80|cpnDistance = 319.01|remainingDistanceTot = 1243.65\"}}",,,,,,,,,,,,,,,,,,2023-01-25,,,,,,,,,,,,hashM(3321187252000-2023-01-25),,,hashS(EWR),hashS(BOS),,,,,,,3321187252000,2023-01-25,0,2023-05-03T10:37:05.568997Z,,true,2024-01-10T00:00:00.000000Z
2023-01-25-3321187252000,hashM(3321187252000-2023-01-25-2),3321187252000-2023-01-25-2,,,2,,,,,,,,,,,,,,BOS,ACK,2023-04-05T12:00:00.000000Z,2023-04-05T18:45:00.000000Z,ACK,105,,V,,,,,,,,,,,,,,,,,,,,69.02,USD,DISTANCE,2023-01-25,,"{\"input\":{\"fareCalcLine\":\"\",\"issueDate\":[2023,1,25],\"paymentCurrency\":\"USD\",\"paymentTotal\":\"647.80\",\"paymentTotalTaxes\":\"50.00\",\"itinerary\":[{\"sequenceNumber\":1,\"departureAirport\":\"EWR\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":2,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"ACK\"},{\"sequenceNumber\":3,\"departureAirport\":\"ACK\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":4,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"DCA\"}]},\"tktStatus\":\"DISTANCE-ALL\",\"exceptionMsg\":null,\"inputStep\":null,\"fclStep\":{\"value\":\"FARE_CALC-input-empty\",\"details\":\"ERROR: fare calc is empty\"},\"dstStep\":{\"value\":\"DISTANCE-proration-success\",\"details\":\"SUCCESS: check details|ratio = 0.12|remainingAmount = 597.80|cpnDistance = 143.60|remainingDistanceTot = 1243.65\"}}",,,,,,,,,,,,,,,,,,2023-01-25,,,,,,,,,,,,hashM(3321187252000-2023-01-25),,,hashS(BOS),hashS(ACK),,,,,,,3321187252000,2023-01-25,0,2023-05-03T10:37:05.568997Z,,true,2024-01-10T00:00:00.000000Z
2023-01-25-3321187252000,hashM(3321187252000-2023-01-25-3),3321187252000-2023-01-25-3,,,3,,,,,,,,,,,,,,ACK,BOS,2023-04-05T12:00:00.000000Z,2023-04-05T18:45:00.000000Z,MH,105,,V,,,,,,,,,,,,,,,,,,,,69.02,USD,DISTANCE,2023-01-25,,"{\"input\":{\"fareCalcLine\":\"\",\"issueDate\":[2023,1,25],\"paymentCurrency\":\"USD\",\"paymentTotal\":\"647.80\",\"paymentTotalTaxes\":\"50.00\",\"itinerary\":[{\"sequenceNumber\":1,\"departureAirport\":\"EWR\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":2,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"ACK\"},{\"sequenceNumber\":3,\"departureAirport\":\"ACK\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":4,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"DCA\"}]},\"tktStatus\":\"DISTANCE-ALL\",\"exceptionMsg\":null,\"inputStep\":null,\"fclStep\":{\"value\":\"FARE_CALC-input-empty\",\"details\":\"ERROR: fare calc is empty\"},\"dstStep\":{\"value\":\"DISTANCE-proration-success\",\"details\":\"SUCCESS: check details|ratio = 0.12|remainingAmount = 597.80|cpnDistance = 143.60|remainingDistanceTot = 1243.65\"}}",,,,,,,,,,,,,,,,,,2023-01-25,,,,,,,,,,,,hashM(3321187252000-2023-01-25),,,hashS(ACK),hashS(BOS),,,,,,,3321187252000,2023-01-25,0,2023-05-03T10:37:05.568997Z,,true,2024-01-10T00:00:00.000000Z
2023-01-25-3321187252000,hashM(3321187252000-2023-01-25-4),3321187252000-2023-01-25-4,,,4,,,,,,,,,,,,,,BOS,DCA,2023-04-05T12:00:00.000000Z,2023-04-05T18:45:00.000000Z,MH,105,,V,,,,,,,,,,,,,,,,,,,,306.41,USD,DISTANCE,2023-01-25,,"{\"input\":{\"fareCalcLine\":\"\",\"issueDate\":[2023,1,25],\"paymentCurrency\":\"USD\",\"paymentTotal\":\"647.80\",\"paymentTotalTaxes\":\"50.00\",\"itinerary\":[{\"sequenceNumber\":1,\"departureAirport\":\"EWR\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":2,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"ACK\"},{\"sequenceNumber\":3,\"departureAirport\":\"ACK\",\"arrivalAirport\":\"BOS\"},{\"sequenceNumber\":4,\"departureAirport\":\"BOS\",\"arrivalAirport\":\"DCA\"}]},\"tktStatus\":\"DISTANCE-ALL\",\"exceptionMsg\":null,\"inputStep\":null,\"fclStep\":{\"value\":\"FARE_CALC-input-empty\",\"details\":\"ERROR: fare calc is empty\"},\"dstStep\":{\"value\":\"DISTANCE-proration-success\",\"details\":\"SUCCESS: check details|ratio = 0.51|remainingAmount = 597.80|cpnDistance = 637.45|remainingDistanceTot = 1243.65\"}}",,,,,,,,,,,,,,,,,,2023-01-25,,,,,,,,,,,,hashM(3321187252000-2023-01-25),,,hashS(BOS),hashS(DCA),,,,,,,3321187252000,2023-01-25,0,2023-05-03T10:37:05.568997Z,,true,2024-01-10T00:00:00.000000Z
