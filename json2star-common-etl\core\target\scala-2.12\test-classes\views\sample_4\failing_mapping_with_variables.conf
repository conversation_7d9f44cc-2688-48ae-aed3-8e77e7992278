{
  "defaultComment" : "A coment here",
  "partition-spec" : {
    "key" : "PNR_CREATION_DATE",
    "column-name": "PART_PNR_CREATION_MONTH",
    "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
  },
  "tables": [
    {
      "name": "FACT_RESERVATION_HISTO", // table with multiple simple root source
      "mapping" : {
        "merge": {
          "key-columns": ["RESERVATION_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}, {"blocks": [{"base":"$.mainResource.current.image.dummy"}]}],
        "columns": [
          {"name": "RESERVATION_ID", "is-mandatory": "true", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "VERSION", "is-mandatory": "true", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "RESERVATION_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR, version"
        }
      },
    },
    {
      "name": "FACT_TEST_CYCLIC_COL_DEPENDENCY", // table with column with variables
      "mapping": {
        "merge": {
          "key-columns": ["CARRIER_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image"},{"marketing": "$.products[?(@.subType == 'AIR')].airSegment.marketing"}]},
        ],
        "columns": [
          {"name": "MARKETING_SEGMENT_ID", "is-mandatory": "true", "column-type": "longColumn", "sources": {"blocks": [{"marketing": "$.id"}]},"expr": "hashXS({0})" },
          {"name": "MARKETING_SEGMENT_IDENTIFIER", "is-mandatory": "true", "column-type": "strColumn", "has-variable" : true,"sources": {"blocks": [{"marketing": "$[?(@.flightDesignator.carrierCode == '{RELATED_CARRIER_CODE}')].id"}]} },
          {"name": "RELATED_CARRIER_CODE", "is-mandatory": "true", "column-type": "strColumn", "has-variable" : true, "sources": {"blocks": [{"base": "$.products[?(@.id == '{RELATED_SEGMENT_IDENTIFIER}')].airSegment.flightDesignator.carrierCode"}]} },
          {"name": "RELATED_SEGMENT_IDENTIFIER", "is-mandatory": "true", "column-type": "strColumn", "has-variable" : true, "sources": {"blocks": [{"base": "$.products[?(@.id == '{MARKETING_SEGMENT_IDENTIFIER}')].id"}]} },
          {"name": "CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"companyCode": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }}
    }
  ]
}