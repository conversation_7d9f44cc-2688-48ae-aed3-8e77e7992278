reservationIdVal: {
  "name": "RESERVATION_ID",
  "column-type": "binaryStrColumn",
  "is-mandatory": "true",
  "sources": {"blocks": [{"base": "$.id"}]},
  "expr": "hashM({0})"
}


   "defaultComment" : "A comment here",
   "partition-spec" : {
          "key" : "PNR_CREATION_DATE",
          "column-name": "PART_PNR_CREATION_MONTH",
          "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
    },
  "tables": [
    {
        "name": "FACT_RESERVATION_HISTO",
        "mapping": {
          "merge": {
            "key-columns": ["RESERVATION_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{ "blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            ${reservationIdVal},
            {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
            {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.reference"}]}},
            {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "NIP", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.nip"}]}},
            {"name": "GROUP_SIZE", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.size"}]}},
            {"name": "GROUP_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.group.name"}]}},
            {"name": "GROUP_SIZE_TAKEN", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.sizeTaken"}]}},
            {"name": "POINT_OF_SALE_OWNER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.owner.office.id"}, {"base":"$.owner.office.iataNumber"}, {"base":"$.owner.office.systemCode"}, {"base":"$.owner.office.agentType"}, {"base":"$.owner.login.cityCode"}, {"base":"$.owner.login.countryCode"}, {"base":"$.owner.login.numericSign"}, {"base":"$.owner.login.initials"}, {"base":"$.owner.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "POINT_OF_SALE_CREATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}, {"base": "$.creation.pointOfSale.office.iataNumber"}, {"base": "$.creation.pointOfSale.office.systemCode"}, {"base": "$.creation.pointOfSale.office.agentType"}, {"base": "$.creation.pointOfSale.login.cityCode"}, {"base": "$.creation.pointOfSale.login.countryCode"}, {"base": "$.creation.pointOfSale.login.numericSign"}, {"base": "$.creation.pointOfSale.login.initials"}, {"base": "$.creation.pointOfSale.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "POINT_OF_SALE_LAST_UPDATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.lastModification.pointOfSale.office.id"}, {"base": "$.lastModification.pointOfSale.office.iataNumber"}, {"base": "$.lastModification.pointOfSale.office.systemCode"}, {"base": "$.lastModification.pointOfSale.office.agentType"}, {"base": "$.lastModification.pointOfSale.login.cityCode"}, {"base": "$.lastModification.pointOfSale.login.countryCode"}, {"base": "$.lastModification.pointOfSale.login.numericSign"}, {"base": "$.lastModification.pointOfSale.login.initials"}, {"base": "$.lastModification.pointOfSale.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "RESERVATION_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          },
          "description": {
            "description": "It contains information related to the booking on PNR-level.",
            "granularity": "1 PNR"
          }
        },
        "table-snowflake": {
            "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
        }
    },
    {
      "name": "FACT_AIR_SEGMENT_PAX_HISTO",
      "mapping": {
        "merge": {
          "key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"},
          {"tr": "$.travelers[*]"}
        ]}],
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr":  "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root" : "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
          {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"tr":"$.id"}]}, "expr": "hashM({0})"},
          {
            "name": "SEGMENT_SCHEDULE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
            "sources": {"blocks": [
              {"prod": "$.airSegment.marketing.flightDesignator.carrierCode"},
              {"prod": "$.airSegment.operating.flightDesignator.carrierCode"},
              {"prod": "$.airSegment.departure.iataCode"},
              {"prod": "$.airSegment.arrival.iataCode"},
              {"prod": "$.airSegment.marketing.flightDesignator.flightNumber"},
              {"prod": "$.airSegment.marketing.flightDesignator.operationalSuffix"},
              {"prod": "$.airSegment.operating.flightDesignator.flightNumber"},
              {"prod": "$.airSegment.operating.flightDesignator.operationalSuffix"},
              {"prod": "$.airSegment.departure.localDateTime"},
              {"prod": "$.airSegment.arrival.localDateTime"},
              {"prod": "$.airSegment.departure.terminal"},
              {"prod": "$.airSegment.arrival.terminal"},
              {"prod": "$.airSegment.operating.codeshareAgreement"},
              {"prod": "$.airSegment.aircraft.aircraftType"}
            ]},
            "expr": "hashM({0})"
          },
          {"name": "CABIN_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.cabin.code"}]}, "expr": "hashS({0})"},
          {"name": "BOOKING_CLASS_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.code"}]}, "expr": "hashS({0})"},
          {"name": "BOOKING_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.bookingStatusCode"}]}, "expr": "hashS({0})"},
          {
            "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [
              {"prod": "$.airSegment.creation.pointOfSale.office.id"},
              {"prod": "$.airSegment.creation.pointOfSale.office.iataNumber"},
              {"prod": "$.airSegment.creation.pointOfSale.office.systemCode"},
              {"prod": "$.airSegment.creation.pointOfSale.office.agentType"},
              {"prod": "$.airSegment.creation.pointOfSale.login.cityCode"},
              {"prod": "$.airSegment.creation.pointOfSale.login.countryCode"},
              {"prod": "$.airSegment.creation.pointOfSale.login.numericSign"},
              {"prod": "$.airSegment.creation.pointOfSale.login.initials"},
              {"prod": "$.airSegment.creation.pointOfSale.login.dutyCode"}
            ]},
            "expr": "hashM({0})"
          },
          { // ADDED one to showcase access to different json sections
            "name": "FIRST_NAME", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"}]},
            "has-variable": true
          },
          {"name": "CABIN_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.cabin.code"}]}},
          {"name": "BOOKING_CLASS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.code"}]}},
          {"name": "BOOKING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.bookingStatusCode"}]}},
          {"name": "IS_INFO", "column-type": "booleanColumn", "sources": {"blocks": [{"prod": "$.airSegment.isInformational"}]}},
          {"name": "PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}]}}, // ADDED one for filtering the delivery
          {
            "name": "PDI_NOREC", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isNoRec"}]},
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_GOSHOW", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isGoShow"}]},
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_DISTRIBUTION_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].distributionId"}]},
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "PDI_TRANSFER_FLAG", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].transferFlag"}]},
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {"name": "DEPARTURE_DATE_TIME", "column-type": "timestampColumn", "sources": {"blocks": [{"prod": "$.airSegment.departure.localDateTime"}]}},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"root" : "$.mainResource.current.image.reference"}]}},
          {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.creation.dateTime"}]}},
          {"name": "VERSION", "column-type": "intColumn", "is-mandatory": "true", "sources": {"blocks": [{"root" : "$.mainResource.current.image.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to a Passenger-Segment, with DCS delivery info.",
          "granularity": "1 PAX-SEG"
        }
      },
      "table-snowflake": {
        "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
      }
    },
    {
      "name": "PNR_TKT_PARTIAL_CORR_PIT",
      "mapping": {
        "merge": {
          "key-columns": ["RESERVATION_ID", "TRAVEL_DOCUMENT_ID", "AIR_SEGMENT_PAX_ID", "COUPON_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"corr": "$.correlatedResourcesCurrent.PNR-TKT.correlations[*]"},
          {"coupon": "$.corrTktPnr.items[*]"}
        ]}],
        "columns": [
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
          {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
          {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY_TRAVEL_DOC", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"corr": "$.toId"}]}},
          {"name": "REFERENCE_KEY_PNR", "column-type": "strColumn", "is-mandatory": "false", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}},
          {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.pnrAirSegmentId"}, {"coupon": "$.pnrTravelerId"}]}},
          {"name": "REFERENCE_KEY_COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "false", "sources": {"blocks": [{"coupon": "$.ticketCouponId"}]}},
          {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains PNR-TKT correlation information as seen by PNR.",
          "granularity": "1 PAX-SEG",
          "links": ["???"]
        }
      },
    },
    {
      "name": "DIM_POINT_OF_SALE",
      "mapping": {
        "merge": {
            "key-columns": ["POINT_OF_SALE_ID"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [
              { "name": "owner", "rs": { "blocks": [
                  {"pos": "$.mainResource.current.image.owner"},
                  {"office" : "$.office"}]}
              },
              { "name": "creation", "rs": { "blocks": [
                  {"pos": "$.mainResource.current.image.creation.pointOfSale"},
                  {"office" : "$.office"}]}
              },
              { "name": "lastModification", "rs": { "blocks": [
                  {"pos": "$.mainResource.current.image.lastModification.pointOfSale"},
                  {"office" : "$.office"}]}
              },
              { "name": "queuing", "rs": { "blocks": [
                  {"pos": "$.mainResource.current.image"},
                  {"office" : "$.queuingOffice"}]}
              },
              { "name": "air", "rs": { "blocks": [
                  {"pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"},
                  {"office" : "$.office"}]}
              }
            ],
            "columns": [
              { "name": "OFFICE_AMID", "column-type": "strColumn","sources": { "blocks": [{"office": "$.id"}]} },
              { "name": "OFFICE_IATA_NUMBER","column-type": "strColumn", "sources":  { "blocks": [{"office": "$.iataNumber"}]}},
              { "name": "OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources":  { "blocks": [{"office": "$.systemCode"}]}},
              { "name": "OFFICE_AGENT_TYPE","column-type": "strColumn","sources":  { "blocks": [{"office": "$.agentType"}]}},
              { "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn", "sources": { "root-specific": [
                  {"rs-name": "owner", "blocks": [
                    {"office": "$.id"},
                    {"office": "$.iataNumber"},
                    {"office": "$.systemCode"},
                    {"office": "$.agentType"},
                    {"pos": "$.login.cityCode"},
                    {"pos": "$.login.countryCode"},
                    {"pos": "$.login.numericSign"},
                    {"pos": "$.login.initials"},
                    {"pos": "$.login.dutyCode"}
                  ]},
                  {"rs-name": "creation", "blocks": [
                    {"office": "$.id"},
                    {"office": "$.iataNumber"},
                    {"office": "$.systemCode"},
                    {"office": "$.agentType"},
                    {"pos": "$.login.cityCode"},
                    {"pos": "$.login.countryCode"},
                    {"pos": "$.login.numericSign"},
                    {"pos": "$.login.initials"},
                    {"pos": "$.login.dutyCode"}
                  ]},
                  {"rs-name": "lastModification", "blocks": [
                    {"office": "$.id"},
                    {"office": "$.iataNumber"},
                    {"office": "$.systemCode"},
                    {"office": "$.agentType"},
                    {"pos": "$.login.iataNumber"}, // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {"pos": "$.login.systemCode"}, // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {"pos": "$.login.numericSign"},
                    {"pos": "$.login.initials"},
                    {"pos": "$.login.dutyCode"}
                  ]},
                  {"rs-name": "queuing", "blocks": [
                    {"office": "$.id"}
                  ]},
                  {"rs-name": "air", "blocks": [
                    {"office": "$.id"},
                    {"office": "$.iataNumber"},
                    {"office": "$.systemCode"},
                    {"office": "$.agentType"},
                    {"pos": "$.login.cityCode"},
                    {"pos": "$.login.countryCode"},
                    {"pos": "$.login.numericSign"},
                    {"pos": "$.login.initials"},
                    {"pos": "$.login.dutyCode"}
                  ]}
                ]}, "expr":  "hashM({0})"
              },
              { "name": "LOGIN_CITY_CODE","column-type": "strColumn","sources": { "root-specific": [
                  {"rs-name": "owner", "blocks": [{"pos": "$.login.cityCode"}]},
                  {"rs-name": "creation", "blocks": [{"pos": "$.login.cityCode"}]},
                  {"rs-name": "lastModification", "blocks": [{"pos": "$.login.cityCode"}]},
                  {"rs-name": "air", "blocks": [{"pos": "$.login.cityCode"}]}
                ]}
              },
              { "name": "LOGIN_COUNTRY_CODE","column-type": "strColumn","sources": { "root-specific": [
                  {"rs-name": "owner", "blocks": [{"pos": "$.login.countryCode"}]},
                  {"rs-name": "creation", "blocks": [{"pos": "$.login.countryCode"}]},
                  {"rs-name": "lastModification", "blocks": [{"pos": "$.login.countryCode"}]},
                  {"rs-name": "air", "blocks": [{"pos": "$.login.countryCode"}]}
                ]}
              },
              { "name": "LOGIN_NUMERIC_SIGN", "column-type": "strColumn","sources": { "root-specific": [
                  {"rs-name": "owner", "blocks": [{"pos": "$.login.numericSign"}]},
                  {"rs-name": "creation", "blocks": [{"pos": "$.login.numericSign"}]},
                  {"rs-name": "lastModification", "blocks": [{"pos": "$.login.numericSign"}]},
                  {"rs-name": "air", "blocks": [{"pos": "$.login.numericSign"}]}
                ]}
              },
              { "name": "LOGIN_INITIALS", "column-type": "strColumn", "sources": { "root-specific": [
                  {"rs-name": "owner", "blocks": [{"pos": "$.login.initials"}]},
                  {"rs-name": "creation", "blocks": [{"pos": "$.login.initials"}]},
                  {"rs-name": "lastModification", "blocks": [{"pos": "$.login.initials"}]},
                  {"rs-name": "air", "blocks": [{"pos": "$.login.initials"}]}
                ]}
              },
              { "name": "LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": { "root-specific": [
                  {"rs-name": "owner", "blocks": [{"pos": "$.login.dutyCode"}]},
                  {"rs-name": "creation", "blocks": [{"pos": "$.login.dutyCode"}]},
                  {"rs-name": "lastModification", "blocks": [{"pos": "$.login.dutyCode"}]},
                  {"rs-name": "air", "blocks": [{"pos": "$.login.dutyCode"}]}
                ]}
              },
            ],
            "pit": {
              "type": "no-pit-table"
            }
          }
        },
        {
        "name": "DIM_CARRIER",
        "mapping": {
          "merge": {
            "key-columns": ["CARRIER_ID"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"}]},
            { "blocks": [{"companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"}]}

          ],
          "columns": [
            {"name": "CARRIER_ID", "column-type": "longColumn", "sources": {"blocks": [{"companyCode": "$.value"}]},"expr": "hashXS({0})" },
            {"name": "CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"companyCode": "$.value"}]}}
          ],
          "pit": {
              "type": "no-pit-table"
          }
        }
      }
]

