// MAGIC %md
// MAGIC #Notebook Information
// MAGIC This notebook contains **4** functional tests for SKD.

// COMMAND ----------

import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import org.apache.spark.sql.functions.{col, abs, when,coalesce, lit }

// COMMAND ----------

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, daysBack, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard
val db = vConfig.appConfig.common.outputDatabase
val homeCurrency = vConfig.appConfig.common.homeCurrency.get

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val date_formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val minTime = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(daysBack.toInt)


// COMMAND ----------

case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()

// COMMAND ----------

// DBTITLE 1,TC-RA-001: All converted currencies should be the same as home currency
//At the moment we don't add fares at Coupon's level as the currency is not converted yet.

val allCurrencies = spark.sql(s"""
  SELECT * FROM(
      -- Cupon level currencies
      SELECT CURRENCY
      FROM $db.FACT_COUPON_TAX_HISTO
      WHERE AMOUNT_TYPE = 'ACCOUNTED'
        and load_date > to_date($minTime)
      UNION ALL
      SELECT CURRENCY
      FROM $db.FACT_COUPON_COMMISSION_HISTO
      WHERE AMOUNT_TYPE = 'ACCOUNTED'
        and load_date > to_date($minTime)
      UNION ALL
      SELECT CURRENCY
      FROM $db.FACT_COUPON_FEE_HISTO
      WHERE AMOUNT_TYPE = 'ACCOUNTED'
        and load_date > to_date($minTime)

      -- Ticket level currencies
      UNION ALL
      SELECT CURRENCY
      FROM $db.FACT_TAX_HISTO
      WHERE AMOUNT_TYPE = 'ACCOUNTED'
        and load_date > to_date($minTime)
      UNION ALL
      SELECT CURRENCY
      FROM $db.FACT_COMMISSION_HISTO
      WHERE AMOUNT_TYPE = 'ACCOUNTED'
        and load_date > to_date($minTime)
      UNION ALL
      SELECT CURRENCY
      FROM $db.FACT_FEE_HISTO
      WHERE AMOUNT_TYPE = 'ACCOUNTED'
        and load_date > to_date($minTime)
      UNION ALL
      SELECT CURRENCY
      FROM $db.FACT_FARE_HISTO
      WHERE AMOUNT_TYPE = 'ACCOUNTED'
        and load_date > to_date($minTime)
  )
""")

val countFailsTC1 = allCurrencies.
  filter(col("CURRENCY") =!= homeCurrency).count()

val countTotalTC1 = allCurrencies.count()


testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-RA-001: All converted currencies should be the same as home currency",
  countFailsTC1 == 0,
  countFailsTC1,
  countTotalTC1,
  if (countTotalTC1 != 0) countFailsTC1.toFloat / countTotalTC1 else 0,
  if (countFailsTC1 != 0) "Some currencies are not the same as home currency." else ""
)
// COMMAND ----------

// DBTITLE 1,TC-RA-002: Sum of coupons price should be within a percentage of ticket price
val acceptableFailingRecordPercentage = 0.2
val acceptableRatio = 0.2

//Taxes part
val coupon_taxes = spark.sql(s"""
  SELECT
    c.TRAVEL_DOCUMENT_ID,
    c.VERSION,
    SUM(c.AMOUNT) as coupon_total_taxes
  FROM $db.FACT_COUPON_TAX_HISTO c
  WHERE c.AMOUNT_TYPE = 'ACCOUNTED' AND c.TYPE='R'
    and c.load_date > to_date($minTime)
  GROUP BY  c.TRAVEL_DOCUMENT_ID, c.VERSION
  """)

val ticket_taxes = spark.sql(s"""
  SELECT
    t.TRAVEL_DOCUMENT_ID,
    t.VERSION,
    SUM(t.AMOUNT) as ticket_total_taxes
  FROM $db.FACT_TAX_HISTO t
  WHERE t.AMOUNT_TYPE = 'ACCOUNTED'
    and t.load_date > to_date($minTime)
  GROUP BY t.TRAVEL_DOCUMENT_ID, t.VERSION
  """)

//Commissions part
val coupon_commissions = spark.sql(s"""
  SELECT
    c.TRAVEL_DOCUMENT_ID,
    c.VERSION,
    SUM(c.AMOUNT) as coupon_total_commissions
  FROM $db.FACT_COUPON_COMMISSION_HISTO c
  WHERE c.AMOUNT_TYPE = 'ACCOUNTED'
    and c.load_date > to_date($minTime)
  GROUP BY  c.TRAVEL_DOCUMENT_ID, c.VERSION
  """)

val ticket_commissions = spark.sql(s"""
  SELECT
    t.TRAVEL_DOCUMENT_ID,
    t.VERSION,
    SUM(t.AMOUNT) as ticket_total_commissions
  FROM $db.FACT_COMMISSION_HISTO t
  WHERE t.AMOUNT_TYPE = 'ACCOUNTED'
    and t.load_date > to_date($minTime)
  GROUP BY t.TRAVEL_DOCUMENT_ID, t.VERSION
  """)

//Total part
val coupon_total = coupon_taxes
  .join(coupon_commissions,
    Seq("TRAVEL_DOCUMENT_ID", "VERSION"))
  .select(
    col("TRAVEL_DOCUMENT_ID"),
    col("VERSION"),
    (col("coupon_total_taxes") + col("coupon_total_commissions")).as("coupon_totals")
  )

val ticket_total = ticket_taxes
  .join(ticket_commissions,
    Seq("TRAVEL_DOCUMENT_ID", "VERSION"), "left")
  .select(
    col("TRAVEL_DOCUMENT_ID"),
    col("VERSION"),
    ( coalesce(col("ticket_total_taxes"), lit(0)) +  coalesce(col("ticket_total_commissions"), lit(0))).as("ticket_totals")
  )

val comparison = ticket_total
  .join(coupon_total,
    Seq("TRAVEL_DOCUMENT_ID", "VERSION"))
  .select(
    col("TRAVEL_DOCUMENT_ID"),
    col("VERSION"),
    col("ticket_totals").as("ticket_amount"),
    col("coupon_totals").as("coupon_amount"),
    ((col("ticket_totals") - col("coupon_totals")) /
      when(col("ticket_totals") === 0, null)
        .otherwise(col("ticket_totals"))).as("difference_ratio")
  )


val failingRecords = comparison.filter(
  abs(col("difference_ratio")) > acceptableRatio
)

val countFailsTC2 = failingRecords.count()
val countTotalTC2 = comparison.count()
val failingRatio = if (countTotalTC2 != 0) countFailsTC2.toFloat / countTotalTC2 else 0

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  s"TC-RA-002: Sum of coupons should be within ${acceptableRatio * 100}% of ticket price",
  countFailsTC2 < acceptableFailingRecordPercentage,
  countFailsTC2,
  countTotalTC2,
  failingRatio,
  if (countFailsTC2 != 0) s"The ratio between some cupon's sum and their associated ticket's are higher than $acceptableRatio." else ""
)

// COMMAND ----------

// DBTITLE 1,TC-RA-003: All coupons should be prorated
val totalCount = spark.sql(s"""
  select *
  from $db.fact_coupon_histo
  where load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_coupon_histo c
  where not exists (
    select * from $db.fact_coupon_fare_histo f
    where c.coupon_id = f.coupon_id and c.version = f.version
      and f.amount_type = 'PRORATION')
    and c.load_date > to_date($minTime)
""").count()

// WARNING: about 60% of coupons with status = FLOWN were not prorated in PDT feed -> to be checked if it's the same in PRD data

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  s"TC-RA-003: All coupons should be prorated",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of coupons not prorated : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// DBTITLE 1,TC-RA-004: if >=5 coupons then conjunctive ticket number(s) shall be filled

val totalCount = spark.sql(s"""
  select doc.reference_key, doc.version, count(1) as nbrcpns
  from $db.fact_travel_document_histo doc
    inner join $db.fact_coupon_histo cpn on cpn.travel_document_id = doc.travel_document_id and cpn.version = doc.version
  where doc.load_date > to_date($minTime)
  group by 1, 2
  having nbrcpns >= 5
""").count()

val failCount = spark.sql(s"""
  select doc.reference_key, doc.version, count(1) as nbrcpns
  from $db.fact_travel_document_histo doc
    inner join $db.fact_coupon_histo cpn on cpn.travel_document_id = doc.travel_document_id and cpn.version = doc.version
  where doc.conjunctive_document_1 is null
    and doc.conjunctive_document_2 is null
    and doc.conjunctive_document_3 is null
    and doc.load_date > to_date($minTime)
  group by 1, 2
  having nbrcpns >= 5
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  s"TC-RA-004: Tickets that have at least 5 coupons should have conjunctive documents",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of tickets that have at least 5 coupons and that do not have conjunctive documents : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// DBTITLE 1,TC-RA-005: all travel documents shall have at least 1 FOP

val totalCount = spark.sql(s"""
  select * from $db.fact_travel_document_histo where load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_travel_document_histo doc
    left outer join $db.fact_form_of_payment_histo fop on fop.travel_document_id = doc.travel_document_id and fop.version = doc.version
  where fop.form_of_payment_id is null
    and doc.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-RA-005: All tickets should have at least 1 form of payment",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of tickets that do not have any form of payment : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ##write to db

// COMMAND ----------

val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")