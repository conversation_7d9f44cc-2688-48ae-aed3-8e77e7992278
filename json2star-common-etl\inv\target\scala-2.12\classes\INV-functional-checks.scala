// MAGIC %md
// MAGIC #Notebook Information
// MAGIC This notebook contains **5** functional tests for INV.

// COMMAND ----------

import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

// COMMAND ----------

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, daysBack, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard
val dbName = vConfig.appConfig.common.outputDatabase
val refTable = "fact_flight_date_histo"

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val date_formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val minTime = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(daysBack.toInt)

// COMMAND ----------

case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()

// COMMAND ----------

// MAGIC %md
// MAGIC # begin tests

// COMMAND ----------

//test that the flix qualifiers per flight leg is the same (only one)

// this table is generated only for airlines that have flixes
if(spark.catalog.tableExists(s"$dbName.FACT_FLIGHT_INVOLUNTARY_CANCELLATION")) {
  val tc1Threshold = 0.01

  val countFailsTC1 = spark.sql(s"""
    SELECT FLIGHT_INVOLUNTARY_CANCELLATION_ID, count(distinct QUALIFIER) as total
    FROM $dbName.FACT_FLIGHT_INVOLUNTARY_CANCELLATION
    GROUP BY FLIGHT_INVOLUNTARY_CANCELLATION_ID having total > 1
  """).count()

  val countTotalTC1 = spark.sql(s"""
      SELECT FLIGHT_LEG_ID FROM $dbName.fact_flight_leg
  """).count()


  testRecords += TestRecord(
    domain,
    domain_version,
    customer,
    phase,
    currentTimestamp,
    task,
    "TC-INV-001 : Having only one flix qualifier per flight leg",
    countFailsTC1 < tc1Threshold,
    countFailsTC1,
    countTotalTC1,
    if ( countTotalTC1 != 0) countFailsTC1.toFloat / countTotalTC1 else 0,
    if ( countFailsTC1 > tc1Threshold) "Errors in leg-flix-qualifier data" else ""
  )
}

// COMMAND ----------

//test that all departed flights have 0 as the total value for Balanced Adjustment for a Leg
val tc2Threshold = 0.01

val countFailsTC2 = spark.sql(s"""
    SELECT sum(l.INVENTORY_CONTROL_MAXI_REGRADE_ADJUSTMENT) as total
    FROM
        -- DEPARTED FLIGHTS (from yesterday and older)
        (select * from $dbName.fact_flight_date
        where SYSTEM_FLAGS != 'LINK_BROKEN'
        and IS_CANCELLED == FALSE
        and SCHEDULED_DEPARTURE_DATE < current_date()) fd
    INNER JOIN $dbName.fact_cabin_counters_leg l
      ON fd.FLIGHT_DATE_ID = l.FLIGHT_DATE_ID
    GROUP BY l.FLIGHT_LEG_ID
    HAVING total != 0
""").count()

val countTotalTC2 = spark.sql(s"""
    SELECT FLIGHT_LEG_ID FROM $dbName.fact_flight_leg
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-INV-002 : The final value for Balanced Adjustment should be 0 for a Leg",
  countFailsTC2 < tc2Threshold,
  countFailsTC2,
  countTotalTC2,
  if ( countTotalTC2 != 0) countFailsTC2.toFloat / countTotalTC2 else 0,
  if ( countFailsTC2 > tc2Threshold) "Errors in balanced-adjustment-leg data" else ""
)

// COMMAND ----------

//test A departed flight must have segment boarded figures

// Possible limitations:
// - SCHEDULED_DEPARTURE_DATE refers to the first leg of the flight, in case of multi-leg flights, this could span over another day
// - SCHEDULED_DEPARTURE_DATE does not take into account delayed flights, the effective flight could be the day after (is the operational suffix mandatory?)
val tc3Threshold = 0.05
val tc3FunctionalCase = "TC-INV-003 : A departed flight must have segment boarded figures"
val tc3ErrorDetails = "Errors in fact_class_counters_segment data (departed flights without pax)"

val countFailsTC3 = spark.sql(s"""
    WITH
      dcs_flight_dates AS (
        SELECT *
        FROM $dbName.fact_flight_date
        WHERE SYSTEM_FLAGS <> 'LINK_BROKEN'
        AND IS_CANCELLED == FALSE
        AND SCHEDULED_DEPARTURE_DATE < current_date()
      ),
      dcs_flight_dates_with_counters AS (
        SELECT
          f.FLIGHT_DATE_ID,
          SUM(DCS_BOARDED_PAX) AS DCS_BOARDED_PAX,
          SUM(DCS_COMMERCIAL_STANDBY_PAX_BOARDED) AS DCS_COMMERCIAL_STANDBY_PAX_BOARDED,
          SUM(DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE) AS DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE,
          SUM(DCS_DENIED_BOARDING_PAX) AS DCS_DENIED_BOARDING_PAX,
          SUM(DCS_GO_SHOW_PAX) AS DCS_GO_SHOW_PAX,
          SUM(DCS_NO_SHOW_PAX) AS DCS_NO_SHOW_PAX,
          SUM(DCS_GROUP_PAX_BOARDED) AS DCS_GROUP_PAX_BOARDED,
          SUM(DCS_GROUP_NO_SHOWS) AS DCS_GROUP_NO_SHOWS,
          SUM(DCS_STAFF_STANDBY_PAX_BOARDED) AS DCS_STAFF_STANDBY_PAX_BOARDED,
          SUM(DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE) AS DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE
        FROM $dbName.fact_class_counters_segment c JOIN dcs_flight_dates f ON c.FLIGHT_DATE_ID = f.FLIGHT_DATE_ID
        GROUP BY f.FLIGHT_DATE_ID
      )
      SELECT FLIGHT_DATE_ID
      FROM dcs_flight_dates_with_counters
      WHERE (
        DCS_BOARDED_PAX +
        DCS_COMMERCIAL_STANDBY_PAX_BOARDED +
        DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE +
        DCS_DENIED_BOARDING_PAX +
        DCS_GO_SHOW_PAX +
        DCS_NO_SHOW_PAX +
        DCS_GROUP_PAX_BOARDED +
        DCS_GROUP_NO_SHOWS +
        DCS_STAFF_STANDBY_PAX_BOARDED +
        DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE
      ) == 0
""").count()

val countTotalTC3 = spark.sql(s"""
    SELECT FLIGHT_DATE_ID
    FROM $dbName.fact_flight_date
    WHERE SYSTEM_FLAGS <> 'LINK_BROKEN'
    AND IS_CANCELLED == FALSE
    AND SCHEDULED_DEPARTURE_DATE < current_date()
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  tc3FunctionalCase,
  countFailsTC3 < tc3Threshold,
  countFailsTC3,
  countTotalTC3,
  if ( countTotalTC3 != 0) countFailsTC3.toFloat / countTotalTC3 else 0,
  if ( countFailsTC3 > tc3Threshold) tc3ErrorDetails else ""
)



// COMMAND ----------

//test A departed flight must have leg boarded figures

/* counters available only at class level
// Possible limitations:
// - SCHEDULED_DEPARTURE_DATE refers to the first leg of the flight, in case of multi-leg flights, this could span over another day
// - SCHEDULED_DEPARTURE_DATE does not take into account delayed flights, the effective flight could be the day after (is the operational suffix mandatory?)
val tc4Threshold = 0.05
val tc4FunctionalCase = "TC-INV-004 : A departed flight must have leg boarded figures"
val tc4ErrorDetails = "Errors in fact_cabin_counters_leg data (departed flights without pax)"

val countFailsTC4 = spark.sql(s"""
    WITH
      dcs_flight_dates AS (
        SELECT *
        FROM $dbName.fact_flight_date
        WHERE SYSTEM_FLAGS <> 'LINK_BROKEN'
        AND IS_CANCELLED == FALSE
        AND SCHEDULED_DEPARTURE_DATE < current_date()
      ),
      dcs_flight_dates_with_counters AS (
        SELECT
          f.FLIGHT_DATE_ID,
          SUM(DCS_BOARDED_PAX) AS DCS_BOARDED_PAX,
          SUM(DCS_COMMERCIAL_STANDBY_PAX_BOARDED) AS DCS_COMMERCIAL_STANDBY_PAX_BOARDED,
          SUM(DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE) AS DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE,
          SUM(DCS_DENIED_BOARDING_PAX) AS DCS_DENIED_BOARDING_PAX,
          SUM(DCS_GO_SHOW_PAX) AS DCS_GO_SHOW_PAX,
          SUM(DCS_NO_SHOW_PAX) AS DCS_NO_SHOW_PAX,
          SUM(DCS_GROUP_PAX_BOARDED) AS DCS_GROUP_PAX_BOARDED,
          SUM(DCS_GROUP_NO_SHOWS) AS DCS_GROUP_NO_SHOWS,
          SUM(DCS_STAFF_STANDBY_PAX_BOARDED) AS DCS_STAFF_STANDBY_PAX_BOARDED,
          SUM(DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE) AS DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE
        FROM $dbName.fact_cabin_counters_leg c JOIN dcs_flight_dates f ON c.FLIGHT_DATE_ID = f.FLIGHT_DATE_ID
        GROUP BY f.FLIGHT_DATE_ID
      )
      SELECT FLIGHT_DATE_ID
      FROM dcs_flight_dates_with_counters
      WHERE (
        DCS_BOARDED_PAX +
        DCS_COMMERCIAL_STANDBY_PAX_BOARDED +
        DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE +
        DCS_DENIED_BOARDING_PAX +
        DCS_GO_SHOW_PAX +
        DCS_NO_SHOW_PAX +
        DCS_GROUP_PAX_BOARDED +
        DCS_GROUP_NO_SHOWS +
        DCS_STAFF_STANDBY_PAX_BOARDED +
        DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE
      ) > 0
""").count()

val countTotalTC4 = spark.sql(s"""
    SELECT FLIGHT_DATE_ID
    FROM $dbName.fact_flight_date
    WHERE SYSTEM_FLAGS <> 'LINK_BROKEN'
    AND IS_CANCELLED == FALSE
    AND SCHEDULED_DEPARTURE_DATE < current_date()
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  tc4FunctionalCase,
  countFailsTC4 < tc4Threshold,
  countFailsTC4,
  countTotalTC4,
  if ( countTotalTC4 != 0) countFailsTC4.toFloat / countTotalTC4 else 0,
  if ( countFailsTC4 > tc4Threshold) tc4ErrorDetails else ""
)
*/


// COMMAND ----------

//test Flights with an operational suffix and that do have a cancelled counterpart the day before

val tc5Threshold = 0.01
val tc5FunctionalCase = "TC-INV-005 : Flights with an operational suffix and that do have a cancelled counterpart the day before"
val tc5ErrorDetails = "Errors in fact_flight_date data (incoherent flights with operational suffix)"

val countValidTC5 = spark.sql(s"""
    WITH
      flight_dates_with_operational_suffix AS (
        SELECT *
        FROM $dbName.fact_flight_date
        WHERE operational_suffix IS NOT NULL
      ),
      flight_dates_with_operational_suffix_and_day_before AS (
        SELECT DATE_SUB(scheduled_departure_date, 1) AS day_before_flight_date, *
        FROM flight_dates_with_operational_suffix
      )
    SELECT
      *
    FROM
        flight_dates_with_operational_suffix_and_day_before t1
    JOIN
        $dbName.fact_flight_date t2
    ON
        t1.day_before_flight_date = t2.scheduled_departure_date
        AND t1.carrier_code = t2.carrier_code
        AND t1.flight_number = t2.flight_number
        AND t2.is_cancelled = TRUE
""").count()

val countTotalTC5 = spark.sql(s"""
    SELECT FLIGHT_DATE_ID
    FROM $dbName.fact_flight_date
    WHERE operational_suffix IS NOT NULL
""").count()

val countFailsTC5 = countTotalTC5 - countValidTC5

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  tc5FunctionalCase,
  countFailsTC5 < tc5Threshold,
  countFailsTC5,
  countTotalTC5,
  if ( countTotalTC5 != 0) countFailsTC5.toFloat / countTotalTC5 else 0,
  if ( countFailsTC5 > tc5Threshold) tc5ErrorDetails else ""
)


// COMMAND ----------

//test A class has exactly a cabin
val tc6Threshold = 0.01
val tc6FunctionalCase = "TC-INV-006 : A class has exactly a cabin"
val tc6ErrorDetails = "Errors in fact_class_counters_segment data"

val countValidTC6 = spark.sql(s"""
    SELECT CLASS_COUNTERS_SEGMENT_ID
    FROM $dbName.fact_class_counters_segment
    WHERE booking_class IS NOT NULL AND booking_class <> '' AND cabin IS NOT NULL AND cabin <> ''
""").count()

val countTotalTC6 = spark.sql(s"""
    SELECT CLASS_COUNTERS_SEGMENT_ID
    FROM $dbName.fact_class_counters_segment
""").count()

val countFailsTC6 = countTotalTC6 - countValidTC6

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  tc6FunctionalCase,
  countFailsTC6 < tc6Threshold,
  countFailsTC6,
  countTotalTC6,
  if ( countTotalTC6 != 0) countFailsTC6.toFloat / countTotalTC6 else 0,
  if ( countFailsTC6 > tc6Threshold) tc6ErrorDetails else ""
)


// COMMAND ----------

//test Only one class appears on the same flight date
val tc7Threshold = 0.01
val tc7FunctionalCase = "TC-INV-007 : Only one class appears on the same flight date"
val tc7ErrorDetails = "Errors in fact_class_counters_segment data"

val countValidTC7 = spark.sql(s"""
    SELECT FLIGHT_SEGMENT_ID, BOOKING_CLASS
    FROM (
      SELECT FLIGHT_SEGMENT_ID, BOOKING_CLASS, COUNT(*) AS entries
      FROM $dbName.fact_class_counters_segment
      GROUP BY FLIGHT_SEGMENT_ID, BOOKING_CLASS
    )
    WHERE entries == 1
""").count()

val countTotalTC7 = spark.sql(s"""
    SELECT DISTINCT FLIGHT_SEGMENT_ID, BOOKING_CLASS
    FROM $dbName.fact_class_counters_segment
""").count()

val countFailsTC7 = countTotalTC7 - countValidTC7

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  tc7FunctionalCase,
  countFailsTC7 < tc7Threshold,
  countFailsTC7,
  countTotalTC7,
  if ( countTotalTC7 != 0) countFailsTC7.toFloat / countTotalTC7 else 0,
  if ( countFailsTC7 > tc7Threshold) tc7ErrorDetails else ""
)


// COMMAND ----------

// MAGIC %md
// MAGIC ##write to db

// COMMAND ----------

val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")