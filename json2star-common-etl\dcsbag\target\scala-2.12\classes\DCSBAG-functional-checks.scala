
// COMMAND ----------


import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils


// COMMAND ----------


val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, daysBack, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard
val dbName = vConfig.appConfig.common.outputDatabase
val refTable = "fact_bag_histo"

val homeWeightUnit = vConfig.appConfig.common.homeWeightUnit.get
val homeCurrency = vConfig.appConfig.common.homeCurrency.get

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val date_formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val minTime = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(daysBack.toInt)

// COMMAND ----------


case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()


// COMMAND ----------

// DBTITLE 1,TC-DCSB-001 : Check all the bags in the fact_bag_leg_delivery_histo exist in the fact_bag_histo

val countFailsTC1 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_bag_leg_delivery_histo
  LEFT JOIN $dbName.fact_bag_histo
  ON fact_bag_leg_delivery_histo.BAG_ID = fact_bag_histo.bag_id
  AND fact_bag_leg_delivery_histo.version = fact_bag_histo.version
  WHERE fact_bag_histo.bag_id IS NULL
  AND fact_bag_leg_delivery_histo.load_date > to_date($minTime)
""").count()

val countTotalTC1 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_bag_leg_delivery_histo
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-001 - Check all the bags in the fact_bag_leg_delivery_histo exist in the fact_bag_histo",
  countFailsTC1 == 0,
  countFailsTC1,
  countTotalTC1,
  if (countTotalTC1 != 0) countFailsTC1.toFloat / countTotalTC1 else 0,
  if (countFailsTC1 != 0) "Error processing some bags on operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-002 : Ensure the weight field in the bag table and bag_group table is always positive

val countFailsTC2 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_bag_histo
  INNER JOIN $dbName.FACT_BAG_GROUP_HISTO
  ON fact_bag_histo.BAG_GROUP_ID = FACT_BAG_GROUP_HISTO.BAG_GROUP_ID
  AND fact_bag_histo.version = FACT_BAG_GROUP_HISTO.version
  WHERE FACT_BAG_HISTO.load_date > to_date($minTime)
  AND (nvl(fact_bag_histo.WEIGHT_VALUE,0) < 0
  OR nvl(FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE,0) < 0)
""").count()

val countTotalTC2 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_bag_histo
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-002 - Ensure the weight field in the bag table and bag_group table is always positive",
  countFailsTC2 == 0,
  countFailsTC2,
  countTotalTC2,
  if (countTotalTC2 != 0) countFailsTC2.toFloat / countTotalTC2 else 0,
  if (countFailsTC2 != 0) "Error processing some bags out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-003 : Maximum bags per responsible_passenger

val thresholdMax = 10;

val countFailsTC3 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_bag_group_histo
  WHERE checked_bags_count > $thresholdMax
  AND load_date > to_date($minTime)
""").count()

val countTotalTC3 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_bag_group_histo
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-003 - Maximum bags per responsible_passenger",
  countFailsTC3 == 0,
  countFailsTC3,
  countTotalTC3,
  if (countTotalTC3 != 0) countFailsTC3.toFloat / countTotalTC3 else 0,
  if (countFailsTC3 != 0) "Error processing some bags out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-004 : Maximum weight per bag

val thresholdMax = 50;

val countFailsTC4 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_bag_histo
  WHERE weight_value > $thresholdMax
  AND load_date > to_date($minTime)
""").count()

val countTotalTC4 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_bag_histo
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-004 - Maximum weight per bag",
  countFailsTC4 == 0,
  countFailsTC4,
  countTotalTC4,
  if (countTotalTC4 != 0) countFailsTC4.toFloat / countTotalTC4 else 0,
  if (countFailsTC4 != 0) "Error processing some bags out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-005 : Checked sum of weight_value of Bag_Group_Histo is equivalent to weight_value of bag_histo (check the consistency between bag_histo and bags_group_histo)

val countFailsTC5 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE nvl(FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE,0) !=
    (SELECT sum(nvl(weight_value,0)) from $dbName.fact_bag_histo
    WHERE fact_bag_histo.VERSION = FACT_BAG_GROUP_HISTO.version
    AND fact_bag_histo.BAG_GROUP_ID = FACT_BAG_GROUP_HISTO.BAG_GROUP_ID)
  AND load_date > to_date($minTime)
""").count()

val countTotalTC5 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-005 - Check the consistency between fact_bag_histo and FACT_BAG_GROUP_HISTO",
  countFailsTC5 == 0,
  countFailsTC5,
  countTotalTC5,
  if (countTotalTC5 != 0) countFailsTC5.toFloat / countTotalTC5 else 0,
  if (countFailsTC5 != 0) "Error processing some bag groups out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-006 : Check all the bags in the fact_Excess_baggage_item_histo in the fact_bag_histo

val countFailsTC6 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_Excess_baggage_item_histo
  LEFT JOIN $dbName.fact_bag_histo
  ON fact_Excess_baggage_item_histo.BAG_ID = fact_bag_histo.bag_id
  AND fact_Excess_baggage_item_histo.version = fact_bag_histo.version
  WHERE fact_bag_histo.bag_id IS NULL
  AND FACT_EXCESS_BAGGAGE_ITEM_HISTO.load_date > to_date($minTime)
""").count()

val countTotalTC6 = spark.sql(s"""
  SELECT *
  FROM $dbName.fact_Excess_baggage_item_histo
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-006 - Check all the bags in the fact_Excess_baggage_item_histo in the fact_bag_histo",
  countFailsTC6 == 0,
  countFailsTC6,
  countTotalTC6,
  if (countTotalTC6 != 0) countFailsTC6.toFloat / countTotalTC6 else 0,
  if (countFailsTC6 != 0) "Error processing some bags out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-007 : Bag groups with no weight in fact_bag_histo or FACT_BAG_GROUP_HISTO tables

val countFailsTC7 = spark.sql(s"""
  SELECT *
  FROM (
    SELECT fact_bag_histo.BAG_GROUP_ID, fact_bag_histo.VERSION, sum(nvl(WEIGHT_VALUE,0)) as sum_bag, nvl(FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE,0) as bag_group_weight, min(fact_bag_histo.load_date) as load_date
    FROM $dbName.fact_bag_histo
    INNER JOIN $dbName.FACT_BAG_GROUP_HISTO
    ON fact_bag_histo.VERSION = FACT_BAG_GROUP_HISTO.version
    AND fact_bag_histo.BAG_GROUP_ID = FACT_BAG_GROUP_HISTO.BAG_GROUP_ID
    GROUP BY fact_bag_histo.BAG_GROUP_ID, fact_bag_histo.VERSION, FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE)
  WHERE sum_bag = 0
  AND bag_group_weight = 0
  AND load_date > to_date($minTime)
""").count()

val countTotalTC7 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-007 - Bag groups with no weight in fact_bag_histo or FACT_BAG_GROUP_HISTO tables",
  countFailsTC7 == 0,
  countFailsTC7,
  countTotalTC7,
  if (countTotalTC7 != 0) countFailsTC7.toFloat / countTotalTC7 else 0,
  if (countFailsTC7 != 0) "Error processing some bag groups out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-008 : Bag groups with weights in FACT_BAG_GROUP_HISTO but no weight in fact_bag_histo

val countFailsTC8 = spark.sql(s"""
  SELECT *
  FROM (
    SELECT fact_bag_histo.BAG_GROUP_ID, fact_bag_histo.VERSION, sum(nvl(WEIGHT_VALUE,0)) as sum_bag, nvl(FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE,0) as bag_group_weight, min(fact_bag_histo.load_date) as load_date
    FROM $dbName.fact_bag_histo
    INNER JOIN $dbName.FACT_BAG_GROUP_HISTO
    ON fact_bag_histo.VERSION = FACT_BAG_GROUP_HISTO.version
    AND fact_bag_histo.BAG_GROUP_ID = FACT_BAG_GROUP_HISTO.BAG_GROUP_ID
    GROUP BY fact_bag_histo.BAG_GROUP_ID, fact_bag_histo.VERSION, FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE)
  WHERE sum_bag = 0
  AND bag_group_weight > 0
  AND load_date > to_date($minTime)
""").count()

val countTotalTC8 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-008 - Bag groups with weights in FACT_BAG_GROUP_HISTO but no weight in fact_bag_histo",
  countFailsTC8 == 0,
  countFailsTC8,
  countTotalTC8,
  if (countTotalTC8 != 0) countFailsTC8.toFloat / countTotalTC8 else 0,
  if (countFailsTC8 != 0) "Error processing some bag groups out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-009 : Bag groups with weight in fact_bag_histo but no weights in FACT_BAG_GROUP_HISTO

val countFailsTC9 = spark.sql(s"""
  SELECT *
  FROM (
    SELECT fact_bag_histo.BAG_GROUP_ID, fact_bag_histo.VERSION, sum(nvl(WEIGHT_VALUE,0)) as sum_bag, nvl(FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE,0) as bag_group_weight, min(fact_bag_histo.load_date) as load_date
    FROM $dbName.fact_bag_histo
    INNER JOIN $dbName.FACT_BAG_GROUP_HISTO
    ON fact_bag_histo.VERSION = FACT_BAG_GROUP_HISTO.version
    AND fact_bag_histo.BAG_GROUP_ID = FACT_BAG_GROUP_HISTO.BAG_GROUP_ID
    GROUP BY fact_bag_histo.BAG_GROUP_ID, fact_bag_histo.VERSION, FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE)
  WHERE sum_bag > 0
  AND bag_group_weight = 0
  AND load_date > to_date($minTime)
""").count()

val countTotalTC9 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-009 - Bag groups with weight in fact_bag_histo but no weights in FACT_BAG_GROUP_HISTO",
  countFailsTC9 == 0,
  countFailsTC9,
  countTotalTC9,
  if (countTotalTC9 != 0) countFailsTC9.toFloat / countTotalTC9 else 0,
  if (countFailsTC9 != 0) "Error processing some bag groups out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-010 : Bag groups with different weights in fact_bag_histo and FACT_BAG_GROUP_HISTO

val countFailsTC10 = spark.sql(s"""
  SELECT *
  FROM (
    SELECT fact_bag_histo.BAG_GROUP_ID, fact_bag_histo.VERSION, sum(nvl(WEIGHT_VALUE,0)) as sum_bag, nvl(FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE,0) as bag_group_weight, min(fact_bag_histo.load_date) as load_date
    FROM $dbName.fact_bag_histo
    INNER JOIN $dbName.FACT_BAG_GROUP_HISTO
    ON fact_bag_histo.VERSION = FACT_BAG_GROUP_HISTO.version
    AND fact_bag_histo.BAG_GROUP_ID = FACT_BAG_GROUP_HISTO.BAG_GROUP_ID
    GROUP BY fact_bag_histo.BAG_GROUP_ID, fact_bag_histo.VERSION, FACT_BAG_GROUP_HISTO.CHECKED_BAGS_WEIGHT_VALUE)
  WHERE sum_bag != bag_group_weight
  AND sum_bag > 0
  AND bag_group_weight > 0
  AND load_date > to_date($minTime)
""").count()

val countTotalTC10 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-010 - Bag groups with different weights in fact_bag_histo and FACT_BAG_GROUP_HISTO",
  countFailsTC10 == 0,
  countFailsTC10,
  countTotalTC10,
  if (countTotalTC10 != 0) countFailsTC10.toFloat / countTotalTC10 else 0,
  if (countFailsTC10 != 0) "Error processing some bag groups out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-011 : Check no empty messages are processed

val countFailsTC11 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE checked_bags_count is null
  AND load_date > to_date($minTime)
""").count()

val countTotalTC11 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-011 - Check no empty messages are processed",
  countFailsTC11 == 0,
  countFailsTC11,
  countTotalTC11,
  if (countTotalTC11 != 0) countFailsTC11.toFloat / countTotalTC11 else 0,
  if (countFailsTC11 != 0) "Error processing some bag groups out operational view" else ""
)


// COMMAND ----------

// MAGIC %md
// MAGIC #Weight conversion

// COMMAND ----------

case class WeightConv(src: String, dst: String, rate: Double)
val data = Seq(
  WeightConv("KILOGRAMS", "POUNDS", 2.20462262185),
  WeightConv("POUNDS", "KILOGRAMS", 0.45359237),
  WeightConv("POUNDS", "POUNDS", 1),
  WeightConv("KILOGRAMS", "KILOGRAMS", 1)
)
val df = data.toDF("from_unit", "to_unit", "rate")
df.createOrReplaceTempView("weight_conv_temp_view")

// COMMAND ----------

// DBTITLE 1,TC-DCSB-012 : Check weight conversion is correct for FACT_EXCESS_BAGGAGE_ITEM_HISTO

val countFailsTC12 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_EXCESS_BAGGAGE_ITEM_HISTO
  LEFT JOIN weight_conv_temp_view
  ON from_unit = WEIGHT_UNIT_ORIGINAL
  AND to_unit = "$homeWeightUnit"
  --check that the weight value is correctly converted
  WHERE (round(weight_value,2) != round(rate * weight_value_original,2)
    --check that the converted weight unit is always the same as the HOME_WEIGHT_UNIT
    OR nvl(WEIGHT_UNIT,0) != "$homeWeightUnit"
    --check that both original and converted value are null if one is null
    OR ((weight_value_original is null) AND (weight_value is not null))
    OR ((weight_value_original is not null) AND (weight_value is null)))
  AND load_date > to_date($minTime)
""").count()

val countTotalTC12 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_EXCESS_BAGGAGE_ITEM_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-012 - Check weight conversion is correct for FACT_EXCESS_BAGGAGE_ITEM_HISTO",
  countFailsTC12 == 0,
  countFailsTC12,
  countTotalTC12,
  if (countTotalTC12 != 0) countFailsTC12.toFloat / countTotalTC12 else 0,
  if (countFailsTC12 != 0) "Error processing some bags out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-013 : Check weight conversion is correct for checked bags in FACT_BAG_GROUP_HISTO

val countFailsTC13 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  LEFT JOIN weight_conv_temp_view a
  ON a.from_unit = CHECKED_BAGS_WEIGHT_UNIT_ORIGINAL
  AND a.to_unit = "$homeWeightUnit"
  LEFT JOIN weight_conv_temp_view b
  ON b.from_unit = HAND_BAGS_WEIGHT_UNIT_ORIGINAL
  AND b.to_unit = "$homeWeightUnit"
  --check that the weight value is correctly converted
  WHERE (round(CHECKED_BAGS_weight_value,2) != round(a.rate * CHECKED_BAGS_weight_value_original,2)
    OR round(HAND_BAGS_weight_value,2) != round(b.rate * HAND_BAGS_weight_value_original,2)
    --check that the converted weight unit is always the same as the HOME_WEIGHT_UNIT
    OR nvl(CHECKED_BAGS_WEIGHT_UNIT,0) != "$homeWeightUnit"
    OR nvl(HAND_BAGS_WEIGHT_UNIT,0) != "$homeWeightUnit"
    --check that both original and converted value are null if one is null
    OR ((CHECKED_BAGS_weight_value_original is null) AND (CHECKED_BAGS_weight_value is not null))
    OR ((CHECKED_BAGS_weight_value_original is not null) AND (CHECKED_BAGS_weight_value is null))
    OR ((HAND_BAGS_weight_value_original is null) AND (HAND_BAGS_weight_value is not null))
    OR ((HAND_BAGS_weight_value_original is not null) AND (HAND_BAGS_weight_value is null)))
  AND load_date > to_date($minTime)
""").count()

val countTotalTC13 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_GROUP_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-013 - Check weight conversion is correct for checked bags in FACT_BAG_GROUP_HISTO",
  countFailsTC13 == 0,
  countFailsTC13,
  countTotalTC13,
  if (countTotalTC13 != 0) countFailsTC13.toFloat / countTotalTC13 else 0,
  if (countFailsTC13 != 0) "Error processing some bag groups out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-014 : Check weight conversion is correct for FACT_BAG_HISTO

val countFailsTC14 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_HISTO
  JOIN weight_conv_temp_view
  ON from_unit = WEIGHT_UNIT_ORIGINAL
  AND to_unit = "$homeWeightUnit"
  --check that the weight value is correctly converted
  WHERE (round(weight_value,2) != round(rate * weight_value_original,2)
    --check that the converted weight unit is always the same as the HOME_WEIGHT_UNIT
    OR nvl(WEIGHT_UNIT,0) != "$homeWeightUnit"
    --check that both original and converted value are null if one is null
    OR ((weight_value_original is null) AND (weight_value is not null))
    OR ((weight_value_original is not null) AND (weight_value is null)))
  AND  load_date > to_date($minTime)
""").count()

val countTotalTC14 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_BAG_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-014 - Check weight conversion is correct for FACT_BAG_HISTO",
  countFailsTC14 == 0,
  countFailsTC14,
  countTotalTC14,
  if (countTotalTC14 != 0) countFailsTC14.toFloat / countTotalTC14 else 0,
  if (countFailsTC14 != 0) "Error processing some bags out operational view" else ""
)


// COMMAND ----------

// MAGIC %md
// MAGIC #Currency conversion

// COMMAND ----------

spark.sql(s"""
  CREATE OR REPLACE TEMPORARY VIEW conversion_rates AS
  SELECT * FROM refdata_database.exchange_rates_post_process
  WHERE TO_CURRENCY = "$homeCurrency"
""")

// COMMAND ----------

// DBTITLE 1,TC-DCSB-015 : Check currency conversion consistency in FACT_EXCESS_BAGGAGE_CHARGE_HISTO

val countFailsTC15 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_EXCESS_BAGGAGE_CHARGE_HISTO
  LEFT JOIN conversion_rates
  ON DATE_BEGIN >= EFFECTIVE_DATE
  AND DATE_BEGIN < DISCONTINUE_DATE
  AND FROM_CURRENCY = TOTAL_CHARGE_CURRENCY_ORIGINAL
  --check the values are correctly converted to home currency (filtering when the origin currency is the same as home currency)
  WHERE ((FROM_CURRENCY != TOTAL_CHARGE_CURRENCY_ORIGINAL
      AND round(nvl(TOTAL_CHARGE_AMOUNT_ORIGINAL,0) * rate_value,2) = round(nvl(TOTAL_CHARGE_AMOUNT,0),2))
    --check that converted currency unit is always the same as home currency
    OR nvl(TOTAL_CHARGE_CURRENCY,0) != "$homeCurrency"
    --check that both original and converted value are null if one is null
    OR ((TOTAL_CHARGE_AMOUNT is null) AND (TOTAL_CHARGE_AMOUNT_ORIGINAL is not null))
    OR ((TOTAL_CHARGE_AMOUNT is not null) AND (TOTAL_CHARGE_AMOUNT_ORIGINAL is null)))
  AND load_date > to_date($minTime)
""").count()

val countTotalTC15 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_EXCESS_BAGGAGE_CHARGE_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-015 - Check currency conversion consistency in FACT_EXCESS_BAGGAGE_CHARGE_HISTO",
  countFailsTC15 == 0,
  countFailsTC15,
  countTotalTC15,
  if (countTotalTC15 != 0) countFailsTC15.toFloat / countTotalTC15 else 0,
  if (countFailsTC15 != 0) "Error processing some bags out operational view" else ""
)


// COMMAND ----------

// DBTITLE 1,TC-DCSB-016 : Check currency conversion consistency in FACT_EXCESS_BAGGAGE_ITEM_HISTO

val countFailsTC16 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_EXCESS_BAGGAGE_ITEM_HISTO
  LEFT JOIN conversion_rates a
  ON DATE_BEGIN >= a.EFFECTIVE_DATE
  AND DATE_BEGIN < a.DISCONTINUE_DATE
  AND a.FROM_CURRENCY = CHARGE_CURRENCY_ORIGINAL
  LEFT JOIN conversion_rates b
  ON DATE_BEGIN >= b.EFFECTIVE_DATE
  AND DATE_BEGIN < b.DISCONTINUE_DATE
  AND b.FROM_CURRENCY = RATE_CURRENCY_ORIGINAL
  --check the values are correctly converted to home currency (filtering when the origin currency is the same as home currency)
  WHERE ((a.FROM_CURRENCY != CHARGE_CURRENCY_ORIGINAL
      AND round(nvl(CHARGE_AMOUNT_ORIGINAL,0) * a.rate_value,2) = round(nvl(CHARGE_AMOUNT,0),2))
    OR (b.FROM_CURRENCY != RATE_CURRENCY_ORIGINAL
      AND round(nvl(RATE_AMOUNT_ORIGINAL,0) * b.rate_value,2) = round(nvl(RATE_AMOUNT,0),2))
    --check that converted currency unit is always the same as home currency
    OR nvl(CHARGE_CURRENCY,0) != "$homeCurrency"
    OR nvl(RATE_CURRENCY,0) != "$homeCurrency"
    --check that both original and converted value are null if one is null
    OR ((CHARGE_AMOUNT is null) AND (CHARGE_AMOUNT_ORIGINAL is not null))
    OR ((CHARGE_AMOUNT is not null) AND (CHARGE_AMOUNT_ORIGINAL is null))
    OR ((RATE_AMOUNT is null) AND (RATE_AMOUNT_ORIGINAL is not null))
    OR ((RATE_AMOUNT is not null) AND (RATE_AMOUNT_ORIGINAL is null)))
 AND load_date > to_date($minTime)
""").count()

val countTotalTC16 = spark.sql(s"""
  SELECT *
  FROM $dbName.FACT_EXCESS_BAGGAGE_ITEM_HISTO
  WHERE load_date > to_date($minTime)
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-DCSB-016 - Check currency conversion consistency in FACT_EXCESS_BAGGAGE_ITEM_HISTO",
  countFailsTC16 == 0,
  countFailsTC16,
  countTotalTC16,
  if (countTotalTC16 != 0) countFailsTC16.toFloat / countTotalTC16 else 0,
  if (countFailsTC16 != 0) "Error processing some bags out operational view" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ##write to db

// COMMAND ----------

val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")