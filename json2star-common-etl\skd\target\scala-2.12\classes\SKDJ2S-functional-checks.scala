// MAGIC %md
// MAGIC #Notebook Information
// MAGIC This notebook contains **4** functional tests for SKD.

// COMMAND ----------

import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

// COMMAND ----------

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, daysBack, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard
val dbName = vConfig.appConfig.common.outputDatabase
val refTable = "fact_flight_date_histo"

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val date_formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val minTime = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(daysBack.toInt)

// COMMAND ----------

case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()

// COMMAND ----------

// MAGIC %md
// MAGIC # begin tests

// COMMAND ----------

// DBTITLE 1,TC-SKD-001 : Operating codeshare with partnership in one segment
//test that all flights marked as "Operating" in the FACT_FLIGHT_DATE table have at least one corresponding "Marketing" flight in the FACT_CODESHARE_FLIGHT_SEGMENT table
val tc1Threshold = 0.01

val countFailsTC1 = spark.sql(s"""
    select * from 
      (select FLIGHT_DATE_ID a_FLIGHT_DATE_ID
        from $dbName.fact_flight_date 
        where codeshare_role = "OPERATING")
      left outer join
      (select distinct FLIGHT_DATE_ID b_FLIGHT_DATE_ID
        from $dbName.fact_codeshare_flight_segment 
        where partner_codeshare_role = "MARKETING")
        on a_FLIGHT_DATE_ID = b_FLIGHT_DATE_ID
        where b_FLIGHT_DATE_ID is null
""").count()

val countTotalTC1 = spark.sql(s"""
    select *
    from $dbName.fact_flight_date_histo
    where codeshare_role = "OPERATING"
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-SKD-001 : Operating codeshare with partnership in one segment",
  countFailsTC1 < tc1Threshold,
  countFailsTC1,
  countTotalTC1,
  if ( countTotalTC1 != 0) countFailsTC1.toFloat / countTotalTC1 else 0,
  if ( countFailsTC1 > tc1Threshold) "Errors in codeshare data" else ""
)

// COMMAND ----------

// DBTITLE 1,TC-SKD-002 - Marketing codeshare with partnership in one segment
//test that all flights marked as "Marketing" in the FACT_FLIGHT_DATE table have one corresponding "Operating" flight in the FACT_CODESHARE_FLIGHT_SEGMENT table
val tc2Threshold = 0.01

val countFailsTC2 = spark.sql(s"""
    select * from (
      select count(b_FLIGHT_DATE_ID) count_b from 
      (select FLIGHT_DATE_ID a_FLIGHT_DATE_ID
        from $dbName.fact_flight_date 
        where codeshare_role = "MARKETING")
      left outer join
      (select distinct FLIGHT_DATE_ID b_FLIGHT_DATE_ID
        from $dbName.fact_codeshare_flight_segment 
        where partner_codeshare_role = "OPERATING")
        on a_FLIGHT_DATE_ID = b_FLIGHT_DATE_ID
        group by a_FLIGHT_DATE_ID
      )
      where count_b != 1
""").count()

val countTotalTC2 = spark.sql(s"""
    select *
    from $dbName.fact_flight_date_histo
    where codeshare_role = "MARKETING"
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-SKD-002 : Marketing codeshare with partnership in one segment",
  countFailsTC2 < tc2Threshold,
  countFailsTC2,
  countTotalTC2,
  if ( countTotalTC2 != 0) countFailsTC2.toFloat / countTotalTC2 else 0,
  if ( countFailsTC2 > tc2Threshold) "Errors in codeshare data" else ""
)

// COMMAND ----------

// DBTITLE 1,TC-SKD-003 - Prime codeshare
//test that all flights marked as "Prime" in the FACT_FLIGHT_DATE table have no corresponding flights in the FACT_CODESHARE_FLIGHT_SEGMENT table
val tc3Threshold = 0.01

val countFailsTC3 = spark.sql(s"""
    select * from (
      (select FLIGHT_DATE_ID a_FLIGHT_DATE_ID
        from $dbName.fact_flight_date 
        where codeshare_role = "PRIME")
      left outer join
      (select distinct FLIGHT_DATE_ID b_FLIGHT_DATE_ID
        from $dbName.fact_codeshare_flight_segment)
        on a_FLIGHT_DATE_ID = b_FLIGHT_DATE_ID
      )
      where b_FLIGHT_DATE_ID is not null
""").count()

val countTotalTC3 = spark.sql(s"""
    select *
    from $dbName.fact_flight_date_histo
    where codeshare_role = "PRIME"
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-SKD-003 : Prime flight with no codeshare partnerships",
  countFailsTC3 < tc3Threshold,
  countFailsTC3,
  countTotalTC3,
  if ( countTotalTC3 != 0) countFailsTC3.toFloat / countTotalTC3 else 0,
  if ( countFailsTC3 > tc3Threshold) "Errors in codeshare data" else ""
)

// COMMAND ----------

// DBTITLE 1,TC-SKD-004 - Delayed flight occurrence
//check that all flights with an operational suffix have a canceled flight the day before
val tc4Threshold = 0.01

val countFailsTC4 = spark.sql(s"""
    select * from
        --all flights with operational suffix
        (select *
        from $dbName.fact_flight_date
        where operational_suffix is not null) a
      left join
        --all cancelled flights
        (select flight_number, DATEADD(DAY, -1, SCHEDULED_FLIGHT_DEPARTURE_DATE_LOCAL) as dateb
        from $dbName.fact_flight_date
        where is_cancelled == true) b
      on a.SCHEDULED_FLIGHT_DEPARTURE_DATE_LOCAL == dateb
      and a.flight_number == b.flight_number
      where dateb is null
 
""").count()

val countTotalTC4 = spark.sql(s"""
    select *
    from $dbName.fact_flight_date
    where operational_suffix is not null
""").count()

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-SKD-004 : Delayed flight occurrence",
  countFailsTC4 < tc4Threshold,
  countFailsTC4,
  countTotalTC4,
  if ( countTotalTC4 != 0) countFailsTC4.toFloat / countTotalTC4 else 0,
  if ( countFailsTC4 > tc4Threshold) "Some delayed flight data incorrect" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ##write to db

// COMMAND ----------

val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")
