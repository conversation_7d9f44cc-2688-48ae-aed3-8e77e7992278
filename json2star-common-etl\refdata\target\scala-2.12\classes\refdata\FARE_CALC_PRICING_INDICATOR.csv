CODE^LABEL
0^Automatically priced
1^Manually created or updated TST.
2^No fare: free or charter entered in the TST.
3^Pricing by fare basis.
4^Manual manipulation of taxes and/or fees at pricing or issuance time.
5^The system did not price according to the entered passenger type.
6^For US only: Exchange ticket request
7^Bulk fare ticket request
8^Fare price override at issuance time
9^Inclusive tour ticket request
A^SATA fare used.
B^Amount discount override applied to fare base at pricing time.
C^Amount or percent discount override applied to total fare combined with segment selection at pricing time.
D^Amount or percent discount override applied to base fare combined with segment selection at pricing time.
E^Percentage discount override at pricing time.
F^Private fares have been used at pricing time.
G^Dynamic discounted fare used at pricing time.
H^HIP may apply. The system has priced but is unable to check a higher intermediate point.
I^Override Fare Calculation by M/IT at pricing time.
J^Override fare diagnostic entry at pricing time.
K^Override Fare Calculation by M/BT at pricing time
L^Booking date override
M^Negotiated rates have been used at pricing time. Agent/consolidator is a fare updater.
N^Negotiated rates have been used at pricing time. Airline is a fare updater.
O^Past date TST override at issuance time.
P^Lowest possible fare override at pricing time.
Q^Depends on TST Indicator. Either manually stored endorsement before pricing or manually added a tour code (FT element) on a private or public fare
R^Validating carrier override at issuance time.
S^Booking class override used on negotiated fares.
T^Amount discount override applied to Total fare at pricing time.
U^Stopover/transfer override is used in the pricing entry.
V^Pricing override used with a pricing past date.
W^Booking class override used on non-negotiated fares.
Z^Net fare field manually updated (not used in US market)