swagger: 'not used'
info: 'not used'
paths: 'not used'
definitions:
  DcsPassengerPush:
    type: object
    description: >-
      Mocked sample for the data push schema for DcsPassenger
    properties:
      processedDcsPassenger:
        $ref: '#/definitions/DcsPassenger'
    example: 'not used'
  DcsPassenger:
    type: object
    description: >-
      The DcsPassenger data comprises of hierarchical structure
    properties:
      id:
        type: string
        description: Unique Customer Identifier (UCI)
        example: '2501ADE000000001'
      specialSeat:
        type: string
        description: This is Description from DIH
        example: This is Example from DIH
        piiType: This is PII Type from DIH
      differentSpecialSeat:
        type: string
        description: This is Description from DIH but different path in JSON
        example: This is Example from DIH but different path in JSON
        piiType: This is PII Type from DIH but different path in JSON