{
  // both tables are master pit => only one can be selected via selectors
  "tables": [
    // test stackable addon error in the spark closure
    {
      "name": "FACT_OK_HISTO_1",
      "table-selectors": ["fail_closure"],
      "mapping": {
        "description": {"description": "Contains information of a baggage group (a set of bags travelling together on an identical itinerary), such as number and weight of checked bags and hand bags, and the responsible passenger.", "granularity": "1 bags group"},
        "merge": {
          "key-columns": ["BAG_GROUP_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "BAG_GROUP_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        }
      },
      "stackable-addons": [
        {
          "type": "dummy",
          "action": "fail_closure"
        }
      ]
    },
    // test stackable addon error in the spark batch
    {
      "name": "FACT_OK_HISTO_2",
      "table-selectors": ["fail_batch"],
      "mapping": {
        "description": {"description": "Contains information of a baggage group (a set of bags travelling together on an identical itinerary), such as number and weight of checked bags and hand bags, and the responsible passenger.", "granularity": "1 bags group"},
        "merge": {
          "key-columns": ["BAG_GROUP_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "BAG_GROUP_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        }
      },
      "stackable-addons": [
        {
          "type": "dummy",
          "action": "fail_batch"
        }
      ]
    }
  ]
}
