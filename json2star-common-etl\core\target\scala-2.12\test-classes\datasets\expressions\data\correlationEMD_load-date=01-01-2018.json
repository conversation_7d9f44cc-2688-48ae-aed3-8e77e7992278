{"mainResource": {"id": "OMESRO-2022-02-27", "type": "com.amadeus.pulse.message.Pnr", "current": {"image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "id": "OMESRO-2022-02-27", "type": "pnr", "reference": "OMESRO", "version": "2", "purgeDate": {"date": "2022-05-23"}, "owner": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X", "agentType": "AIRLINE"}, "login": {"initials": "OX", "dutyCode": "SU", "countryCode": "FR", "cityCode": "NCE"}}, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X", "agentType": "AIRLINE"}, "login": {"numericSign": "3366", "initials": "OX", "countryCode": "FR", "cityCode": "NCE"}}}, "lastModification": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X", "agentType": "AIRLINE"}, "login": {"numericSign": "3366", "initials": "OX", "dutyCode": "SU", "countryCode": "FR", "cityCode": "NCE"}}, "comment": "OX-6X/O6X"}, "queuingOffice": {"id": "NCE6X08OM"}, "nip": 2, "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "passengerTypeCode": "ADT", "names": [{"firstName": "SIMPSONXEYN", "lastName": "HOMERXEYN"}], "gender": "UNKNOWN", "identityDocuments": [{"type": "service", "id": "OMESRO-2022-02-27-OT-7", "code": "DOCS", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "6X"}, "status": "HK", "nip": 1, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}}}, "document": {"documentType": "PASSPORT", "number": "FR043243", "expiryDate": "2022-12-30", "gender": "MALE", "name": {"fullName": "SIMPSONXEYN HOMERXEYN", "firstName": "SIMPSONXEYN", "lastName": "HOMERXEYN"}, "birthDate": "1956-12-05"}, "text": "P//FR043243//05DEC56/M/30DEC22/HOMERXEYN/SIMPSONXEYN/"}], "contacts": [{"type": "contact", "id": "OMESRO-2022-02-27-OT-6", "ref": "processedPnr.contacts"}], "passenger": {"uniqueIdentifier": "610A103F00001A8D"}}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "passengerTypeCode": "ADT", "names": [{"firstName": "AMIDALARIYK", "lastName": "PADMERIYK"}], "gender": "UNKNOWN", "identityDocuments": [{"type": "service", "id": "OMESRO-2022-02-27-OT-8", "code": "DOCS", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "6X"}, "status": "HK", "nip": 1, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}}}, "document": {"documentType": "PASSPORT", "number": "FR043243", "expiryDate": "2022-12-30", "gender": "FEMALE", "name": {"fullName": "AMIDALARIYK PADMERIYK", "firstName": "AMIDALARIYK", "lastName": "PADMERIYK"}, "birthDate": "1999-05-16"}, "text": "P//FR043243//16MAY99/F/30DEC22/PADMERIYK/AMIDALARIYK/"}], "contacts": [{"type": "contact", "id": "OMESRO-2022-02-27-OT-5", "ref": "processedPnr.contacts"}], "passenger": {"uniqueIdentifier": "610A103F00001A8E"}}], "products": [{"type": "product", "subType": "SERVICE", "id": "OMESRO-2022-02-27-OT-9", "service": {"code": "XBAG", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "6X"}, "status": "HK", "nip": 1, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}, "login": {"numericSign": "3366", "initials": "OX"}}}, "priceCategory": {"code": "C", "subCode": "0AA"}, "text": "TTL 012KG 01PCFREETEXT"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}]}, {"type": "product", "subType": "SERVICE", "id": "OMESRO-2022-02-27-OT-10", "service": {"code": "XBAG", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "6X"}, "status": "HK", "nip": 1, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}, "login": {"numericSign": "3366", "initials": "OX"}}}, "priceCategory": {"code": "C", "subCode": "0AA"}, "text": "TTL 012KG 01PCFREETEXT"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}]}, {"type": "product", "subType": "AIR", "id": "OMESRO-2022-02-27-ST-1", "airSegment": {"departure": {"iataCode": "LHR", "localDateTime": "2022-05-19T16:00:00", "dateTime": "2022-05-19T15:00:00Z"}, "arrival": {"iataCode": "NCE", "localDateTime": "2022-05-19T18:00:00", "dateTime": "2022-05-19T16:00:00Z"}, "aircraft": {"aircraftType": "320"}, "marketing": {"flightDesignator": {"carrierCode": "6X", "flightNumber": "404"}, "bookingClass": {"code": "Y", "cabin": {"code": "M"}, "subClass": {"code": 0, "pointOfSale": {"office": {"systemCode": "6X"}, "login": {"countryCode": "FR"}}}, "levelOfService": "ECONOMY"}, "isOpenNumber": false, "id": "6X-404-2022-05-19-LHR-NCE"}, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "00", "agentType": "AIRLINE"}, "login": {"countryCode": "FR", "cityCode": "NCE"}}}, "isInformational": false, "bookingStatusCode": "HK", "id": "2022-05-19-LHR-NCE"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-OT-10", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}]}, {"type": "product", "subType": "AIR", "id": "OMESRO-2022-02-27-ST-2", "airSegment": {"departure": {"iataCode": "NCE", "terminal": "2", "localDateTime": "2022-03-26T07:00:00", "dateTime": "2022-03-26T06:00:00Z"}, "arrival": {"iataCode": "LHR", "terminal": "5", "localDateTime": "2022-03-26T08:00:00", "dateTime": "2022-03-26T08:00:00Z"}, "aircraft": {"aircraftType": "343"}, "marketing": {"flightDesignator": {"carrierCode": "6X", "flightNumber": "7747"}, "bookingClass": {"code": "Y", "cabin": {"code": "Y"}, "subClass": {"code": 0, "pointOfSale": {"office": {"systemCode": "6X"}, "login": {"countryCode": "FR"}}}, "levelOfService": "ECONOMY"}, "isOpenNumber": false, "id": "6X-7747-2022-03-26-NCE-LHR"}, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "00", "agentType": "AIRLINE"}, "login": {"countryCode": "FR", "cityCode": "NCE"}}}, "isInformational": false, "bookingStatusCode": "HK", "id": "2022-03-26-NCE-LHR"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-OT-9", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}]}], "ticketingReferences": [{"type": "ticketing-reference", "id": "OMESRO-2022-02-27-OT-34", "referenceTypeCode": "FA", "documents": [{"documentType": "ETICKET", "primaryDocumentNumber": "1722400002706", "status": "ISSUED", "coupons": [{"sequenceNumber": 1, "product": {"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}}, {"sequenceNumber": 2, "product": {"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}}], "creation": {"dateTime": "2022-02-28T00:00:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X"}}}, "price": {"total": "211.16", "currency": "EUR"}}], "isInfant": false, "traveler": {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}]}, {"type": "ticketing-reference", "id": "OMESRO-2022-02-27-OT-35", "referenceTypeCode": "FA", "documents": [{"documentType": "ETICKET", "primaryDocumentNumber": "1722400002707", "status": "ISSUED", "coupons": [{"sequenceNumber": 1, "product": {"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}}, {"sequenceNumber": 2, "product": {"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}}], "creation": {"dateTime": "2022-02-28T00:00:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X"}}}, "price": {"total": "211.16", "currency": "EUR"}}], "isInfant": false, "traveler": {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}]}, {"type": "ticketing-reference", "id": "OMESRO-2022-02-27-OT-36", "referenceTypeCode": "FA", "documents": [{"documentType": "EMD_ASSOCIATED", "primaryDocumentNumber": "1728200002025", "status": "ISSUED", "coupons": [{"sequenceNumber": 1, "product": {"type": "product", "id": "OMESRO-2022-02-27-OT-9", "ref": "processedPnr.products"}}], "creation": {"dateTime": "2022-02-28T00:00:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X"}}}, "price": {"total": "26.00", "currency": "EUR"}}], "isInfant": false, "traveler": {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-OT-9", "ref": "processedPnr.products"}]}], "keywords": [{"type": "service", "id": "OMESRO-2022-02-27-OT-11", "code": "REFE", "subType": "SPECIAL_KEYWORD", "serviceProvider": {"code": "6X"}, "text": "6X1724GZ7HQZ3/ORMS", "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}], "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}]}], "contacts": [{"type": "contact", "id": "OMESRO-2022-02-27-OT-5", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "*********"}, "purpose": ["STANDARD"], "travelerRefs": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}]}, {"type": "contact", "id": "OMESRO-2022-02-27-OT-6", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "*********"}, "purpose": ["STANDARD"], "travelerRefs": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}]}], "automatedProcesses": [{"type": "automated-process", "id": "OMESRO-2022-02-27-OT-33", "code": "OK", "dateTime": "2022-02-27T00:00:00", "office": {"id": "NCE6X08OM"}, "applicableCarrierCode": "6X", "isApplicableToInfants": false, "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}], "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}]}], "paymentMethods": [{"type": "payment-method", "id": "OMESRO-2022-02-27-OT-23", "code": "FP", "formsOfPayment": [{"code": "STD", "fopIndicator": "NEW", "freeText": "CA"}], "isInfant": false}, {"type": "payment-method", "id": "OMESRO-2022-02-27-OT-25", "code": "FP", "formsOfPayment": [{"code": "STD", "fopIndicator": "NEW", "freeText": "CA"}], "isInfant": false}], "fareElements": [{"type": "fare-element", "id": "OMESRO-2022-02-27-OT-24", "code": "FV", "text": "PAX 6X"}, {"type": "fare-element", "id": "OMESRO-2022-02-27-OT-27", "code": "FV", "text": "PAX 6X"}], "quotations": [{"type": "quotation-record", "id": "OMESRO-2022-02-27-QT-25", "subType": "TSM_P", "isManual": false, "documentType": "EMD_ASSOCIATED", "issuanceType": "FIRST_ISSUE", "flags": ["ISSUANCE_REQUIRED"], "creation": {"dateTime": "2022-02-27T00:00:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}}}, "pricingConditions": {"fareCalculation": {"pricingIndicator": "0", "text": "NCE 6X LHR10.83 6X NCE13.00EUR23.83END", "isManual": false}, "isInternationalSale": true}, "price": {"detailedPrices": [{"amount": "26.00", "currency": "EUR", "elementaryPriceType": "TOTAL"}, {"amount": "23.83", "currency": "EUR", "elementaryPriceType": "BASE_FARE"}, {"amount": "26.00", "currency": "EUR", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "26.00", "currency": "EUR", "elementaryPriceType": "FARE_EXCHANGE_VALUE"}], "taxes": [{"code": "IZ", "nature": "EB", "category": "NEW", "amount": "2.17", "currency": "EUR"}]}, "coupons": [{"reasonForIssuance": {"code": "C", "subCode": "0AA", "description": "BAGGAGE - UP TO 23 KG"}, "product": {"type": "product", "id": "OMESRO-2022-02-27-OT-9", "ref": "processedPnr.products"}, "id": "OMESRO-2022-02-27-QT-25-9", "isNonRefundable": true, "isNonExchangeable": true, "associatedDocuments": [{"documentType": "ETICKET", "couponNumbers": [1], "documentNumber": "1722400002706"}]}], "paymentMethods": [{"type": "payment-method", "id": "OMESRO-2022-02-27-OT-25", "ref": "processedPnr.paymentMethods"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}]}]}, "correlations": [{"name": "PNR-SKD", "relation": {"rel": "related"}}, {"name": "PNR-TKT", "relation": {"rel": "related"}}]}, "previous": {"image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "id": "OMESRO-2022-02-27", "type": "pnr", "reference": "OMESRO", "version": "1", "purgeDate": {"date": "2022-05-23"}, "owner": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X", "agentType": "AIRLINE"}, "login": {"initials": "OX", "dutyCode": "SU", "countryCode": "FR", "cityCode": "NCE"}}, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X", "agentType": "AIRLINE"}, "login": {"numericSign": "3366", "initials": "OX", "countryCode": "FR", "cityCode": "NCE"}}}, "lastModification": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "6X", "agentType": "AIRLINE"}, "login": {"numericSign": "3366", "initials": "OX", "dutyCode": "SU", "countryCode": "FR", "cityCode": "NCE"}}, "comment": "ISS-6X/O6X"}, "queuingOffice": {"id": "NCE6X08OM"}, "nip": 2, "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "passengerTypeCode": "ADT", "names": [{"firstName": "SIMPSONXEYN", "lastName": "HOMERXEYN"}], "gender": "UNKNOWN", "identityDocuments": [{"type": "service", "id": "OMESRO-2022-02-27-OT-7", "code": "DOCS", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "6X"}, "status": "HK", "nip": 1, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}}}, "document": {"documentType": "PASSPORT", "number": "FR043243", "expiryDate": "2022-12-30", "gender": "MALE", "name": {"fullName": "SIMPSONXEYN HOMERXEYN", "firstName": "SIMPSONXEYN", "lastName": "HOMERXEYN"}, "birthDate": "1956-12-05"}, "text": "P//FR043243//05DEC56/M/30DEC22/HOMERXEYN/SIMPSONXEYN/"}], "contacts": [{"type": "contact", "id": "OMESRO-2022-02-27-OT-6", "ref": "processedPnr.contacts"}], "passenger": {"uniqueIdentifier": "610A103F00001A8D"}}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "passengerTypeCode": "ADT", "names": [{"firstName": "AMIDALARIYK", "lastName": "PADMERIYK"}], "gender": "UNKNOWN", "identityDocuments": [{"type": "service", "id": "OMESRO-2022-02-27-OT-8", "code": "DOCS", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "6X"}, "status": "HK", "nip": 1, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}}}, "document": {"documentType": "PASSPORT", "number": "FR043243", "expiryDate": "2022-12-30", "gender": "FEMALE", "name": {"fullName": "AMIDALARIYK PADMERIYK", "firstName": "AMIDALARIYK", "lastName": "PADMERIYK"}, "birthDate": "1999-05-16"}, "text": "P//FR043243//16MAY99/F/30DEC22/PADMERIYK/AMIDALARIYK/"}], "contacts": [{"type": "contact", "id": "OMESRO-2022-02-27-OT-5", "ref": "processedPnr.contacts"}], "passenger": {"uniqueIdentifier": "610A103F00001A8E"}}], "products": [{"type": "product", "subType": "SERVICE", "id": "OMESRO-2022-02-27-OT-9", "service": {"code": "XBAG", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "6X"}, "status": "HK", "nip": 1, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}, "login": {"numericSign": "3366", "initials": "OX"}}}, "priceCategory": {"code": "C", "subCode": "0AA"}, "text": "TTL 012KG 01PCFREETEXT"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}]}, {"type": "product", "subType": "SERVICE", "id": "OMESRO-2022-02-27-OT-10", "service": {"code": "XBAG", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "6X"}, "status": "HK", "nip": 1, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}, "login": {"numericSign": "3366", "initials": "OX"}}}, "priceCategory": {"code": "C", "subCode": "0AA"}, "text": "TTL 012KG 01PCFREETEXT"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}]}, {"type": "product", "subType": "AIR", "id": "OMESRO-2022-02-27-ST-1", "airSegment": {"departure": {"iataCode": "LHR", "localDateTime": "2022-05-19T16:00:00", "dateTime": "2022-05-19T15:00:00Z"}, "arrival": {"iataCode": "NCE", "localDateTime": "2022-05-19T18:00:00", "dateTime": "2022-05-19T16:00:00Z"}, "aircraft": {"aircraftType": "320"}, "marketing": {"flightDesignator": {"carrierCode": "6X", "flightNumber": "404"}, "bookingClass": {"code": "Y", "cabin": {"code": "M"}, "subClass": {"code": 0, "pointOfSale": {"office": {"systemCode": "6X"}, "login": {"countryCode": "FR"}}}, "levelOfService": "ECONOMY"}, "isOpenNumber": false, "id": "6X-404-2022-05-19-LHR-NCE"}, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "00", "agentType": "AIRLINE"}, "login": {"countryCode": "FR", "cityCode": "NCE"}}}, "isInformational": false, "bookingStatusCode": "HK", "id": "2022-05-19-LHR-NCE"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-OT-10", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}]}, {"type": "product", "subType": "AIR", "id": "OMESRO-2022-02-27-ST-2", "airSegment": {"departure": {"iataCode": "NCE", "terminal": "2", "localDateTime": "2022-03-26T07:00:00", "dateTime": "2022-03-26T06:00:00Z"}, "arrival": {"iataCode": "LHR", "terminal": "5", "localDateTime": "2022-03-26T08:00:00", "dateTime": "2022-03-26T08:00:00Z"}, "aircraft": {"aircraftType": "343"}, "marketing": {"flightDesignator": {"carrierCode": "6X", "flightNumber": "7747"}, "bookingClass": {"code": "Y", "cabin": {"code": "Y"}, "subClass": {"code": 0, "pointOfSale": {"office": {"systemCode": "6X"}, "login": {"countryCode": "FR"}}}, "levelOfService": "ECONOMY"}, "isOpenNumber": false, "id": "6X-7747-2022-03-26-NCE-LHR"}, "creation": {"dateTime": "2022-02-27T23:54:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM", "iataNumber": "00631002", "systemCode": "00", "agentType": "AIRLINE"}, "login": {"countryCode": "FR", "cityCode": "NCE"}}}, "isInformational": false, "bookingStatusCode": "HK", "id": "2022-03-26-NCE-LHR"}, "products": [{"type": "product", "id": "OMESRO-2022-02-27-OT-9", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}]}], "keywords": [{"type": "service", "id": "OMESRO-2022-02-27-OT-11", "code": "REFE", "subType": "SPECIAL_KEYWORD", "serviceProvider": {"code": "6X"}, "text": "6X1724GZ7HQZ3/ORMS", "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}], "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}]}], "contacts": [{"type": "contact", "id": "OMESRO-2022-02-27-OT-5", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "*********"}, "purpose": ["STANDARD"], "travelerRefs": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}]}, {"type": "contact", "id": "OMESRO-2022-02-27-OT-6", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "*********"}, "purpose": ["STANDARD"], "travelerRefs": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}]}], "automatedProcesses": [{"type": "automated-process", "id": "OMESRO-2022-02-27-OT-12", "code": "OK", "dateTime": "2022-02-28T00:00:00", "office": {"id": "NCE6X08OM"}, "isApplicableToInfants": false, "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-2", "ref": "processedPnr.travelers"}], "products": [{"type": "product", "id": "OMESRO-2022-02-27-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "OMESRO-2022-02-27-ST-2", "ref": "processedPnr.products"}]}], "paymentMethods": [{"type": "payment-method", "id": "OMESRO-2022-02-27-OT-23", "code": "FP", "formsOfPayment": [{"code": "STD", "fopIndicator": "NEW", "freeText": "CA"}], "isInfant": false}, {"type": "payment-method", "id": "OMESRO-2022-02-27-OT-25", "code": "FP", "formsOfPayment": [{"code": "STD", "fopIndicator": "NEW", "freeText": "CA"}], "isInfant": false}], "fareElements": [{"type": "fare-element", "id": "OMESRO-2022-02-27-OT-24", "code": "FV", "text": "PAX 6X"}, {"type": "fare-element", "id": "OMESRO-2022-02-27-OT-27", "code": "FV", "text": "PAX 6X"}], "quotations": [{"type": "quotation-record", "id": "OMESRO-2022-02-27-QT-25", "subType": "TSM_P", "isManual": false, "documentType": "EMD_ASSOCIATED", "issuanceType": "FIRST_ISSUE", "flags": ["ISSUANCE_REQUIRED"], "creation": {"dateTime": "2022-02-27T00:00:00Z", "pointOfSale": {"office": {"id": "NCE6X08OM"}}}, "pricingConditions": {"fareCalculation": {"pricingIndicator": "0", "text": "NCE 6X LHR10.83 6X NCE13.00EUR23.83END", "isManual": false}, "isInternationalSale": true}, "price": {"detailedPrices": [{"amount": "26.00", "currency": "EUR", "elementaryPriceType": "TOTAL"}, {"amount": "23.83", "currency": "EUR", "elementaryPriceType": "BASE_FARE"}, {"amount": "26.00", "currency": "EUR", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "26.00", "currency": "EUR", "elementaryPriceType": "FARE_EXCHANGE_VALUE"}], "taxes": [{"code": "IZ", "nature": "EB", "category": "NEW", "amount": "2.17", "currency": "EUR"}]}, "coupons": [{"reasonForIssuance": {"code": "C", "subCode": "0AA", "description": "BAGGAGE - UP TO 23 KG"}, "product": {"type": "product", "id": "OMESRO-2022-02-27-OT-9", "ref": "processedPnr.products"}, "id": "OMESRO-2022-02-27-QT-25-9", "isNonRefundable": true, "isNonExchangeable": true}], "paymentMethods": [{"type": "payment-method", "id": "OMESRO-2022-02-27-OT-25", "ref": "processedPnr.paymentMethods"}], "travelers": [{"type": "stakeholder", "id": "OMESRO-2022-02-27-PT-1", "ref": "processedPnr.travelers"}]}]}}}, "correlatedResourcesCurrent": {"PNR-TKT": {"id": "OMESRO-2022-02-27", "version": "TKT-2", "isFullUpdate": true, "fromFullVersion": "2", "correlations": [{"fromVersion": "2", "toId": "1722400002707-2022-02-28", "toVersion": "0", "corrTktPnr": {"items": [{"ticketCouponId": "1722400002707-2022-02-28-1", "pnrTravelerId": "OMESRO-2022-02-27-PT-2", "pnrAirSegmentId": "OMESRO-2022-02-27-ST-2", "pnrTicketingReferenceId": "OMESRO-2022-02-27-OT-35"}, {"ticketCouponId": "1722400002707-2022-02-28-2", "pnrTravelerId": "OMESRO-2022-02-27-PT-2", "pnrAirSegmentId": "OMESRO-2022-02-27-ST-1", "pnrTicketingReferenceId": "OMESRO-2022-02-27-OT-35"}]}}, {"fromVersion": "2", "toId": "1722400002706-2022-02-28", "toVersion": "2", "corrTktPnr": {"items": [{"ticketCouponId": "1722400002706-2022-02-28-1", "pnrTravelerId": "OMESRO-2022-02-27-PT-1", "pnrAirSegmentId": "OMESRO-2022-02-27-ST-2", "pnrTicketingReferenceId": "OMESRO-2022-02-27-OT-34"}, {"ticketCouponId": "1722400002706-2022-02-28-2", "pnrTravelerId": "OMESRO-2022-02-27-PT-1", "pnrAirSegmentId": "OMESRO-2022-02-27-ST-1", "pnrTicketingReferenceId": "OMESRO-2022-02-27-OT-34"}]}}], "fromDomain": "PNR", "toDomain": "TKT"}, "PNR-EMD": {"id": "OMESRO-2022-02-27", "version": "EMD-2", "isFullUpdate": true, "fromFullVersion": "2", "correlations": [{"fromVersion": "2", "toId": "1728200002025-2022-02-28", "toVersion": "0", "corrEmdPnr": {"items": [{"ticketCouponId": "1728200002025-2022-02-28-1", "pnrTravelerId": "OMESRO-2022-02-27-PT-1", "pnrAirSegmentId": "OMESRO-2022-02-27-ST-2", "pnrServiceId": "OMESRO-2022-02-27-OT-9", "pnrTicketingReferenceId": "OMESRO-2022-02-27-OT-36"}]}}], "fromDomain": "PNR", "toDomain": "EMD"}, "PNR-SKD": {"id": "OMESRO-2022-02-27", "version": "SKD-2", "isFullUpdate": true, "fromFullVersion": "2", "correlations": [{"fromVersion": "0", "toId": "6X-7747-2022-03-26", "toVersion": "1617416242", "corrSkdPnr": {"items": [{"pnrAirsegmentId": "OMESRO-2022-02-27-ST-2", "flightSegmentId": "2022-03-26-NCE-LHR"}]}}], "fromDomain": "PNR", "toDomain": "SKD"}}}