This integration test checks that the DIM table is correctly updated by the following data producer apps:

step1) PreFillerApp
step2) Json2StarApp

where
step1 is a PreFillerApp that loads the DIM table from the csv file - it is executed on demand
step2 is a Json2StarApp that updates the DIM table from the json file - it is executed regularly
step3 is a PreFillerApp that loads the DIM table from the csv file - it is executed on demand

Data

- the code-label data for a given REF-DATA (offline referential source) is

| Code | Label          |
|------|----------------|
| CNN  | Child          |
| FFY  | Frequent Flyer |

- the json data for a given FEED (online data source) contains the following codes

| Code |
|------|
| ADT  |
| CNN  |

Note: the load date is set to 2099 is to verify that the JSON data is loaded after the PrefillerApp
The DIM table should have the following output

| Code | Label          | Source         |
|------|----------------|----------------|
| ADT  | ADT            | FROM_FEED_DATA |
| CNN  | Child          | FROM_REF_DATA  |
| FFY  | Frequent Flyer | FROM_REF_DATA  |

