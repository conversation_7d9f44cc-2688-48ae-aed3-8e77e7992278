{"mainResource": {"id": "6107617C000052A1", "type": "com.amadeus.pulse.message.dcscml.dcspax.DCSPassenger", "current": {"image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.dcscml.dcspax.DCSPassenger", "id": "6107617C000052A1", "version": "000000000001", "passenger": {"name": {"firstName": "KUMARAN", "lastName": "MUGESWARY"}, "passengerType": "ADULT"}, "recordLocator": "5GJZBK", "cprFeedType": "MARKETING_SBR", "isMasterRecord": true, "segmentDeliveries": [{"id": "6007B17C0002A6C0", "segment": {"departure": {"iataCode": "KUL", "at": "2023-02-04T00:25:00Z"}, "arrival": {"iataCode": "BKI", "at": "2023-02-04T03:10:00Z"}, "number": "2612", "carrierCode": "MH", "class": "S", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "2612"}, "status": "HK", "id": "MH-2612-2023-02-04-KUL-BKI"}, "dcsProductType": "PASSIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6C0-KUL", "departure": {"iataCode": "KUL", "at": "2023-02-04"}, "arrival": {"iataCode": "BKI"}, "operatingFlight": {"carrierCode": "MH", "number": "2612"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "previousTicket": {"primaryDocumentNumber": "2322466644049", "conjunctiveDocumentNumbers": ["2322466644049"], "associationStatus": "UNASSOCIATED"}, "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}], "services": [{"id": "1000000009D65EFA", "code": "CEID", "subType": "SPECIAL_KEYWORD", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "UHJAHMHUUPB6HCI7SH2JYJ6C4"}], "lastModification": {"dateTime": "2023-01-31T21:00:19.899Z", "triggerEventName": "1AINTERNAL", "user": {"officeId": "LON1A0955"}}}, "correlations": [{"name": "DCSPAX-DCSBAG", "relation": {"rel": "related"}}, {"name": "DCSPAX-PNR", "relation": {"rel": "related"}}, {"name": "DCSPAX-EMD", "relation": {"rel": "related"}}, {"name": "DCSPAX-TKT", "relation": {"rel": "related"}}]}}, "correlatedResourcesCurrent": {"DCSPAX-TKT": {"id": "6107617C000052A1", "version": "TKT-1", "isFullUpdate": true, "fromFullVersion": "000000000001", "fromDomain": "DCSPAX", "toDomain": "TKT"}, "DCSPAX-EMD": {"id": "6107617C000052A1", "version": "EMD-1", "isFullUpdate": true, "fromFullVersion": "000000000001", "fromDomain": "DCSPAX", "toDomain": "EMD"}, "DCSPAX-DCSBAG": {"id": "6107617C000052A1", "version": "DCSBAG-1", "isFullUpdate": true, "fromFullVersion": "000000000001", "fromDomain": "DCSPAX", "toDomain": "DCSBAG"}, "DCSPAX-PNR": {"id": "6107617C000052A1", "version": "PNR-1", "isFullUpdate": true, "fromFullVersion": "000000000001", "correlations": [{"fromVersion": "000000000001", "toId": "5GJZBK-2023-01-11", "toVersion": "2", "corrDcspaxPnr": {"items": [{"dcsPassengerSegmentDeliveryId": "6007B17C0002A6C0", "pnrTravelerId": "5GJZBK-2023-01-11-PT-2", "pnrAirSegmentId": "5GJZBK-2023-01-11-ST-2"}]}}], "fromDomain": "DCSPAX", "toDomain": "PNR"}}}