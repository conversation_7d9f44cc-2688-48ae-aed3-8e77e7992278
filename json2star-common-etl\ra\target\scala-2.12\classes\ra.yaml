swagger: '2.0'
################################################################################
#                              API Information                                 #
################################################################################
info:
  version: 1.1.0
  title: Amadeus Dynamic Intelligence Hub Data Model - RevenueAccountingAirTravelDocument
  description: >-
    This document describes DIH output for Revenue Accounting Data Push Feed

################################################################################
#                                    Paths                                     #
################################################################################
paths:
  ########## DIH revenue accounting feeds definition ##########
  /revenueAccounting/revenue-accounting-feed:
    post:
      tags:
        - FEED
      summary: Revenue Accounting Data Push
      description: Revenue Accounting feed pushed by Amadeus DIH. Please note this is NOT a POST API - this section is only used to specify the data model pushed.
      responses:
        default:
          description: Revenue Accounting Data Push Feed
          schema:
            $ref: '#/definitions/revenueAccountingDataPush'
definitions:
  ########## Payloads ##########
  revenueAccountingDataPush:
    type: object
    properties:
      meta:
        $ref: '#/definitions/Meta'
      processedRevenueAccounting:
        $ref: '#/definitions/RevenueAccountingAirTravelDocument'
      previousRecord:
        description: Representation of the previous version of the record in the form of JSON Patch as defined by RFC 6902 (see https://tools.ietf.org/html/rfc6902). The actual JSON of this previous record needs to be built using the latest version and applying the JSON patch.
        type: array
        items:
          $ref: '#/definitions/PatchOperation'
      events:
        $ref: '#/definitions/Events'

    ########## Data models ##########
  Dictionaries:
    type: object
    properties:
      tickets:
        type: object
        description: >-
          Set of key/value pairs with ticketId as key i.e. primary ticket number
          + issuance date - key example '*************-2018-05-15'
        additionalProperties:
          $ref: '#/definitions/Relationship'
      emds:
        type: object
        description: >-
          Set of key/value pairs with emdId as key i.e. primary EMD number +
          issuance date - key example '1721234567891-2018-05-15'
        additionalProperties:
          $ref: '#/definitions/Relationship'
      pnrs:
        type: object
        description: >-
          Set of key/value pairs with pnrId as key i.e. record locator + creation date - key example 'ABCDEF-2018-05-15'
        additionalProperties:
          $ref: '#/definitions/Relationship'
    example:
      tickets:
        '1722500003153-2018-10-18':
          type: 'air-travel-document'
          id: '*************-2019-10-05'
          version: '1'
          href: 'http://airlines.api.amadeus.com/v1/ticketing/processed-tickets/*************-2019-10-05'
      emds:
        '1722500009467-2019-10-11':
          type: 'air-travel-document'
          id: '1725555555555-2019-10-05'
          version: '1'
          href: 'http://airlines.api.amadeus.com/v1/ticketing/processed-emds/1725555555555-2019-10-05'
      pnrs:
        'ABC123-2018-10-05':
          type: 'pnr'
          id: 'ABC123-2019-10-05'
          version: '1'
          href: 'http://airlines.api.amadeus.com/v1/reservation/processed-pnrs/*************-2019-10-05'

  ########## Entities definition ##########
  Events:
    type: object
    description: Structure of Dynamic Intelligence Hub functional events
    properties:
      recordDomain:
        type: string
        description: Functional domain of the record
        example: REVENUE_ACCOUNTING
      recordId:
        type: string
        description: Ticket ID or EMD ID
        example: *************-2018-10-18
      originFeedTimeStamp:
        type: string
        description: Incoming revenue accounting feed time stamp
      events:
        type: array
        description: List of events that have been detected on the Revenue Accounting document
        items:
          type: object
          properties:
            origin:
              type: string
              description: Type of event e.g. 'COMPARISON' / 'TIME_INITIATED_EVENT'
              example: COMPARISON
            eventType:
              type: string
              description: >-
                In case of comparison events, type of operation notified by the event
              example: CREATED
              enum:
                - CREATED
                - UPDATED
                - DELETED
            currentPath:
              type: string
              description: >-
                String containing the JSON Pointer (see IETF RFC6901) that points to the data referenced by the
                Event in the latest version of the entity. It is only applicable for CREATED and UPDATED events.
              example: ''
            previousPath:
              type: string
              description: >-
                String containing the JSON Pointer (see IETF RFC6901) that points to the data referenced by the
                Event in the previous version of the entity. It is only applicable for DELETED and UPDATED events.
              example: ''
    example:
      recordDomain: 'REVENUE_ACCOUNTING_PNR'
      recordId: '*************-2018-10-18'
      events:
        - origin: 'COMPARISON'
          eventType: 'UPDATED'
  Meta:
    type: object
    description: Technical information related to the feed
    properties:
      triggerEventLog:
        description: information related to the initial event that trigger the process
        $ref: '#/definitions/EventLog'
      version:
        description: Version of the JSON feed produced
        type: string
        example: '1.0.0'
    example:
      triggerEventLog:
        id: '46541dsfsSDRWFS54'
        triggerEventName: 'DATA_INIT'
      version: '1.0.0'
  RevenueAccountingAirTravelDocument:
    title: RevenueAccountingAirTravelDocument
    type: object
    properties:
      id:
        type: string
        description: id of ticket or EMD for which the revenue accounting feed is received.
        example: '*************-2018-10-18'
      version:
        type: string
        description: An identifier to track the sequential flow of messages.
        example: '1.1'
      creation:
        description: Indicates the creation DateTime and the point of sale of the entity.
        $ref: '#/definitions/EventLog'
      latestEvent:
        description: >-
          Indicates the last updated DateTime.
          Indicate the last point of sale of the entity.
        $ref: '#/definitions/EventLog'
      documentType:
        type: string
        description: Type of document
        enum:
          - TICKET
          - EMD_ASSOCIATED
          - EMD_STANDALONE
          - ETICKET
          - PAPER_TICKET
          - PAPER_MD
          - VIRTUAL_MD
          - TICKETLESS_DOCUMENT
          - MD
          - VIRTUAL_TICKET
          - EMD
        example: 'TICKET'
      primaryDocumentNumber:
        type: string
        description: Ticket primary number or EMD primary number
        example: '*************'
      conjunctiveDocumentNumbers:
        type: array
        items:
          type: string
          description: The list of conjunctive document numbers.
        example: '*************'
      originalDocuments:
        description: The ticket(s) against which this ticket was exchanged.
        type: array
        items:
          properties:
            documentNumber:
              type: string
              description: >-
                Identifier of the travel document prefixed by its owner code [NALC - 3 digits].
                Can either be a primary or a conjunctive document number.
              example: '*************'
            creation:
              description: Indicates the creation DateTime of the entity.
              $ref: '#/definitions/EventLog'
      numberOfBooklets:
        type: number
        description: Number of conjunctive tickets/EMDs in the document.
        example: '1'
      issuanceType:
        type: string
        description: Indicates the type of transaction.
        enum:
          - FIRST_ISSUE
          - REISSUE
          - FIRST_ISSUE_IT_FARE
          - FIRST_ISSUE_BT_FARE
          - REISSUE_IT_FARE
          - REISSUE_BT_FARE
        example: 'FIRST_ISSUE'
      endorsementFreeFlow:
        type: string
        description: Used to identify any restrictions, airline comments, or rules that may apply to the ticket/EMD
        example: 'FARE RESTRICTION MAY APPLY -BG 6X'
      validatingCarrierCode:
        type: string
        description: Ticket validating carrier code
        example: '6X'
      validatingCarrierPnr:
        description: >-
          Validating carrier record locator and owner of the record locator
        $ref: '#/definitions/AssociatedPnr'
      associatedPnrs:
        type: array
        items:
          $ref: '#/definitions/AssociatedPnr'
      originCityIataCode:
        type: string
        description: origin city code
        example: 'PAR'
      destinationCityIataCode:
        type: string
        description: destination city code
        example: 'LON'
      traveler:
        description: Traveler information.
        $ref: '#/definitions/Stakeholder'
      pricingConditions:
        $ref: '#/definitions/PricingConditions'
      formsOfPayment:
        type: array
        items:
          $ref: '#/definitions/DisplayedFormOfPayment'
      price:
        $ref: '#/definitions/Price'
      revenueAccountingCoupons:
        type: array
        items:
          $ref: '#/definitions/RevenueAccountingCoupon'
      revenueAccountingStatus:
        type: string
        enum:
          - CONSUMED
          - OPEN
          - PARTIALLY_CONSUMED
          - VOID
        description: >-
          Indicates the operational status of the document based on the operational status of all its coupons.
          OPEN- at least one coupon is still open for use
          CONSUMED- all coupons are in a final status (except when all coupons are voided)
          PARTIALLY CONSUMED- some coupons are in a final status.
          VOID: all coupons have been void
      agencyMemo:
        $ref: '#/definitions/AgencyMemo'
      afterSaleFares:
        type: array
        items:
          $ref: '#/definitions/AfterSaleFare'
      isNonRevenuePassenger:
        type: boolean
        description: >-
          In Revenue Accounting, a passenger is treated as revenue passenger or Non-Revenue passengers based on various factors like if the passenger is staff, discount above a specific value detected during proration process, ABR rules etc. 
          These factors are airline specific.This field is a boolean field which is false when a passenger is a "revenue" passenger. The default value is false. Functionally, by default, a passenger is considered as RevenuePassenger in  Amadeus Revenue Accounting, also when this field is empty.'
      revenueStatus:
        type: string
        enum:
          - PROVISIONAL
          - FINAL
        description: >-
          This field indicates if the revenue information received for the document is confirmed or further changes can be expected. The revenue can be accounted only once confirmed.
          In case of direct sale of ticket through ATO/CTO, the revenue is confirmed through Sales Report. In case of indirect sales, specific Event Types in HOT/CAT files indicate confirmation of revenue.
      transactionCode:
        type: string
        enum:
          - REFUND
          - ELECTRONIC_TICKET_SALE
          - EMD_ASSOCIATED
          - EMD_STANDALONE
        description: >-
          A code to indicate the type of transaction being reported to BSP. 
          REFUND: for refund,
          ELECTRONIC_TICKET_SALE: for electronic ticket sale,
          EMD_ASSOCIATED: for EMD-Associated,
          EMD_STANDALONE: for EMD-Standalone.
      reasonForIssuance:
        $ref: '#/definitions/ReasonForIssuance'
      dictionaries:
        $ref: '#/definitions/Dictionaries'
  Relationship:
    type: object
    properties:
      type:
        type: string
      id:
        type: string
      ref:
        type: string
      version:
        type: string
        description: Only populated for correlated resources defined in Dictionaries
      href:
        description: Only populated for correlated resources defined in Dictionaries
        type: string
  DisplayedFormOfPayment:
    type: object
    description: >-
      Contains the Forms Of Payment that have been used to pay for the products 
      issued in the current travel document [flights for tickets, ancillary services/upgrade services/fees for EMDs].
    properties:
      code:
        type: string
        description: >-
          Form Of Payment category as defined in IATA PADIS code list for data
          element 9888. Examples - CA for Cash, CC for Credit Card, CK for
          Check, MS for Miscellaneous, EF for Electronic Funds transfer.
        example: 'CC'
      displayedAmount:
        $ref: '#/definitions/ElementaryPrice'
      freeText:
        type: string
        description: Free text as entered in the order via the FP element
        example: 'Form of payment free text'
      paymentCard:
        $ref: '#/definitions/PaymentCard'
      authorization:
        $ref: '#/definitions/Authorization'
  Tax:
    type: object
    properties:
      amount:
        type: string
        description: Defines amount with decimal separator.
      currency :
        type : string
        description: >-
          Defines a monetary unit. It is a three alpha code. Example: EUR for Euros, USD for US dollar, etc.
      description :
        type : string
        description: Defines the type of amount
      code:
        type: string
        description: International Standards Organization (ISO) Tax code.
      nature:
        type: string
        description: Filling on SSP side.
      taxType:
        type: string
        description: >-
          This defines if the tax is 'collected' or 'calculated'.
      taxRecipient:
        type: object
        description: This object defines the category of recipient receiving tax and its country
        $ref: '#/definitions/TaxRecipient'
      originalAmount:
        type: object
        description: containing currency Code and its conversion rate of the original amount
        $ref: '#/definitions/ConvertedAmount'
      convertedAmount:
        type: object
        description: containing currency Code and its conversion rate post conversion of the amount
        $ref: '#/definitions/ConvertedAmount'
  TaxRecipient:
    type: object
    description: This object defines the category of recipient receiving tax and its country
    properties:
      taxRecipientCategoryCode:
        type: string
        description: >-
          This attribute defines the final recipient of the tax (i.e. country, carrier).Possible values are
          1. CO - Country Tax
          2. CA - Carrier Tax
      taxRecipientId:
        type: string
        description: >-
          This attribute defines the entity which will receive the tax (i.e. the code of the country that will receive the tax)
  ConvertedAmount:
    type: object
    description: >-
      containing currency Code, amount, decimal places and its conversion rate post conversion of the amount
    properties:
      amount:
        type: string
        description: amount post conversion
      currencyCode:
        type: string
        description: Currency code like GBP, EUR, USD etc
      referenceAmount:
        type: string
      referenceCurrencyCode:
        type: string
        description: Currency code like GBP, EUR, USD etc
      currencyConversionRate:
        type: object
        description: currency conversion details
        $ref: '#/definitions/CurrencyConversionRate'
  CurrencyConversionRate:
    type: object
    description: currency conversion details
    properties:
      rate:
        type: string
        description: currency conversion rate or rate of exchange
      targetCurrency:
        type: string
        description: the currency you are converting to
  ReasonForIssuance:
    type: object
    description: >-
      AN EMD IS CATEGORISED BASED ON ITS REASON FOR ISSUANCE CODE (RFIC), WHICH
      DEFINES THE GROUP OF SERVICES IT BELONGS TO. THERE CAN ONLY BE ONE RFIC
      CODE PER EMD. SOME CODES ARE DEFINED BY IATA, HOWEVER, OTHERS CAN BE
      DEFINED BY INDIVIDUAL AIRLINES.  AN EMD, AND EACH RFIC, CAN HAVE MULTIPLE
      REASON FOR ISSUANCE SUB-CODES (RFISC). THERE IS ONE RFISC IN EACH EMD
      COUPON AND THEY ARE AIRLINE-SPECIFIC.
    properties:
      code:
        type: string
        description: >-
          The reason for issuance code (RFIC) chargeability indicator defined for the sellable object
        example: 'E'
      subCode:
        type: string
        description: >-
          The reason for issurance sub code (RFISC) chargeability indicator defined for the sellable object
        example: '0BX'
  FrequentFlyer:
    type: object
    properties:
      frequentFlyerNumber:
        type: string
        description: A code to identify a frequent traveller - Loyalty card number
        example: '6X090807061234'
      airlineLevel:
        type: object
        properties:
          companyCode:
            type: string
            description: Used to identify the airline designator of the frequent flyer program.
            example: '6X'
  ElementaryPrice:
    type: object
    properties:
      amount:
        type: string
        description: >-
          Amount of the fare. could be alpha numeric. Ex- 500.20 or 514.13A, 'A' signifies additional collection.
        example: '4871.81'
      currency:
        type: string
        description: Currency type of the fare.
        example: 'CAD'
      elementaryPriceType:
        type: string
        description: 'Defines the price type, e.g. for base fare, total...'
        enum:
          - BASE_FARE
          - EQUIV_FARE
          - PAYMENT
          - LISTING
          - BILLED
          - ACCOUNTED
          - REFERENCE_FARE
          - FILLING
          - REPORTING
          - REFUNDED
          - PRORATION
          - ORIGIN
          - UNDEFINED
          - RECONCILIATION
        example: 'BASE_FARE'
  PaymentCard:
    type: object
    properties:
      expiryDate:
        type: string
        description: Expiration date of the card - format is month & year - MMYY
        example: '0318'
      maskedCardNumber:
        type: string
        description: Masked credit card number
        example: '****************'
  Authorization:
    type: object
    properties:
      approvalCode:
        type: string
        description: >-
          A series of characters assigned by the applicable credit card
          company's authorization system to confirm the approval of a credit
          sale transaction, with a maximum of 8 digits
        example: '38562'
  FlightSegment:
    description: >-
      Defining a flight segment, including both Operating and Marketing details when applicable.
    type: object
    properties:
      departure:
        $ref: '#/definitions/FlightEndPoint'
      arrival:
        $ref: '#/definitions/FlightEndPoint'
      marketing:
        $ref: '#/definitions/MarketingFlight'
      operating:
        $ref: '#/definitions/OperatingFlight'
  FlightEndPoint:
    type: object
    description: Departure or arrival information
    properties:
      iataCode:
        type: string
        description: IATA Airport code
        example: 'LHR'
      dateTime:
        type: string
        description: >-
          UTC schedule dateTime of the departure or arrival.
          Conversion of localDateTime in UTC date time.
        format: date-time
      localDateTime:
        type: string
        description: 'Local date and time with the following format \ yyyy-mm-ddThh:mm:ss'
        example: '2018-10-03T09:35:42.000'
      location:
        $ref: '#/definitions/Location'
  MarketingFlight:
    type: object
    description: Information about the marketing flight
    properties:
      bookingClass:
        $ref: '#/definitions/BookingClass'
      flightDesignator:
        $ref: '#/definitions/FlightDesignator'
  OperatingFlight:
    type: object
    description: Information about the operating flight
    properties:
      bookingClass:
        $ref: '#/definitions/BookingClass'
      flightDesignator:
        $ref: '#/definitions/FlightDesignator'
  FlightDesignator:
    type: object
    properties:
      carrierCode:
        type: string
        description: Two letter IATA standard carrier code
        minLength: 1
        maxLength: 2
        example: 6X
      flightNumber:
        type: string
        description: The flight number as assigned by the carrier - 1-4 digit number
        minLength: 1
        maxLength: 4
        example: '555'
      operationalSuffix:
        type: string
        description: The flight number suffix as assigned by the carrier - 1 char
        example: A
  EventLog:
    type: object
    description: Contains the when/how/who of a particular change.
    properties:
      id:
        type: string
        description: Identifier of the change
        example: "64GqpZfbcAW61PtL"
      triggerEventName:
        description: >-
          Trigger of the message - set to 'DATA_INIT' for initialization messages
          Description of the overriding use case (promo-code discount, merchandising discounts, manual agent overrides...)
        type: string
        example: 'DATA_INIT'
      dateTime:
        type: string
        description: Date/Time in UTC of the change.
        example: '2018-10-03T09:35:42.000Z'
      location:
        type: object
        properties:
          iataCode:
            type: string
            description: City code identifying the ticketing location.
            example: 'LON'
          address:
            type: object
            properties:
              countryCode:
                type: string
                description: The country code where the ticket is issued.
                example: 'GB'
      user:
        type: object
        properties:
          iataNumber:
            type: string
            description: A unique IATA identification number assigned to a travel agent.
            example: '91496646'
          officeId:
            type: string
            description: Identification within 1A System
            example: 'NCE6X0978'
          organizationCode:
            type: string
            description: Airline code or Computerized Reservation System (CRS) code of the system that delivers the issuance request message.
            example: '1A'
      pointOfSale:
        $ref: '#/definitions/PointOfSale'
      changeLog:
        type: array
        description: Contains the array of changeLogs
        items:
          properties:
            elementType:
              type: string
              description: List of the entities modified in current event.
              example: 'TKT_FLI'
            description:
              type: string
              description: Description of the Action performed for the given event.
              example: 'TAX_COMPARISON'
  PointOfSale:
    type: object
    properties:
      login:
        $ref: '#/definitions/Login'
      office:
        $ref: '#/definitions/Office'
      location:
        $ref: '#/definitions/Location'
  Login:
    type: object
    properties:
      countryCode:
        type: string
        description: ISO country code of the agent
      cityCode:
        type: string
        description: city code of the issuing agent/system
      region:
        type: string
        description: Region code of the office issuing the document.
  Office:
    type: object
    properties:
      id:
        type: string
        description: An identifier for a corporate user of a computer reservation system or global distribution system, typically a travel agency, also known as office ID.
      iataNumber:
        type: string
        description: IATA assigned office number
      agentType:
        type: string
        description: The issuing channel.  A for airline agent, T for travel agent
      systemCode:
        type: string
        description: 2-3 character airline/CRS code of the system that originates the current transaction or the issuance.
  Location:
    type: object
    properties:
      iataCityCode:
        type: string
        description: IATA location code
        example: PAR
      iataRegion:
        type: string
        description: Region code of the office issuing the document.
        example: MEAST (Middle East) , AFRIC (Africa)
  PatchOperation:
    description: A single JSON Patch operation as defined by RFC 6902 (see https://tools.ietf.org/html/rfc6902)
    type: object
    required:
      - "op"
      - "path"
    properties:
      op:
        type: string
        description: The operation to be performed
        enum:
          - "add"
          - "remove"
          - "replace"
          - "move"
          - "copy"
          - "test"
      path:
        type: string
        description: A JSON Pointer as defined by RFC 6901 (see https://tools.ietf.org/html/rfc6901)
      value:
        type: object
        description: The value to be used within the operations
      from:
        type: string
        description: A JSON Pointer as defined by RFC 6901 (see https://tools.ietf.org/html/rfc6901)
  AssociatedPnr:
    type: object
    description: Describes the association between the current reservation and another one.
    properties:
      reference:
        type: string
        description: Record locator [Amadeus or OA] with which the current reservation is associated - in case of a codeshare association, it enables to identify the operating PNR
        example: 'JKL789'
      creation:
        $ref: '#/definitions/EventLog'
        description: Creation date and system code [GDS/CRS] from which originates the associated reference
  Fee:
    type: object
    description: >-
      Structure to describe a Fee applied to provide a service or a product to the client.
      Each  type of  fees  is  assigned  a  code,  a 3-digit  subcode  and  a commercial name.
    properties:
      code:
        type: string
        description: This attribute provides the code identifying the type of fee being imposed by the carrier.
        example: 'OA'
      amount:
        type: string
        description: Defines amount with decimal separator.
      currency :
        type : string
        description: >-
          Defines a monetary unit. It is a three alpha code. Example: EUR for Euros, USD for US dollar, etc.
      description:
        type: string
        description: Amount type of the fee amount
        example: BASE_FARE
      originalAmount:
        type: object
        description: containing currency Code and its conversion rate of the original amount
        $ref: '#/definitions/ConvertedAmount'
      convertedAmount:
        type: object
        description: containing currency Code and its conversion rate post conversion of the amount
        $ref: '#/definitions/ConvertedAmount'
  Stakeholder:
    type: object
    description: Definition of the traveler.
    properties:
      name:
        description: Names of the stakeholder.
        $ref: '#/definitions/Name'
      passengerTypeCode:
        description: >-
          3-characters code defining the passenger type - possible values: ADT, CHD, INS, INF, UNA
        type: string
        example: 'ADT'
  Name:
    type: object
    description: Description of the name of a physical person.
    properties:
      firstName:
        type: string
        example: 'JAMES'
      lastName:
        type: string
        example: 'CARTER'
  PricingConditions:
    type: object
    description: >-
      Provides the context under which a product has been priced.
      Those conditions are dictated at pricing and can affect the subsequent issuance behaviour in terms of eligibility and processing.
    properties:
      fareCalculation:
        $ref: '#/definitions/FareCalculation'
      negoFareContract:
        $ref: '#/definitions/NegoFareContract'
      isInternationalSale:
        type: boolean
        description: International fare applied for the given itinerary
      isDomesticSale:
        type: boolean
        description: Domestic fare applied for the given itinerary
      isNonEndorsable:
        type: boolean
        description: Indicates that the fare cannot be endorsed - false as default value
        example: false
      fareComponents:
        type: array
        items:
          $ref: '#/definitions/FareComponent'
      flightBounds:
        type: array
        items :
          $ref: '#/definitions/FareComponent'
  FareCalculation:
    type: object
    description: Provides details on the type of pricing applied to the product.
    properties:
      pricingIndicator:
        type: string
        description: >-
          This attribute provides the Fare Calculation Pricing Indicator. It indicates whether the pricing was automatic or manual. The identifier reflects the method of pricing sent with the fare calculation of the electronic ticket.

          0 - It identifies the fare has been automatically computed
          1 - It identifies a manual built fare
          2...9 - It identifies a fare that was auto priced, but baggage and/or TFC data has been manipulated
          A-Z - Undefined industry use
        example: '0'
      text:
        type: string
        description: Content of the fare calculation.
        example: 'PAR 6X YTO289.85SKWI5LGT 6X PAR289.85SKWI5LGT NUC579.70END ROE0.862490'
  NegoFareContract:
    type: object
    description: >-
      Nature and reference of the commercial agreement by which the airline provides incentives to the selling travel agency for the quoted negotiated fare.
      This specific type of fare is used in conjunction with tours, referring to Inclusive Tour fares and Bulk Tour fares.
      The agent incentive on the IT/BT fare notably depends on the commission element entered in PNR.
    properties:
      incentiveScheme:
        type: string
        description: Nature of the agreement between the airline and the selling travel agency
      tourCode:
        type: string
        description: >-
          Used when a published tour or a special negotiated fare is sold in conjunction with the ticket
        example: 'TOUR99'
      isInclusiveBulkTicketing:
        type:  boolean
        description: 'IT/BT fares are special fares, discussed or negotiated with the airline. As the conditions are specific to defined agencies, the aim is to avoid being able to calculate the fare.'
        example: 'true'
  FareComponent:
    type: object
    description: A portion of a journey or itinerary between two consecutive fare break points.
    properties:
      tripIndicator:
        type: boolean
        description: Define the trip (e.g. Round trip, One Way...)
  Price:
    type: object
    description: Price valuation information
    properties:
      currency:
        type: string
        description: currency Code apply to all elements of the price
      fees:
        type: array
        items:
          $ref: '#/definitions/Fee'
      detailedPrices:
        type: array
        items:
          $ref: '#/definitions/ElementaryPrice'
      taxes:
        description: Additional charge for external actors (government, airport...)
        type: array
        items:
          $ref: '#/definitions/Tax'
      commissions:
        description: Commissions details
        type: array
        items:
          $ref: '#/definitions/Commission'
      surcharges:
        description: Surcharge amounts such as the airport fee or the roadside assistance
        type: array
        items:
          $ref: '#/definitions/Surcharge'
  Commission:
    type: object
    properties:
      commissionType:
        type: string
        description: This field describes the type of Commission.
        example: 'COAM'
      amount:
        $ref: '#/definitions/ElementaryPrice'
      percentage:
        type: number
        description: Percentage in case of a rate commission - can be decimal.
  Surcharge:
    type: object
    properties:
      amount:
        type: string
        description: Defines the monetary value with decimal position. It can be in cash or miles.
      currencyCode:
        type: string
        description: Defines a specific monetary unit using ISO4217 currency code.
      description:
        type: string
        description: Defines the type of amount
      pricingSubType:
        type: string
        description: >-
          This attribute can be described to define surcharge levied by airline, hotels, cars or any seller related to a product. Airline ticket surcharges, such as a Q surcharge, are typically used by airlines to pass added costs along to customers. surcharge itself is generally used by airlines as a way to charge customers for what they say are miscellaneous additional operating costs.
        enum:
          - AIRLINE_SURCHARGE
          - AIRLINE_DIFFERENTIAL
          - AIRLINE_STOPOVER
          - AIRLINE_MILEAGE
          - AIRLINE_MINIMUM_MISCELLANEOUS_CHECKS
          - AIRLINE_ONE_WAY_SUBJOURNEY_CHECK
          - AIRLINE_RETURN_SUBJOURNEY_CHECK
        example: ' AIRLINE_SURCHARGE'
  RevenueAccountingCoupon:
    type: object
    description: >-
      A coupon refers to a product that has been issued in the current
      travel document. In tickets, a coupon refers to a flight segment.
      An airline ticket portion that bears the notion "good for passage",
      or in the case of an electronic ticket, the electronic coupon indicates the
      particular places a passenger is entitled to be carried.
      A single ticket/document number may contain up to 4 segments.
      For tickets/documents with more than 4 segments of travel, a conjunction
      ticket is required to continue the itinerary.
      In EMDs, a coupon can refer to an ancillary service, an upgrade, a fee or a residual value.
      * soldSegment - Original image of the coupon at first issuance of the
      document. 
      * currentSegment - Latest image of the coupon in case it has
      been subject to modification[s] such as revalidation e.g. change of
      flight number. 
      * usedSegment - Image of the coupon when its status is
      considered as used i.e. either flown [B] or exchanged [E], refunded
      [RF], printed [PR], print exchange [PE], converted to Flight
      Interruption Manifest [G], closed [CLO], voided [V].
    properties:
      id:
        type: string
        description: Coupon id
        example: '*************-2018-10-18-1'
      number:
        type: number
        description: >-
          The coupon number inside this conjunctive ticket should be the
          same as ”Coupon Number” in case of non conjunctive tickets
          (from1 to 4).
        example: 1
      sequenceNumber:
        type: number
        description: >-
          The coupon sequence number over ticket(s). In the case of
          conjunctive tickets it defines the coupon sequence number over
          all conjunctive tickets and in this case it can be greater than
          four.
        example: 1
      documentNumber:
        type: string
        description: >-
          A single ticket/document number may contain up to 4 segments.
          For tickets/documents with more than 4 segments of travel, a
          conjunction ticket is required to continue the itinerary.
          Conjunctive tickets/EMDs (i.e. for an ETKT, a conjunctive ticket
          is issued when the ticket has more than 4 coupons)
        example: '*************'
      status:
        type: string
        description: >-
          Coupon operational status. It follows IATA PADIS Code List for data element 4405
        enum:
          - EXCHANGED
          - FLOWN
          - EXCHANGED_TO_FIM
          - COUPON_NOTIFICATION
          - OPEN_FOR_USE
          - REFUNDED
          - SUSPENDED
          - VOID
          - CLOSED
          - REVOKED
          - PRINTED
          - PRINT_EXCHANGE
          - NOT_AVAILABLE
          - BOARDED
          - IRREGULAR_OPERATIONS
          - ORIGINAL_ISSUE
          - CHECKED_IN
          - AIRPORT_CONTROL
          - UNAVAILABLE
          - REFUND_TAXES_AND_FEES
          - PAPER_TICKET
        example: 'EXCHANGED'
      fare:
        $ref: '#/definitions/Fare'
      reasonForIssuance:
        $ref: '#/definitions/ReasonForIssuance'
      settlementAuthorizationCode:
        type: string
        description: >-
          Contains the code present in the coupon that authenticates the
          settlement of all monetary charges of the coupon.
        example: '1326459788'
      isFromConnection:
        type: boolean
        description: Indicates whether the flight segment priced is departing from a connection with the previous flight
        example: false
      voluntaryIndicator:
        type: string
        description: >-
          Coupon Involuntary Indicator indicates if any manual operations
          have been performed on the coupon. When an Involuntary change is
          performed on a coupon IATA Ticketing handbook requires one to
          enter a endorsement in the FE field. 
          * 'W' - Weather/ATC delay 
          * 'U' -   Involuntary upgrade 
          * 'D' - Diversion 
          * 'F' - Strike/labor 
          * 'IG' - Involuntary Grade Change 
          * 'I' -  Involuntary (no reason given) 
          * 'O' - Oversale 
          * 'S' - Schedule change 
          * 'V' - Voluntary (customer requested) 
          * 'SO' - Flight cancelled 
          * '727' - Involuntary downgrade - passenger compensated 
          * '728' - Involuntary downgrade - passenger not compensated
        example: 'S'
      frequentFlyer:
        $ref: '#/definitions/FrequentFlyer'
      isCodeshare:
        type: boolean
        description: >-
          Depicts whether the coupon has codeshare flight or not. 
          Default value is false
        example: true'
      soldSegment:
        $ref: '#/definitions/FlightSegment'
      usedSegment:
        $ref: '#/definitions/FlightSegment'
      isFromStopOver:
        type: boolean
        description: Indicates whether the flight segment priced is departing from a stop over relative to the previous flight
      interlineBilling:
        description: Model representing all the information related to the coupon interline billing
        $ref: '#/definitions/InterlineBilling'
      codeshareBilling:
        description: Model representing all the information related to the coupon codeshare billing
        $ref: '#/definitions/CodeshareBilling'
      prorationMethods:
        description: >-
          Provides information about the proration methods that were applied on the coupon to split the fare between agent, validating carrier and operating carrier, containing the list of prorated values resulting from the different methods applied.
        $ref: '#/definitions/ProrationMethods'
      price:
        $ref: '#/definitions/Price'
  InterlineBilling:
    type: object
    properties:
      billingStatus:
        type: string
        description: Indicates the Billing Status of the Transaction, filled during the Outward and Inward Billing processes.
        enum:
          - ACCEPTED
          - BILLED
          - DISCARD_ON_GOING
          - SUBMISSION_ON_GOING
          - ACCEPTANCE_ON_GOING
          - REJECTION_ON_GOING
          - EXPIRED
          - ON_HOLD
          - CLEARED
          - INVOICED
          - VALIDATED
          - SELECTED
          - CREATED
          - REJECTED
          - SUBMITTED
          - WAIVED
          - FAILED
        example: 'ACCEPTED'
      interlineableServiceCharges:
        type: array
        items:
          $ref: '#/definitions/ElementaryPrice'
  CodeshareBilling:
    type: object
    properties:
      codeshareAgreement:
        type: string
        description: This indicates the type of codeshare agreement between marketing and operating flights.
      detailedPrices  :
        type: array
        items:
          $ref: '#/definitions/ElementaryPrice'
  ProrationMethods:
    type: object
    properties:
      prorationFactor:
        type: string
        description: Indicates the adjusted mileage to take into account the difference in cost levels in different parts of the world and the higher cost of short haul flights.
        example: '991'
      ticketedPointMileage:
        type: string
        description: >-
          It represents a distance covered by one flight coupon of a passenger ticket. so you have a TPM by city pair Org&Dest sometimes it's used by airlines to compute frequent flyer coupon value. the TPM is multiplied by a coefficient to get the coupon prorated value
        example: '543'
      prorationValues:
        type: array
        description: Provides the outcome of the proration module for a given proration method
        items:
          $ref: '#/definitions/ProrationValues'
  ProrationValues:
    type: object
    properties:
      prorationSource:
        type: string
        description: Indicates the applied proration method
        example: '1A_SPA'
      prorationCode:
        type: string
        description: Code of the method used for proration
        enum:
          - PROVISO_APPLIED
          - STRAIGHT_RATE_PRORATION
          - SINGLE_SEGMENT
          - SEGMENT_NOT_POSSIBLE
          - SPA_APPLIED
          - REPLICATION_ORIGINAL_ROUTE_PRORATION
          - INVOLUNTARY_REISSUE_FREQUENT_FLYER_AWARD
          - STANDARD_FIM_PRORATION
          - SIMPLIFIED_FIM_PRORATION
          - ZED_PRORATION_METHOD
          - NULL_FARE
          - BEST_EFFORT_ALGORITHM
          - EMD_MCO_PRORATED_AMOUNT_ALREADY_FIZED_AT_COUPON_LEVEL
        example: 'STRAIGHT_RATE_PRORATION'
      proratedAmount:
        $ref: '#/definitions/ElementaryPrice'
  Fare:
    type: object
    description: Characteristics of an applied Fare
    properties:
      pricedPassengerTypeCodes:
        type: array
        items:
          type: string
          description: Passenger type code(s) used to price a given traveler
      fareBasis:
        type: object
        description: Composed of codes used by airlines to identify the type of a fare proposed to the booker and to allow airline staff and travel agents to find the rules applicable to that fare.
        $ref: '#/definitions/FareBasis'
      fareCondition:
        type: object
        description: Certain conditions applied at fare level
        $ref: '#/definitions/FareCondition'
  FareBasis:
    type: object
    description: >-
      Composed of codes used by airlines to identify the type of a fare proposed to the booker and to allow airline staff 
      and travel agents to find the rules applicable to that fare.
    properties:
      fareBasisCode:
        type: string
        description: Defines the code of the fare delivered to the booker - up to 6 alphanumerical chars
        example: 'TTI'
  FareCondition:
    type: object
    description: Certain conditions applied at fare level
    properties:
      isFareStudent:
        type: boolean
        description: To identify if student discount applied to the fare
  BookingClass:
    type: object
    description: Booking class description
    properties:
      code:
        type: string
        description: code identifying the booking class
        example: 'Y'
      cabin:
        type: object
        properties:
          code:
            type: string
            description: code identifying the cabin
            example: 'Y'
  AgencyMemo:
    type: object
    properties:
      number:
        type: string
        description: Identifier for Memo
        example: '12533000289159'
      agencyMemoType:
        type: string
        description: Indicates if the memo is a Debit or Credit. It also indicates if it is an Industry type (for BSP/ARC Agencies) or Non-Industry type (for GSA, Non-BSP Agencies) or Direct Type (for ATO/CTO).
        enum:
          - AGENCY_DEBIT_MEMO
          - AGENCY_CREDIT_MEMO
          - AGENCY_DEBIT_MEMO_NON_TICKET_RELATED
          - AGENCY_CREDIT_MEMO_NON_TICKET_RELATED
          - IATA_SETTLEMENT_PLAN_DEBIT_RECORD
          - IATA_SETTLEMENT_PLAN_CREDIT_RECORD
          - DEFAULT_AGENCY_DEBIT_MEMO
          - DEFAULT_AGENCY_CREDIT_MEMO
          - AGENCY_DEBIT_MEMO_INTERNAL
          - AGENCY_CREDIT_MEMO_INTERNAL
          - INTERNAL_DEBIT_MEMO
          - INTERNAL_CREDIT_MEMO
          - INVOICE
          - CREDIT_NOTE
        example: 'INVOICE'
      reasonForMemo:
        type: object
        description: Information to identify the root cause of the problem responsible for the memo issuance
        properties:
          code:
            type: string
            description: A code set by Amadeus Revenue Accounting application to identify the root cause of the problem responsible for the memo issuance.
            example: 'SHFOP'
          description:
            type: string
            description: A long text to explain the root cause for the problem responsible for the memo issuance.
            example: 'Memo raised in respect of booking(s) below'
  AfterSaleFare:
    type: object
    properties:
      fareType:
        type: string
        description: >-
          This attribute describes the type of fare, as follows:
          NET_FARE - Net fare, this is the fare that a Travel Agent pays to airline. 
          SELLING - Selling fare, this is the fare that a passenger pays to a Travel Agent or Airline Agent. 
          PUBLISHED - Published fare, this is the fare the airline provides to all Travel Agents or Airline Agents. 
          NET_FILED - Net fare as stored in the E-tkt and EMD. 
          RESIDUAL_FARE - Residual value resulting from proration process. 
          ADDITIONAL_FARE_COLLECTION - Additional fare collection, corresponding to the fare balance between the new document and the exchanged document (New Fare - Old Fare). 
          NEW_FARE - Concerning the Exchange transaction, it corresponds to the the relevant fare of the new document. 
          OLD_FARE - Concerning the Exchange transaction, it corresponds to the the sum of the relevant fares of the exchanged documents. 
          NON REFUNDABLE AMOUNT - Concerning the Exchange transaction, it corresponds to the non refundable fare amount resulting of the exchange. 
          LOWEST_APPLICABLE_PUBLISHED - Lowest Applicable Published Fare retrieved by Fare&Pricing Engine as applicable on the whole ticket. Only available in Base currency.
        enum:
          - SELLING
          - PUBLISHED
          - NET_FILED
          - RESIDUAL_FARE
          - ADDITIONAL_FARE_COLLECTION
          - NON_REFUNDABLE_AMOUNT
          - LOWEST_APPLICABLE_PUBLISHED
          - NET_FARE
          - NEW_FARE
          - OLD_FARE
        example: 'PUBLISHED'
      detailedPrices:
        type: array
        items:
          $ref: '#/definitions/ElementaryPrice'