Goal: integration test to verify the Delta table copy executor

The mapping file contain these tables:
- FACT_PASSENGER_DUMMY: it is the latest VIEW of FACT_PASSENGER_HISTO - it must not be created its table selector never used 
- FACT_PASSENGER: it is the latest VIEW of FACT_PASSENGER_HISTO
- FACT_PASSENGER_HISTO: it is a FACT TABLE
- FACT_SECONDARY_HISTO: it is a FACT TABLE
- INTERNAL_MY_TABLE it is an INTERNAL TABLE

The goal is to generate the following views for this scenario
 - Delta table copy sql statement