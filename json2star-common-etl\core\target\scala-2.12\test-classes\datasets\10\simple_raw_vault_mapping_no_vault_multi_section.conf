{
   "partition-spec" : {
          "key" : "PNR_CREATION_DATE",
          "column-name": "PART_PNR_CREATION_MONTH",
          "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
    },
  "tables": [
    {
      "name": "FACT_MULTI_SECTION",
      "mapping": {
      "merge": {
        "key-columns": ["REFERENCE_KEY", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{ "blocks": [ {"pr": "$.mainResource.current.image.products[*]"}, {"tr": "$.travelers[*]"}]}],
      "columns": [
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"pr": "$.id"}, {"tr": "$.id"}]}},
        {"name": "PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"pr": "$.id"}]}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.id"}]}},
        {"name": "SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"pr": "$.subType"}]}},

        {
          "name": "FIRST_NAME", "column-type": "strColumn",
          "sources": {"blocks": [{"root" : "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"}]},
          "has-variable": true
        },

        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          pit-key: "REFERENCE_KEY",
          pit-version: "VERSION",
          valid-from: "DATE_BEGIN",
          valid-to: "DATE_END",
          is-last: "IS_LAST_VERSION"
        }
      }}
    }
  ],
  "links": [ ]
}