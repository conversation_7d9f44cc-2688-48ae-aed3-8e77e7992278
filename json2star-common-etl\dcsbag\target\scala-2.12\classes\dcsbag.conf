versionExprVal: "bigint({0})"
versionTypeVal: "longColumn"
minDateFromDateArray: "to_date(array_min(regexp_extract_all({0}, '(\\\\d+)-(\\\\d+)-(\\\\d+)',0)), 'yyyy-MM-dd')"
HashMIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashM({0}) )"
HashSIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashS({0}) )"
// "_-3101D2A10020E639" => "3101D2A10020E639", "3101D2A10020E638-3101D2A10020E639" => "3101D2A10020E638"
ifFirstValueNullTakeSecondValue : "if(split_part({0}, '-', 1) == '_', split_part({0}, '-', 2), split_part({0}, '-', 1))"
// "_-3101D2A10020E639-EY-456-AF-321-A-CDG-PPT-CDG" => "3101D2A10020E639-EY-456-A-2024-12-16-MNL-PPT-PPT", "3101D2A10020E638-3101D2A10020E639-EY-456-AF-321-A-CDG-PPT-CDG" => "3101D2A10020E638-EY-456-A-2024-12-16-MNL-PPT-PPT"
getFullIdithCorrectPassengerIdSegId : "concat_ws('-',"${ifFirstValueNullTakeSecondValue}",slice(split({0} ,'-'),3,2),slice(split({0} ,'-'),7,150))"
sanitizeDate: "regexp_replace({0}, 'T..:..:..Z|T..:..:..', '')"
sanitizeDateCorrectPassenger: "regexp_replace(concat_ws('-',"${ifFirstValueNullTakeSecondValue}",slice(split({0} ,'-'),3,150)), 'T..:..:..Z|T..:..:..', '')"
// This domain contains some specificities regarding PIT. Every histo table contains IS_PURGE_EVENT column,
// a boolean that tells if the record comes from a purge event. Also IS_LAST_VERSION and END_DATE are generated
// based on whether the record comes from a purge event.
// This allows to avoid considering as "last" records that are purged, while still updating the validity date
// of the record with the previous version.
// This purge logic is applied only on functional purge event (BBGIDQ). Technical purge event are filtered out.
"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.mainResource.current.image": "DcsBaggagePush.processedBagsGroup",
      "$.mainResource.current.image.bagsGroup": "DcsBaggagePush.processedBagsGroup",
      "$.mainResource.current.image.bags": "DcsBaggagePush.processedBags",
      "$.mainResource.current.image.excessCharges": "DcsBaggagePush.processedExcessBaggageCharges",
      "$.mainResource.current.image.lastModification": "DcsBaggagePush.lastModification",
      "$.mainResource.current.image.version": "DcsBaggagePush.processedBagsGroup.etag"
    }
  },
  "mapping": {
    "record-filter": "$.mainResource.current.image[?(@.bagsGroup || @.lastModification.triggerEventName=='BBGIDQ')]"
  }
},

"tables": [
  {
    "name": "FACT_BAG_GROUP",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_BAG_GROUP_HISTO"
      "additional-filter-expression": "IS_LAST_VERSION = true"
      "columns-to-drop": ["IS_PURGE_EVENT", "IS_LAST_VERSION", "DATE_END"]
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_BAG_GROUP_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Baggage Group",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains information of a baggage group (a set of bags travelling together on an identical itinerary), such as number and weight of checked bags and hand bags, and the responsible passenger.",
        "granularity": "1 bag group"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_GROUP_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (See REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "INTERNAL_CORR_BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base":"$.bagsGroup.responsiblePassenger.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the CORR functional key (See RESPONSIBLE_PASSENGER_DCS)", "rule": "replace", "gdpr-zone": "orange"}}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.id"}]}
			, "meta": {"description": {"value": "Functional key: bagsGroupId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CHECKED_BAGS_COUNT", "column-type": "intColumn", "sources": {"blocks": [{"base":"$.bagsGroup.checkedBagsCount"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "CHECKED_BAGS_WEIGHT_VALUE", "column-type": "floatColumn", "sources": {}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "CHECKED_BAGS_WEIGHT_UNIT", "column-type": "strColumn", "sources": {}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "CHECKED_BAGS_WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"base":"$.bagsGroup.checkedBagsWeight.value"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "CHECKED_BAGS_WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.bagsGroup.checkedBagsWeight.unit"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "HAND_BAGS_COUNT", "column-type": "intColumn", "sources": {"blocks": [{"base":"$.bagsGroup.handBagsCount"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "HAND_BAGS_WEIGHT_VALUE", "column-type": "floatColumn", "sources": {}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "HAND_BAGS_WEIGHT_UNIT", "column-type": "strColumn", "sources": {}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "HAND_BAGS_WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"base":"$.bagsGroup.handBagsWeight.value"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "HAND_BAGS_WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.bagsGroup.handBagsWeight.unit"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "RESPONSIBLE_PASSENGER_DCS", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.bagsGroup.responsiblePassenger.id"}]}
			, "meta": {"description": {"value": "The DCS id of the passenger responsible of the bag group", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "OTHER_POOLED_PASSENGERS_DCS", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.bagsGroup.pooledPassengers.collection[*].id"}]}
			, "meta": {"description": {"value": "The DCS ids of the other pooled passengers to which belongs the bag group", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base":"$.version"}]}, "expr": ${versionExprVal}
			, "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base":"$.lastModification.dateTime"}]}
			, "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}
			, "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}
			, "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "INTERNAL_DATE_END",
          "is-last": "INTERNAL_IS_LAST_RECEIVED"
        }
      }
    },
    "stackable-addons": [
      {
        "type": "weight-conversion",
        "conversions": [
          {"src-col": "CHECKED_BAGS_WEIGHT_VALUE_ORIGINAL", "src-unit-col": "CHECKED_BAGS_WEIGHT_UNIT_ORIGINAL", "dst-col": "CHECKED_BAGS_WEIGHT_VALUE", "dst-unit-col": "CHECKED_BAGS_WEIGHT_UNIT"},
          {"src-col": "HAND_BAGS_WEIGHT_VALUE_ORIGINAL", "src-unit-col": "HAND_BAGS_WEIGHT_UNIT_ORIGINAL", "dst-col": "HAND_BAGS_WEIGHT_VALUE", "dst-unit-col": "HAND_BAGS_WEIGHT_UNIT"}
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_BAG",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_BAG_HISTO"
      "additional-filter-expression": "IS_LAST_VERSION = true"
      "columns-to-drop": ["IS_PURGE_EVENT", "IS_LAST_VERSION", "DATE_END"]
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_BAG_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Baggage Group",
    "mapping": {
      "description": {
        "description": "Contains information of an individual bag, such as weight, bag tag details, and the tracking logs of the last seen location",
        "granularity": " 1 bag in 1 bag group"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"bag": "$.bags[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}, "expr": "hashM({0})"
			, "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CORR_BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.bagTag.licensePlate"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the corr functional key (based on licence plate)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_CORR_BAG_OWNER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"bag": "$.owner.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": "hashM("${ifFirstValueNullTakeSecondValue}")"
          , "meta": {"description": {"value": "Hash of the corr functional key ( based  on owner pax UCI)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}
			, "meta": {"description": {"value": "Functional key: bagsGroupId-bagId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "BAG_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.bagType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BAG_TYPE"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WEIGHT_VALUE", "column-type": "floatColumn", "sources": {}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WEIGHT_UNIT", "column-type": "strColumn", "sources": {}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"bag": "$.weight.value"}]}
          , "meta": {"description": {"value": "The weight value of the individual bag", "rule": "replace"}, "example": {"value": "22.8", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.weight.unit"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "OWNER_PASSENGER_DCS", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.owner.id"}]}
          , "meta": {"gdpr-zone": "orange"}},
        {"name": "BAG_TAG_FINAL_DESTINATION", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.bagTag.finalDestination"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "BAG_TAG_ISSUANCE_DAY", "column-type": "intColumn", "sources": {"blocks": [{"bag": "$.bagTag.issuanceDay"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "BAG_TAG_ISSUING_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.bagTag.issuingCarrier"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "BAG_TAG_LICENSE_PLATE", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.bagTag.licensePlate"}]}
			, "meta": {"gdpr-zone": "orange"}},
        {"name": "BAG_TAG_SOURCE_SYSTEM", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.bagTag.systemSource"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BAG_SOURCE_SYSTEM"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "BAG_TAG_ORIGINAL_FINAL_DESTINATION", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.linkedBagTag.finalDestination"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "BAG_TAG_ORIGINAL_ISSUING_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.linkedBagTag.issuingCarrier"}]}
			, "meta": {"description": {"value": "The original bag tag’s issuing carrier numeric code (NALC)", "rule": "replace"}, "example": {"value": "123", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAG_TAG_ORIGINAL_LICENSE_PLATE", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.linkedBagTag.licensePlate"}]}
			, "meta": {"gdpr-zone": "orange"}},
        {"name": "SOURCE_SYSTEM", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.systemSource"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BAG_SOURCE_SYSTEM"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "LASTSEEN_LOG_DEVICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.lastSeenLocation.deviceId"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "LASTSEEN_LOG_DEVICE_USER", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.lastSeenLocation.deviceUser"}]}
			, "meta": {"gdpr-zone": "orange"}},
        {"name": "LASTSEEN_LOG_TIMESTAMP_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"bag": "$.lastSeenLocation.localDateTime"}]}
			, "meta": {"description": {"value": "Local timestamp of the bag's last seen location", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "LASTSEEN_LOG_TIMESTAMP_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"bag": "$.lastSeenLocation.utcDateTime"}]}
			, "meta": {"description": {"value": "UTC timestamp of the bag's last seen location", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "LASTSEEN_LOCATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.lastSeenLocation.trackedLocation.identifier"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "LASTSEEN_LOCATION_TERMINAL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"bag": "$.lastSeenLocation.trackedLocation.airportCode"}, {"bag": "$.lastSeenLocation.trackedLocation.terminalId"}]}, "expr": ${HashSIdCheckEndNotNull}
      , "fk": [{"table": "DIM_AIRPORT_TERMINAL", "column": "AIRPORT_TERMINAL_ID"}]
			, "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})","fk": [{"table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
			, "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
			, "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}
			, "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}
			, "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "weight-conversion",
        "conversions": [
          {"src-col": "WEIGHT_VALUE_ORIGINAL", "src-unit-col": "WEIGHT_UNIT_ORIGINAL", "dst-col": "WEIGHT_VALUE", "dst-unit-col": "WEIGHT_UNIT"}
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_BAG_LEG_DELIVERY",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_BAG_LEG_DELIVERY_HISTO"
      "additional-filter-expression": "IS_LAST_VERSION = true"
      "columns-to-drop": ["IS_PURGE_EVENT", "IS_LAST_VERSION", "DATE_END"]
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_BAG_LEG_DELIVERY_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg Delivery",
    "subdomain-main-table": "true",
    "mapping": {
	  "description": {
        "description": "Contains information of the bag transport/delivery on a given flight leg, such as flight information, load/transport status, and log information of the bag's checkin.",
        "granularity": "1 leg delivery for 1 bag"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.mainResource.current.image"},
            {
              "cartesian": [
                [{"bag": "$.bags[*]"}, {"leg": "$.legs[*]"}],
                [{"itinerary": "$.bagsGroup.itinerary[*]"}]
              ]
            }
          ]
        }
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}]}, "expr": "hashM({0})"
			, "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
//        {"name": "CORR_BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.bagTag.licensePlate"}, {"leg": "$.id"}]}, "expr": "hashM({0})"
//          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CORR_BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"}, {"leg": "$.operatingFlight.*"}, {"leg": "$.boardPoint.localDateTime"}, {"leg": "$.boardPoint.iataCode"}, {"leg": "$.offPoint.iataCode"}]}, "expr": "hashM("${sanitizeDate}")"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CORR_BAG_LEG_DELIVERY_BAG_OWNER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"bag": "$.owner.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}, {"leg": "$.operatingFlight.*"}, {"leg": "$.boardPoint.localDateTime"}, {"leg": "$.boardPoint.iataCode"}, {"leg": "$.offPoint.iataCode"}]}, "expr": "hashM("${sanitizeDateCorrectPassenger}")"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}]}
			, "meta": {"description": {"value": "Functional key: bagsGroupId-bagId-LegId", "rule": "replace"}, "example": {"value": "10AC810000344E97-10AC81000060D17B-MUC", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "LEG_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", // keep the Bag Leg Deliveries with a DEPARTURE_AIRPORT present in the BAG_GROUP scope (ITINERARY)
          "sources": {"blocks": [{"bag": "$.legs[?(@.boardPoint.iataCode=='{ITINERARY_LEG_IDENTIFIER}' && @.boardPoint.iataCode=='{DEPARTURE_AIRPORT}')].id"}]}, "has-variable": true
      , "meta": {"gdpr-zone": "orange"}},
        {"name": "DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.boardPoint.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.offPoint.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.operatingFlight.carrierCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE"}]}
			, "meta": {"description": {"value": "Airline operating the flight leg on which the bag is transported.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_FLIGHT_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"leg": "$.operatingFlight.flightNumber"}]}
			, "meta": {"description": {"value": "Flight number of the flight leg on which the bag is transported.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.operatingFlight.operationalSuffix"}]}
			, "meta": {"description": {"value": "Operational suffix of the flight leg on which the bag is transported.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DEPARTURE_DATETIME_LOCAL", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.boardPoint.localDateTime"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.travelClassCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "BAG_ACCEPTANCE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.acceptanceStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BAG_ACCEPTANCE_STATUS"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "ACTIVATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.activationStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_ACTIVATION_STATUS"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_AUTHORIZED_TO_LOAD", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.authorityToLoad"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "IS_AUTHORIZED_TO_TRANSPORT", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.authorityToTransport"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "CHECKIN_LOCATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.checkinLocation.trackedLocation.identifier"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "ITINERARY_LEG_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"itinerary": "$.id"}]}, "meta": {"description": {"value": "Leg identifier obtained from the eligible itinerary", "rule": "replace"}, "example": {"value": "LHR", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_BAG_HISTO", "column": "BAG_ID"}]},
        {"name": "CHECKIN_LOCATION_TERMINAL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"leg": "$.checkinLocation.trackedLocation.airportCode"}, {"leg": "$.checkinLocation.trackedLocation.terminalId"}]}, "expr": ${HashSIdCheckEndNotNull}
          , "fk": [{"table": "DIM_AIRPORT_TERMINAL", "column": "AIRPORT_TERMINAL_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
			, "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
			, "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}
			, "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}
			, "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SPECIAL_BAG_HANDLING",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SPECIAL_BAG_HANDLING_HISTO"
      "additional-filter-expression": "IS_LAST_VERSION = true"
      "columns-to-drop": ["IS_PURGE_EVENT", "IS_LAST_VERSION", "DATE_END"]
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SPECIAL_BAG_HANDLING_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg Delivery",
    "subdomain-main-table": "true",
    "mapping": {
	  "description": {
        "description": "Contains information of special bags, such as bag type and details/status of the corresponding chargeable service.",
        "granularity": "1 special bag handing on 1 bag leg delivery"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SPECIAL_BAG_HANDLING_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"bag": "$.bags[*]"}, {"leg": "$.legs[*]"}, {"spec": "$.specialBagHandlingItems[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SPECIAL_BAG_HANDLING_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}, {"spec": "$.id"}]}, "expr": "hashM({0})"
			, "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}, {"spec": "$.id"}]}
			, "meta": {"description": {"value": "Functional key: bagsGroupId-bagId-LegId-specialHandlingId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SPECIAL_BAG_HANDLING_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"spec": "$.id"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "FREETEXT", "column-type": "strColumn", "sources": {"blocks": [{"spec": "$.freeText"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "TYPE", "column-type": "strColumn", "sources": {"blocks": [{"spec": "$.label"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_SPECIAL_BAG_HANDLING_TYPE"}]}
			, "meta": {"description": {"value": "The type of special bag handling", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"spec": "$.service.code"}]}, "create-fk": {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_SERVICE", "column": "SERVICE_ID"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"spec": "$.service.description"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})","fk": [{"table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_BAG_HISTO", "column": "BAG_ID"}]},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_BAG_LEG_DELIVERY_HISTO", "column": "BAG_LEG_DELIVERY_ID"}]},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
			, "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
			, "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}
			, "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}
			, "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_EXCESS_BAGGAGE_CHARGE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_EXCESS_BAGGAGE_CHARGE_HISTO"
      "additional-filter-expression": "IS_LAST_VERSION = true"
      "columns-to-drop": ["IS_PURGE_EVENT", "IS_LAST_VERSION", "DATE_END"]
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_EXCESS_BAGGAGE_CHARGE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Excess Baggage",
    "mapping": {
	  "description": {
        "description": "Contains information on a globally computed excess baggage charge, such as the payable amount, payment status and waiver reason if any.",
        "granularity": "1 excess charge for 1 bag group"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "EXCESS_BAGGAGE_CHARGE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"exc": "$.excessCharges[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "EXCESS_BAGGAGE_CHARGE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}]}, "expr": "hashM({0})"
			, "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}]}
			, "meta": {"description": {"value": "Functional key: bagsGroupId-excessChargeId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TOTAL_CHARGE_AMOUNT", "column-type": "floatColumn", "sources": {}
			, "meta": {"description": {"value": "The total charge amount of excess baggage including taxes, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TOTAL_CHARGE_CURRENCY", "column-type": "strColumn", "sources": {}
			, "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.bags[*].legs[*].boardPoint.localDateTime"}]}, "expr" : ${minDateFromDateArray}
			, "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {}
			, "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "TOTAL_CHARGE_AMOUNT_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"exc": "$.totalCharge.total"}]}
			, "meta": {"description": {"value": "Contains taxes, and hence can differ from the sum of all excess item rates.", "rule": "concat"}, "gdpr-zone": "green"}},
        {"name": "TOTAL_CHARGE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"exc": "$.totalCharge.currency"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "PAYMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"exc": "$.paymentStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_PAYMENT_STATUS"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WAIVER_REASON", "column-type": "strColumn", "sources": {"blocks": [{"exc": "$.waiverReason.text"}]}
      , "meta": {"gdpr-zone": "green"}},
        {"name": "RESPONSIBLE_PASSENGER_DCS", "column-type": "strColumn", "sources": {"blocks": [{"exc": "$.responsiblePassenger.id"}]}
			, "meta": {"description": {"value": "Indicates the DCS id of the passenger responsible for the excess baggage charge", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})","fk": [{"table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
			, "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
			, "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}
			, "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}
			, "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "TOTAL_CHARGE_AMOUNT_ORIGINAL", "src-unit-col": "TOTAL_CHARGE_CURRENCY_ORIGINAL", "src-date-col" : "EXCHANGE_RATE_DATE_NEEDED", "dst-col": "TOTAL_CHARGE_AMOUNT", "dst-unit-col": "TOTAL_CHARGE_CURRENCY","dst-date-col" :"EXCHANGE_RATE_DATE_TAKEN"}
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_EXCESS_BAGGAGE_ITEM",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_EXCESS_BAGGAGE_ITEM_HISTO"
      "additional-filter-expression": "IS_LAST_VERSION = true"
      "columns-to-drop": ["IS_PURGE_EVENT", "IS_LAST_VERSION", "DATE_END"]
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_EXCESS_BAGGAGE_ITEM_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Excess Baggage",
    "subdomain-main-table": "true",
    "mapping": {
	  "description": {
        "description": "Contains information on an individual bag involved in a excess baggage charge, such as calculation airport and details (weight, charge, rate), and references to the chargeable document",
        "granularity": "1 excess item for 1 excess charge"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "EXCESS_BAGGAGE_ITEM_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"exc": "$.excessCharges[*]"}, {"calc": "$.calculations[*]"}, {"item": "$.items[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "EXCESS_BAGGAGE_ITEM_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}, {"calc": "$.id"}, {"item": "$.id"}]}, "expr": "hashM({0})"
			    , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}, {"calc": "$.id"}, {"item": "$.id"}]}
			    , "meta": {"description": {"value": "Functional key: bagsGroupId-excessChargeId-excessCalcId-excessItemId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "CALCULATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"calc": "$.id"}]}
			    , "meta": {"gdpr-zone": "orange"}},
        {"name": "ITEM_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.id"}]}
			    , "meta": {"gdpr-zone": "orange"}},
        {"name": "ITEM_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.itemType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_EXCESS_BAGGAGE_ITEM_TYPE"}]}
			    , "meta": {"description": {"value": "The type of an excess baggage item", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CALCULATION_DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"calc": "$.boardPoint.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "CALCULATION_ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"calc": "$.offPoint.iataCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "WEIGHT_VALUE", "column-type": "floatColumn", "sources": {}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "WEIGHT_UNIT", "column-type": "strColumn", "sources": {}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "WEIGHT_VALUE_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"item": "$.weight.value"}]}
          , "meta": {"example": {"value": "24.9", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.weight.unit"}]}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "CHARGE_AMOUNT", "column-type": "floatColumn", "sources": {}
			    , "meta": {"description": {"value": "The charge amount of the excess baggage item, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHARGE_CURRENCY", "column-type": "strColumn", "sources": {}
			    , "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHARGE_AMOUNT_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"item": "$.charge.total"}]}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "CHARGE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.charge.currency"}]}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "CHARGE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.bags[*].legs[*].boardPoint.localDateTime"}]}, "expr" : ${minDateFromDateArray}
          , "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHARGE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {}
          , "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "QUANTITY", "column-type": "intColumn", "sources": {"blocks": [{"item": "$.quantity"}]}
			    , "meta": {"description": {"value": "The quantity used in the excess baggage charge computation", "rule": "replace"}, "example": {"value": "1", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RATE_AMOUNT", "column-type": "floatColumn", "sources": {}
			    , "meta": {"description": {"value": "The rate amount of the excess baggage item excluding taxes, in home currency", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RATE_CURRENCY", "column-type": "strColumn", "sources": {}
			    , "meta": {"description": {"value": "The airline's home currency", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RATE_AMOUNT_ORIGINAL", "column-type": "floatColumn", "sources": {"blocks": [{"item": "$.rate.total"}]}
			    , "meta": {"description": {"value": "Does not contain taxes, and hence the sum of all item rates can differ from the total amount on excess charge level.", "rule": "concat"}, "gdpr-zone": "green"}},
        {"name": "RATE_CURRENCY_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.rate.currency"}]}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "RATE_EXCHANGE_RATE_DATE_NEEDED", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.bags[*].legs[*].boardPoint.localDateTime"}]}, "expr" : ${minDateFromDateArray}
          , "meta": {"description": {"value": "The date for which the home currency conversion rate was needed", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RATE_EXCHANGE_RATE_DATE_TAKEN", "column-type": "dateColumn", "sources": {}
          , "meta": {"description": {"value": "The date for which the home currency conversion rate was found and taken", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CHARGEABLE_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.paymentDocument.number"}]}
			    , "meta": {"gdpr-zone": "orange"}},
        {"name": "WAIVER_REASON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.waiverReason.text"}]}
			    , "meta": {"gdpr-zone": "green"}},
        {"name": "WAIVER_REASON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.waiverReason.status"}]}
          , "meta": {"description": {"value": "Filled by default with the status of the waiver reason free text. Alternatively it can contain other attributes of the waiver reason, such as the predefined reasons from the DCS drop-down list.", "rule": "replace"}, "example": {"value": "ACTIVE, DISRUPTION", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.bag.id"}]}, "expr": ${HashMIdCheckEndNotNull}
          ,"fk": [{"table": "FACT_BAG_HISTO", "column": "BAG_ID"}]},
        {"name": "EXCESS_BAGGAGE_CHARGE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"exc": "$.id"}]}, "expr": "hashM({0})"
          ,"fk": [{"table": "FACT_EXCESS_BAGGAGE_CHARGE_HISTO", "column": "EXCESS_BAGGAGE_CHARGE_ID"}]},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          ,"fk": [{"table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
			    , "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
			    , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}
			    , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}
			    , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "stackable-addons": [
      {
        "type": "weight-conversion",
        "conversions": [
          {"src-col": "WEIGHT_VALUE_ORIGINAL", "src-unit-col": "WEIGHT_UNIT_ORIGINAL", "dst-col": "WEIGHT_VALUE", "dst-unit-col": "WEIGHT_UNIT"}
        ]
      },
      {
        "type": "currency-conversion",
        "conversions": [
          {"src-col": "RATE_AMOUNT_ORIGINAL", "src-unit-col": "RATE_CURRENCY_ORIGINAL", "src-date-col" : "RATE_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "RATE_AMOUNT", "dst-unit-col": "RATE_CURRENCY","dst-date-col" :"RATE_EXCHANGE_RATE_DATE_TAKEN"}
          {"src-col": "CHARGE_AMOUNT_ORIGINAL", "src-unit-col": "CHARGE_CURRENCY_ORIGINAL", "src-date-col" : "CHARGE_EXCHANGE_RATE_DATE_NEEDED", "dst-col": "CHARGE_AMOUNT", "dst-unit-col": "CHARGE_CURRENCY","dst-date-col" :"CHARGE_EXCHANGE_RATE_DATE_TAKEN"}
        ]
      }
    ],
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SYSTEM_USER_ACTION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SYSTEM_USER_ACTION_HISTO"
      "additional-filter-expression": "IS_LAST_VERSION = true"
      "columns-to-drop": ["IS_PURGE_EVENT", "IS_LAST_VERSION", "DATE_END"]
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SYSTEM_USER_ACTION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "System User Action",
    "subdomain-main-table": "true",
    "mapping": {
	  "description": {
        "description": "Captures information on the system user and the performed action: trigger event, office id, user id, workstation",
        "granularity": "1 user information for 1 bag group"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SYSTEM_USER_ACTION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"modif": "$.lastModification"}, {"user": "$.user"}]},
      ]
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "SYSTEM_USER_ACTION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}
			, "meta": {"description": {"value": "Functional key: bagsGroupId", "rule": "replace"}, "gdpr-zone": "orange"}},
        {"name": "TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"modif": "$.triggerEventName"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_TRIGGER_EVENT"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "USER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.id"}]}
			, "meta": {"gdpr-zone": "orange"}},
        {"name": "OFFICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.officeId"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WORKSTATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.id"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WORKSTATION_LOC_CITY", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.cityCode"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WORKSTATION_LOC_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.airportCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WORKSTATION_LOC_TERMINAL", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.terminalId"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WORKSTATION_LOC_AREA", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.areaCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT_AREA"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "WORKSTATION_LOC_BUILDING", "column-type": "strColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.buildingId"}]}
			, "meta": {"gdpr-zone": "green"}},
        {"name": "BAG_GROUP_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "meta": {"gdpr-zone": "orange"}},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})","fk": [{"table": "FACT_BAG_GROUP_HISTO", "column": "BAG_GROUP_ID"}]},
        {"name": "WORKSTATION_LOC_TERMINAL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"user": "$.workstation.fullLocation.airportCode"}, {"user": "$.workstation.fullLocation.terminalId"}]}, "expr": ${HashSIdCheckEndNotNull}
      , "fk": [{"table": "DIM_AIRPORT_TERMINAL", "column": "AIRPORT_TERMINAL_ID"}]
			, "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
			, "meta": {"description": {"value": "Version of the DCS Baggage message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}
			, "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}
			, "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}
			, "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "DIM_AIRLINE",
    "mapping": {
	  "description": {"description": "Lists the airlines used for bag tag issuance and operating carrier of bag leg deliveries", "granularity": "1 airline"},
      "merge": {
        "key-columns": ["AIRLINE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"carrier": "$.mainResource.current.image.bags[*].bagTag.issuingCarrier"}]},
        {"blocks": [{"carrier": "$.mainResource.current.image.bags[*].legs[*].operatingFlight.carrierCode"}]}
      ],
      "columns": [
        {"name": "AIRLINE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"carrier": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "AIRLINE_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"carrier": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_BAG_TYPE",
    "mapping": {
	  "description": {"description": "Lists the types of individual bags", "granularity": "1 bag type"},
      "merge": {
        "key-columns": ["BAG_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"type": "$.mainResource.current.image.bags[*].bagType"}]}
      ],
      "columns": [
        {"name": "BAG_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"type": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "BAG_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"type": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT",
    "mapping": {
	  "description": {"description": "Lists the airports used in departure/arrival airports of bag itineraries, leg deliveries and excess baggage items", "granularity": "1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"airport": "$.mainResource.current.image.bags[*].bagTag.finalDestination"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.bags[*].linkedBagTag.finalDestination"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.bags[*].lastSeenLocation.trackedLocation.airportCode"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.bags[*].legs[*].boardPoint.iataCode"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.bags[*].legs[*].offPoint.iataCode"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.bags[*].legs[*].checkinLocation.trackedLocation.airportCode"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.excessCharges[*].calculations[*].boardPoint.iataCode"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.excessCharges[*].calculations[*].offPoint.iataCode"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.excessCharges[*].calculations[*].items[*].boardPoint.iataCode"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.excessCharges[*].calculations[*].items[*].offPoint.iataCode"}]},
        {"blocks": [{"airport": "$.mainResource.current.image.lastModification.user.workstation.fullLocation.airportCode"}]}
      ],
      "columns": [
        {"name": "AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"airport": "$.value"}]}, "expr": "hashS({0})"
			, "meta": {"description": {"value": "Hash of IATA airport code", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"airport": "$.value"}]}
			, "meta": {"description": {"value": "IATA airport code", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT_TERMINAL",
    "mapping": {
	  "description": {"description": "Lists the airport terminals used in bag tracking logs and system user logs", "granularity": "1 terminal in 1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_TERMINAL_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"loc": "$.mainResource.current.image.bags[*].lastSeenLocation.trackedLocation"}, {"id" : "$.terminalId"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.bags[*].legs[*].checkinLocation.trackedLocation"}, {"id" : "$.terminalId"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.lastModification.user.workstation.fullLocation"}, {"id" : "$.terminalId"}]}
      ],
      "columns": [
        {"name": "AIRPORT_TERMINAL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"loc": "$.airportCode"}, {"loc": "$.terminalId"}]}, "expr": "hashS({0})"
			, "meta": {"description": {"value": "Hash of the terminal (airport-terminal)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.airportCode"}]}},
        {"name": "TERMINAL", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.terminalId"}]}},
        {
          "name": "AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"loc": "$.airportCode"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT_AREA",
    "mapping": {
	  "description": {"description": "Lists the airport areas/zones used in bag tracking logs and system user logs", "granularity": "1 airport area"},
      "merge": {
        "key-columns": ["AIRPORT_AREA_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"loc": "$.mainResource.current.image.bags[*].lastSeenLocation.trackedLocation.areaCode"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.bags[*].legs[*].checkinLocation.trackedLocation.areaCode"}]},
        {"blocks": [{"loc": "$.mainResource.current.image.lastModification.user.workstation.fullLocation.areaCode"}]}
      ],
      "columns": [
        {"name": "AIRPORT_AREA_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"loc": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "AIRPORT_AREA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.value"}]}},
        {
          "name": "AIRPORT_AREA_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"loc": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the airport area code", "rule": "replace"}, "example": {"value": "Airport Check In", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  "prefiller": [{
    "data-source-key": "AIRPORT_AREA",
    "column-filler" : [
      {"dim-col" : "AIRPORT_AREA_ID", "src-col" : "CODE"},
      {"dim-col" : "AIRPORT_AREA_CODE", "src-col" : "CODE"},
      {"dim-col" : "AIRPORT_AREA_LABEL", "src-col" : "LABEL"}
    ]
  }]
  },
  {
    "name": "DIM_BAG_SOURCE_SYSTEM",
    "mapping": {
	  "description": {"description": "Lists the source systems for bag tags", "granularity": "1 source system"},
      "merge": {
        "key-columns": ["BAG_SOURCE_SYSTEM_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dev": "$.mainResource.current.image.bags[*].systemSource"}]},
        {"blocks": [{"dev": "$.mainResource.current.image.bags[*].bagTag.systemSource"}]},
        {"blocks": [{"dev": "$.mainResource.current.image.bags[*].linkedBagTag.systemSource"}]}
      ],
      "columns": [
        {"name": "BAG_SOURCE_SYSTEM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dev": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "BAG_SOURCE_SYSTEM", "column-type": "strColumn", "sources": {"blocks": [{"dev": "$.value"}]}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_TRIGGER_EVENT",
    "mapping": {
	  "description": {"description": "Lists the different trigger events which updated the bag group", "granularity": "1 event"},
      "merge": {
        "key-columns": ["TRIGGER_EVENT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.lastModification.triggerEventName"}]}
      ],
      "columns": [
        {"name": "TRIGGER_EVENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "TRIGGER_EVENT", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_SPECIAL_BAG_HANDLING_TYPE",
    "mapping": {
	  "description": {"description": "Lists the types of special bag handling", "granularity": "1 special handling type"},
      "merge": {
        "key-columns": ["SPECIAL_BAG_HANDLING_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.bags[*].legs[*].specialBagHandlingItems[*].label"}]}
      ],
      "columns": [
        {"name": "SPECIAL_BAG_HANDLING_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"
			, "meta": {"description": {"value": "Hashed type of special bag handling", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SPECIAL_BAG_HANDLING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}
			, "meta": {"description": {"value": "The type of special bag handling", "rule": "replace"}, "gdpr-zone": "green"}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_SERVICE",
    "mapping": {
	  "description": {"description": "Lists the service codes used for special bag handling", "granularity": "1 service"},
      "merge": {
        "key-columns": ["SERVICE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"service": "$.mainResource.current.image.bags[*].legs[*].specialBagHandlingItems[*].service"},{"dim": "$.code"}]}
      ],
      "columns": [
        {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}},
        {"name": "SERVICE_SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"service": "$.subType"}]}},
        {"name": "SERVICE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"service": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the service code", "rule": "replace"}, "example": {"value": "Excess baggage", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "SSR_CODE",
      "column-filler" : [
        {"dim-col" : "SERVICE_ID", "src-col" : "CODE"},
        {"dim-col" : "SERVICE_CODE", "src-col" : "CODE"},
        {"dim-col" : "SERVICE_SUBTYPE", "src-col" : "SERVICE_SUBTYPE"},
        {"dim-col" : "SERVICE_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_PAYMENT_STATUS",
    "mapping": {
	  "description": {"description": "Lists the payment statuses of excess baggage charges", "granularity": "1 payment status"},
      "merge": {
        "key-columns": ["PAYMENT_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.excessCharges[*].paymentStatus"}]}
      ],
      "columns": [
        {"name": "PAYMENT_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "PAYMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_EXCESS_BAGGAGE_ITEM_TYPE",
    "mapping": {
	  "description": {"description": "Lists the types of excess baggage item types", "granularity": "1 item type"},
      "merge": {
        "key-columns": ["EXCESS_BAGGAGE_ITEM_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.excessCharges[*].calculations[*].items[*].itemType"}]}
      ],
      "columns": [
        {"name": "EXCESS_BAGGAGE_ITEM_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"
			, "meta": {"description": {"value": "Hash of the excess baggage item type", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "EXCESS_BAGGAGE_ITEM_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}
			, "meta": {"description": {"value": "The type of an excess baggage item", "rule": "replace"}, "gdpr-zone": "green"}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_CABIN",
    "mapping": {
	  "description": {"description": "Lists the cabins in which bags get transported/delivered", "granularity": "1 cabin"},
      "merge": {
        "key-columns": ["CABIN_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.bags[*].legs[*].travelClassCode"}]}
      ],
      "columns": [
        {"name": "CABIN_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "CABIN_CODE", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_BAG_ACCEPTANCE_STATUS",
    "mapping": {
	  "description": {"description": "Lists the bag acceptance statuses for bag leg deliveries", "granularity": "1 bag acceptance status"},
      "merge": {
        "key-columns": ["BAG_ACCEPTANCE_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.bags[*].legs[*].acceptanceStatus"}]}
      ],
      "columns": [
        {"name": "BAG_ACCEPTANCE_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "BAG_ACCEPTANCE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_ACTIVATION_STATUS",
    "mapping": {
	  "description": {"description": "Lists the activation statuses for bag leg deliveries", "granularity": "1 activation status"},
      "merge": {
        "key-columns": ["ACTIVATION_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.bags[*].legs[*].activationStatus"}]}
      ],
      "columns": [
        {"name": "ACTIVATION_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"},
        {"name": "ACTIVATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}},
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },

  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_DCSPAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_GROUP_ID", "PASSENGER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.DCSBAG-DCSPAX"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"root1":"$.mainResource.current.image.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX-DCSBAG correlation information.",
        "granularity": "1 PAX-BAG",
        "links": ["???"]
      }
    }
  },

  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_DCSPAX",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_GROUP_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.mainResource.current.image"},
          {"bg": "$.bagsGroup"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"bg": "$.responsiblePassenger.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"bg": "$.responsiblePassenger.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"bg":"$.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX-DCSBAG correlation information.",
        "granularity": "1 PAX-BAG",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_OWNER_PASSENGER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_ID", "PASSENGER_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.DCSBAG-DCSPAX"},
          {"corr": "$.correlations[*]"}
          {"item": "$.corrDcspaxDcsbag.items[*]"},
          {"bag": "$.correlatedLegs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"bag": "$.bagId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "RESPONSIBLE_PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"bag": "$.bagId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_RESPONSIBLE_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"root1":"$.mainResource.current.image.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_SEGMENT_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX_SEG_DEL-BAG_LEG_DEL",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_OWNER_PASSENGER",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.mainResource.current.image"},
          {"bag": "$.bags[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"bag": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_OWNER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"bag": "$.owner.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": "hashM("${ifFirstValueNullTakeSecondValue}")"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"bag": "$.id"}]}},
        {"name": "REFERENCE_KEY_PASSENGER_OWNER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"bag": "$.owner.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": ${ifFirstValueNullTakeSecondValue}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "column-type": "strColumn",  "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_SEGMENT_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX_SEG_DEL-BAG_LEG_DEL",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_LEG_DEL_DCSPAX_SEGMENT_DEL",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_LEG_DELIVERY_ID", "SEGMENT_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.DCSBAG-DCSPAX"},
          {"pax": "$.correlations[*]"},
          {"corr": "$.corrDcspaxDcsbag.items[*]"},
          {"item": "$.correlatedLegs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"pax": "$.toId"}, {"corr": "$.dcsPassengerSegmentDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.bagId"}, {"item": "$.bagLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"pax": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"item": "$.bagId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"pax": "$.toId"}, {"corr": "$.dcsPassengerSegmentDeliveryId"}]}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.bagId"}, {"item": "$.bagLegDeliveryId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"pax": "$.toId"}]}},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"item": "$.bagId"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"root1":"$.mainResource.current.image.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_SEGMENT_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX_SEG_DEL-BAG_LEG_DEL",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_LEG_DEL_DCSPAX_SEGMENT_DEL",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_LEG_DELIVERY_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.mainResource.current.image"},
          {"bag": "$.bags[*]"},
          {"leg": "$.legs[*]"},
          {"seg": "$.flightSegmentRefs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"bag": "$.owner.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": "hashM("${ifFirstValueNullTakeSecondValue}")"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"bag": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"},{"bag": "$.owner.id"}, {"leg": "$.operatingFlight.carrierCode"}, {"leg": "$.operatingFlight.flightNumber"}, {"seg": "$.id"}]}, "expr": "hashM("${getFullIdithCorrectPassengerIdSegId}")"},
        {"name": "RESPONSIBLE_PASSENGER_ID", "column-type": "binaryStrColumn",  "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"},{"bag": "$.owner.id"}, {"leg": "$.operatingFlight.carrierCode"}, {"leg": "$.operatingFlight.flightNumber"}, {"seg": "$.id"}]}, "expr": ${getFullIdithCorrectPassengerIdSegId}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}]}},
        {"name": "REFERENCE_KEY_PASSENGER_OWNER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"bag": "$.owner.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": ${ifFirstValueNullTakeSecondValue}},
        {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "column-type": "strColumn","sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"bag": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_SEGMENT_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX_SEG_DEL-BAG_LEG_DEL",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_LEG_DEL_DCSPAX_LEG_DEL",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_LEG_DELIVERY_ID", "LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.DCSBAG-DCSPAX"},
          {"corr": "$.correlations[*]"},
          {"items": "$.corrDcspaxDcsbag.items[*]"},
          {"legs": "$.correlatedLegs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"items": "$.dcsPassengerSegmentDeliveryId"}, {"legs": "$.dcsPassengerLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"legs": "$.bagId"}, {"legs": "$.bagLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},

        {"name": "REFERENCE_KEY_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"items": "$.dcsPassengerSegmentDeliveryId"}, {"legs": "$.dcsPassengerLegDeliveryId"}]}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"legs": "$.bagId"}, {"legs": "$.bagLegDeliveryId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},

        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"root1":"$.mainResource.current.image.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_LEG_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX_LEG_DEL-BAG_LEG_DEL",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_DCSBAG_LEG_DEL_DCSPAX_LEG_DEL",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["DCSBAG_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "BAG_LEG_DELIVERY_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.mainResource.current.image"},
          {"bag": "$.bags[*]"},
          {"leg": "$.legs[*]"},
          {"seg": "$.flightSegmentRefs[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"bag": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}, {"leg": "$.operatingFlight.carrierCode"}, {"leg": "$.operatingFlight.flightNumber"}, {"seg": "$.id"}, {"leg": "$.id"}]}, "expr":  "hashM("${getFullIdithCorrectPassengerIdSegId}")"},
        {"name": "RESPONSIBLE_PASSENGER_ID", "column-type": "binaryStrColumn",  "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}, {"leg": "$.operatingFlight.carrierCode"}, {"leg": "$.operatingFlight.flightNumber"}, {"seg": "$.id"}, {"leg": "$.id"}]},"expr":  ${getFullIdithCorrectPassengerIdSegId}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}, {"leg": "$.id"}]}},
        {"name": "REFERENCE_KEY_PASSENGER_OWNER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"bag": "$.owner.id"},{"base": "$.bagsGroup.responsiblePassenger.id"}]}, "expr": ${ifFirstValueNullTakeSecondValue}},
        {"name": "REFERENCE_KEY_PASSENGER_RESPONSIBLE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.bagsGroup.responsiblePassenger.id"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"base":"$.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains DCSPAX_LEG_DELIVERY-DCSBAG_BAG_LEG_DELIVERY correlation.",
        "granularity": "1 PAX_LEG_DEL-BAG_LEG_DEL",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_RESERVATION_BAG_GROUP_HISTO", // PNR-BAG 1/3
    "table-selectors": [["DCSBAG_ACTIVE","PNR_ACTIVE","DIH_CORRELATION"],["DCSBAG_ACTIVE","PNR_PASSIVE"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "BAG_GROUP_ID", "VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.DCSBAG-PNR"}, {"corr": "$.correlations[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"root1":"$.mainResource.current.image.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSBAG-PNR correlation information as seen by DCSBAG.",
      "granularity": "1 BAG_GROUP-PNR",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_RESERVATION_BAG_GROUP_HISTO", // PNR-BAG 1/3
    "table-selectors": [["DCSBAG_ACTIVE","PNR_ACTIVE","DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "BAG_GROUP_ID",  "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root1": "$"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "CORR_BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green", "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green", "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "gdpr-zone": "green", "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSBAG-PNR correlation information as seen by DCSBAG.",
      "granularity": "1 BAG_GROUP-PNR",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVELER_BAG_HISTO", //PNR - BAG 2/3
    "table-selectors": [["DCSBAG_ACTIVE","PNR_ACTIVE","DIH_CORRELATION"],["DCSBAG_ACTIVE","PNR_PASSIVE"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVELER_ID", "BAG_ID", "VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.DCSBAG-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrDcsbagPnr.items[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"items": "$.bagId"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_TRAVELER", "column-type": "strColumn", "sources": {"blocks": [{"items": "$.pnrTravelerId"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_BAG", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"items": "$.bagId"}]}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"root1":"$.mainResource.current.image.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"}
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSBAG-PNR_traveler correlation information as seen by DCSBAG.",
      "granularity": "1 BAG-PNR_TRAVELER",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVELER_BAG_HISTO", //PNR - BAG 2/3
    "table-selectors": [["DCSBAG_ACTIVE","PNR_ACTIVE","DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVELER_ID", "CORR_BAG_LEG_DELIVERY_BAG_OWNER_ID","VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root1": "$"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "CORR_BAG_LEG_DELIVERY_BAG_OWNER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
//        {"name": "INTERNAL_CORR_BAG_OWNER_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVELER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_CORR_BAG", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green", "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green", "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"}
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "gdpr-zone": "green", "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSBAG-PNR_traveler correlation information as seen by DCSBAG.",
      "granularity": "1 BAG-PNR_TRAVELER",
      "links": ["???"]
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO",
    "table-selectors": [["DCSBAG_ACTIVE","PNR_ACTIVE", "DIH_CORRELATION"],["DCSBAG_ACTIVE","PNR_PASSIVE"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "BAG_LEG_DELIVERY_ID", "VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.DCSBAG-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrDcsbagPnr.items[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"items": "$.pnrAirSegmentId"}, {"items": "$.pnrTravelerId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"items": "$.bagId"}, {"items": "$.bagLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {"blocks": [{"items": "$.pnrAirSegmentId"}, {"items": "$.pnrTravelerId"}]}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_BAG_LEG_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"items": "$.bagId"}, {"items": "$.bagLegDeliveryId"}]}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.fromFullVersion"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.lastModification.dateTime"}]}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {"blocks": [{"root1":"$.mainResource.current.image.bagsGroup.id"}]}, "meta": {"gdpr-zone": "green"}, "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}, "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSBAG_LEGDEL-PNR_AIR_SEG_PAX correlation information as seen by DCSBAG.",
      "granularity": "1 BAG_LEG_DELIVERY-PNR_AIR_SEGMENT_PAX",
      "links": ["???"]
    }
  }
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_BAG_LEG_DELIVERY_HISTO",
    "table-selectors": [["DCSBAG_ACTIVE","PNR_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "CORR_BAG_LEG_DELIVERY_ID", "VERSION_RESERVATION", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"root1": "$"}, {"base": "$.correlatedResourcesCurrent.DCSBAG-PNR"}, {"corr": "$.correlations[*]"}, {"items": "$.corrDcsbagPnr.items[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "CORR_BAG_LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_BAG_GROUP", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_CORR_BAG_LEG_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "INTERNAL_IS_LAST_RECEIVED", "column-type": "booleanColumn", "sources": {}},
        {"name": "IS_PURGE_EVENT", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green", "expr": "isnull({0})"},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "gdpr-zone": "green", "post-expr": "INTERNAL_IS_LAST_RECEIVED AND (!IS_PURGE_EVENT)"},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "gdpr-zone": "green", "post-expr": "if(!IS_PURGE_EVENT,INTERNAL_DATE_END,DATE_BEGIN)"}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "description": {
      "description": "It contains DCSBAG_LEGDEL-PNR_AIR_SEG_PAX correlation information as seen by DCSBAG.",
      "granularity": "1 BAG_LEG_DELIVERY-PNR_AIR_SEGMENT_PAX",
      "links": ["???"]
    }
  }
]
