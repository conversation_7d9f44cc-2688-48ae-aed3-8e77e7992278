[[{"${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/dcspax.yaml": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_6_yaml_swagger/input/dcspax.yaml"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_01Traveler_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_01Traveler_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/dscpax-pax1_v3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch3/data/dscpax-pax1_v3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/validation/sample_1/app.conf": ["${BASE}/core/target/scala-2.12/test-classes/validation/sample_1/app.conf"], "${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/format_delta/data/expected_results/FACT_TABLE1.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/format_delta/data/expected_results/FACT_TABLE1.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511306_v0_load-date=11-21-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511306_v0_load-date=11-21-2022.json"], "${BASE}/core/src/test/resources/datasets/weight_conversion/data/expected_results/FACT_BAG_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/data/expected_results/FACT_BAG_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/data_types/data/recordv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/data_types/data/recordv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/weight_conversion/data/expected_results/FACT_BAG_GROUP_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/data/expected_results/FACT_BAG_GROUP_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/expected_results/FACT_RESERVATION.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch1/expected_results/FACT_RESERVATION.csv"], "${BASE}/core/src/test/resources/datasets/proration/data/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/purge/data/step1/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/views/pnr_simplified/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/mapping.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-04-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-04-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/views/pnr_simplified/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/README.md"], "${BASE}/core/src/test/resources/config/application-with-event-grid.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/application-with-event-grid.conf"], "${BASE}/core/src/test/resources/datasets/18/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/18/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/views/sample_1/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/README.md"], "${BASE}/core/src/test/resources/config/samples/cartesian_product.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/samples/cartesian_product.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/format_delta/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/format_delta/mapping.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-all-doc-number.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/1/data/sample-ticketing-reference-with-all-doc-number.json"], "${BASE}/core/src/test/resources/datasets/13/data/pnr/PNR_aa_ey_uat_22USLC_v15_load-date=08-16-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/13/data/pnr/PNR_aa_ey_uat_22USLC_v15_load-date=08-16-2022.json"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_variables/data/expected_results/INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/expected_results/INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/8/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/15/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/15/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/src/test/resources/datasets/proration/data/MH-PRD_load-date=01-10-2024.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/MH-PRD_load-date=01-10-2024.json"], "${BASE}/core/src/test/resources/views/sample_5/expected/jdf_meta.json": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_5/expected/jdf_meta.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/dscpax-pnr_v1_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/data/dscpax-pnr_v1_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/views/sample_1/expected/copy_table_snowflake.sql": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/copy_table_snowflake.sql"], "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_1.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/order_sample_1.conf"], "${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_RESERVATION.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/expected_results/FACT_RESERVATION.csv"], "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_results_not_found.csv": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/expected/lookup_results_not_found.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/currency_conversion/simplified_dcsbag.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/simplified_dcsbag.conf"], "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/views/sample_5/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_5/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest_correlation/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/README.md"], "${BASE}/core/src/test/resources/datasets/15/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/15/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_plus_minus_tables.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_plus_minus_tables.conf"], "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/objectfilter/FACT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/objectfilter/FACT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_BAG_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_BAG_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v2-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v2-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/simple_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/simple_mapping.conf"], "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_00Reservation_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_00Reservation_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/18/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/18/README.md"], "${BASE}/core/src/test/resources/datasets/source_correlation/dcspax.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/dcspax.conf"], "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_SKD_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_SKD_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/raw_data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch1/raw_data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/5/data/batch3/file6_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch3/file6_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/config/tktemd.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/config/tktemd.conf"], "${BASE}/core/src/test/resources/datasets/17/data/expected_results/FACT_SERVICE_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/17/data/expected_results/FACT_SERVICE_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/multi_roots/data/expected_results/DIM_CARRIER.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/expected_results/DIM_CARRIER.csv"], "${BASE}/core/src/test/resources/views/sample_5/expected/create_table_delta.sql": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_5/expected/create_table_delta.sql"], "${BASE}/core/src/test/resources/datasets/optimize/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/data/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_DCSBAG_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_DCSBAG_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/partial_processing/application-test1.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/partial_processing/application-test1.conf"], "${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/exchange_rate/model.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/model.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/14/data/pnr/PNR_ab_ey_uat_6I2QQ4_v5_load-date=11-21-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/pnr/PNR_ab_ey_uat_6I2QQ4_v5_load-date=11-21-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/partial_processing/application-test2.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/partial_processing/application-test2.conf"], "${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_pnr_tkt_latest_schema.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/correlation_pnr_tkt_latest_schema.conf"], "${BASE}/core/src/test/resources/datasets/8/data/correlationWPASDF_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/correlationWPASDF_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_in_all_images.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_in_all_images.csv"], "${BASE}/core/src/test/resources/stackable/data/expected_results/FACT_OK_HISTO_1.csv": ["${BASE}/core/target/scala-2.12/test-classes/stackable/data/expected_results/FACT_OK_HISTO_1.csv"], "${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/currency_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/12/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/source_variables/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/config/README.md": ["${BASE}/core/target/scala-2.12/test-classes/config/README.md"], "${BASE}/core/src/test/resources/views/sample_2/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_2/README.md"], "${BASE}/core/src/test/resources/views/sample_1/expected/jdf_views.sql": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/jdf_views.sql"], "${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/app.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_6_yaml_swagger/input/app.conf"], "${BASE}/core/src/test/resources/datasets/external_data/simplified_dcsbag_with_external_data.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/external_data/simplified_dcsbag_with_external_data.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/revised_pit_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/revised_pit_mapping.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/input/KEY A-v2-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/input/KEY A-v2-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/b/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/b/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX.csv"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/mapping.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/expected_results/DIM_PASSENGER_TYPE_2.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_2Input/data/expected_results/DIM_PASSENGER_TYPE_2.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/README.md"], "${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_02Segment_Booking_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_02Segment_Booking_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/11/simple_raw_vault_mapping_no_vault.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/simple_raw_vault_mapping_no_vault.conf"], "${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_6_yaml_swagger/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/views/sample_4/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_4/mapping.conf"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/multi_roots/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/README.md"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/stackable/dummy_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/stackable/dummy_mapping.conf"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/dscpax-pax1_v1_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch1/data/dscpax-pax1_v1_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_11_05_30_45_load-date=03-11-2025.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_11_05_30_45_load-date=03-11-2025.json"], "${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_01_05_30_45_load-date=03-01-2025.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_01_05_30_45_load-date=03-01-2025.json"], "${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/views/model_comp/expected/case3": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case3"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/input/KEY A-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/input/KEY A-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/arrayfilter/FACT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/arrayfilter/FACT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/data_types/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/data_types/mapping.conf"], "${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v4_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v4_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/views/sample_1/expected/create_table_delta.sql": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/create_table_delta.sql"], "${BASE}/core/src/test/resources/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v11_load-date=19-08-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v11_load-date=19-08-2022.json"], "${BASE}/core/src/test/resources/datasets/cartesian_product/simplified_pnr.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/cartesian_product/simplified_pnr.conf"], "${BASE}/core/src/test/resources/datasets/13/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/13/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_04Ticketing_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_04Ticketing_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ad_ey_uat_7492400944169_v3_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/tkt/TKT_ad_ey_uat_7492400944169_v3_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/views/sample_7/input/dcspax.yaml": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_7/input/dcspax.yaml"], "${BASE}/core/src/test/resources/datasets/correlation_purge/config/corr.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/config/corr.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/14/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/14/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_8/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_8/README.md"], "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping.conf"], "${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/dcspax.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_6_yaml_swagger/input/dcspax.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/config/multi_roots/sample_config.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/multi_roots/sample_config.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/correlation_dcspax_pnr.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/correlation_dcspax_pnr.conf"], "${BASE}/core/src/test/resources/datasets/16/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/README.md"], "${BASE}/core/src/test/resources/views/sample_1/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/mapping.conf"], "${BASE}/core/src/test/resources/datasets/17/data/service_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/17/data/service_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/purge/data/step2/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step2/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_11/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_11/README.md"], "${BASE}/core/src/test/resources/stackable/data/expected_results_failure/FACT_OK_HISTO_1.csv": ["${BASE}/core/target/scala-2.12/test-classes/stackable/data/expected_results_failure/FACT_OK_HISTO_1.csv"], "${BASE}/core/src/test/resources/datasets/18/data/b/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/18/data/b/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/expressions/model.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/model.conf"], "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/11/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/README.md"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/bag_load-date=01-01-2099.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_2_j2s/data/bag_load-date=01-01-2099.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/external_data/data/expected_results/FACT_BAG_GROUP_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/external_data/data/expected_results/FACT_BAG_GROUP_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/expected_results/FACT_AIR_SEGMENT_PAX.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch2/expected_results/FACT_AIR_SEGMENT_PAX.csv"], "${BASE}/core/src/test/resources/views/sample_2/expected/doc.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_2/expected/doc.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step3/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/expected_results/step3/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationEMD_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/correlationEMD_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/cartesian_product/data/expected_results/FACT_PRODUCT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/cartesian_product/data/expected_results/FACT_PRODUCT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step2/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/expected_results/step2/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-dupe-primary-doc-number.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/1/data/sample-ticketing-reference-with-dupe-primary-doc-number.json"], "${BASE}/core/src/test/resources/views/sample_1/app.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/app.conf"], "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/nofilter/FACT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/nofilter/FACT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/source_variables/simple_raw_vault_mapping_no_vault.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/simple_raw_vault_mapping_no_vault.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/12/data/tkt/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/weight_conversion/weight_fail_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/weight_fail_mapping.conf"], "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/correlationfilter/FACT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/correlationfilter/FACT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/corr_simplified/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/mapping.conf"], "${BASE}/core/src/test/resources/datasets/5/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/5/README.md"], "${BASE}/core/src/test/resources/datasets/expressions/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/12/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/README.md"], "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/data/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json"], "${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_aa_ey_uat_N389UA_v6_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/pnr/PNR_aa_ey_uat_N389UA_v6_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/external_data/data/expected_results/FACT_BAG_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/external_data/data/expected_results/FACT_BAG_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_1Input/data/expected_results/DIM_PASSENGER_TYPE_1.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_1Input/data/expected_results/DIM_PASSENGER_TYPE_1.csv"], "${BASE}/core/src/test/resources/views/sample_7/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_7/README.md"], "${BASE}/core/src/test/resources/datasets/source_correlation/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/README.md"], "${BASE}/core/src/test/resources/datasets/latest/simple_mapping_no_vault_latest.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/simple_mapping_no_vault_latest.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_plus_minus_columns.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_plus_minus_columns.conf"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/src/test/resources/config/samples/pnr.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/samples/pnr.conf"], "${BASE}/core/src/test/resources/views/model_comp/expected/case5": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case5"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_srcAdditionalCols/data/PASSENGER_TYPE.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_srcAdditionalCols/data/PASSENGER_TYPE.csv"], "${BASE}/core/src/test/resources/datasets/optimize/simple_mapping_zorder.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/simple_mapping_zorder.conf"], "${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ad_ey_pdt_USCK5S_6072160023101_v16_load-date=07-04-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/PNR_ad_ey_pdt_USCK5S_6072160023101_v16_load-date=07-04-2022.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/datasets/8/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/purge/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/10/simple_raw_vault_mapping_no_vault_multi_section.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/10/simple_raw_vault_mapping_no_vault_multi_section.conf"], "${BASE}/core/src/test/resources/datasets/10/data/expected_results/FACT_MULTI_SECTION.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/10/data/expected_results/FACT_MULTI_SECTION.csv"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/DIM_PASSENGER_TYPE_5.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/DIM_PASSENGER_TYPE_5.csv"], "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/views/sample_11/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_11/mapping.conf"], "${BASE}/core/src/test/resources/datasets/proration/data/expected_results/FACT_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/expected_results/FACT_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/stackable/error_handling_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/stackable/error_handling_mapping.conf"], "${BASE}/core/src/test/resources/datasets/proration/extdata/input_optd_por_public_sample.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/proration/extdata/input_optd_por_public_sample.csv"], "${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationWPASDF_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/correlationWPASDF_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/nullvalues/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/nullvalues/README.md"], "${BASE}/core/src/test/resources/views/sample_7/input/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_7/input/mapping.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_3.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/order_sample_3.conf"], "${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-primary-doc-number.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/1/data/sample-ticketing-reference-with-primary-doc-number.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_3_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_3_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv"], "${BASE}/core/src/test/resources/views/model_comp/expected/case4": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case4"], "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_DCSPAX_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_DCSPAX_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE.csv"], "${BASE}/core/src/test/resources/datasets/recordsfilter/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/mapping.conf"], "${BASE}/core/src/test/resources/validation/sample_9/README.md": ["${BASE}/core/target/scala-2.12/test-classes/validation/sample_9/README.md"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_2_CURRENCY_CONVERSIONS_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_2_CURRENCY_CONVERSIONS_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/cartesian_product/data/PNR_6Q5JNY_v10_load-date=01-01-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/cartesian_product/data/PNR_6Q5JNY_v10_load-date=01-01-2023.json"], "${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/14/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/14/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_PNR_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_PNR_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/application.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/application.conf"], "${BASE}/core/src/test/resources/config/novault-integration-test.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/novault-integration-test.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v12_load-date=19-08-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v12_load-date=19-08-2022.json"], "${BASE}/core/src/test/resources/datasets/json_processor/generic_item_example.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/json_processor/generic_item_example.json"], "${BASE}/core/src/test/resources/datasets/2/data/complex-ticketing-reference.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/2/data/complex-ticketing-reference.json"], "${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/template.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/template.json"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/13/data/tkt/TKT_aa_ey_uat_0142172648268_v9_load-date=08-16-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/13/data/tkt/TKT_aa_ey_uat_0142172648268_v9_load-date=08-16-2022.json"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_dimAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_3.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_dimAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_3.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/13/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/13/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_srcAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_4.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_srcAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_4.csv"], "${BASE}/core/src/test/resources/datasets/single_table/single_table_model.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/single_table/single_table_model.conf"], "${BASE}/core/src/test/resources/stackable/data/expected_results_failure/FACT_OK_HISTO_2.csv": ["${BASE}/core/target/scala-2.12/test-classes/stackable/data/expected_results_failure/FACT_OK_HISTO_2.csv"], "${BASE}/core/src/test/resources/views/sample_2/expected/jdf_meta.json": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_2/expected/jdf_meta.json"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2020.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/data/bag_load-date=01-01-2020.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch1/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_02_05_30_45_load-date=03-02-2025.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_02_05_30_45_load-date=03-02-2025.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step1/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/expected_results/step1/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/16/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/src/test/resources/datasets/latest/simple_mapping_no_vault_latest_schema.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/simple_mapping_no_vault_latest_schema.conf"], "${BASE}/core/src/test/resources/datasets/10/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/10/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/17/views/expected_delta_create_multi_root_mapping.sql": ["${BASE}/core/target/scala-2.12/test-classes/datasets/17/views/expected_delta_create_multi_root_mapping.sql"], "${BASE}/core/src/test/resources/config/application-purge.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/application-purge.conf"], "${BASE}/core/src/test/resources/validation/sample_1/expected_results/test_results.csv": ["${BASE}/core/target/scala-2.12/test-classes/validation/sample_1/expected_results/test_results.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/expressions/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/README.md"], "${BASE}/core/src/test/resources/datasets/proration/simplified_tktemd.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/proration/simplified_tktemd.conf"], "${BASE}/core/src/test/resources/datasets/14/data/pnr/PNR_aa_ey_uat_6I2QQ4_v4_load-date=11-21-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/pnr/PNR_aa_ey_uat_6I2QQ4_v4_load-date=11-21-2022.json"], "${BASE}/core/src/test/resources/datasets/exchange_rate/data/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/single_table/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/single_table/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/17/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/17/README.md"], "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/json_processor/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/json_processor/README.md"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/src/test/resources/config/application-with-snowflake.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/application-with-snowflake.conf"], "${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511307_v0_load-date=11-21-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511307_v0_load-date=11-21-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/weight_conversion/simplified_dcsbag.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/simplified_dcsbag.conf"], "${BASE}/core/src/test/resources/datasets/8/data/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/5/data/batch1/file1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch1/file1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/exchange_rate/data/expected_results/EXCHANGE_RATE_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/expected_results/EXCHANGE_RATE_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/optimize/data/correlationWPASDF_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/correlationWPASDF_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv"], "${BASE}/core/src/test/resources/stackable/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/stackable/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/PNR_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/datasets/multi_roots/simple_raw_vault_mapping_no_vault_dim.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/simple_raw_vault_mapping_no_vault_dim.conf"], "${BASE}/core/src/test/resources/views/sample_4/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_4/README.md"], "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION.csv"], "${BASE}/core/src/test/resources/datasets/13/data/pnr/PNR_ab_ey_uat_22USLC_v20_load-date=08-16-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/13/data/pnr/PNR_ab_ey_uat_22USLC_v20_load-date=08-16-2022.json"], "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_BAGS_GROUP_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_BAGS_GROUP_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/nullvalues/data/expected_results/FACT_NULLVALUES.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/nullvalues/data/expected_results/FACT_NULLVALUES.csv"], "${BASE}/core/src/test/resources/views/sample_4/not_failing_mapping_with_variables.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_4/not_failing_mapping_with_variables.conf"], "${BASE}/core/src/test/resources/views/sample_1/expected/copy_table_delta.sql": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/copy_table_delta.sql"], "${BASE}/core/src/test/resources/datasets/purge/data/step2/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step2/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/expressions/data/bagsmanylegs_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/bagsmanylegs_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv"], "${BASE}/core/src/test/resources/datasets/8/data/correlationEMD_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/correlationEMD_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3bis_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3bis_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/views/sample_11/app.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_11/app.conf"], "${BASE}/core/src/test/resources/datasets/multi_roots/data/expected_results/DIM_POINT_OF_SALE.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/expected_results/DIM_POINT_OF_SALE.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/README.md"], "${BASE}/core/src/test/resources/views/sample_7/expected/jdf_meta.json": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_7/expected/jdf_meta.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/simple_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/simple_mapping.conf"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_1_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_1_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv"], "${BASE}/core/src/test/resources/datasets/format_delta/data/delta/part-00000-ab18c1bd-10cd-4650-bab9-38eb85452f0d-c000.snappy.parquet": ["${BASE}/core/target/scala-2.12/test-classes/datasets/format_delta/data/delta/part-00000-ab18c1bd-10cd-4650-bab9-38eb85452f0d-c000.snappy.parquet"], "${BASE}/core/src/test/resources/views/sample_1/expected/doc.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/doc.md"], "${BASE}/core/src/test/resources/datasets/18/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/18/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_pnr_tkt_latest.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/correlation_pnr_tkt_latest.conf"], "${BASE}/core/src/test/resources/views/model_comp/expected/case6": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case6"], "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/datasets/sfpush/expected_results/data/FACT_HISTO_MIRROR.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/expected_results/data/FACT_HISTO_MIRROR.csv"], "${BASE}/core/src/test/resources/datasets/source_variables/data/a/6JDA9D-2023-01-18_v14_load-date=10-30-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/a/6JDA9D-2023-01-18_v14_load-date=10-30-2022.json"], "${BASE}/core/src/test/resources/datasets/purge/data/step1/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_7/expected/doc_enriched.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_7/expected/doc_enriched.md"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch1/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/sfpush/application.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/application.conf"], "${BASE}/core/src/test/resources/views/sample_1/expected/star-schema_TEST_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/star-schema_TEST_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/optimize/data/correlationEMD_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/correlationEMD_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json"], "${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_dcspax_pnr_latest_schema.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/correlation_dcspax_pnr_latest_schema.conf"], "${BASE}/core/src/test/resources/config/selectors/sample_config.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/selectors/sample_config.conf"], "${BASE}/core/src/test/resources/views/corr_simplified/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/README.md"], "${BASE}/core/src/test/resources/config/multi_roots/sample_config_wrong.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/multi_roots/sample_config_wrong.conf"], "${BASE}/core/src/test/resources/views/sample_8/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_8/mapping.conf"], "${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_9/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_9/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush/data/bag_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/data/bag_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/latest/update_delete/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/README.md"], "${BASE}/core/src/test/resources/datasets/8/simple_raw_vault_mapping_no_vault.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/simple_raw_vault_mapping_no_vault.conf"], "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/expected_results/FACT_RESERVATION.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch2/expected_results/FACT_RESERVATION.csv"], "${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511308_v0_load-date=11-21-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511308_v0_load-date=11-21-2022.json"], "${BASE}/core/src/test/resources/views/sample_3/expected/jdf_meta.json": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_3/expected/jdf_meta.json"], "${BASE}/core/src/test/resources/datasets/currency_conversion/currency_fail_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/currency_fail_mapping.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/validation/sample_2/0/2024/03/22/30.avro": ["${BASE}/core/target/scala-2.12/test-classes/validation/sample_2/0/2024/03/22/30.avro"], "${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2_master_algo.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2_master_algo.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/18/simple_raw_vault_mapping_no_vault.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/18/simple_raw_vault_mapping_no_vault.conf"], "${BASE}/core/src/test/resources/datasets/latest/insert/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/proration/data/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_3/expected/full_doc.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_3/expected/full_doc.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/views/sample_9/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_9/mapping.conf"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ad_ey_pdt_USCK5S_6072160023101_v5_load-date=07-04-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_ad_ey_pdt_USCK5S_6072160023101_v5_load-date=07-04-2022.json"], "${BASE}/core/src/test/resources/datasets/weight_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/validation/sample_1/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/validation/sample_1/mapping.conf"], "${BASE}/core/src/test/resources/datasets/2/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/2/README.md"], "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_only_meta_changes.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_only_meta_changes.conf"], "${BASE}/core/src/test/resources/views/model_comp/expected/case2": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case2"], "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/5/data/batch2/file3_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch2/file3_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/correlation_purge/config/pnr.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/config/pnr.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE2.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE2.csv"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/README.md"], "${BASE}/core/src/test/resources/datasets/17/multi_root_mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/17/multi_root_mapping.conf"], "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/objectelementfilter/FACT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/objectelementfilter/FACT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/b/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/expected_results/b/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_results.csv": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/expected/lookup_results.csv"], "${BASE}/core/src/test/resources/datasets/14/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/14/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/b/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/b/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_all_changes.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_all_changes.conf"], "${BASE}/core/src/test/resources/views/sample_1/expected/create_table_snowflake.sql": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/create_table_snowflake.sql"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/15/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/15/README.md"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ac_ey_uat_N389UA_v8_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/pnr/PNR_ac_ey_uat_N389UA_v8_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/sfpush/expected_results/data/FACT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/expected_results/data/FACT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/12/data/tkt/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv"], "${BASE}/core/src/test/resources/validation/sample_1/README.md": ["${BASE}/core/target/scala-2.12/test-classes/validation/sample_1/README.md"], "${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables_literal.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_4/failing_mapping_with_variables_literal.conf"], "${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_dcspax_pnr_latest.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/correlation_dcspax_pnr_latest.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ab_ey_uat_N389UA_v7_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/pnr/PNR_ab_ey_uat_N389UA_v7_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/stackable/data/expected_results/FACT_OK_HISTO_2.csv": ["${BASE}/core/target/scala-2.12/test-classes/stackable/data/expected_results/FACT_OK_HISTO_2.csv"], "${BASE}/core/src/test/resources/views/sample_2/expected/create_table_delta.sql": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_2/expected/create_table_delta.sql"], "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv"], "${BASE}/core/src/test/resources/datasets/5/data/batch2/file2_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch2/file2_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/currency_conversion/data/input/exchange_rates/xrt.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/input/exchange_rates/xrt.json"], "${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/views/sample_1/expected/full_doc.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/full_doc.md"], "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order-asyncapi.yaml": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/order-asyncapi.yaml"], "${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_dimAdditionalCols/data/PASSENGER_TYPE.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_dimAdditionalCols/data/PASSENGER_TYPE.csv"], "${BASE}/core/src/test/resources/views/sample_2/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_2/mapping.conf"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/raw_data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch3/raw_data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/5/data/batch3/file4_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch3/file4_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/12/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/16/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/src/test/resources/views/sample_5/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_5/mapping.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_03Service_Booking_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_03Service_Booking_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2019.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/data/bag_load-date=01-01-2019.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_AAAAAA_6666666666666_v0_load-date=06-29-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_AAAAAA_6666666666666_v0_load-date=06-29-2022.json"], "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_2.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/order_sample_2.conf"], "${BASE}/core/src/test/resources/config/application-test.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/application-test.conf"], "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/expected_results/FACT_AIR_SEGMENT_PAX.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch1/expected_results/FACT_AIR_SEGMENT_PAX.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/purge/pnr.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/purge/pnr.conf"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_TKTEMD_v1_0_0.plantuml": ["${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_TKTEMD_v1_0_0.plantuml"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_stats.txt": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/expected/lookup_stats.txt"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/FACT_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/FACT_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_changed_columns.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_changed_columns.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/source_variables/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/data/bag_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json"], "${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/expressions/data/expected_results/FACT_TRANSFORMATION.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/expected_results/FACT_TRANSFORMATION.csv"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_1Input/data/PASSENGER_TYPE.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_1Input/data/PASSENGER_TYPE.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/model_comp/expected/case1": ["${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case1"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_3_prefiller/data/input/PASSENGER_TYPE.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_3_prefiller/data/input/PASSENGER_TYPE.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/15/data/expected_results/FACT_SERVICE_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/15/data/expected_results/FACT_SERVICE_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/external_data/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/external_data/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/mapping.conf"], "${BASE}/core/src/test/resources/log4j2.properties": ["${BASE}/core/target/scala-2.12/test-classes/log4j2.properties"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/source_variables/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/config/samples/correlation_dcspax_pnr.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/samples/correlation_dcspax_pnr.conf"], "${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/views/sample_3/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_3/mapping.conf"], "${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_RESERVATION.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/expected_results/FACT_RESERVATION.csv"], "${BASE}/core/src/test/resources/datasets/expressions/data/bagsmanylegsv2_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/bagsmanylegsv2_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/correlation_purge/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/README.md"], "${BASE}/core/src/test/resources/validation/sample_9/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/validation/sample_9/mapping.conf"], "${BASE}/core/src/test/resources/datasets/expressions/data/correlationEMD_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/correlationEMD_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_1_prefiller/data/input/PASSENGER_TYPE.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_1_prefiller/data/input/PASSENGER_TYPE.csv"], "${BASE}/core/src/test/resources/datasets/nullvalues/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/nullvalues/mapping.conf"], "${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_4/expected/doc.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_4/expected/doc.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_5/expected/doc.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_5/expected/doc.md"], "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/dscpax-pax1_v2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch2/data/dscpax-pax1_v2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json"], "${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2.csv"], "${BASE}/core/src/test/resources/config/samples/tkt.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/samples/tkt.conf"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/data/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/README.md"], "${BASE}/core/src/test/resources/datasets/proration/data/MH-PRD-no-fare-calc_load-date=01-10-2024.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/MH-PRD-no-fare-calc_load-date=01-10-2024.json"], "${BASE}/core/src/test/resources/datasets/partial_processing/mapping.conf": ["${BASE}/core/target/scala-2.12/test-classes/datasets/partial_processing/mapping.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/src/test/resources/datasets/nullvalues/data/pnr_null_values_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/nullvalues/data/pnr_null_values_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/1/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/1/README.md"], "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/expressions/data/correlationWPASDF_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/correlationWPASDF_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/a/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/expected_results/a/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/config/samples/correlation_pnr_tkt.conf": ["${BASE}/core/target/scala-2.12/test-classes/config/samples/correlation_pnr_tkt.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_3/README.md": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_3/README.md"], "${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables_unknown.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_4/failing_mapping_with_variables_unknown.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/source_variables/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch2/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/src/test/resources/datasets/recordsfilter/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/README.md"], "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_3_CURRENCY_CONVERSIONS_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_3_CURRENCY_CONVERSIONS_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables.conf": ["${BASE}/core/target/scala-2.12/test-classes/views/sample_4/failing_mapping_with_variables.conf"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/format_delta/data/delta/_delta_log/00000000000000000000.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/format_delta/data/delta/_delta_log/00000000000000000000.json"], "${BASE}/core/src/test/resources/datasets/16/expected_result/internal_corr_ids_asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/16/expected_result/internal_corr_ids_asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/src/test/resources/datasets/18/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/18/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v2_load-date=02-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v2_load-date=02-01-2018.json"], "${BASE}/core/src/test/resources/datasets/cartesian_product/data/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/cartesian_product/data/README.md"], "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ae_ey_pdt_USCK5S_6072160023101_v10_load-date=07-15-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_ae_ey_pdt_USCK5S_6072160023101_v10_load-date=07-15-2022.json"], "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/src/test/resources/datasets/README.md": ["${BASE}/core/target/scala-2.12/test-classes/datasets/README.md"], "${BASE}/core/src/test/resources/datasets/15/data/tkt/TKT_aa_ey_uat_6072401543235_v1_load-date=19-08-2022.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/15/data/tkt/TKT_aa_ey_uat_6072401543235_v1_load-date=19-08-2022.json"], "${BASE}/core/src/test/resources/datasets/13/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/13/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/src/test/resources/datasets/5/data/batch3/file5_load-date=01-01-2018.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch3/file5_load-date=01-01-2018.json"], "${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-no-doc-numbers.json": ["${BASE}/core/target/scala-2.12/test-classes/datasets/1/data/sample-ticketing-reference-no-doc-numbers.json"], "${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/expected_results/FACT_RESERVATION_HISTO.csv"]}, {"${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/pnr/PNR_ac_ey_uat_N389UA_v8_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ac_ey_uat_N389UA_v8_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/correlation_pnr_tkt_latest.conf": ["${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_pnr_tkt_latest.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv": ["${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/data/expected_results/FACT_BAG_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/weight_conversion/data/expected_results/FACT_BAG_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/stackable/data/expected_results_failure/FACT_OK_HISTO_1.csv": ["${BASE}/core/src/test/resources/stackable/data/expected_results_failure/FACT_OK_HISTO_1.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/pnr.conf": ["${BASE}/core/src/test/resources/datasets/purge/pnr.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/README.md": ["${BASE}/core/src/test/resources/views/corr_simplified/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json": ["${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/18/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/18/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/mapping.conf": ["${BASE}/core/src/test/resources/datasets/sfpush/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/expected_results/FACT_RESERVATION.csv": ["${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_RESERVATION.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/config/corr.conf": ["${BASE}/core/src/test/resources/datasets/correlation_purge/config/corr.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_plus_minus_columns.conf": ["${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_plus_minus_columns.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/simple_mapping_no_vault_latest_schema.conf": ["${BASE}/core/src/test/resources/datasets/latest/simple_mapping_no_vault_latest_schema.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch1/data/dscpax-pax1_v1_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/dscpax-pax1_v1_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/bagsmanylegs_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/expressions/data/bagsmanylegs_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/correlationWPASDF_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/optimize/data/correlationWPASDF_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/MH-PRD-no-fare-calc_load-date=01-10-2024.json": ["${BASE}/core/src/test/resources/datasets/proration/data/MH-PRD-no-fare-calc_load-date=01-10-2024.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_1Input/data/PASSENGER_TYPE.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_1Input/data/PASSENGER_TYPE.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": ["${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/README.md": ["${BASE}/core/src/test/resources/datasets/proration/data/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_3/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_3/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/expected_results/EXCHANGE_RATE_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/exchange_rate/data/expected_results/EXCHANGE_RATE_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/17/views/expected_delta_create_multi_root_mapping.sql": ["${BASE}/core/src/test/resources/datasets/17/views/expected_delta_create_multi_root_mapping.sql"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case5": ["${BASE}/core/src/test/resources/views/model_comp/expected/case5"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/stackable/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/stackable/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/12/data/tkt/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/correlationEMD_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/optimize/data/correlationEMD_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/expected_results/DIM_POINT_OF_SALE.csv": ["${BASE}/core/src/test/resources/datasets/multi_roots/data/expected_results/DIM_POINT_OF_SALE.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v4_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v4_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/full_doc.md": ["${BASE}/core/src/test/resources/views/sample_1/expected/full_doc.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_2_CURRENCY_CONVERSIONS_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_2_CURRENCY_CONVERSIONS_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_3/expected/jdf_meta.json": ["${BASE}/core/src/test/resources/views/sample_3/expected/jdf_meta.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/simple_raw_vault_mapping_no_vault.conf": ["${BASE}/core/src/test/resources/datasets/8/simple_raw_vault_mapping_no_vault.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_AAAAAA_6666666666666_v0_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_AAAAAA_6666666666666_v0_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_9/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_9/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/format_delta/data/delta/part-00000-ab18c1bd-10cd-4650-bab9-38eb85452f0d-c000.snappy.parquet": ["${BASE}/core/src/test/resources/datasets/format_delta/data/delta/part-00000-ab18c1bd-10cd-4650-bab9-38eb85452f0d-c000.snappy.parquet"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/FACT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/FACT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/README.md": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/correlation_pnr_tkt_latest_schema.conf": ["${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_pnr_tkt_latest_schema.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/pnr/PNR_ab_ey_uat_N389UA_v7_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ab_ey_uat_N389UA_v7_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/tkt/TKT_ad_ey_uat_7492400944169_v3_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ad_ey_uat_7492400944169_v3_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/README.md": ["${BASE}/core/src/test/resources/datasets/sfpush/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/proration/simplified_tktemd.conf": ["${BASE}/core/src/test/resources/datasets/proration/simplified_tktemd.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch1/expected_results/FACT_AIR_SEGMENT_PAX.csv": ["${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/expected_results/FACT_AIR_SEGMENT_PAX.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch1/file1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/5/data/batch1/file1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/format_delta/mapping.conf": ["${BASE}/core/src/test/resources/datasets/format_delta/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511308_v0_load-date=11-21-2022.json": ["${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511308_v0_load-date=11-21-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case1": ["${BASE}/core/src/test/resources/views/model_comp/expected/case1"], "${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/README.md": ["${BASE}/core/src/test/resources/datasets/exchange_rate/data/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_01Traveler_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_01Traveler_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/config/samples/tkt.conf": ["${BASE}/core/src/test/resources/config/samples/tkt.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping.conf": ["${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_7/expected/doc_enriched.md": ["${BASE}/core/src/test/resources/views/sample_7/expected/doc_enriched.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv": ["${BASE}/core/src/test/resources/datasets/12/data/tkt/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/nullvalues/README.md": ["${BASE}/core/src/test/resources/datasets/nullvalues/README.md"], "${BASE}/core/target/scala-2.12/test-classes/config/multi_roots/sample_config_wrong.conf": ["${BASE}/core/src/test/resources/config/multi_roots/sample_config_wrong.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch2/file3_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/5/data/batch2/file3_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/input/KEY A-v2-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/input/KEY A-v2-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/json_processor/generic_item_example.json": ["${BASE}/core/src/test/resources/datasets/json_processor/generic_item_example.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/order_sample_1.conf": ["${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_1.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_TKTEMD_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_TKTEMD_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_dimAdditionalCols/data/PASSENGER_TYPE.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_dimAdditionalCols/data/PASSENGER_TYPE.csv"], "${BASE}/core/target/scala-2.12/test-classes/stackable/data/expected_results/FACT_OK_HISTO_2.csv": ["${BASE}/core/src/test/resources/stackable/data/expected_results/FACT_OK_HISTO_2.csv"], "${BASE}/core/target/scala-2.12/test-classes/validation/sample_1/mapping.conf": ["${BASE}/core/src/test/resources/validation/sample_1/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/README.md": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/README.md"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/doc.md": ["${BASE}/core/src/test/resources/views/sample_1/expected/doc.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/18/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/18/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch3/file6_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/5/data/batch3/file6_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/README.md": ["${BASE}/core/src/test/resources/views/sample_1/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/nofilter/FACT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/nofilter/FACT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/17/multi_root_mapping.conf": ["${BASE}/core/src/test/resources/datasets/17/multi_root_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/correlationWPASDF_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/8/data/correlationWPASDF_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/cartesian_product/data/PNR_6Q5JNY_v10_load-date=01-01-2023.json": ["${BASE}/core/src/test/resources/datasets/cartesian_product/data/PNR_6Q5JNY_v10_load-date=01-01-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch2/file2_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/5/data/batch2/file2_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/config/multi_roots/sample_config.conf": ["${BASE}/core/src/test/resources/config/multi_roots/sample_config.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/config/samples/correlation_dcspax_pnr.conf": ["${BASE}/core/src/test/resources/config/samples/correlation_dcspax_pnr.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/currency_fail_mapping.conf": ["${BASE}/core/src/test/resources/datasets/currency_conversion/currency_fail_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/stackable/data/expected_results_failure/FACT_OK_HISTO_2.csv": ["${BASE}/core/src/test/resources/stackable/data/expected_results_failure/FACT_OK_HISTO_2.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_9/README.md": ["${BASE}/core/src/test/resources/views/sample_9/README.md"], "${BASE}/core/target/scala-2.12/test-classes/config/README.md": ["${BASE}/core/src/test/resources/config/README.md"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_6_yaml_swagger/input/dcspax.yaml": ["${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/dcspax.yaml"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/expected/lookup_results_not_found.csv": ["${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_results_not_found.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/bagsmanylegsv2_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/expressions/data/bagsmanylegsv2_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/README.md": ["${BASE}/core/src/test/resources/datasets/16/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/data_types/mapping.conf": ["${BASE}/core/src/test/resources/datasets/data_types/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_2/README.md": ["${BASE}/core/src/test/resources/views/sample_2/README.md"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/README.md": ["${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/nullvalues/mapping.conf": ["${BASE}/core/src/test/resources/datasets/nullvalues/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_11/README.md": ["${BASE}/core/src/test/resources/views/sample_11/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/simple_raw_vault_mapping_no_vault_dim.conf": ["${BASE}/core/src/test/resources/datasets/multi_roots/simple_raw_vault_mapping_no_vault_dim.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_11_05_30_45_load-date=03-11-2025.json": ["${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_11_05_30_45_load-date=03-11-2025.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/weight_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/create_table_delta.sql": ["${BASE}/core/src/test/resources/views/sample_1/expected/create_table_delta.sql"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_02_05_30_45_load-date=03-02-2025.json": ["${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_02_05_30_45_load-date=03-02-2025.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_6_yaml_swagger/input/app.conf": ["${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/app.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/src/test/resources/datasets/12/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_variables/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/application.conf": ["${BASE}/core/src/test/resources/datasets/sfpush/application.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json": ["${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/18/README.md": ["${BASE}/core/src/test/resources/datasets/18/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/expected_result/internal_corr_ids_asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/src/test/resources/datasets/16/expected_result/internal_corr_ids_asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/external_data/data/expected_results/FACT_BAG_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/external_data/data/expected_results/FACT_BAG_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_8/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_8/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/README.md": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/13/data/tkt/TKT_aa_ey_uat_0142172648268_v9_load-date=08-16-2022.json": ["${BASE}/core/src/test/resources/datasets/13/data/tkt/TKT_aa_ey_uat_0142172648268_v9_load-date=08-16-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/README.md": ["${BASE}/core/src/test/resources/datasets/correlation_purge/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_2_j2s/data/bag_load-date=01-01-2099.json": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/bag_load-date=01-01-2099.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/objectfilter/FACT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/objectfilter/FACT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/order-asyncapi.yaml": ["${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order-asyncapi.yaml"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_8/README.md": ["${BASE}/core/src/test/resources/views/sample_8/README.md"], "${BASE}/core/target/scala-2.12/test-classes/log4j2.properties": ["${BASE}/core/src/test/resources/log4j2.properties"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/14/README.md": ["${BASE}/core/src/test/resources/datasets/14/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/13/data/pnr/PNR_ab_ey_uat_22USLC_v20_load-date=08-16-2022.json": ["${BASE}/core/src/test/resources/datasets/13/data/pnr/PNR_ab_ey_uat_22USLC_v20_load-date=08-16-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511306_v0_load-date=11-21-2022.json": ["${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511306_v0_load-date=11-21-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/17/data/expected_results/FACT_SERVICE_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/17/data/expected_results/FACT_SERVICE_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/15/README.md": ["${BASE}/core/src/test/resources/datasets/15/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/config/samples/cartesian_product.conf": ["${BASE}/core/src/test/resources/config/samples/cartesian_product.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_7/input/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_7/input/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_BAGS_GROUP_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_BAGS_GROUP_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch2/expected_results/FACT_AIR_SEGMENT_PAX.csv": ["${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/expected_results/FACT_AIR_SEGMENT_PAX.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/expected_results/step1/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step1/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case6": ["${BASE}/core/src/test/resources/views/model_comp/expected/case6"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_4/not_failing_mapping_with_variables.conf": ["${BASE}/core/src/test/resources/views/sample_4/not_failing_mapping_with_variables.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/data/bag_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush/data/bag_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/stackable/data/expected_results/FACT_OK_HISTO_1.csv": ["${BASE}/core/src/test/resources/stackable/data/expected_results/FACT_OK_HISTO_1.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_5/README.md": ["${BASE}/core/src/test/resources/views/sample_5/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/expected_results/DIM_CARRIER.csv": ["${BASE}/core/src/test/resources/datasets/multi_roots/data/expected_results/DIM_CARRIER.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/PNR_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/correlationWPASDF_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/expressions/data/correlationWPASDF_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/config/tktemd.conf": ["${BASE}/core/src/test/resources/datasets/correlation_purge/config/tktemd.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_2/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_2/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/MH-PRD_load-date=01-10-2024.json": ["${BASE}/core/src/test/resources/datasets/proration/data/MH-PRD_load-date=01-10-2024.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/data_types/data/recordv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/data_types/data/recordv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_4/README.md": ["${BASE}/core/src/test/resources/views/sample_4/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/pnr/PNR_ab_ey_uat_6I2QQ4_v5_load-date=11-21-2022.json": ["${BASE}/core/src/test/resources/datasets/14/data/pnr/PNR_ab_ey_uat_6I2QQ4_v5_load-date=11-21-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/README.md": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_srcAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_4.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_srcAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_4.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/external_data/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/external_data/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/expected_results/FACT_RESERVATION.csv": ["${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_RESERVATION.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/create_table_snowflake.sql": ["${BASE}/core/src/test/resources/views/sample_1/expected/create_table_snowflake.sql"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/validation/sample_9/mapping.conf": ["${BASE}/core/src/test/resources/validation/sample_9/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/src/test/resources/datasets/16/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case2": ["${BASE}/core/src/test/resources/views/model_comp/expected/case2"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_1_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_1_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_5/expected/jdf_meta.json": ["${BASE}/core/src/test/resources/views/sample_5/expected/jdf_meta.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/README.md": ["${BASE}/core/src/test/resources/datasets/latest/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/data/dscpax-pnr_v1_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/dscpax-pnr_v1_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_04Ticketing_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_04Ticketing_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/mapping.conf": ["${BASE}/core/src/test/resources/datasets/recordsfilter/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/data/bag_load-date=01-01-2019.json": ["${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2019.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/single_table/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/single_table/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/pnr/PNR_aa_ey_uat_N389UA_v6_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_aa_ey_uat_N389UA_v6_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/validation/sample_1/app.conf": ["${BASE}/core/src/test/resources/validation/sample_1/app.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_dimAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_3.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_dimAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_3.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_7/input/dcspax.yaml": ["${BASE}/core/src/test/resources/views/sample_7/input/dcspax.yaml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/data/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json": ["${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json"], "${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_DCSPAX_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_DCSPAX_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch3/raw_data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/raw_data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/config/application-with-event-grid.conf": ["${BASE}/core/src/test/resources/config/application-with-event-grid.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/correlationEMD_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationEMD_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/PNR_ad_ey_pdt_USCK5S_6072160023101_v16_load-date=07-04-2022.json": ["${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ad_ey_pdt_USCK5S_6072160023101_v16_load-date=07-04-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/format_delta/data/expected_results/FACT_TABLE1.csv": ["${BASE}/core/src/test/resources/datasets/format_delta/data/expected_results/FACT_TABLE1.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_ad_ey_pdt_USCK5S_6072160023101_v5_load-date=07-04-2022.json": ["${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ad_ey_pdt_USCK5S_6072160023101_v5_load-date=07-04-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/arrayfilter/FACT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/arrayfilter/FACT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/README.md": ["${BASE}/core/src/test/resources/datasets/latest/update_delete/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/README.md": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_2Input/data/expected_results/DIM_PASSENGER_TYPE_2.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/expected_results/DIM_PASSENGER_TYPE_2.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_srcAdditionalCols/data/PASSENGER_TYPE.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_srcAdditionalCols/data/PASSENGER_TYPE.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/partial_processing/mapping.conf": ["${BASE}/core/src/test/resources/datasets/partial_processing/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/json_processor/README.md": ["${BASE}/core/src/test/resources/datasets/json_processor/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/expected_results/FACT_TRANSFORMATION.csv": ["${BASE}/core/src/test/resources/datasets/expressions/data/expected_results/FACT_TRANSFORMATION.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_2/expected/jdf_meta.json": ["${BASE}/core/src/test/resources/views/sample_2/expected/jdf_meta.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_7/README.md": ["${BASE}/core/src/test/resources/views/sample_7/README.md"], "${BASE}/core/target/scala-2.12/test-classes/config/novault-integration-test.conf": ["${BASE}/core/src/test/resources/config/novault-integration-test.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/DIM_PASSENGER_TYPE_5.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/DIM_PASSENGER_TYPE_5.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/star-schema_TEST_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/sample_1/expected/star-schema_TEST_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/jdf_views.sql": ["${BASE}/core/src/test/resources/views/sample_1/expected/jdf_views.sql"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/validation/sample_9/README.md": ["${BASE}/core/src/test/resources/validation/sample_9/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE2.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE2.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/correlation_dcspax_pnr_latest_schema.conf": ["${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_dcspax_pnr_latest_schema.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/10/simple_raw_vault_mapping_no_vault_multi_section.conf": ["${BASE}/core/src/test/resources/datasets/10/simple_raw_vault_mapping_no_vault_multi_section.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/17/README.md": ["${BASE}/core/src/test/resources/datasets/17/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/input/exchange_rates/xrt.json": ["${BASE}/core/src/test/resources/datasets/currency_conversion/data/input/exchange_rates/xrt.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/model.conf": ["${BASE}/core/src/test/resources/datasets/exchange_rate/model.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_6_yaml_swagger/input/dcspax.conf": ["${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/dcspax.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/15/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/src/test/resources/datasets/15/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/expected_results/step3/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step3/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_1Input/data/expected_results/DIM_PASSENGER_TYPE_1.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_1Input/data/expected_results/DIM_PASSENGER_TYPE_1.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/external_data/simplified_dcsbag_with_external_data.conf": ["${BASE}/core/src/test/resources/datasets/external_data/simplified_dcsbag_with_external_data.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/18/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/18/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/README.md": ["${BASE}/core/src/test/resources/datasets/11/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/nullvalues/data/expected_results/FACT_NULLVALUES.csv": ["${BASE}/core/src/test/resources/datasets/nullvalues/data/expected_results/FACT_NULLVALUES.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/pnr/PNR_aa_ey_uat_6I2QQ4_v4_load-date=11-21-2022.json": ["${BASE}/core/src/test/resources/datasets/14/data/pnr/PNR_aa_ey_uat_6I2QQ4_v4_load-date=11-21-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_4/failing_mapping_with_variables_unknown.conf": ["${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables_unknown.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/partial_processing/application-test2.conf": ["${BASE}/core/src/test/resources/datasets/partial_processing/application-test2.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case3": ["${BASE}/core/src/test/resources/views/model_comp/expected/case3"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_7/expected/jdf_meta.json": ["${BASE}/core/src/test/resources/views/sample_7/expected/jdf_meta.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/partial_processing/application-test1.conf": ["${BASE}/core/src/test/resources/datasets/partial_processing/application-test1.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_plus_minus_tables.conf": ["${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_plus_minus_tables.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_variables/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/8/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_5/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_5/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/b/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/b/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/correlationWPASDF_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationWPASDF_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_PNR_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_PNR_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/1/README.md": ["${BASE}/core/src/test/resources/datasets/1/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/18/data/b/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/18/data/b/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION.csv": ["${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/simplified_dcsbag.conf": ["${BASE}/core/src/test/resources/datasets/weight_conversion/simplified_dcsbag.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/README.md": ["${BASE}/core/src/test/resources/datasets/latest_correlation/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch3/file4_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/5/data/batch3/file4_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/app.conf": ["${BASE}/core/src/test/resources/views/sample_1/app.conf"], "${BASE}/core/target/scala-2.12/test-classes/config/samples/pnr.conf": ["${BASE}/core/src/test/resources/config/samples/pnr.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/2/data/complex-ticketing-reference.json": ["${BASE}/core/src/test/resources/datasets/2/data/complex-ticketing-reference.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/config/selectors/sample_config.conf": ["${BASE}/core/src/test/resources/config/selectors/sample_config.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/1/data/sample-ticketing-reference-no-doc-numbers.json": ["${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-no-doc-numbers.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/README.md": ["${BASE}/core/src/test/resources/datasets/multi_roots/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/external_data/data/expected_results/FACT_BAG_GROUP_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/external_data/data/expected_results/FACT_BAG_GROUP_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/optimize/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/1/data/sample-ticketing-reference-with-primary-doc-number.json": ["${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-primary-doc-number.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v12_load-date=19-08-2022.json": ["${BASE}/core/src/test/resources/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v12_load-date=19-08-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/pnr/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/data/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json": ["${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/purge/data/step1/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/15/data/tkt/TKT_aa_ey_uat_6072401543235_v1_load-date=19-08-2022.json": ["${BASE}/core/src/test/resources/datasets/15/data/tkt/TKT_aa_ey_uat_6072401543235_v1_load-date=19-08-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/application.conf": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/application.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/src/test/resources/datasets/16/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/expected_results/FACT_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/proration/data/expected_results/FACT_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/mapping.conf": ["${BASE}/core/src/test/resources/views/corr_simplified/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch1/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/data/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": ["${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/1/data/sample-ticketing-reference-with-dupe-primary-doc-number.json": ["${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-dupe-primary-doc-number.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/8/data/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/simple_raw_vault_mapping_no_vault.conf": ["${BASE}/core/src/test/resources/datasets/11/simple_raw_vault_mapping_no_vault.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_3_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_3_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch2/expected_results/FACT_RESERVATION.csv": ["${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/expected_results/FACT_RESERVATION.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/config/application-with-snowflake.conf": ["${BASE}/core/src/test/resources/config/application-with-snowflake.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_3_CURRENCY_CONVERSIONS_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_3_CURRENCY_CONVERSIONS_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/README.md": ["${BASE}/core/src/test/resources/datasets/12/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/simple_raw_vault_mapping_no_vault.conf": ["${BASE}/core/src/test/resources/datasets/source_variables/simple_raw_vault_mapping_no_vault.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_4/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_4/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/copy_table_snowflake.sql": ["${BASE}/core/src/test/resources/views/sample_1/expected/copy_table_snowflake.sql"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_ae_ey_pdt_USCK5S_6072160023101_v10_load-date=07-15-2022.json": ["${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ae_ey_pdt_USCK5S_6072160023101_v10_load-date=07-15-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch1/raw_data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/raw_data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/stackable/dummy_mapping.conf": ["${BASE}/core/src/test/resources/stackable/dummy_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_02Segment_Booking_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_02Segment_Booking_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_1_prefiller/data/input/PASSENGER_TYPE.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_1_prefiller/data/input/PASSENGER_TYPE.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/correlationfilter/FACT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/correlationfilter/FACT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511307_v0_load-date=11-21-2022.json": ["${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511307_v0_load-date=11-21-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_11/app.conf": ["${BASE}/core/src/test/resources/views/sample_11/app.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": ["${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_00Reservation_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_00Reservation_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v11_load-date=19-08-2022.json": ["${BASE}/core/src/test/resources/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v11_load-date=19-08-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/README.md": ["${BASE}/core/src/test/resources/datasets/latest/insert/README.md"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_1/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/stackable/error_handling_mapping.conf": ["${BASE}/core/src/test/resources/stackable/error_handling_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/weight_fail_mapping.conf": ["${BASE}/core/src/test/resources/datasets/weight_conversion/weight_fail_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/README.md": ["${BASE}/core/src/test/resources/datasets/source_variables/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/input/KEY A-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/input/KEY A-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/validation/sample_1/README.md": ["${BASE}/core/src/test/resources/validation/sample_1/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_2/expected/doc.md": ["${BASE}/core/src/test/resources/views/sample_2/expected/doc.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/1/data/sample-ticketing-reference-with-all-doc-number.json": ["${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-all-doc-number.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/14/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/src/test/resources/datasets/14/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_11/mapping.conf": ["${BASE}/core/src/test/resources/views/sample_11/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/simplified_dcsbag.conf": ["${BASE}/core/src/test/resources/datasets/currency_conversion/simplified_dcsbag.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/13/README.md": ["${BASE}/core/src/test/resources/datasets/13/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/13/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/src/test/resources/datasets/13/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/order_sample_3.conf": ["${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_3.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/model.conf": ["${BASE}/core/src/test/resources/datasets/expressions/model.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/mapping.conf": ["${BASE}/core/src/test/resources/views/pnr_simplified/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/2/README.md": ["${BASE}/core/src/test/resources/datasets/2/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/17/data/service_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/17/data/service_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/correlation_dcspax_pnr.conf": ["${BASE}/core/src/test/resources/datasets/source_correlation/correlation_dcspax_pnr.conf"], "${BASE}/core/target/scala-2.12/test-classes/config/application-purge.conf": ["${BASE}/core/src/test/resources/config/application-purge.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_4/failing_mapping_with_variables_literal.conf": ["${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables_literal.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_DCSBAG_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_DCSBAG_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2.csv": ["${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/integration/step_3_prefiller/data/input/PASSENGER_TYPE.csv": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_3_prefiller/data/input/PASSENGER_TYPE.csv"], "${BASE}/core/target/scala-2.12/test-classes/validation/sample_1/expected_results/test_results.csv": ["${BASE}/core/src/test/resources/validation/sample_1/expected_results/test_results.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/README.md": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/order_sample_2.conf": ["${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_2.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_4/expected/doc.md": ["${BASE}/core/src/test/resources/views/sample_4/expected/doc.md"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_5/expected/doc.md": ["${BASE}/core/src/test/resources/views/sample_5/expected/doc.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/single_table/single_table_model.conf": ["${BASE}/core/src/test/resources/datasets/single_table/single_table_model.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/expected/case4": ["${BASE}/core/src/test/resources/views/model_comp/expected/case4"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/a/6JDA9D-2023-01-18_v14_load-date=10-30-2022.json": ["${BASE}/core/src/test/resources/datasets/source_variables/data/a/6JDA9D-2023-01-18_v14_load-date=10-30-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/README.md": ["${BASE}/core/src/test/resources/datasets/recordsfilter/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step2/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/purge/data/step2/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_3/expected/full_doc.md": ["${BASE}/core/src/test/resources/views/sample_3/expected/full_doc.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3bis_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3bis_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/template.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/template.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/data/bag_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_01_05_30_45_load-date=03-01-2025.json": ["${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_01_05_30_45_load-date=03-01-2025.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/dim_prefiller/mapping.conf": ["${BASE}/core/src/test/resources/datasets/dim_prefiller/mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_4/failing_mapping_with_variables.conf": ["${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables.conf"], "${BASE}/core/target/scala-2.12/test-classes/config/samples/correlation_pnr_tkt.conf": ["${BASE}/core/src/test/resources/config/samples/correlation_pnr_tkt.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/a/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_variables/data/a/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/data/bagsmanylegsv2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch2/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/README.md": ["${BASE}/core/src/test/resources/datasets/purge/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/correlationEMD_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/8/data/correlationEMD_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/expected/star-schema_PNR_03Service_Booking_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_03Service_Booking_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/13/data/pnr/PNR_aa_ey_uat_22USLC_v15_load-date=08-16-2022.json": ["${BASE}/core/src/test/resources/datasets/13/data/pnr/PNR_aa_ey_uat_22USLC_v15_load-date=08-16-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/cartesian_product/simplified_pnr.conf": ["${BASE}/core/src/test/resources/datasets/cartesian_product/simplified_pnr.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/8/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/5/data/batch3/file5_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/5/data/batch3/file5_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/correlation_dcspax_pnr_latest.conf": ["${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_dcspax_pnr_latest.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_1/expected/copy_table_delta.sql": ["${BASE}/core/src/test/resources/views/sample_1/expected/copy_table_delta.sql"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch2/data/dscpax-pax1_v2_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/dscpax-pax1_v2_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch3/data/dscpax-pax1_v3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/dscpax-pax1_v3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/b/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/b/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/update_delete/data/batch1/expected_results/FACT_RESERVATION.csv": ["${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/expected_results/FACT_RESERVATION.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/nullvalues/data/pnr_null_values_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/nullvalues/data/pnr_null_values_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/README.md": ["${BASE}/core/src/test/resources/datasets/source_correlation/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/8/data/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/expected_results/data/FACT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush/expected_results/data/FACT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_5/expected/create_table_delta.sql": ["${BASE}/core/src/test/resources/views/sample_5/expected/create_table_delta.sql"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/expected/lookup_results.csv": ["${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_results.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/currency_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv": ["${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/proration/extdata/input_optd_por_public_sample.csv": ["${BASE}/core/src/test/resources/datasets/proration/extdata/input_optd_por_public_sample.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/expected_results/objectelementfilter/FACT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/objectelementfilter/FACT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/expected_results/b/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/b/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/dcspax.conf": ["${BASE}/core/src/test/resources/datasets/source_correlation/dcspax.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/data/tkt/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": ["${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_variables/data/expected_results/INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_variables/data/expected_results/INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/simple_mapping.conf": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/simple_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/12/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/src/test/resources/datasets/12/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX.csv": ["${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/corr_simplified/expected/star-schema_CORR_SKD_v1_0_0.plantuml": ["${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_SKD_v1_0_0.plantuml"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step1/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/purge/data/step1/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/currency_conversion/data/expected_results/FACT_BAG_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_BAG_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/README.md": ["${BASE}/core/src/test/resources/datasets/README.md"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_all_changes.conf": ["${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_all_changes.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/pnr_simplified/README.md": ["${BASE}/core/src/test/resources/views/pnr_simplified/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_multiple_batches/simple_mapping.conf": ["${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/simple_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/revised_pit_mapping.conf": ["${BASE}/core/src/test/resources/datasets/revised_pit/revised_pit_mapping.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch1/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/5/README.md": ["${BASE}/core/src/test/resources/datasets/5/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/latest/simple_mapping_no_vault_latest.conf": ["${BASE}/core/src/test/resources/datasets/latest/simple_mapping_no_vault_latest.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/cartesian_product/data/README.md": ["${BASE}/core/src/test/resources/datasets/cartesian_product/data/README.md"], "${BASE}/core/target/scala-2.12/test-classes/validation/sample_2/0/2024/03/22/30.avro": ["${BASE}/core/src/test/resources/validation/sample_2/0/2024/03/22/30.avro"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2_master_algo.csv": ["${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2_master_algo.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/18/simple_raw_vault_mapping_no_vault.conf": ["${BASE}/core/src/test/resources/datasets/18/simple_raw_vault_mapping_no_vault.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_in_all_images.csv": ["${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_in_all_images.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_10_yaml_asyncapi/expected/lookup_stats.txt": ["${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_stats.txt"], "${BASE}/core/target/scala-2.12/test-classes/datasets/proration/data/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/proration/data/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/cartesian_product/data/expected_results/FACT_PRODUCT_PAX_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/cartesian_product/data/expected_results/FACT_PRODUCT_PAX_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/config/pnr.conf": ["${BASE}/core/src/test/resources/datasets/correlation_purge/config/pnr.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-04-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-04-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/recordsfilter/data/bag_load-date=01-01-2020.json": ["${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2020.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/optimize/simple_mapping_zorder.conf": ["${BASE}/core/src/test/resources/datasets/optimize/simple_mapping_zorder.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/expected_results/a/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/a/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/correlationEMD_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/expressions/data/correlationEMD_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/expressions/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/weight_conversion/data/expected_results/FACT_BAG_GROUP_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/weight_conversion/data/expected_results/FACT_BAG_GROUP_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/multi_batch/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/format_delta/data/delta/_delta_log/00000000000000000000.json": ["${BASE}/core/src/test/resources/datasets/format_delta/data/delta/_delta_log/00000000000000000000.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/13/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/src/test/resources/datasets/13/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_6_yaml_swagger/README.md": ["${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush/expected_results/data/FACT_HISTO_MIRROR.csv": ["${BASE}/core/src/test/resources/datasets/sfpush/expected_results/data/FACT_HISTO_MIRROR.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/source_correlation/default_update/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/15/data/expected_results/FACT_SERVICE_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/15/data/expected_results/FACT_SERVICE_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/15/expected_result/asso_air_segment_pax_coupon_histo.csv": ["${BASE}/core/src/test/resources/datasets/15/expected_result/asso_air_segment_pax_coupon_histo.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/tktemd/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv": ["${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/10/data/correlationEMD_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/10/data/correlationEMD_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/expressions/README.md": ["${BASE}/core/src/test/resources/datasets/expressions/README.md"], "${BASE}/core/target/scala-2.12/test-classes/config/application-test.conf": ["${BASE}/core/src/test/resources/config/application-test.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/multi_roots/data/bagsmanylegsv3_load-date=02-01-2018.json": ["${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv3_load-date=02-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/correlation_purge/expected_results/step2/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step2/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_2/expected/create_table_delta.sql": ["${BASE}/core/src/test/resources/views/sample_2/expected/create_table_delta.sql"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/14/expected_result/asso_air_segment_pax_coupon.csv": ["${BASE}/core/src/test/resources/datasets/14/expected_result/asso_air_segment_pax_coupon.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/10/data/expected_results/FACT_MULTI_SECTION.csv": ["${BASE}/core/src/test/resources/datasets/10/data/expected_results/FACT_MULTI_SECTION.csv"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_changed_columns.conf": ["${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_changed_columns.conf"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/sfpush_purge/data/a/bagsmanylegsv1_load-date=01-01-2018.json": ["${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/bagsmanylegsv1_load-date=01-01-2018.json"], "${BASE}/core/target/scala-2.12/test-classes/datasets/purge/data/step2/expected_results/FACT_RESERVATION_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/purge/data/step2/expected_results/FACT_RESERVATION_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v2-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json": ["${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v2-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json"], "${BASE}/core/target/scala-2.12/test-classes/views/model_comp/mappings/simple_mapping_only_meta_changes.conf": ["${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_only_meta_changes.conf"], "${BASE}/core/target/scala-2.12/test-classes/views/sample_3/README.md": ["${BASE}/core/src/test/resources/views/sample_3/README.md"], "${BASE}/core/target/scala-2.12/test-classes/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": ["${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv"], "${BASE}/core/target/scala-2.12/test-classes/datasets/16/data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": ["${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json"]}], {"${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496913}, "${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/dcspax.yaml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_6_yaml_swagger/input/dcspax.yaml", "lastModified": 1749205497050}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496970}, "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496913}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496916}, "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_01Traveler_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_01Traveler_v1_0_0.plantuml", "lastModified": 1749205497014}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/dscpax-pax1_v3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/dscpax-pax1_v3_load-date=02-01-2018.json", "lastModified": 1749205496982}, "${BASE}/core/src/test/resources/validation/sample_1/app.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/validation/sample_1/app.conf", "lastModified": 1749205496997}, "${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv", "lastModified": 1749205496652}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496840}, "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496952}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv", "lastModified": 1749205496982}, "${BASE}/core/src/test/resources/datasets/format_delta/data/expected_results/FACT_TABLE1.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/format_delta/data/expected_results/FACT_TABLE1.csv", "lastModified": 1749205496758}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496868}, "${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511306_v0_load-date=11-21-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511306_v0_load-date=11-21-2022.json", "lastModified": 1749205496668}, "${BASE}/core/src/test/resources/datasets/weight_conversion/data/expected_results/FACT_BAG_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/weight_conversion/data/expected_results/FACT_BAG_HISTO.csv", "lastModified": 1749205496991}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv", "lastModified": 1749205496784}, "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/input/KEY%20A-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496899}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv", "lastModified": 1749205496782}, "${BASE}/core/src/test/resources/datasets/data_types/data/recordv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/data_types/data/recordv1_load-date=01-01-2018.json", "lastModified": 1749205496732}, "${BASE}/core/src/test/resources/datasets/weight_conversion/data/expected_results/FACT_BAG_GROUP_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/weight_conversion/data/expected_results/FACT_BAG_GROUP_HISTO.csv", "lastModified": 1749205496991}, "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/expected_results/FACT_RESERVATION.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/update_delete/data/batch1/expected_results/FACT_RESERVATION.csv", "lastModified": 1749205496769}, "${BASE}/core/src/test/resources/datasets/proration/data/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/proration/data/README.md", "lastModified": 1749205496810}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496922}, "${BASE}/core/src/test/resources/datasets/purge/data/step1/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/data/step1/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496821}, "${BASE}/core/src/test/resources/views/pnr_simplified/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/pnr_simplified/mapping.conf", "lastModified": 1749205497019}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496835}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-04-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/input/KEY%20A-v3-2023-09-02T12-30-00Z-false_load-date=09-04-2023.json", "lastModified": 1749205496869}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496902}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496927}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496981}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496833}, "${BASE}/core/src/test/resources/views/pnr_simplified/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/pnr_simplified/README.md", "lastModified": 1749205497013}, "${BASE}/core/src/test/resources/config/application-with-event-grid.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/application-with-event-grid.conf", "lastModified": 1749205496621}, "${BASE}/core/src/test/resources/datasets/18/data/a/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/18/data/a/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496690}, "${BASE}/core/src/test/resources/views/sample_1/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/README.md", "lastModified": 1749205497021}, "${BASE}/core/src/test/resources/config/samples/cartesian_product.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/samples/cartesian_product.conf", "lastModified": 1749205496623}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496861}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json", "lastModified": 1749205496778}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY%20A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json", "lastModified": 1749205496890}, "${BASE}/core/src/test/resources/datasets/format_delta/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/format_delta/mapping.conf", "lastModified": 1749205496758}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496932}, "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_partial_reproc/data/a/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496951}, "${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-all-doc-number.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-all-doc-number.json", "lastModified": 1749205496630}, "${BASE}/core/src/test/resources/datasets/13/data/pnr/PNR_aa_ey_uat_22USLC_v15_load-date=08-16-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/13/data/pnr/PNR_aa_ey_uat_22USLC_v15_load-date=08-16-2022.json", "lastModified": 1749205496660}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496963}, "${BASE}/core/src/test/resources/datasets/source_variables/data/expected_results/INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_variables/data/expected_results/INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO.csv", "lastModified": 1749205496988}, "${BASE}/core/src/test/resources/datasets/8/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496706}, "${BASE}/core/src/test/resources/datasets/15/expected_result/asso_air_segment_pax_coupon_histo.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/15/expected_result/asso_air_segment_pax_coupon_histo.csv", "lastModified": 1749205496676}, "${BASE}/core/src/test/resources/datasets/proration/data/MH-PRD_load-date=01-10-2024.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/proration/data/MH-PRD_load-date=01-10-2024.json", "lastModified": 1749205496810}, "${BASE}/core/src/test/resources/views/sample_5/expected/jdf_meta.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_5/expected/jdf_meta.json", "lastModified": 1749205497046}, "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496908}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/dscpax-pnr_v1_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/dscpax-pnr_v1_load-date=02-01-2018.json", "lastModified": 1749205496969}, "${BASE}/core/src/test/resources/views/sample_1/expected/copy_table_snowflake.sql": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/expected/copy_table_snowflake.sql", "lastModified": 1749205497022}, "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496775}, "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_1.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_1.conf", "lastModified": 1749205497033}, "${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_RESERVATION.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_RESERVATION.csv", "lastModified": 1749205496767}, "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_results_not_found.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_results_not_found.csv", "lastModified": 1749205497027}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496876}, "${BASE}/core/src/test/resources/datasets/currency_conversion/simplified_dcsbag.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/simplified_dcsbag.conf", "lastModified": 1749205496731}, "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/tkt/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json", "lastModified": 1749205496652}, "${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json", "lastModified": 1749205496683}, "${BASE}/core/src/test/resources/views/sample_5/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_5/README.md", "lastModified": 1749205497046}, "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496847}, "${BASE}/core/src/test/resources/datasets/latest_correlation/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/README.md", "lastModified": 1749205496776}, "${BASE}/core/src/test/resources/datasets/15/expected_result/asso_air_segment_pax_coupon.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/15/expected_result/asso_air_segment_pax_coupon.csv", "lastModified": 1749205496676}, "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_plus_minus_tables.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/mappings/simple_mapping_plus_minus_tables.conf", "lastModified": 1749205497012}, "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/objectfilter/FACT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/expected_results/objectfilter/FACT_HISTO.csv", "lastModified": 1749205496826}, "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_BAG_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_BAG_HISTO.csv", "lastModified": 1749205496729}, "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v2-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY%20A-v2-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json", "lastModified": 1749205496910}, "${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496640}, "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496955}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/simple_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/simple_mapping.conf", "lastModified": 1749205496964}, "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_00Reservation_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_00Reservation_v1_0_0.plantuml", "lastModified": 1749205497014}, "${BASE}/core/src/test/resources/datasets/18/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/18/README.md", "lastModified": 1749205496688}, "${BASE}/core/src/test/resources/datasets/source_correlation/dcspax.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/dcspax.conf", "lastModified": 1749205496968}, "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_SKD_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_SKD_v1_0_0.plantuml", "lastModified": 1749205497004}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496868}, "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/raw_data/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/update_delete/data/batch1/raw_data/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496771}, "${BASE}/core/src/test/resources/datasets/5/data/batch3/file6_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/5/data/batch3/file6_load-date=01-01-2018.json", "lastModified": 1749205496698}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json", "lastModified": 1749205496781}, "${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/pnr/expected_results/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496651}, "${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496792}, "${BASE}/core/src/test/resources/datasets/correlation_purge/config/tktemd.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/config/tktemd.conf", "lastModified": 1749205496712}, "${BASE}/core/src/test/resources/datasets/17/data/expected_results/FACT_SERVICE_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/17/data/expected_results/FACT_SERVICE_HISTO.csv", "lastModified": 1749205496686}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496916}, "${BASE}/core/src/test/resources/datasets/multi_roots/data/expected_results/DIM_CARRIER.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/data/expected_results/DIM_CARRIER.csv", "lastModified": 1749205496796}, "${BASE}/core/src/test/resources/views/sample_5/expected/create_table_delta.sql": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_5/expected/create_table_delta.sql", "lastModified": 1749205497046}, "${BASE}/core/src/test/resources/datasets/optimize/data/correlationEMD_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/correlationEMD_load-date=01-01-2018.json", "lastModified": 1749205496804}, "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json", "lastModified": 1749205496723}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496949}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/input/KEY%20A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496865}, "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_DCSBAG_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_DCSBAG_v1_0_0.plantuml", "lastModified": 1749205497002}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496905}, "${BASE}/core/src/test/resources/datasets/partial_processing/application-test1.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/partial_processing/application-test1.conf", "lastModified": 1749205496809}, "${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496767}, "${BASE}/core/src/test/resources/datasets/exchange_rate/model.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/exchange_rate/model.conf", "lastModified": 1749205496746}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496873}, "${BASE}/core/src/test/resources/datasets/14/data/pnr/PNR_ab_ey_uat_6I2QQ4_v5_load-date=11-21-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/14/data/pnr/PNR_ab_ey_uat_6I2QQ4_v5_load-date=11-21-2022.json", "lastModified": 1749205496667}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/input/KEY%20A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496893}, "${BASE}/core/src/test/resources/datasets/partial_processing/application-test2.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/partial_processing/application-test2.conf", "lastModified": 1749205496809}, "${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_pnr_tkt_latest_schema.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/correlation_pnr_tkt_latest_schema.conf", "lastModified": 1749205496777}, "${BASE}/core/src/test/resources/datasets/8/data/correlationWPASDF_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/data/correlationWPASDF_load-date=01-01-2018.json", "lastModified": 1749205496705}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY%20A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496845}, "${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_in_all_images.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_in_all_images.csv", "lastModified": 1749205496641}, "${BASE}/core/src/test/resources/stackable/data/expected_results/FACT_OK_HISTO_1.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/stackable/data/expected_results/FACT_OK_HISTO_1.csv", "lastModified": 1749205496994}, "${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/data/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496639}, "${BASE}/core/src/test/resources/datasets/currency_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json", "lastModified": 1749205496726}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch2/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv", "lastModified": 1749205496785}, "${BASE}/core/src/test/resources/datasets/12/expected_result/asso_air_segment_pax_coupon.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/expected_result/asso_air_segment_pax_coupon.csv", "lastModified": 1749205496659}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY%20A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496935}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY%20A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json", "lastModified": 1749205496884}, "${BASE}/core/src/test/resources/datasets/source_variables/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_variables/README.md", "lastModified": 1749205496983}, "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496896}, "${BASE}/core/src/test/resources/config/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/README.md", "lastModified": 1749205496620}, "${BASE}/core/src/test/resources/views/sample_2/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_2/README.md", "lastModified": 1749205497037}, "${BASE}/core/src/test/resources/views/sample_1/expected/jdf_views.sql": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/expected/jdf_views.sql", "lastModified": 1749205497025}, "${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/app.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_6_yaml_swagger/input/app.conf", "lastModified": 1749205497048}, "${BASE}/core/src/test/resources/datasets/external_data/simplified_dcsbag_with_external_data.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/external_data/simplified_dcsbag_with_external_data.conf", "lastModified": 1749205496756}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496930}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496938}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v3-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY%20A-v3-2023-09-02T12-30-00Z-true_load-date=08-31-2023.json", "lastModified": 1749205496921}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/README.md", "lastModified": 1749205496976}, "${BASE}/core/src/test/resources/datasets/revised_pit/revised_pit_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/revised_pit_mapping.conf", "lastModified": 1749205496827}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496869}, "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496848}, "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496914}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/input/KEY A-v2-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/input/KEY%20A-v2-2023-08-30T08-35-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496924}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/b/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/data/b/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496946}, "${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX.csv", "lastModified": 1749205496765}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/mapping.conf", "lastModified": 1749205496738}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496904}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/expected_results/DIM_PASSENGER_TYPE_2.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/expected_results/DIM_PASSENGER_TYPE_2.csv", "lastModified": 1749205496741}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496875}, "${BASE}/core/src/test/resources/datasets/sfpush/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush/README.md", "lastModified": 1749205496940}, "${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json", "lastModified": 1749205496682}, "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_02Segment_Booking_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_02Segment_Booking_v1_0_0.plantuml", "lastModified": 1749205497016}, "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496895}, "${BASE}/core/src/test/resources/datasets/11/simple_raw_vault_mapping_no_vault.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/simple_raw_vault_mapping_no_vault.conf", "lastModified": 1749205496642}, "${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_6_yaml_swagger/README.md", "lastModified": 1749205497048}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/input/KEY%20A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496877}, "${BASE}/core/src/test/resources/views/sample_4/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_4/mapping.conf", "lastModified": 1749205497044}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch1/README.md", "lastModified": 1749205496968}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496867}, "${BASE}/core/src/test/resources/datasets/multi_roots/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/README.md", "lastModified": 1749205496788}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496971}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch3/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496983}, "${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496802}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496891}, "${BASE}/core/src/test/resources/stackable/dummy_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/stackable/dummy_mapping.conf", "lastModified": 1749205496995}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/dscpax-pax1_v1_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/dscpax-pax1_v1_load-date=02-01-2018.json", "lastModified": 1749205496977}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv", "lastModified": 1749205496787}, "${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_11_05_30_45_load-date=03-11-2025.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_11_05_30_45_load-date=03-11-2025.json", "lastModified": 1749205496745}, "${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_01_05_30_45_load-date=03-01-2025.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_01_05_30_45_load-date=03-01-2025.json", "lastModified": 1749205496744}, "${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496763}, "${BASE}/core/src/test/resources/views/model_comp/expected/case3": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/expected/case3", "lastModified": 1749205497009}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/input/KEY A-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/input/KEY%20A-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496907}, "${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/data/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496701}, "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/arrayfilter/FACT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/expected_results/arrayfilter/FACT_HISTO.csv", "lastModified": 1749205496825}, "${BASE}/core/src/test/resources/datasets/data_types/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/data_types/mapping.conf", "lastModified": 1749205496732}, "${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/data/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496638}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v4_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v4_load-date=02-01-2018.json", "lastModified": 1749205496973}, "${BASE}/core/src/test/resources/views/sample_1/expected/create_table_delta.sql": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/expected/create_table_delta.sql", "lastModified": 1749205497023}, "${BASE}/core/src/test/resources/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v11_load-date=19-08-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v11_load-date=19-08-2022.json", "lastModified": 1749205496673}, "${BASE}/core/src/test/resources/datasets/cartesian_product/simplified_pnr.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/cartesian_product/simplified_pnr.conf", "lastModified": 1749205496710}, "${BASE}/core/src/test/resources/datasets/13/expected_result/asso_air_segment_pax_coupon_histo.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/13/expected_result/asso_air_segment_pax_coupon_histo.csv", "lastModified": 1749205496665}, "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496847}, "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_04Ticketing_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_04Ticketing_v1_0_0.plantuml", "lastModified": 1749205497017}, "${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_ad_ey_uat_7492400944169_v3_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/data/tkt/TKT_ad_ey_uat_7492400944169_v3_load-date=05-13-2022.json", "lastModified": 1749205496683}, "${BASE}/core/src/test/resources/views/sample_7/input/dcspax.yaml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_7/input/dcspax.yaml", "lastModified": 1749205497052}, "${BASE}/core/src/test/resources/datasets/correlation_purge/config/corr.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/config/corr.conf", "lastModified": 1749205496710}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496871}, "${BASE}/core/src/test/resources/datasets/latest/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/README.md", "lastModified": 1749205496760}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496857}, "${BASE}/core/src/test/resources/datasets/14/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/14/README.md", "lastModified": 1749205496665}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496925}, "${BASE}/core/src/test/resources/views/sample_8/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_8/README.md", "lastModified": 1749205497054}, "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/mappings/simple_mapping.conf", "lastModified": 1749205497010}, "${BASE}/core/src/test/resources/views/sample_6_yaml_swagger/input/dcspax.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_6_yaml_swagger/input/dcspax.conf", "lastModified": 1749205497049}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496925}, "${BASE}/core/src/test/resources/config/multi_roots/sample_config.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/multi_roots/sample_config.conf", "lastModified": 1749205496622}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496842}, "${BASE}/core/src/test/resources/datasets/source_correlation/correlation_dcspax_pnr.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/correlation_dcspax_pnr.conf", "lastModified": 1749205496966}, "${BASE}/core/src/test/resources/datasets/16/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/README.md", "lastModified": 1749205496677}, "${BASE}/core/src/test/resources/views/sample_1/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/mapping.conf", "lastModified": 1749205497025}, "${BASE}/core/src/test/resources/datasets/17/data/service_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/17/data/service_load-date=01-01-2018.json", "lastModified": 1749205496687}, "${BASE}/core/src/test/resources/datasets/purge/data/step2/expected_results/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/data/step2/expected_results/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496822}, "${BASE}/core/src/test/resources/views/sample_11/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_11/README.md", "lastModified": 1749205497036}, "${BASE}/core/src/test/resources/stackable/data/expected_results_failure/FACT_OK_HISTO_1.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/stackable/data/expected_results_failure/FACT_OK_HISTO_1.csv", "lastModified": 1749205496994}, "${BASE}/core/src/test/resources/datasets/18/data/b/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/18/data/b/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496692}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496863}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496978}, "${BASE}/core/src/test/resources/datasets/expressions/model.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/expressions/model.conf", "lastModified": 1749205496753}, "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json", "lastModified": 1749205496722}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496853}, "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496914}, "${BASE}/core/src/test/resources/datasets/11/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/README.md", "lastModified": 1749205496636}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/bag_load-date=01-01-2099.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/bag_load-date=01-01-2099.json", "lastModified": 1749205496736}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496922}, "${BASE}/core/src/test/resources/datasets/external_data/data/expected_results/FACT_BAG_GROUP_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/external_data/data/expected_results/FACT_BAG_GROUP_HISTO.csv", "lastModified": 1749205496755}, "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/expected_results/FACT_AIR_SEGMENT_PAX.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/update_delete/data/batch2/expected_results/FACT_AIR_SEGMENT_PAX.csv", "lastModified": 1749205496772}, "${BASE}/core/src/test/resources/views/sample_2/expected/doc.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_2/expected/doc.md", "lastModified": 1749205497038}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496873}, "${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/data/step1/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json", "lastModified": 1749205496818}, "${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step3/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/expected_results/step3/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv", "lastModified": 1749205496713}, "${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationEMD_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/data/correlationEMD_load-date=02-01-2018.json", "lastModified": 1749205496794}, "${BASE}/core/src/test/resources/datasets/cartesian_product/data/expected_results/FACT_PRODUCT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/cartesian_product/data/expected_results/FACT_PRODUCT_PAX_HISTO.csv", "lastModified": 1749205496708}, "${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step2/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/expected_results/step2/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv", "lastModified": 1749205496713}, "${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-dupe-primary-doc-number.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-dupe-primary-doc-number.json", "lastModified": 1749205496630}, "${BASE}/core/src/test/resources/views/sample_1/app.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/app.conf", "lastModified": 1749205497021}, "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/nofilter/FACT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/expected_results/nofilter/FACT_HISTO.csv", "lastModified": 1749205496826}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3_load-date=02-01-2018.json", "lastModified": 1749205496972}, "${BASE}/core/src/test/resources/datasets/source_variables/simple_raw_vault_mapping_no_vault.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_variables/simple_raw_vault_mapping_no_vault.conf", "lastModified": 1749205496988}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496902}, "${BASE}/core/src/test/resources/datasets/12/data/tkt/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/tkt/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv", "lastModified": 1749205496658}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496859}, "${BASE}/core/src/test/resources/datasets/weight_conversion/weight_fail_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/weight_conversion/weight_fail_mapping.conf", "lastModified": 1749205496992}, "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/correlationfilter/FACT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/expected_results/correlationfilter/FACT_HISTO.csv", "lastModified": 1749205496826}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496981}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496876}, "${BASE}/core/src/test/resources/views/corr_simplified/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/corr_simplified/mapping.conf", "lastModified": 1749205497007}, "${BASE}/core/src/test/resources/datasets/5/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/5/README.md", "lastModified": 1749205496695}, "${BASE}/core/src/test/resources/datasets/expressions/data/correlationEMD_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/expressions/data/correlationEMD_load-date=01-01-2018.json", "lastModified": 1749205496751}, "${BASE}/core/src/test/resources/datasets/12/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/README.md", "lastModified": 1749205496642}, "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json", "lastModified": 1749205496718}, "${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_aa_ey_uat_N389UA_v6_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/data/pnr/PNR_aa_ey_uat_N389UA_v6_load-date=05-13-2022.json", "lastModified": 1749205496677}, "${BASE}/core/src/test/resources/datasets/external_data/data/expected_results/FACT_BAG_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/external_data/data/expected_results/FACT_BAG_HISTO.csv", "lastModified": 1749205496755}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_1Input/data/expected_results/DIM_PASSENGER_TYPE_1.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_1Input/data/expected_results/DIM_PASSENGER_TYPE_1.csv", "lastModified": 1749205496739}, "${BASE}/core/src/test/resources/views/sample_7/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_7/README.md", "lastModified": 1749205497051}, "${BASE}/core/src/test/resources/datasets/source_correlation/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/README.md", "lastModified": 1749205496966}, "${BASE}/core/src/test/resources/datasets/latest/simple_mapping_no_vault_latest.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/simple_mapping_no_vault_latest.conf", "lastModified": 1749205496767}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496928}, "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_plus_minus_columns.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/mappings/simple_mapping_plus_minus_columns.conf", "lastModified": 1749205497012}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv", "lastModified": 1749205496978}, "${BASE}/core/src/test/resources/config/samples/pnr.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/samples/pnr.conf", "lastModified": 1749205496625}, "${BASE}/core/src/test/resources/views/model_comp/expected/case5": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/expected/case5", "lastModified": 1749205497009}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496861}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/README.md", "lastModified": 1749205496956}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496879}, "${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496789}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_srcAdditionalCols/data/PASSENGER_TYPE.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_srcAdditionalCols/data/PASSENGER_TYPE.csv", "lastModified": 1749205496742}, "${BASE}/core/src/test/resources/datasets/optimize/simple_mapping_zorder.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/simple_mapping_zorder.conf", "lastModified": 1749205496808}, "${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ad_ey_pdt_USCK5S_6072160023101_v16_load-date=07-04-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/pnr/PNR_ad_ey_pdt_USCK5S_6072160023101_v16_load-date=07-04-2022.json", "lastModified": 1749205496649}, "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/tktemd/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv", "lastModified": 1749205496725}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496948}, "${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/pnr/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json", "lastModified": 1749205496645}, "${BASE}/core/src/test/resources/datasets/8/data/correlationEMD_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/data/correlationEMD_load-date=01-01-2018.json", "lastModified": 1749205496703}, "${BASE}/core/src/test/resources/datasets/purge/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/README.md", "lastModified": 1749205496815}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/input/KEY%20B-v2-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496838}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496944}, "${BASE}/core/src/test/resources/datasets/10/simple_raw_vault_mapping_no_vault_multi_section.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/10/simple_raw_vault_mapping_no_vault_multi_section.conf", "lastModified": 1749205496634}, "${BASE}/core/src/test/resources/datasets/10/data/expected_results/FACT_MULTI_SECTION.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/10/data/expected_results/FACT_MULTI_SECTION.csv", "lastModified": 1749205496634}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/DIM_PASSENGER_TYPE_5.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/DIM_PASSENGER_TYPE_5.csv", "lastModified": 1749205496736}, "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_v1_0_0.plantuml", "lastModified": 1749205497006}, "${BASE}/core/src/test/resources/views/sample_11/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_11/mapping.conf", "lastModified": 1749205497037}, "${BASE}/core/src/test/resources/datasets/proration/data/expected_results/FACT_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/proration/data/expected_results/FACT_COUPON_HISTO.csv", "lastModified": 1749205496812}, "${BASE}/core/src/test/resources/stackable/error_handling_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/stackable/error_handling_mapping.conf", "lastModified": 1749205496997}, "${BASE}/core/src/test/resources/datasets/proration/extdata/input_optd_por_public_sample.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/proration/extdata/input_optd_por_public_sample.csv", "lastModified": 1749205496813}, "${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationWPASDF_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/data/correlationWPASDF_load-date=01-01-2018.json", "lastModified": 1749205496796}, "${BASE}/core/src/test/resources/datasets/nullvalues/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/nullvalues/README.md", "lastModified": 1749205496797}, "${BASE}/core/src/test/resources/views/sample_7/input/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_7/input/mapping.conf", "lastModified": 1749205497054}, "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496848}, "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_3.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_3.conf", "lastModified": 1749205497034}, "${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-primary-doc-number.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/1/data/sample-ticketing-reference-with-primary-doc-number.json", "lastModified": 1749205496632}, "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496898}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496880}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_3_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/integration/step_3_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv", "lastModified": 1749205496737}, "${BASE}/core/src/test/resources/views/model_comp/expected/case4": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/expected/case4", "lastModified": 1749205497009}, "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_DCSPAX_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_DCSPAX_v1_0_0.plantuml", "lastModified": 1749205497002}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE.csv", "lastModified": 1749205496740}, "${BASE}/core/src/test/resources/datasets/recordsfilter/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/mapping.conf", "lastModified": 1749205496827}, "${BASE}/core/src/test/resources/validation/sample_9/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/validation/sample_9/README.md", "lastModified": 1749205497001}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496962}, "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_2_CURRENCY_CONVERSIONS_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_2_CURRENCY_CONVERSIONS_HISTO.csv", "lastModified": 1749205496727}, "${BASE}/core/src/test/resources/datasets/cartesian_product/data/PNR_6Q5JNY_v10_load-date=01-01-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/cartesian_product/data/PNR_6Q5JNY_v10_load-date=01-01-2023.json", "lastModified": 1749205496708}, "${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496803}, "${BASE}/core/src/test/resources/datasets/14/expected_result/asso_air_segment_pax_coupon.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/14/expected_result/asso_air_segment_pax_coupon.csv", "lastModified": 1749205496671}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/input/KEY%20A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json", "lastModified": 1749205496926}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496887}, "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_PNR_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_PNR_v1_0_0.plantuml", "lastModified": 1749205497004}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/application.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/application.conf", "lastModified": 1749205496733}, "${BASE}/core/src/test/resources/config/novault-integration-test.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/novault-integration-test.conf", "lastModified": 1749205496622}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496844}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496936}, "${BASE}/core/src/test/resources/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v12_load-date=19-08-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/15/data/pnr/PNR_aa_ey_uat_NP84R4_v12_load-date=19-08-2022.json", "lastModified": 1749205496674}, "${BASE}/core/src/test/resources/datasets/json_processor/generic_item_example.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/json_processor/generic_item_example.json", "lastModified": 1749205496760}, "${BASE}/core/src/test/resources/datasets/2/data/complex-ticketing-reference.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/2/data/complex-ticketing-reference.json", "lastModified": 1749205496694}, "${BASE}/core/src/test/resources/datasets/12/data/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496650}, "${BASE}/core/src/test/resources/datasets/revised_pit/template.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/template.json", "lastModified": 1749205496938}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv", "lastModified": 1749205496981}, "${BASE}/core/src/test/resources/datasets/13/data/tkt/TKT_aa_ey_uat_0142172648268_v9_load-date=08-16-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/13/data/tkt/TKT_aa_ey_uat_0142172648268_v9_load-date=08-16-2022.json", "lastModified": 1749205496663}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_dimAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_3.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_dimAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_3.csv", "lastModified": 1749205496742}, "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496909}, "${BASE}/core/src/test/resources/datasets/13/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/13/README.md", "lastModified": 1749205496660}, "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY A-v1-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/input/KEY%20A-v1-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496910}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_srcAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_4.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_srcAdditionalCols/data/expected_results/DIM_PASSENGER_TYPE_4.csv", "lastModified": 1749205496742}, "${BASE}/core/src/test/resources/datasets/single_table/single_table_model.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/single_table/single_table_model.conf", "lastModified": 1749205496965}, "${BASE}/core/src/test/resources/stackable/data/expected_results_failure/FACT_OK_HISTO_2.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/stackable/data/expected_results_failure/FACT_OK_HISTO_2.csv", "lastModified": 1749205496995}, "${BASE}/core/src/test/resources/views/sample_2/expected/jdf_meta.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_2/expected/jdf_meta.json", "lastModified": 1749205497039}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496976}, "${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2020.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2020.json", "lastModified": 1749205496823}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496937}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496842}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch1/raw_data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json", "lastModified": 1749205496782}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496871}, "${BASE}/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_02_05_30_45_load-date=03-02-2025.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/exchange_rate/data/RFD_RFD_refdatacurrency_Currency_CurrencyRate_VERSION=1_2025_03_02_05_30_45_load-date=03-02-2025.json", "lastModified": 1749205496745}, "${BASE}/core/src/test/resources/datasets/correlation_purge/expected_results/step1/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/expected_results/step1/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv", "lastModified": 1749205496712}, "${BASE}/core/src/test/resources/datasets/16/expected_result/asso_air_segment_pax_coupon.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/expected_result/asso_air_segment_pax_coupon.csv", "lastModified": 1749205496684}, "${BASE}/core/src/test/resources/datasets/latest/simple_mapping_no_vault_latest_schema.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/simple_mapping_no_vault_latest_schema.conf", "lastModified": 1749205496768}, "${BASE}/core/src/test/resources/datasets/10/data/correlationEMD_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/10/data/correlationEMD_load-date=01-01-2018.json", "lastModified": 1749205496633}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496932}, "${BASE}/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/insert/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496765}, "${BASE}/core/src/test/resources/datasets/17/views/expected_delta_create_multi_root_mapping.sql": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/17/views/expected_delta_create_multi_root_mapping.sql", "lastModified": 1749205496688}, "${BASE}/core/src/test/resources/config/application-purge.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/application-purge.conf", "lastModified": 1749205496620}, "${BASE}/core/src/test/resources/validation/sample_1/expected_results/test_results.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/validation/sample_1/expected_results/test_results.csv", "lastModified": 1749205496999}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496958}, "${BASE}/core/src/test/resources/datasets/expressions/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/expressions/README.md", "lastModified": 1749205496746}, "${BASE}/core/src/test/resources/datasets/proration/simplified_tktemd.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/proration/simplified_tktemd.conf", "lastModified": 1749205496814}, "${BASE}/core/src/test/resources/datasets/14/data/pnr/PNR_aa_ey_uat_6I2QQ4_v4_load-date=11-21-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/14/data/pnr/PNR_aa_ey_uat_6I2QQ4_v4_load-date=11-21-2022.json", "lastModified": 1749205496666}, "${BASE}/core/src/test/resources/datasets/exchange_rate/data/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/exchange_rate/data/README.md", "lastModified": 1749205496744}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496937}, "${BASE}/core/src/test/resources/datasets/single_table/data/dscbag_paxcorr_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/single_table/data/dscbag_paxcorr_load-date=02-01-2018.json", "lastModified": 1749205496965}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496887}, "${BASE}/core/src/test/resources/datasets/17/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/17/README.md", "lastModified": 1749205496686}, "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_v1_0_0.plantuml", "lastModified": 1749205497018}, "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496898}, "${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/data/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496700}, "${BASE}/core/src/test/resources/datasets/json_processor/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/json_processor/README.md", "lastModified": 1749205496760}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv", "lastModified": 1749205496976}, "${BASE}/core/src/test/resources/config/application-with-snowflake.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/application-with-snowflake.conf", "lastModified": 1749205496621}, "${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511307_v0_load-date=11-21-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511307_v0_load-date=11-21-2022.json", "lastModified": 1749205496668}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/input/KEY%20A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496884}, "${BASE}/core/src/test/resources/datasets/weight_conversion/simplified_dcsbag.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/weight_conversion/simplified_dcsbag.conf", "lastModified": 1749205496991}, "${BASE}/core/src/test/resources/datasets/8/data/expected_results/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/data/expected_results/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496706}, "${BASE}/core/src/test/resources/datasets/5/data/batch1/file1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/5/data/batch1/file1_load-date=01-01-2018.json", "lastModified": 1749205496696}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496855}, "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496719}, "${BASE}/core/src/test/resources/datasets/exchange_rate/data/expected_results/EXCHANGE_RATE_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/exchange_rate/data/expected_results/EXCHANGE_RATE_HISTO.csv", "lastModified": 1749205496746}, "${BASE}/core/src/test/resources/datasets/optimize/data/correlationWPASDF_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/correlationWPASDF_load-date=01-01-2018.json", "lastModified": 1749205496806}, "${BASE}/core/src/test/resources/datasets/multi_roots/data/correlationEMD_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/data/correlationEMD_load-date=01-01-2018.json", "lastModified": 1749205496793}, "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json", "lastModified": 1749205496716}, "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496719}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv", "lastModified": 1749205496777}, "${BASE}/core/src/test/resources/stackable/data/dscbag_paxcorr_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/stackable/data/dscbag_paxcorr_load-date=02-01-2018.json", "lastModified": 1749205496993}, "${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/pnr/PNR_aa_ey_pdt_USCK5S_6072160023101_v0_load-date=06-29-2022.json", "lastModified": 1749205496643}, "${BASE}/core/src/test/resources/datasets/multi_roots/simple_raw_vault_mapping_no_vault_dim.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/simple_raw_vault_mapping_no_vault_dim.conf", "lastModified": 1749205496797}, "${BASE}/core/src/test/resources/views/sample_4/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_4/README.md", "lastModified": 1749205497042}, "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION.csv", "lastModified": 1749205496727}, "${BASE}/core/src/test/resources/datasets/13/data/pnr/PNR_ab_ey_uat_22USLC_v20_load-date=08-16-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/13/data/pnr/PNR_ab_ey_uat_22USLC_v20_load-date=08-16-2022.json", "lastModified": 1749205496662}, "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_BAGS_GROUP_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_BAGS_GROUP_HISTO.csv", "lastModified": 1749205496729}, "${BASE}/core/src/test/resources/datasets/nullvalues/data/expected_results/FACT_NULLVALUES.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/nullvalues/data/expected_results/FACT_NULLVALUES.csv", "lastModified": 1749205496799}, "${BASE}/core/src/test/resources/views/sample_4/not_failing_mapping_with_variables.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_4/not_failing_mapping_with_variables.conf", "lastModified": 1749205497044}, "${BASE}/core/src/test/resources/views/sample_1/expected/copy_table_delta.sql": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/expected/copy_table_delta.sql", "lastModified": 1749205497022}, "${BASE}/core/src/test/resources/datasets/purge/data/step2/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/data/step2/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496822}, "${BASE}/core/src/test/resources/datasets/expressions/data/bagsmanylegs_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/expressions/data/bagsmanylegs_load-date=01-01-2018.json", "lastModified": 1749205496748}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch3/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv", "lastModified": 1749205496786}, "${BASE}/core/src/test/resources/datasets/8/data/correlationEMD_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/data/correlationEMD_load-date=02-01-2018.json", "lastModified": 1749205496704}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496923}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3bis_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v3bis_load-date=02-01-2018.json", "lastModified": 1749205496973}, "${BASE}/core/src/test/resources/views/sample_11/app.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_11/app.conf", "lastModified": 1749205497036}, "${BASE}/core/src/test/resources/datasets/multi_roots/data/expected_results/DIM_POINT_OF_SALE.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/data/expected_results/DIM_POINT_OF_SALE.csv", "lastModified": 1749205496796}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496932}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496830}, "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_10_yaml_asyncapi/README.md", "lastModified": 1749205497025}, "${BASE}/core/src/test/resources/views/sample_7/expected/jdf_meta.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_7/expected/jdf_meta.json", "lastModified": 1749205497052}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496888}, "${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496762}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496928}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/simple_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/simple_mapping.conf", "lastModified": 1749205496950}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_1_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/integration/step_1_prefiller/data/expected_results/DIM_PASSENGER_TYPE_5.csv", "lastModified": 1749205496735}, "${BASE}/core/src/test/resources/datasets/format_delta/data/delta/part-00000-ab18c1bd-10cd-4650-bab9-38eb85452f0d-c000.snappy.parquet": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/format_delta/data/delta/part-00000-ab18c1bd-10cd-4650-bab9-38eb85452f0d-c000.snappy.parquet", "lastModified": 1749205496758}, "${BASE}/core/src/test/resources/views/sample_1/expected/doc.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/expected/doc.md", "lastModified": 1749205497024}, "${BASE}/core/src/test/resources/datasets/18/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/18/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496693}, "${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_pnr_tkt_latest.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/correlation_pnr_tkt_latest.conf", "lastModified": 1749205496777}, "${BASE}/core/src/test/resources/views/model_comp/expected/case6": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/expected/case6", "lastModified": 1749205497010}, "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/pnr/data/PNR_ab_ey_pdt_USCK5S_6072160023101_v8_load-date=06-29-2022.json", "lastModified": 1749205496717}, "${BASE}/core/src/test/resources/datasets/sfpush/expected_results/data/FACT_HISTO_MIRROR.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush/expected_results/data/FACT_HISTO_MIRROR.csv", "lastModified": 1749205496942}, "${BASE}/core/src/test/resources/datasets/source_variables/data/a/6JDA9D-2023-01-18_v14_load-date=10-30-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_variables/data/a/6JDA9D-2023-01-18_v14_load-date=10-30-2022.json", "lastModified": 1749205496985}, "${BASE}/core/src/test/resources/datasets/purge/data/step1/expected_results/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/data/step1/expected_results/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496821}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch1/data/expected_results/dcspax/FACT_PASSENGER_HISTO.csv", "lastModified": 1749205496970}, "${BASE}/core/src/test/resources/views/sample_7/expected/doc_enriched.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_7/expected/doc_enriched.md", "lastModified": 1749205497052}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch1/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json", "lastModified": 1749205496783}, "${BASE}/core/src/test/resources/datasets/sfpush/application.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush/application.conf", "lastModified": 1749205496940}, "${BASE}/core/src/test/resources/views/sample_1/expected/star-schema_TEST_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/expected/star-schema_TEST_v1_0_0.plantuml", "lastModified": 1749205497025}, "${BASE}/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s6-dup_histo_no_prev/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496908}, "${BASE}/core/src/test/resources/datasets/optimize/data/correlationEMD_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/correlationEMD_load-date=02-01-2018.json", "lastModified": 1749205496804}, "${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/data/step1/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json", "lastModified": 1749205496820}, "${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_dcspax_pnr_latest_schema.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/correlation_dcspax_pnr_latest_schema.conf", "lastModified": 1749205496777}, "${BASE}/core/src/test/resources/config/selectors/sample_config.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/selectors/sample_config.conf", "lastModified": 1749205496627}, "${BASE}/core/src/test/resources/views/corr_simplified/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/corr_simplified/README.md", "lastModified": 1749205497002}, "${BASE}/core/src/test/resources/config/multi_roots/sample_config_wrong.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/multi_roots/sample_config_wrong.conf", "lastModified": 1749205496622}, "${BASE}/core/src/test/resources/views/sample_8/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_8/mapping.conf", "lastModified": 1749205497054}, "${BASE}/core/src/test/resources/datasets/purge/data/step1/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/data/step1/PNR_ab_ey_pdt_AAAAAA_6666666666666_v8_load-date=06-29-2022.json", "lastModified": 1749205496816}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496882}, "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_1_CURRENCY_CONVERSION_HISTO.csv", "lastModified": 1749205496727}, "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496894}, "${BASE}/core/src/test/resources/views/sample_9/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_9/README.md", "lastModified": 1749205497055}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496875}, "${BASE}/core/src/test/resources/datasets/sfpush/data/bag_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush/data/bag_load-date=01-01-2018.json", "lastModified": 1749205496941}, "${BASE}/core/src/test/resources/datasets/latest/update_delete/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/update_delete/README.md", "lastModified": 1749205496768}, "${BASE}/core/src/test/resources/datasets/8/simple_raw_vault_mapping_no_vault.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/simple_raw_vault_mapping_no_vault.conf", "lastModified": 1749205496707}, "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/expected_results/FACT_RESERVATION.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/update_delete/data/batch2/expected_results/FACT_RESERVATION.csv", "lastModified": 1749205496772}, "${BASE}/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511308_v0_load-date=11-21-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/14/data/tkt/TKT_aa_ey_uat_6076508511308_v0_load-date=11-21-2022.json", "lastModified": 1749205496670}, "${BASE}/core/src/test/resources/views/sample_3/expected/jdf_meta.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_3/expected/jdf_meta.json", "lastModified": 1749205497041}, "${BASE}/core/src/test/resources/datasets/currency_conversion/currency_fail_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/currency_fail_mapping.conf", "lastModified": 1749205496726}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496891}, "${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/data/pnr/PNR_ad_ey_uat_N389UA_v9_load-date=05-13-2022.json", "lastModified": 1749205496681}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch1/data/expected_results/dcspax/INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496979}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496883}, "${BASE}/core/src/test/resources/validation/sample_2/0/2024/03/22/30.avro": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/validation/sample_2/0/2024/03/22/30.avro", "lastModified": 1749205497000}, "${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2_master_algo.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2_master_algo.csv", "lastModified": 1749205496641}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/data/a/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496960}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496906}, "${BASE}/core/src/test/resources/datasets/18/simple_raw_vault_mapping_no_vault.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/18/simple_raw_vault_mapping_no_vault.conf", "lastModified": 1749205496693}, "${BASE}/core/src/test/resources/datasets/latest/insert/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/insert/README.md", "lastModified": 1749205496760}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496840}, "${BASE}/core/src/test/resources/datasets/proration/data/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/proration/data/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv", "lastModified": 1749205496812}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/expected_results/corr/ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO.csv", "lastModified": 1749205496975}, "${BASE}/core/src/test/resources/views/sample_3/expected/full_doc.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_3/expected/full_doc.md", "lastModified": 1749205497040}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496839}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496918}, "${BASE}/core/src/test/resources/views/sample_9/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_9/mapping.conf", "lastModified": 1749205497055}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/integration/README.md", "lastModified": 1749205496733}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496866}, "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/b/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496956}, "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ad_ey_pdt_USCK5S_6072160023101_v5_load-date=07-04-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/tkt/TKT_ad_ey_pdt_USCK5S_6072160023101_v5_load-date=07-04-2022.json", "lastModified": 1749205496654}, "${BASE}/core/src/test/resources/datasets/weight_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/weight_conversion/data/dscbag_paxcorr_load-date=02-01-2018.json", "lastModified": 1749205496990}, "${BASE}/core/src/test/resources/validation/sample_1/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/validation/sample_1/mapping.conf", "lastModified": 1749205496999}, "${BASE}/core/src/test/resources/datasets/2/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/2/README.md", "lastModified": 1749205496694}, "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_only_meta_changes.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/mappings/simple_mapping_only_meta_changes.conf", "lastModified": 1749205497010}, "${BASE}/core/src/test/resources/views/model_comp/expected/case2": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/expected/case2", "lastModified": 1749205497008}, "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496851}, "${BASE}/core/src/test/resources/datasets/5/data/batch2/file3_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/5/data/batch2/file3_load-date=01-01-2018.json", "lastModified": 1749205496696}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496887}, "${BASE}/core/src/test/resources/datasets/correlation_purge/config/pnr.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/config/pnr.conf", "lastModified": 1749205496710}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496855}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE2.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_2Input/data/PASSENGER_TYPE2.csv", "lastModified": 1749205496740}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch2/README.md", "lastModified": 1749205496971}, "${BASE}/core/src/test/resources/datasets/17/multi_root_mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/17/multi_root_mapping.conf", "lastModified": 1749205496687}, "${BASE}/core/src/test/resources/datasets/recordsfilter/expected_results/objectelementfilter/FACT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/expected_results/objectelementfilter/FACT_HISTO.csv", "lastModified": 1749205496826}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/b/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/data/expected_results/b/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496964}, "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_results.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_results.csv", "lastModified": 1749205497026}, "${BASE}/core/src/test/resources/datasets/14/expected_result/asso_air_segment_pax_coupon_histo.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/14/expected_result/asso_air_segment_pax_coupon_histo.csv", "lastModified": 1749205496671}, "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/b/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_partial_reproc/data/b/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496953}, "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_all_changes.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/mappings/simple_mapping_all_changes.conf", "lastModified": 1749205497010}, "${BASE}/core/src/test/resources/views/sample_1/expected/create_table_snowflake.sql": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/expected/create_table_snowflake.sql", "lastModified": 1749205497023}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/data/a/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496959}, "${BASE}/core/src/test/resources/datasets/15/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/15/README.md", "lastModified": 1749205496671}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/data/a/aaabbb_bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496957}, "${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ac_ey_uat_N389UA_v8_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/data/pnr/PNR_ac_ey_uat_N389UA_v8_load-date=05-13-2022.json", "lastModified": 1749205496680}, "${BASE}/core/src/test/resources/datasets/sfpush/expected_results/data/FACT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush/expected_results/data/FACT_HISTO.csv", "lastModified": 1749205496941}, "${BASE}/core/src/test/resources/datasets/12/data/tkt/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/tkt/expected_results/FACT_TRAVEL_DOCUMENT_HISTO.csv", "lastModified": 1749205496657}, "${BASE}/core/src/test/resources/validation/sample_1/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/validation/sample_1/README.md", "lastModified": 1749205496997}, "${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables_literal.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_4/failing_mapping_with_variables_literal.conf", "lastModified": 1749205497043}, "${BASE}/core/src/test/resources/datasets/latest_correlation/correlation_dcspax_pnr_latest.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/correlation_dcspax_pnr_latest.conf", "lastModified": 1749205496776}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496829}, "${BASE}/core/src/test/resources/datasets/16/data/pnr/PNR_ab_ey_uat_N389UA_v7_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/data/pnr/PNR_ab_ey_uat_N389UA_v7_load-date=05-13-2022.json", "lastModified": 1749205496679}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/data/a/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496945}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496926}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/input/KEY%20A-v2-2023-08-30T10-41-00Z-true_load-date=08-31-2023.json", "lastModified": 1749205496918}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496901}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496835}, "${BASE}/core/src/test/resources/stackable/data/expected_results/FACT_OK_HISTO_2.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/stackable/data/expected_results/FACT_OK_HISTO_2.csv", "lastModified": 1749205496994}, "${BASE}/core/src/test/resources/views/sample_2/expected/create_table_delta.sql": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_2/expected/create_table_delta.sql", "lastModified": 1749205497038}, "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/tktemd/expected_results/TKT_PNR_PARTIAL_CORR_PIT.csv", "lastModified": 1749205496725}, "${BASE}/core/src/test/resources/datasets/5/data/batch2/file2_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/5/data/batch2/file2_load-date=01-01-2018.json", "lastModified": 1749205496696}, "${BASE}/core/src/test/resources/datasets/currency_conversion/data/input/exchange_rates/xrt.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/data/input/exchange_rates/xrt.json", "lastModified": 1749205496730}, "${BASE}/core/src/test/resources/datasets/11/data/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/data/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496636}, "${BASE}/core/src/test/resources/views/sample_1/expected/full_doc.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_1/expected/full_doc.md", "lastModified": 1749205497024}, "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order-asyncapi.yaml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_10_yaml_asyncapi/order-asyncapi.yaml", "lastModified": 1749205497032}, "${BASE}/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/insert/data/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496765}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_dimAdditionalCols/data/PASSENGER_TYPE.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_dimAdditionalCols/data/PASSENGER_TYPE.csv", "lastModified": 1749205496741}, "${BASE}/core/src/test/resources/views/sample_2/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_2/mapping.conf", "lastModified": 1749205497039}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch3/raw_data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch3/raw_data/tkt/TKT_ac_ey_uat_7492400944169_v2_load-date=05-13-2022.json", "lastModified": 1749205496787}, "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496898}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/a/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496948}, "${BASE}/core/src/test/resources/datasets/5/data/batch3/file4_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/5/data/batch3/file4_load-date=01-01-2018.json", "lastModified": 1749205496698}, "${BASE}/core/src/test/resources/datasets/12/expected_result/asso_air_segment_pax_coupon_histo.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/expected_result/asso_air_segment_pax_coupon_histo.csv", "lastModified": 1749205496659}, "${BASE}/core/src/test/resources/datasets/8/data/bagsmanylegsv3_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/8/data/bagsmanylegsv3_load-date=02-01-2018.json", "lastModified": 1749205496702}, "${BASE}/core/src/test/resources/datasets/16/expected_result/asso_air_segment_pax_coupon_histo.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/expected_result/asso_air_segment_pax_coupon_histo.csv", "lastModified": 1749205496684}, "${BASE}/core/src/test/resources/views/sample_5/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_5/mapping.conf", "lastModified": 1749205497048}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496900}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch0/raw_data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json", "lastModified": 1749205496780}, "${BASE}/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_03Service_Booking_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/pnr_simplified/expected/star-schema_PNR_03Service_Booking_v1_0_0.plantuml", "lastModified": 1749205497017}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2-rerun/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496856}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496882}, "${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2019.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2019.json", "lastModified": 1749205496823}, "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_AAAAAA_6666666666666_v0_load-date=06-29-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_aa_ey_pdt_AAAAAA_6666666666666_v0_load-date=06-29-2022.json", "lastModified": 1749205496722}, "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_2.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_10_yaml_asyncapi/order_sample_2.conf", "lastModified": 1749205497034}, "${BASE}/core/src/test/resources/config/application-test.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/application-test.conf", "lastModified": 1749205496621}, "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/update_delete/data/batch2/raw_data/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496773}, "${BASE}/core/src/test/resources/datasets/latest/update_delete/data/batch1/expected_results/FACT_AIR_SEGMENT_PAX.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest/update_delete/data/batch1/expected_results/FACT_AIR_SEGMENT_PAX.csv", "lastModified": 1749205496769}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496864}, "${BASE}/core/src/test/resources/datasets/purge/pnr.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/purge/pnr.conf", "lastModified": 1749205496822}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch0/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON_HISTO.csv", "lastModified": 1749205496778}, "${BASE}/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_TKTEMD_v1_0_0.plantuml": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/corr_simplified/expected/star-schema_CORR_TKTEMD_v1_0_0.plantuml", "lastModified": 1749205497004}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496854}, "${BASE}/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_stats.txt": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_10_yaml_asyncapi/expected/lookup_stats.txt", "lastModified": 1749205497030}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/FACT_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/integration/step_2_j2s/data/expected_results/FACT_HISTO.csv", "lastModified": 1749205496737}, "${BASE}/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s5-same_versions_with_histo/data/batch1/input/KEY%20A-v2-2023-08-30T10-41-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496904}, "${BASE}/core/src/test/resources/views/model_comp/mappings/simple_mapping_changed_columns.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/mappings/simple_mapping_changed_columns.conf", "lastModified": 1749205497010}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch3-rerun/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496866}, "${BASE}/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s9-old_versions_with_table_histo/data/batch1/input/KEY%20A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json", "lastModified": 1749205496933}, "${BASE}/core/src/test/resources/datasets/source_variables/data/a/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_variables/data/a/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496986}, "${BASE}/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/data/bag_load-date=01-01-2018.json", "lastModified": 1749205496823}, "${BASE}/core/src/test/resources/datasets/12/data/pnr/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/pnr/PNR_ac_ey_pdt_USCK5S_6072160023101_v11_load-date=07-04-2022.json", "lastModified": 1749205496648}, "${BASE}/core/src/test/resources/datasets/16/data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/data/tkt/TKT_aa_ey_uat_7492400944169_v0_load-date=05-13-2022.json", "lastModified": 1749205496682}, "${BASE}/core/src/test/resources/datasets/expressions/data/expected_results/FACT_TRANSFORMATION.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/expressions/data/expected_results/FACT_TRANSFORMATION.csv", "lastModified": 1749205496753}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/test_1Input/data/PASSENGER_TYPE.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/test_1Input/data/PASSENGER_TYPE.csv", "lastModified": 1749205496739}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496878}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496857}, "${BASE}/core/src/test/resources/views/model_comp/expected/case1": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/model_comp/expected/case1", "lastModified": 1749205497008}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_3_prefiller/data/input/PASSENGER_TYPE.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/integration/step_3_prefiller/data/input/PASSENGER_TYPE.csv", "lastModified": 1749205496738}, "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496954}, "${BASE}/core/src/test/resources/datasets/15/data/expected_results/FACT_SERVICE_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/15/data/expected_results/FACT_SERVICE_HISTO.csv", "lastModified": 1749205496672}, "${BASE}/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_partial_reproc/data/expected_results/a/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496955}, "${BASE}/core/src/test/resources/datasets/external_data/data/dscbag_paxcorr_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/external_data/data/dscbag_paxcorr_load-date=02-01-2018.json", "lastModified": 1749205496754}, "${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496806}, "${BASE}/core/src/test/resources/datasets/sfpush/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush/mapping.conf", "lastModified": 1749205496942}, "${BASE}/core/src/test/resources/log4j2.properties": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/log4j2.properties", "lastModified": 1749205496993}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496835}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496872}, "${BASE}/core/src/test/resources/datasets/source_variables/data/a/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_variables/data/a/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496987}, "${BASE}/core/src/test/resources/config/samples/correlation_dcspax_pnr.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/samples/correlation_dcspax_pnr.conf", "lastModified": 1749205496625}, "${BASE}/core/src/test/resources/datasets/optimize/data/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496801}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/input/KEY A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch1/input/KEY%20A-v2-2023-08-30T10-41-00Z-true_load-date=09-03-2023.json", "lastModified": 1749205496841}, "${BASE}/core/src/test/resources/views/sample_3/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_3/mapping.conf", "lastModified": 1749205497041}, "${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_RESERVATION.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/expected_results/FACT_RESERVATION.csv", "lastModified": 1749205496807}, "${BASE}/core/src/test/resources/datasets/expressions/data/bagsmanylegsv2_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/expressions/data/bagsmanylegsv2_load-date=01-01-2018.json", "lastModified": 1749205496750}, "${BASE}/core/src/test/resources/datasets/correlation_purge/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/README.md", "lastModified": 1749205496710}, "${BASE}/core/src/test/resources/validation/sample_9/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/validation/sample_9/mapping.conf", "lastModified": 1749205497001}, "${BASE}/core/src/test/resources/datasets/expressions/data/correlationEMD_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/expressions/data/correlationEMD_load-date=02-01-2018.json", "lastModified": 1749205496751}, "${BASE}/core/src/test/resources/datasets/dim_prefiller/integration/step_1_prefiller/data/input/PASSENGER_TYPE.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/dim_prefiller/integration/step_1_prefiller/data/input/PASSENGER_TYPE.csv", "lastModified": 1749205496736}, "${BASE}/core/src/test/resources/datasets/nullvalues/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/nullvalues/mapping.conf", "lastModified": 1749205496800}, "${BASE}/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/multi_roots/data/bagsmanylegsv2_load-date=02-01-2018.json", "lastModified": 1749205496790}, "${BASE}/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s14-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496871}, "${BASE}/core/src/test/resources/views/sample_4/expected/doc.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_4/expected/doc.md", "lastModified": 1749205497042}, "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496850}, "${BASE}/core/src/test/resources/views/sample_5/expected/doc.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_5/expected/doc.md", "lastModified": 1749205497046}, "${BASE}/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/dscpax-pax1_v2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/multi_batch/batch2/data/dscpax-pax1_v2_load-date=02-01-2018.json", "lastModified": 1749205496980}, "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/tkt/TKT_ab_ey_pdt_USCK5S_6072160023101_v3_load-date=06-29-2022.json.json", "lastModified": 1749205496653}, "${BASE}/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/11/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO_st4_not_in_v2.csv", "lastModified": 1749205496641}, "${BASE}/core/src/test/resources/config/samples/tkt.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/samples/tkt.conf", "lastModified": 1749205496626}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch1/expected_results/ASSO_AIR_SEGMENT_PAX_COUPON.csv", "lastModified": 1749205496782}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/input/KEY B-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch2/input/KEY%20B-v1-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496836}, "${BASE}/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s10-merge_failure/data/batch2/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496842}, "${BASE}/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/tktemd/data/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json", "lastModified": 1749205496723}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/README.md", "lastModified": 1749205496942}, "${BASE}/core/src/test/resources/datasets/proration/data/MH-PRD-no-fare-calc_load-date=01-10-2024.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/proration/data/MH-PRD-no-fare-calc_load-date=01-10-2024.json", "lastModified": 1749205496810}, "${BASE}/core/src/test/resources/datasets/partial_processing/mapping.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/partial_processing/mapping.conf", "lastModified": 1749205496810}, "${BASE}/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/input/KEY A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s2-new_versions_without_table_histo/data/batch1/input/KEY%20A-v1-2023-08-30T08-35-00Z-false_load-date=08-31-2023.json", "lastModified": 1749205496881}, "${BASE}/core/src/test/resources/datasets/nullvalues/data/pnr_null_values_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/nullvalues/data/pnr_null_values_load-date=01-01-2018.json", "lastModified": 1749205496799}, "${BASE}/core/src/test/resources/datasets/1/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/1/README.md", "lastModified": 1749205496627}, "${BASE}/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/correlation_purge/pnr/expected_results/PNR_TKT_PARTIAL_CORR_PIT.csv", "lastModified": 1749205496721}, "${BASE}/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s8-old_versions_without_table_histo/data/batch2/input/KEY%20A-v3-2023-09-02T12-30-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496931}, "${BASE}/core/src/test/resources/datasets/expressions/data/correlationWPASDF_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/expressions/data/correlationWPASDF_load-date=01-01-2018.json", "lastModified": 1749205496753}, "${BASE}/core/src/test/resources/datasets/sfpush_purge/data/expected_results/a/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_purge/data/expected_results/a/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496962}, "${BASE}/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s7-dup_histo_with_prev/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496917}, "${BASE}/core/src/test/resources/config/samples/correlation_pnr_tkt.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/config/samples/correlation_pnr_tkt.conf", "lastModified": 1749205496625}, "${BASE}/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s11-merge_failure_rerun/data/batch1-rerun/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496847}, "${BASE}/core/src/test/resources/views/sample_3/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_3/README.md", "lastModified": 1749205497040}, "${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables_unknown.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_4/failing_mapping_with_variables_unknown.conf", "lastModified": 1749205497043}, "${BASE}/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s1-new_key/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496830}, "${BASE}/core/src/test/resources/datasets/source_variables/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_variables/data/expected_results/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496988}, "${BASE}/core/src/test/resources/datasets/latest_correlation/data/batch2/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/latest_correlation/data/batch2/raw_data/tkt/TKT_ab_ey_uat_7492400944169_v1_load-date=05-13-2022.json", "lastModified": 1749205496786}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496862}, "${BASE}/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s3-new_versions_with_table_histo/data/batch2/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496891}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/input/KEY A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/input/KEY%20A-v2-2023-08-30T10-41-00Z-false_load-date=09-03-2023.json", "lastModified": 1749205496858}, "${BASE}/core/src/test/resources/datasets/recordsfilter/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/recordsfilter/README.md", "lastModified": 1749205496823}, "${BASE}/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s4-same_versions_no_histo/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496895}, "${BASE}/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_3_CURRENCY_CONVERSIONS_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/currency_conversion/data/expected_results/FACT_3_CURRENCY_CONVERSIONS_HISTO.csv", "lastModified": 1749205496728}, "${BASE}/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/sfpush_multiple_batches/data/expected_results/b/FACT_AIR_SEGMENT_PAX_HISTO.csv", "lastModified": 1749205496949}, "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/tkt/TKT_ac_ey_pdt_USCK5S_6072160023101_v4_load-date=07-04-2022.json.json", "lastModified": 1749205496654}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch2/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496856}, "${BASE}/core/src/test/resources/views/sample_4/failing_mapping_with_variables.conf": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/views/sample_4/failing_mapping_with_variables.conf", "lastModified": 1749205497043}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_2_HISTO.csv", "lastModified": 1749205496853}, "${BASE}/core/src/test/resources/datasets/format_delta/data/delta/_delta_log/00000000000000000000.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/format_delta/data/delta/_delta_log/00000000000000000000.json", "lastModified": 1749205496757}, "${BASE}/core/src/test/resources/datasets/16/expected_result/internal_corr_ids_asso_air_segment_pax_coupon_histo.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/16/expected_result/internal_corr_ids_asso_air_segment_pax_coupon_histo.csv", "lastModified": 1749205496686}, "${BASE}/core/src/test/resources/datasets/18/data/a/bagsmanylegsv1_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/18/data/a/bagsmanylegsv1_load-date=01-01-2018.json", "lastModified": 1749205496689}, "${BASE}/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v2_load-date=02-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/source_correlation/default_update/batch2/data/dscpax-pnr_v2_load-date=02-01-2018.json", "lastModified": 1749205496971}, "${BASE}/core/src/test/resources/datasets/cartesian_product/data/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/cartesian_product/data/README.md", "lastModified": 1749205496708}, "${BASE}/core/src/test/resources/datasets/12/data/tkt/TKT_ae_ey_pdt_USCK5S_6072160023101_v10_load-date=07-15-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/12/data/tkt/TKT_ae_ey_pdt_USCK5S_6072160023101_v10_load-date=07-15-2022.json", "lastModified": 1749205496656}, "${BASE}/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s13-merge_failure_rerun/data/batch1/expected_results/FACT_SECONDARY_1_HISTO.csv", "lastModified": 1749205496860}, "${BASE}/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/revised_pit/s12-merge_failure_rerun/data/batch1/expected_results/FACT_MASTER_HISTO.csv", "lastModified": 1749205496852}, "${BASE}/core/src/test/resources/datasets/README.md": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/README.md", "lastModified": 1749205496707}, "${BASE}/core/src/test/resources/datasets/15/data/tkt/TKT_aa_ey_uat_6072401543235_v1_load-date=19-08-2022.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/15/data/tkt/TKT_aa_ey_uat_6072401543235_v1_load-date=19-08-2022.json", "lastModified": 1749205496674}, "${BASE}/core/src/test/resources/datasets/13/expected_result/asso_air_segment_pax_coupon.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/13/expected_result/asso_air_segment_pax_coupon.csv", "lastModified": 1749205496664}, "${BASE}/core/src/test/resources/datasets/5/data/batch3/file5_load-date=01-01-2018.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/5/data/batch3/file5_load-date=01-01-2018.json", "lastModified": 1749205496698}, "${BASE}/core/src/test/resources/datasets/1/data/sample-ticketing-reference-no-doc-numbers.json": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/1/data/sample-ticketing-reference-no-doc-numbers.json", "lastModified": 1749205496628}, "${BASE}/core/src/test/resources/datasets/optimize/data/expected_results/FACT_RESERVATION_HISTO.csv": {"file": "file:///mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/src/test/resources/datasets/optimize/data/expected_results/FACT_RESERVATION_HISTO.csv", "lastModified": 1749205496808}}]