
### Tables overview
#### Fact Tables
| Table | Description | GDPR Zone | Granularity | Primary key | Subdomain |
| ---- | ---- | ---- | ---- | ---- | ---- |
| FACT_PASSENGER_HISTO_LIGHT |  | red |  | PASSENGER_ID |  |

#### Dimension Tables
| Table | Description | GDPR Zone | Granularity | Primary key |
| ---- | ---- | ---- | ---- | ---- |
| DIM_PASSENGER | A DIM table | green | 1 test type | PASSENGER_TYPE_ID |
| DIM_PASSENGER_WITH_PREFILLER | A DIM table - This table is preloaded with static referential data coming from Amadeus | green | 1 test type | PASSENGER_TYPE_ID |

#### Association Tables
No Table is available
### Tables details with fields
#### Fact Tables Fields
| Table | Field | Description | Example | GDPR Zone | Format | Mandatory | In FK to | Source Path |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| FACT_PASSENGER_HISTO_LIGHT | PASSENGER_ID | <p>Hash of Unique Customer Identifier (UCI)</p> |  | <p>green</p> | STRING | PK |  | <p>hashM(.id)</p><p>hashM(.segDel.id)</p> |
| FACT_PASSENGER_HISTO_LIGHT | COLUMN_A | <p>CUSTOM DESCRIPTION FOR A</p> | <p>CUSTOM EXAMPLE FOR A</p><p>This is Example from DIH</p> | <p>red</p><p>CUSTOM PII TYPE FOR A</p><p>This is PII Type from DIH</p> | STRING | N |  | <p>.specialSeat</p><p>.segDel.specialSeat</p> |
| FACT_PASSENGER_HISTO_LIGHT | COLUMN_B | <p>CUSTOM DESCRIPTION FOR B</p><p>This is Description from DIH</p> | <p>CUSTOM EXAMPLE FOR B</p> | <p>red</p><p>CUSTOM PII TYPE FOR B</p><p>This is PII Type from DIH</p> | STRING | N |  | <p>.specialSeat</p><p>.segDel.specialSeat</p> |
| FACT_PASSENGER_HISTO_LIGHT | COLUMN_C | <p>CUSTOM DESCRIPTION FOR C</p><p>This is Description from DIH</p> | <p>CUSTOM EXAMPLE FOR C</p><p>This is Example from DIH</p> | <p>red</p><p>CUSTOM PII TYPE FOR C</p> | STRING | N |  | <p>.specialSeat</p><p>.segDel.specialSeat</p> |
| FACT_PASSENGER_HISTO_LIGHT | COLUMN_D |  | <p>CUSTOM EXAMPLE FOR D</p> | <p>orange</p> | STRING | N |  | <p>.specialSeat[\*].seatId</p><p>.segDel.specialSeat[\*].seatId</p> |
| FACT_PASSENGER_HISTO_LIGHT | COLUMN_E | <p>Hash of This is Description from DIH</p> |  | <p>green</p><p>This is PII Type from DIH</p> | STRING | N |  | <p>if(element_at(split(.specialSeat,'-'),array_size(split(.specialSeat,'-'))) == '_', NULL,hashM(.specialSeat) )</p><p>if(element_at(split(.segDel.specialSeat,'-'),array_size(split(.segDel.specialSeat,'-'))) == '_', NULL,hashM(.segDel.specialSeat) )</p> |
| FACT_PASSENGER_HISTO_LIGHT | COLUMN_F |  |  | <p>green</p> | STRING | N |  | <p>.mySpecialSeat</p><p>.segDel.mySpecialSeat</p> |
| FACT_PASSENGER_HISTO_LIGHT | COLUMN_G | <p>This is Description from DIH but different path in JSON</p> | <p>This is Example from DIH but different path in JSON</p> | <p>green</p><p>This is PII Type from DIH but different path in JSON</p> | STRING | N |  | <p>.mySecondSpecialSeat</p><p>.segDel.mySecondSpecialSeat</p> |
| FACT_PASSENGER_HISTO_LIGHT | RELATES_TO |  | <p>PASSENGER</p><p>SEGMENT_DELIVERY</p> |  | STRING | N |  |  |

#### Dimension Tables Fields
| Table | Field | Description | Example | GDPR Zone | Format | Mandatory | In FK to | Source Path |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| DIM_PASSENGER | PASSENGER_TYPE_ID | <p>Hash of the functional key (code)</p> |  | <p>green</p> | STRING | PK | DIM_PASSENGER_WITH_PREFILLER | <p>hashS(.travelers[\*].passengerTypeCode)</p> |
| DIM_PASSENGER | PASSENGER_TYPE_CODE |  |  | <p>green</p> | STRING | Y |  | <p>.travelers[\*].passengerTypeCode</p> |
| DIM_PASSENGER | PASSENGER_TYPE_LABEL |  |  | <p>green</p> | STRING | Y |  | <p>.travelers[\*].passengerTypeCode</p> |
| DIM_PASSENGER | RECORD_SOURCE |  | <p>FROM_FEED_DATA</p> | <p>green</p> | STRING | Y |  |  |
| DIM_PASSENGER | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| DIM_PASSENGER_WITH_PREFILLER | PASSENGER_TYPE_ID | <p>Hash of the functional key (code)</p> |  | <p>green</p> | STRING | PK | DIM_PASSENGER | <p>hashS(.travelers[\*].passengerTypeCode)</p> |
| DIM_PASSENGER_WITH_PREFILLER | PASSENGER_TYPE_CODE |  |  | <p>green</p> | STRING | Y |  | <p>.travelers[\*].passengerTypeCode</p> |
| DIM_PASSENGER_WITH_PREFILLER | PASSENGER_TYPE_LABEL |  |  | <p>green</p> | STRING | Y |  | <p>.travelers[\*].passengerTypeCode</p> |
| DIM_PASSENGER_WITH_PREFILLER | RECORD_SOURCE |  | <p>FROM_FEED_DATA</p> | <p>green</p> | STRING | Y |  |  |
| DIM_PASSENGER_WITH_PREFILLER | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |

#### Association Tables Fields
No Table is available

