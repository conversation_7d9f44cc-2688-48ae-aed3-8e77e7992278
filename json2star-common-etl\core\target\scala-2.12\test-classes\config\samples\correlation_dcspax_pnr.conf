{"defaultComment": "A comment here", "partition-spec": {"key": "PNR_CREATION_DATE", "column-name": "PART_PNR_CREATION_MONTH", "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"}, "tables": [{"name": "ASSO_COUPON_SERVICE_DELIVERY_HISTO", "source-correlation": {"domain-a": {"name": "DCSPAX", "table-name": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO", "version-column-name": "VERSION"}, "domain-b": "PNR", "target": {"columns": [{"name": "COUPON_ID", "is-mandatory": true, "column-type": "strColumn", "sources": {}}, {"name": "SERVICE_DELIVERY_ID", "is-mandatory": true, "column-type": "strColumn", "sources": {}}, {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}}, {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {}}, {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}}, {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "column-type": "strColumn", "sources": {}}, {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}}, {"name": "VERSION_PASSENGER", "column-type": "strColumn", "sources": {}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "domain-a-key": "SERVICE_DELIVERY_ID", "domain-b-key": "COUPON_ID", "domain-a-version": "VERSION_PASSENGER", "domain-b-version": "VERSION_TRAVEL_DOCUMENT", "start-date": "DATE_BEGIN", "end-date": "DATE_END", "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": ["DATE_BEGIN"]}}}]}