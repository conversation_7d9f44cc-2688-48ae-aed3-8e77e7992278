{"correlatedResourcesCurrent": {"PNR-EMD": {"fromDomain": "PNR", "fromFullVersion": "8", "id": "AAAAAA-2022-06-29", "isFullUpdate": true, "toDomain": "EMD", "version": "EMD-4"}, "PNR-TKT": {"correlations": [{"corrTktPnr": {"items": [{"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-17", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-1"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-18", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-2"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-19", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-3"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-25", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-4"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-23", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-5"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-24", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-6"}]}, "fromVersion": "7", "toId": "6072160023097-2022-06-29", "toVersion": "0"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-17", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-1"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-18", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-2"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-19", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-3"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-25", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-4"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-23", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-5"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-24", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-6"}]}, "fromVersion": "7", "toId": "6072160023099-2022-06-29", "toVersion": "0"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-17", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-1"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-18", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-2"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-19", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-3"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-25", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-4"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-23", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-5"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-24", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-6"}]}, "fromVersion": "7", "toId": "6666666666666-2022-06-29", "toVersion": "0"}], "fromDomain": "PNR", "fromFullVersion": "8", "id": "AAAAAA-2022-06-29", "isFullUpdate": true, "toDomain": "TKT", "version": "TKT-7"}}, "correlatedResourcesPrevious": {"PNR-EMD": {"fromDomain": "PNR", "fromFullVersion": "7", "id": "AAAAAA-2022-06-29", "isFullUpdate": true, "toDomain": "EMD", "version": "EMD-3"}, "PNR-TKT": {"correlations": [{"corrTktPnr": {"items": [{"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-17", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-1"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-18", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-2"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-19", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-3"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-25", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-4"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-23", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-5"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-24", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-382", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3", "ticketCouponId": "6072160023097-2022-06-29-6"}]}, "fromVersion": "7", "toId": "6072160023097-2022-06-29", "toVersion": "0"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-17", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-1"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-18", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-2"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-19", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-3"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-25", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-4"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-23", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-5"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-24", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-383", "pnrTravelerId": "AAAAAA-2022-06-29-PT-5", "ticketCouponId": "6072160023099-2022-06-29-6"}]}, "fromVersion": "7", "toId": "6072160023099-2022-06-29", "toVersion": "0"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-17", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-1"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-18", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-2"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-19", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-3"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-25", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-4"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-23", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-5"}, {"pnrAirSegmentId": "AAAAAA-2022-06-29-ST-24", "pnrTicketingReferenceId": "AAAAAA-2022-06-29-OT-384", "pnrTravelerId": "AAAAAA-2022-06-29-PT-3-INF", "ticketCouponId": "6666666666666-2022-06-29-6"}]}, "fromVersion": "7", "toId": "6666666666666-2022-06-29", "toVersion": "0"}], "fromDomain": "PNR", "fromFullVersion": "7", "id": "AAAAAA-2022-06-29", "isFullUpdate": false, "toDomain": "TKT", "version": "TKT-6"}}, "mainResource": {"current": {"correlations": [{"name": "PNR-EMD", "relation": {"rel": "related"}}, {"name": "PNR-TKT", "relation": {"rel": "related"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "associatedPnrs": [{"associationType": "SPLIT", "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"systemCode": "1A"}}}, "direction": "PARENT", "reference": "USCLOE"}], "automatedProcesses": [{"applicableCarrierCode": "EY", "code": "OK", "dateTime": "2022-06-29T00:00:00", "id": "AAAAAA-2022-06-29-OT-284", "isApplicableToInfants": false, "office": {"id": "XMYEY02AU"}, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}, {"applicableCarrierCode": "EY", "code": "OK", "dateTime": "2022-06-29T00:00:00", "id": "AAAAAA-2022-06-29-OT-381", "isApplicableToInfants": false, "office": {"id": "XMYEY02AU"}, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}], "contacts": [{"email": {"address": "<EMAIL>"}, "id": "AAAAAA-2022-06-29-OT-1", "purpose": ["STANDARD"], "travelerRefs": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"id": "AAAAAA-2022-06-29-OT-2", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "+61 *********"}, "purpose": ["STANDARD"], "travelerRefs": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}], "creation": {"comment": "GST-EY/781808", "dateTime": "2022-06-29T08:10:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU", "initials": "PH", "numericSign": "0202"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "fareElements": [{"code": "FE", "id": "AAAAAA-2022-06-29-OT-352", "text": "PAX AUD220.00 NONREF - NON ENDO/ REF", "type": "fare-element"}, {"code": "FE", "id": "AAAAAA-2022-06-29-OT-354", "text": "PAX AUD220.00 NONREF - NON ENDO/ REF", "type": "fare-element"}, {"code": "FE", "id": "AAAAAA-2022-06-29-OT-356", "text": "INF NON ENDO/ REF", "type": "fare-element"}, {"code": "FV", "id": "AAAAAA-2022-06-29-OT-353", "text": "PAX EY", "type": "fare-element"}, {"code": "FV", "id": "AAAAAA-2022-06-29-OT-355", "text": "PAX EY", "type": "fare-element"}, {"code": "FV", "id": "AAAAAA-2022-06-29-OT-357", "text": "INF EY", "type": "fare-element"}], "flightItinerary": [{"flights": [{"connectedFlights": {"connectionTimeDuration": "-21H-25M", "connectionType": "INTERACTIVE_MARRIAGE", "flightSegments": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}, {"flights": [{"connectedFlights": {"connectionTimeDuration": "1H55M", "connectionType": "INTERACTIVE_MARRIAGE", "flightSegments": [{"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}, {"flights": [{"connectedFlights": {"connectionTimeDuration": "-21H-25M", "connectionType": "CONNECTION", "flightSegments": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}]}}, {"connectedFlights": {"connectionTimeDuration": "1H5M", "connectionType": "CONNECTION", "flightSegments": [{"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}], "id": "AAAAAA-2022-06-29", "lastModification": {"comment": "/DCS-SYNCUS-0001AA/LON1A0955", "dateTime": "2022-06-29T08:16:00Z", "pointOfSale": {"login": {"cityCode": "LON", "countryCode": "GB", "dutyCode": "SU", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE", "iataNumber": "00010301", "id": "LON1A0955", "systemCode": "1A"}}}, "nip": 2, "owner": {"login": {"cityCode": "XMY", "countryCode": "AU", "dutyCode": "SU", "initials": "AA"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}, "paymentMethods": [{"code": "FP", "formsOfPayment": [{"code": "na", "fopIndicator": "OLD", "freeText": "CCCA"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD16.00/AAPS1OK"}], "id": "AAAAAA-2022-06-29-OT-361", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "na", "fopIndicator": "OLD", "freeText": "CCCA"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD12.00/AAPS1OK"}], "id": "AAAAAA-2022-06-29-OT-362", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "na", "fopIndicator": "OLD", "freeText": "CCCA"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD1.00/AAPS1OK"}], "id": "AAAAAA-2022-06-29-OT-363", "isInfant": true, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}], "products": [{"id": "AAAAAA-2022-06-29-OT-26", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "CHLD", "creation": {"dateTime": "2022-06-29T07:52:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "18JAN16"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-162", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-163", "products": [{"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS ADV PSGR MOBILE AND/OR EMAIL AS SSR CTCM/CTCE"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS CHECKIN 72HRS BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-233", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "MAAS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 2, "serviceProvider": {"code": "EY"}, "status": "NO", "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-234", "products": [{"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "MAAS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 2, "serviceProvider": {"code": "EY"}, "status": "NO", "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0600/31JUL/UTC"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "MAAS W2 KK6 MUCQYG6261Y31JUL.YY PLS CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0800/07AUG/UTC"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS ADV PSGR MOBILE AND/OR EMAIL AS SSR CTCM/CTCE"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS CHECKIN 72HRS BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0600/02JUL/UTC"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "THE SOURCE CURRENCY WITH AUD CODE IS NOT DEFINED IN THE SYSTEM."}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-344", "products": [{"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-345", "products": [{"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "77W"}, "arrival": {"dateTime": "2022-07-01T19:55:00Z", "iataCode": "AUH", "localDateTime": "2022-07-01T23:55:00", "terminal": "3"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008A0B900002334", "id": "AAAAAA-2022-06-29-OT-144", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008A0B900002335", "id": "AAAAAA-2022-06-29-OT-145", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-01T05:15:00Z", "iataCode": "SYD", "localDateTime": "2022-07-01T15:15:00", "terminal": "1"}, "id": "2022-07-01-SYD-AUH", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "Q", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "451"}, "id": "EY-451-2022-07-01-SYD-AUH", "isOpenNumber": false, "isPrime": true}}, "id": "AAAAAA-2022-06-29-ST-17", "products": [{"id": "AAAAAA-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-162", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-233", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "781"}, "arrival": {"dateTime": "2022-07-02T04:55:00Z", "iataCode": "MUC", "localDateTime": "2022-07-02T06:55:00", "terminal": "2"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008A0B90000233A", "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-OT-310", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "AAAAAA-2022-06-29-OT-312", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"associatedTickets": [{"associationStatus": "ASSOCIATED", "coupons": [{"number": 2}], "documentNumber": "6072160023099", "documentType": "ETICKET", "primaryDocumentNumber": "6072160023099"}], "distributionId": "5008A0B90000233B", "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-OT-315", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "AAAAAA-2022-06-29-OT-317", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "CTN", "statusType": "TCS"}, {"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"activeIdentityDocument": {"passengerTypeCode": "INF"}, "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-OT-330", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "AAAAAA-2022-06-29-OT-332", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-01T22:30:00Z", "iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00", "terminal": "3"}, "id": "2022-07-02-AUH-MUC", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "Q", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "id": "EY-5-2022-07-02-AUH-MUC", "isOpenNumber": false, "isPrime": true}}, "id": "AAAAAA-2022-06-29-ST-18", "products": [{"id": "AAAAAA-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-163", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-234", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "TRN"}, "arrival": {"dateTime": "2022-07-02T07:00:00Z", "iataCode": "QYG", "localDateTime": "2022-07-02T09:00:00"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-02T06:00:00Z", "iataCode": "MUC", "localDateTime": "2022-07-02T08:00:00", "terminal": "1"}, "id": "2022-07-02-MUC-QYG", "isInformational": false, "marketing": {"bookingClass": {"code": "Y"}, "flightDesignator": {"carrierCode": "W2", "flightNumber": "6261"}, "id": "W2-6261-2022-07-02-MUC-QYG", "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-ST-19", "products": [{"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "781"}, "arrival": {"dateTime": "2022-07-12T15:55:00Z", "iataCode": "AUH", "localDateTime": "2022-07-12T19:55:00", "terminal": "3"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:12:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008B0B90000D6E8", "id": "AAAAAA-2022-06-29-OT-339", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008B0B90000D6E9", "id": "AAAAAA-2022-06-29-OT-340", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-12T10:10:00Z", "iataCode": "MUC", "localDateTime": "2022-07-12T12:10:00", "terminal": "2"}, "id": "2022-07-12-MUC-AUH", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "M", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "6"}, "id": "EY-6-2022-07-12-MUC-AUH", "isOpenNumber": false, "isPrime": true}}, "id": "AAAAAA-2022-06-29-ST-23", "products": [{"id": "AAAAAA-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-344", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "789"}, "arrival": {"dateTime": "2022-07-13T07:05:00Z", "iataCode": "MEL", "localDateTime": "2022-07-13T17:05:00", "terminal": "2"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:12:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008B0B90000D6EA", "id": "AAAAAA-2022-06-29-OT-341", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008B0B90000D6EB", "id": "AAAAAA-2022-06-29-OT-342", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-12T17:50:00Z", "iataCode": "AUH", "localDateTime": "2022-07-12T21:50:00", "terminal": "3"}, "id": "2022-07-12-AUH-MEL", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "M", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "460"}, "id": "EY-460-2022-07-12-AUH-MEL", "isOpenNumber": false, "isPrime": true}}, "id": "AAAAAA-2022-06-29-ST-24", "products": [{"id": "AAAAAA-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-345", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "TRN"}, "arrival": {"dateTime": "2022-07-12T04:00:00Z", "iataCode": "MUC", "localDateTime": "2022-07-12T06:00:00", "terminal": "1"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:12:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-12T03:00:00Z", "iataCode": "QYG", "localDateTime": "2022-07-12T05:00:00"}, "id": "2022-07-12-QYG-MUC", "isInformational": false, "marketing": {"bookingClass": {"code": "Y"}, "flightDesignator": {"carrierCode": "W2", "flightNumber": "6240"}, "id": "W2-6240-2022-07-12-QYG-MUC", "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-ST-25", "products": [{"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}], "purgeDate": {"date": "2022-07-16"}, "queuingOffice": {"id": "XMYEY02AU"}, "quotations": [{"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-4-17", "product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-4-18", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-4-19", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-4-25", "product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-4-23", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-4-24", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "AAAAAA-2022-06-29-QT-4", "isManual": false, "issuanceType": "REISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "16.00", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "2896.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE_BALANCE"}, {"amount": "2896.00", "currency": "AUD", "elementaryPriceType": "NEW_BASE_FARE"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "PENALTY"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "RESIDUAL_VALUE"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "TST_ADDITIONAL_COLLECTION"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "TAX_BALANCE"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "TICKET_DIFFERENCE"}, {"amount": "287.05", "currency": "AUD", "elementaryPriceType": "OLD_TAX_TOTAL"}, {"amount": "287.05", "currency": "AUD", "elementaryPriceType": "NEW_TAX_TOTAL"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "TOTAL_ADDITIONAL_COLLECTION"}], "taxes": [{"amount": "60.00", "category": "OLD", "code": "AU", "currency": "AUD", "nature": "DP"}, {"amount": "13.30", "category": "OLD", "code": "DE", "currency": "AUD", "nature": "SE"}, {"amount": "27.80", "category": "OLD", "code": "F6", "currency": "AUD", "nature": "TO"}, {"amount": "89.00", "category": "OLD", "code": "OY", "currency": "AUD", "nature": "CB"}, {"amount": "37.40", "category": "OLD", "code": "RA", "currency": "AUD", "nature": "EB"}, {"amount": "55.55", "category": "OLD", "code": "WY", "currency": "AUD", "nature": "DE"}, {"amount": "4.00", "category": "OLD", "code": "ZR", "currency": "AUD", "nature": "AP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG1056.67W2 X/MUC EY X/AUH EY MEL945.25Q SYDMEL3.00NUC2054.92END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-5-17", "product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-5-18", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-5-19", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-5-25", "product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-5-23", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-5-24", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "AAAAAA-2022-06-29-QT-5", "isManual": false, "issuanceType": "REISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "12.00", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "2191.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE_BALANCE"}, {"amount": "2191.00", "currency": "AUD", "elementaryPriceType": "NEW_BASE_FARE"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "PENALTY"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "RESIDUAL_VALUE"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "TST_ADDITIONAL_COLLECTION"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "TAX_BALANCE"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "TICKET_DIFFERENCE"}, {"amount": "227.05", "currency": "AUD", "elementaryPriceType": "OLD_TAX_TOTAL"}, {"amount": "227.05", "currency": "AUD", "elementaryPriceType": "NEW_TAX_TOTAL"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "TOTAL_ADDITIONAL_COLLECTION"}], "taxes": [{"amount": "13.30", "category": "OLD", "code": "DE", "currency": "AUD", "nature": "SE"}, {"amount": "27.80", "category": "OLD", "code": "F6", "currency": "AUD", "nature": "TO"}, {"amount": "89.00", "category": "OLD", "code": "OY", "currency": "AUD", "nature": "CB"}, {"amount": "37.40", "category": "OLD", "code": "RA", "currency": "AUD", "nature": "EB"}, {"amount": "55.55", "category": "OLD", "code": "WY", "currency": "AUD", "nature": "DE"}, {"amount": "4.00", "category": "OLD", "code": "ZR", "currency": "AUD", "nature": "AP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG792.50W2 X/MUC EY X/AUH EY MEL708.93Q SYDMEL3.00NUC1554.43END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-6-17", "product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-6-18", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-6-19", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-6-25", "product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-6-23", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-6-24", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "AAAAAA-2022-06-29-QT-6", "isManual": false, "issuanceType": "REISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "1.00", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "357.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE_BALANCE"}, {"amount": "357.00", "currency": "AUD", "elementaryPriceType": "NEW_BASE_FARE"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "PENALTY"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "RESIDUAL_VALUE"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "TST_ADDITIONAL_COLLECTION"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "TAX_BALANCE"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "TICKET_DIFFERENCE"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "OLD_TAX_TOTAL"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "NEW_TAX_TOTAL"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "TOTAL_ADDITIONAL_COLLECTION"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG105.66W2 X/MUC EY X/AUH EY MEL94.52Q SYDMEL3.00NUC253.18END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}], "reference": "AAAAAA", "remarks": [{"content": "PNR WITH RAIL SEGMENT", "id": "AAAAAA-2022-06-29-OT-9", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "subType": "RX", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}], "sourcePublicationId": "2269", "ticketingReferences": [{"documents": [{"coupons": [{"product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "16.00"}, "primaryDocumentNumber": "6072160023097", "status": "ISSUED"}], "id": "AAAAAA-2022-06-29-OT-382", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "12.00"}, "primaryDocumentNumber": "6072160023099", "status": "ISSUED"}], "id": "AAAAAA-2022-06-29-OT-383", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "1.00"}, "primaryDocumentNumber": "6666666666666", "status": "ISSUED"}], "id": "AAAAAA-2022-06-29-OT-384", "isInfant": true, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"number": 1}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023095", "documentType": "ETICKET", "status": "ORIGINAL"}, {"creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023096", "documentType": "ETICKET", "status": "ORIGINAL"}], "id": "AAAAAA-2022-06-29-OT-358", "isInfant": true, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FO", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"number": 1}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023087", "documentType": "ETICKET", "status": "ORIGINAL"}, {"creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023088", "documentType": "ETICKET", "status": "ORIGINAL"}], "id": "AAAAAA-2022-06-29-OT-359", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FO", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"number": 1}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023089", "documentType": "ETICKET", "status": "ORIGINAL"}, {"creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023090", "documentType": "ETICKET", "status": "ORIGINAL"}], "id": "AAAAAA-2022-06-29-OT-360", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FO", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}], "travelers": [{"contacts": [{"id": "AAAAAA-2022-06-29-OT-1", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "AAAAAA-2022-06-29-OT-2", "ref": "processedPnr.contacts", "type": "contact"}], "gender": "MALE", "id": "AAAAAA-2022-06-29-PT-3", "infant": {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "names": [{"firstName": "CHARLES", "lastName": "GST", "title": "MR"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "AAAAAA-2022-06-29-OT-309", "passengerDetails": {"gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "510860B8000219E2"}, "passengerTypeCode": "ADT", "type": "stakeholder"}, {"adult": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "dateOfBirth": "2021-08-15", "gender": "FEMALE", "id": "AAAAAA-2022-06-29-PT-3-INF", "names": [{"firstName": "PAT", "lastName": "GST", "title": "MISS"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "AAAAAA-2022-06-29-OT-329", "passengerDetails": {"dateOfBirth": "2021-08-15", "gender": "FEMALE"}, "type": "passenger-delivery"}]}, "passengerTypeCode": "INF", "type": "stakeholder"}, {"contacts": [{"id": "AAAAAA-2022-06-29-OT-1", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "AAAAAA-2022-06-29-OT-2", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2016-01-18", "gender": "FEMALE", "id": "AAAAAA-2022-06-29-PT-5", "names": [{"firstName": "QUEEN", "lastName": "GST", "title": "MISS"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "AAAAAA-2022-06-29-OT-314", "passengerDetails": {"dateOfBirth": "2016-01-18", "gender": "FEMALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "510860B8000219E3"}, "passengerTypeCode": "CHD", "type": "stakeholder"}], "type": "pnr", "version": "8"}}, "id": "AAAAAA-2022-06-29", "previous": {"correlations": [{"name": "PNR-EMD", "relation": {"rel": "related"}}, {"name": "PNR-TKT", "relation": {"rel": "related"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "associatedPnrs": [{"associationType": "SPLIT", "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"systemCode": "1A"}}}, "direction": "PARENT", "reference": "USCLOE"}], "automatedProcesses": [{"applicableCarrierCode": "EY", "code": "OK", "dateTime": "2022-06-29T00:00:00", "id": "AAAAAA-2022-06-29-OT-284", "isApplicableToInfants": false, "office": {"id": "XMYEY02AU"}, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}, {"applicableCarrierCode": "EY", "code": "OK", "dateTime": "2022-06-29T00:00:00", "id": "AAAAAA-2022-06-29-OT-381", "isApplicableToInfants": false, "office": {"id": "XMYEY02AU"}, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}], "contacts": [{"email": {"address": "<EMAIL>"}, "id": "AAAAAA-2022-06-29-OT-1", "purpose": ["STANDARD"], "travelerRefs": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"id": "AAAAAA-2022-06-29-OT-2", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "+61 *********"}, "purpose": ["STANDARD"], "travelerRefs": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}], "creation": {"comment": "GST-EY/781808", "dateTime": "2022-06-29T08:10:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU", "initials": "PH", "numericSign": "0202"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "fareElements": [{"code": "FE", "id": "AAAAAA-2022-06-29-OT-352", "text": "PAX AUD220.00 NONREF - NON ENDO/ REF", "type": "fare-element"}, {"code": "FE", "id": "AAAAAA-2022-06-29-OT-354", "text": "PAX AUD220.00 NONREF - NON ENDO/ REF", "type": "fare-element"}, {"code": "FE", "id": "AAAAAA-2022-06-29-OT-356", "text": "INF NON ENDO/ REF", "type": "fare-element"}, {"code": "FV", "id": "AAAAAA-2022-06-29-OT-353", "text": "PAX EY", "type": "fare-element"}, {"code": "FV", "id": "AAAAAA-2022-06-29-OT-355", "text": "PAX EY", "type": "fare-element"}, {"code": "FV", "id": "AAAAAA-2022-06-29-OT-357", "text": "INF EY", "type": "fare-element"}], "flightItinerary": [{"flights": [{"connectedFlights": {"connectionTimeDuration": "-21H-25M", "connectionType": "INTERACTIVE_MARRIAGE", "flightSegments": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}, {"flights": [{"connectedFlights": {"connectionTimeDuration": "1H55M", "connectionType": "INTERACTIVE_MARRIAGE", "flightSegments": [{"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}, {"flights": [{"connectedFlights": {"connectionTimeDuration": "-21H-25M", "connectionType": "CONNECTION", "flightSegments": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}]}}, {"connectedFlights": {"connectionTimeDuration": "1H5M", "connectionType": "CONNECTION", "flightSegments": [{"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}], "id": "AAAAAA-2022-06-29", "lastModification": {"comment": "PH-EY/781808", "dateTime": "2022-06-29T08:16:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU", "dutyCode": "GS", "initials": "PH", "numericSign": "0202"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "nip": 2, "owner": {"login": {"cityCode": "XMY", "countryCode": "AU", "dutyCode": "GS", "initials": "PH"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}, "paymentMethods": [{"code": "FP", "formsOfPayment": [{"code": "na", "fopIndicator": "OLD", "freeText": "CCCA"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD16.00/AAPS1OK"}], "id": "AAAAAA-2022-06-29-OT-361", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "na", "fopIndicator": "OLD", "freeText": "CCCA"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD12.00/AAPS1OK"}], "id": "AAAAAA-2022-06-29-OT-362", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "na", "fopIndicator": "OLD", "freeText": "CCCA"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD1.00/AAPS1OK"}], "id": "AAAAAA-2022-06-29-OT-363", "isInfant": true, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}], "products": [{"id": "AAAAAA-2022-06-29-OT-26", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "CHLD", "creation": {"dateTime": "2022-06-29T07:52:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "18JAN16"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-162", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-163", "products": [{"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS ADV PSGR MOBILE AND/OR EMAIL AS SSR CTCM/CTCE"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS CHECKIN 72HRS BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-233", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "MAAS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 2, "serviceProvider": {"code": "EY"}, "status": "NO", "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-234", "products": [{"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "MAAS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 2, "serviceProvider": {"code": "EY"}, "status": "NO", "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0600/31JUL/UTC"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "MAAS W2 KK6 MUCQYG6261Y31JUL.YY PLS CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0800/07AUG/UTC"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS ADV PSGR MOBILE AND/OR EMAIL AS SSR CTCM/CTCE"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS CHECKIN 72HRS BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0600/02JUL/UTC"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "THE SOURCE CURRENCY WITH AUD CODE IS NOT DEFINED IN THE SYSTEM."}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-344", "products": [{"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-345", "products": [{"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "77W"}, "arrival": {"dateTime": "2022-07-01T19:55:00Z", "iataCode": "AUH", "localDateTime": "2022-07-01T23:55:00", "terminal": "3"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008A0B900002334", "id": "AAAAAA-2022-06-29-OT-144", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008A0B900002335", "id": "AAAAAA-2022-06-29-OT-145", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-01T05:15:00Z", "iataCode": "SYD", "localDateTime": "2022-07-01T15:15:00", "terminal": "1"}, "id": "2022-07-01-SYD-AUH", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "Q", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "451"}, "id": "EY-451-2022-07-01-SYD-AUH", "isOpenNumber": false, "isPrime": true}}, "id": "AAAAAA-2022-06-29-ST-17", "products": [{"id": "AAAAAA-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-162", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-233", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "781"}, "arrival": {"dateTime": "2022-07-02T04:55:00Z", "iataCode": "MUC", "localDateTime": "2022-07-02T06:55:00", "terminal": "2"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008A0B90000233A", "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-OT-310", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "AAAAAA-2022-06-29-OT-312", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008A0B90000233B", "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-OT-315", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "AAAAAA-2022-06-29-OT-317", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"activeIdentityDocument": {"passengerTypeCode": "INF"}, "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-OT-330", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "AAAAAA-2022-06-29-OT-332", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-01T22:30:00Z", "iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00", "terminal": "3"}, "id": "2022-07-02-AUH-MUC", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "Q", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "id": "EY-5-2022-07-02-AUH-MUC", "isOpenNumber": false, "isPrime": true}}, "id": "AAAAAA-2022-06-29-ST-18", "products": [{"id": "AAAAAA-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-163", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-234", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "TRN"}, "arrival": {"dateTime": "2022-07-02T07:00:00Z", "iataCode": "QYG", "localDateTime": "2022-07-02T09:00:00"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-02T06:00:00Z", "iataCode": "MUC", "localDateTime": "2022-07-02T08:00:00", "terminal": "1"}, "id": "2022-07-02-MUC-QYG", "isInformational": false, "marketing": {"bookingClass": {"code": "Y"}, "flightDesignator": {"carrierCode": "W2", "flightNumber": "6261"}, "id": "W2-6261-2022-07-02-MUC-QYG", "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-ST-19", "products": [{"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "781"}, "arrival": {"dateTime": "2022-07-12T15:55:00Z", "iataCode": "AUH", "localDateTime": "2022-07-12T19:55:00", "terminal": "3"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:12:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008B0B90000D6E8", "id": "AAAAAA-2022-06-29-OT-339", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008B0B90000D6E9", "id": "AAAAAA-2022-06-29-OT-340", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-12T10:10:00Z", "iataCode": "MUC", "localDateTime": "2022-07-12T12:10:00", "terminal": "2"}, "id": "2022-07-12-MUC-AUH", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "M", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "6"}, "id": "EY-6-2022-07-12-MUC-AUH", "isOpenNumber": false, "isPrime": true}}, "id": "AAAAAA-2022-06-29-ST-23", "products": [{"id": "AAAAAA-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-344", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "789"}, "arrival": {"dateTime": "2022-07-13T07:05:00Z", "iataCode": "MEL", "localDateTime": "2022-07-13T17:05:00", "terminal": "2"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:12:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008B0B90000D6EA", "id": "AAAAAA-2022-06-29-OT-341", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008B0B90000D6EB", "id": "AAAAAA-2022-06-29-OT-342", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-12T17:50:00Z", "iataCode": "AUH", "localDateTime": "2022-07-12T21:50:00", "terminal": "3"}, "id": "2022-07-12-AUH-MEL", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "M", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "460"}, "id": "EY-460-2022-07-12-AUH-MEL", "isOpenNumber": false, "isPrime": true}}, "id": "AAAAAA-2022-06-29-ST-24", "products": [{"id": "AAAAAA-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-345", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "TRN"}, "arrival": {"dateTime": "2022-07-12T04:00:00Z", "iataCode": "MUC", "localDateTime": "2022-07-12T06:00:00", "terminal": "1"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:12:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-12T03:00:00Z", "iataCode": "QYG", "localDateTime": "2022-07-12T05:00:00"}, "id": "2022-07-12-QYG-MUC", "isInformational": false, "marketing": {"bookingClass": {"code": "Y"}, "flightDesignator": {"carrierCode": "W2", "flightNumber": "6240"}, "id": "W2-6240-2022-07-12-QYG-MUC", "isOpenNumber": false}}, "id": "AAAAAA-2022-06-29-ST-25", "products": [{"id": "AAAAAA-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-OT-337", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}], "purgeDate": {"date": "2022-07-16"}, "queuingOffice": {"id": "XMYEY02AU"}, "quotations": [{"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-4-17", "product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-4-18", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-4-19", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-4-25", "product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-4-23", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-4-24", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "AAAAAA-2022-06-29-QT-4", "isManual": false, "issuanceType": "REISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "16.00", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "2896.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE_BALANCE"}, {"amount": "2896.00", "currency": "AUD", "elementaryPriceType": "NEW_BASE_FARE"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "PENALTY"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "RESIDUAL_VALUE"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "TST_ADDITIONAL_COLLECTION"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "TAX_BALANCE"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "TICKET_DIFFERENCE"}, {"amount": "287.05", "currency": "AUD", "elementaryPriceType": "OLD_TAX_TOTAL"}, {"amount": "287.05", "currency": "AUD", "elementaryPriceType": "NEW_TAX_TOTAL"}, {"amount": "16.00", "currency": "AUD", "elementaryPriceType": "TOTAL_ADDITIONAL_COLLECTION"}], "taxes": [{"amount": "60.00", "category": "OLD", "code": "AU", "currency": "AUD", "nature": "DP"}, {"amount": "13.30", "category": "OLD", "code": "DE", "currency": "AUD", "nature": "SE"}, {"amount": "27.80", "category": "OLD", "code": "F6", "currency": "AUD", "nature": "TO"}, {"amount": "89.00", "category": "OLD", "code": "OY", "currency": "AUD", "nature": "CB"}, {"amount": "37.40", "category": "OLD", "code": "RA", "currency": "AUD", "nature": "EB"}, {"amount": "55.55", "category": "OLD", "code": "WY", "currency": "AUD", "nature": "DE"}, {"amount": "4.00", "category": "OLD", "code": "ZR", "currency": "AUD", "nature": "AP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG1056.67W2 X/MUC EY X/AUH EY MEL945.25Q SYDMEL3.00NUC2054.92END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-5-17", "product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-5-18", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-5-19", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-5-25", "product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-5-23", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-5-24", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "AAAAAA-2022-06-29-QT-5", "isManual": false, "issuanceType": "REISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "12.00", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "2191.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE_BALANCE"}, {"amount": "2191.00", "currency": "AUD", "elementaryPriceType": "NEW_BASE_FARE"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "PENALTY"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "RESIDUAL_VALUE"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "TST_ADDITIONAL_COLLECTION"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "TAX_BALANCE"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "TICKET_DIFFERENCE"}, {"amount": "227.05", "currency": "AUD", "elementaryPriceType": "OLD_TAX_TOTAL"}, {"amount": "227.05", "currency": "AUD", "elementaryPriceType": "NEW_TAX_TOTAL"}, {"amount": "12.00", "currency": "AUD", "elementaryPriceType": "TOTAL_ADDITIONAL_COLLECTION"}], "taxes": [{"amount": "13.30", "category": "OLD", "code": "DE", "currency": "AUD", "nature": "SE"}, {"amount": "27.80", "category": "OLD", "code": "F6", "currency": "AUD", "nature": "TO"}, {"amount": "89.00", "category": "OLD", "code": "OY", "currency": "AUD", "nature": "CB"}, {"amount": "37.40", "category": "OLD", "code": "RA", "currency": "AUD", "nature": "EB"}, {"amount": "55.55", "category": "OLD", "code": "WY", "currency": "AUD", "nature": "DE"}, {"amount": "4.00", "category": "OLD", "code": "ZR", "currency": "AUD", "nature": "AP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG792.50W2 X/MUC EY X/AUH EY MEL708.93Q SYDMEL3.00NUC1554.43END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-6-17", "product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-6-18", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "AAAAAA-2022-06-29-QT-6-19", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-6-25", "product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-6-23", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "MKX"}, "id": "AAAAAA-2022-06-29-QT-6-24", "isFromConnection": true, "product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-12", "notValidBeforeDate": "2022-07-12"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "AAAAAA-2022-06-29-QT-6", "isManual": false, "issuanceType": "REISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "1.00", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "357.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE_BALANCE"}, {"amount": "357.00", "currency": "AUD", "elementaryPriceType": "NEW_BASE_FARE"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "PENALTY"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "RESIDUAL_VALUE"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "TST_ADDITIONAL_COLLECTION"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "TAX_BALANCE"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "TICKET_DIFFERENCE"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "OLD_TAX_TOTAL"}, {"amount": "0.00", "currency": "AUD", "elementaryPriceType": "NEW_TAX_TOTAL"}, {"amount": "1.00", "currency": "AUD", "elementaryPriceType": "TOTAL_ADDITIONAL_COLLECTION"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG105.66W2 X/MUC EY X/AUH EY MEL94.52Q SYDMEL3.00NUC253.18END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}], "reference": "AAAAAA", "remarks": [{"content": "PNR WITH RAIL SEGMENT", "id": "AAAAAA-2022-06-29-OT-9", "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "subType": "RX", "travelers": [{"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}], "sourcePublicationId": "2269", "ticketingReferences": [{"documents": [{"coupons": [{"product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "16.00"}, "primaryDocumentNumber": "6072160023097", "status": "ISSUED"}], "id": "AAAAAA-2022-06-29-OT-382", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "12.00"}, "primaryDocumentNumber": "6072160023099", "status": "ISSUED"}], "id": "AAAAAA-2022-06-29-OT-383", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "1.00"}, "primaryDocumentNumber": "6666666666666", "status": "ISSUED"}], "id": "AAAAAA-2022-06-29-OT-384", "isInfant": true, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"number": 1}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023095", "documentType": "ETICKET", "status": "ORIGINAL"}, {"creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023096", "documentType": "ETICKET", "status": "ORIGINAL"}], "id": "AAAAAA-2022-06-29-OT-358", "isInfant": true, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FO", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"number": 1}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023087", "documentType": "ETICKET", "status": "ORIGINAL"}, {"creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023088", "documentType": "ETICKET", "status": "ORIGINAL"}], "id": "AAAAAA-2022-06-29-OT-359", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FO", "traveler": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"number": 1}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023089", "documentType": "ETICKET", "status": "ORIGINAL"}, {"creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082"}}}, "documentNumber": "6072160023090", "documentType": "ETICKET", "status": "ORIGINAL"}], "id": "AAAAAA-2022-06-29-OT-360", "isInfant": false, "products": [{"id": "AAAAAA-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-23", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-24", "ref": "processedPnr.products", "type": "product"}, {"id": "AAAAAA-2022-06-29-ST-25", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FO", "traveler": {"id": "AAAAAA-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}], "travelers": [{"contacts": [{"id": "AAAAAA-2022-06-29-OT-1", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "AAAAAA-2022-06-29-OT-2", "ref": "processedPnr.contacts", "type": "contact"}], "gender": "MALE", "id": "AAAAAA-2022-06-29-PT-3", "infant": {"id": "AAAAAA-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "names": [{"firstName": "CHARLES", "lastName": "GST", "title": "MR"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "AAAAAA-2022-06-29-OT-309", "passengerDetails": {"gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "510860B8000219E2"}, "passengerTypeCode": "ADT", "type": "stakeholder"}, {"adult": {"id": "AAAAAA-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "dateOfBirth": "2021-08-15", "gender": "FEMALE", "id": "AAAAAA-2022-06-29-PT-3-INF", "names": [{"firstName": "PAT", "lastName": "GST", "title": "MISS"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "AAAAAA-2022-06-29-OT-329", "passengerDetails": {"dateOfBirth": "2021-08-15", "gender": "FEMALE"}, "type": "passenger-delivery"}]}, "passengerTypeCode": "INF", "type": "stakeholder"}, {"contacts": [{"id": "AAAAAA-2022-06-29-OT-1", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "AAAAAA-2022-06-29-OT-2", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2016-01-18", "gender": "FEMALE", "id": "AAAAAA-2022-06-29-PT-5", "names": [{"firstName": "QUEEN", "lastName": "GST", "title": "MISS"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "AAAAAA-2022-06-29-OT-314", "passengerDetails": {"dateOfBirth": "2016-01-18", "gender": "FEMALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "510860B8000219E3"}, "passengerTypeCode": "CHD", "type": "stakeholder"}], "type": "pnr", "version": "7"}}, "type": "com.amadeus.pulse.message.Pnr"}}