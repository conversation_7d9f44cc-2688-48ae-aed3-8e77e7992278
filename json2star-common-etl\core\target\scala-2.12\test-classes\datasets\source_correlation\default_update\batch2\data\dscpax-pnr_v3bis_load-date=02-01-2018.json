{"mainResource": {"id": "6107617C000052AD", "type": "com.amadeus.pulse.message.dcscml.dcspax.DCSPassenger", "current": {"image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.dcscml.dcspax.DCSPassenger", "id": "6107617C000052AD", "version": "1675200359279", "passenger": {"name": {"firstName": "SLVNTHN", "lastName": "THANESKUMAR"}, "passengerType": "ADULT"}, "recordLocator": "5GJZBK", "cprFeedType": "MARKETING_SBR", "isMasterRecord": true, "segmentDeliveries": [{"id": "6007B17C0002A6BF", "segment": {"departure": {"iataCode": "PEN", "at": "2023-02-03T22:00:00Z"}, "arrival": {"iataCode": "KUL", "at": "2023-02-03T23:00:00Z"}, "number": "1133", "carrierCode": "MH", "class": "S", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "1133"}, "status": "HK", "id": "MH-1133-2023-02-03-PEN-KUL"}, "dcsProductType": "ACTIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6BF-PEN", "departure": {"iataCode": "PEN", "at": "2023-02-04"}, "arrival": {"iataCode": "KUL"}, "operatingFlight": {"carrierCode": "MH", "number": "1133"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}, {"regulatoryProgram": {"name": "AQQ", "countryCode": "USA"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}, {"regulatoryProgram": {"name": "iAPP", "countryCode": "KOR"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "associatedPassengerSegments": {"infantId": "10BFC200064A1CA5"}, "associatedAirTravelDocuments": [{"id": "2322466644050", "documentType": "ETICKET", "primaryDocumentNumber": "2322466644050", "conjunctiveDocumentNumbers": ["2322466644050"], "associationStatus": "ASSOCIATED", "coupons": [{"id": "2322466644050-1", "number": 1, "status": "OPEN_FOR_USE"}]}], "services": [{"id": "1000000009D65F03", "code": "INFT", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "PRETHESHA/THANESKUMAR 02JAN22"}], "revenueIntegrity": {"checks": [{"id": "6007B17C0002A6BF-BOARD_OFF_POINTS_CHECK", "checkType": "BOARD_OFF_POINTS_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-CLASS_CHECK", "checkType": "CLASS_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-VALIDITY_DATE_CHECK", "checkType": "VALIDITY_DATE_CHECK", "status": "NOT_REQUIRED"}, {"id": "6007B17C0002A6BF-EXACT_FLIGHT_CHECK", "checkType": "EXACT_FLIGHT_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-SEQUENCE_CHECK", "checkType": "SEQUENCE_CHECK", "status": "NOT_REQUIRED"}, {"id": "6007B17C0002A6BF-CARRIER_CHECK", "checkType": "CARRIER_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-CUSTOMER_TYPE_CHECK", "checkType": "CUSTOMER_TYPE_CHECK", "status": "NOT_REQUIRED"}, {"id": "6007B17C0002A6BF-GO_SHOW_CHECK", "checkType": "GO_SHOW_CHECK", "status": "NOT_REQUIRED"}, {"id": "6007B17C0002A6BF-TRAVEL_DATE_CHECK", "checkType": "TRAVEL_DATE_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-FLIGHT_NUMBER_CHECK", "checkType": "FLIGHT_NUMBER_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-FULL_FIRST_NAME_CHECK", "checkType": "FULL_FIRST_NAME_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-NO_SHOW_CHECK", "checkType": "NO_SHOW_CHECK", "status": "IGNORED"}]}, "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}, {"id": "6007B17C0002A6C2", "segment": {"departure": {"iataCode": "KUL", "at": "2023-02-04T00:25:00Z"}, "arrival": {"iataCode": "BKI", "at": "2023-02-04T03:10:00Z"}, "number": "2612", "carrierCode": "MH", "class": "S", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "2612"}, "status": "HK", "id": "MH-2612-2023-02-04-KUL-BKI"}, "dcsProductType": "PASSIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6C2-KUL", "departure": {"iataCode": "KUL", "at": "2023-02-04"}, "arrival": {"iataCode": "BKI"}, "operatingFlight": {"carrierCode": "MH", "number": "2612"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "associatedPassengerSegments": {"infantId": "10BFC200064A1CA6"}, "previousTicket": {"primaryDocumentNumber": "2322466644050", "conjunctiveDocumentNumbers": ["2322466644050"], "associationStatus": "UNASSOCIATED"}, "services": [{"id": "1000000009D65F04", "code": "INFT", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "PRETHESHA/THANESKUMAR 02JAN22"}], "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}, {"id": "6007B17C0002A6C6", "segment": {"departure": {"iataCode": "BKI", "at": "2023-02-05T21:20:00Z"}, "arrival": {"iataCode": "KUL", "at": "2023-02-06T00:00:00Z"}, "number": "2637", "carrierCode": "MH", "class": "N", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "2637"}, "status": "HK", "id": "MH-2637-2023-02-05-BKI-KUL"}, "dcsProductType": "PASSIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6C6-BKI", "departure": {"iataCode": "BKI", "at": "2023-02-06"}, "arrival": {"iataCode": "KUL"}, "operatingFlight": {"carrierCode": "MH", "number": "2637"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "associatedPassengerSegments": {"infantId": "10BFC200064A1CA7"}, "previousTicket": {"primaryDocumentNumber": "2322466644050", "conjunctiveDocumentNumbers": ["2322466644050"], "associationStatus": "UNASSOCIATED"}, "services": [{"id": "1000000009D65F05", "code": "INFT", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "PRETHESHA/THANESKUMAR 02JAN22"}], "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}, {"id": "6007B17C0002A6C9", "segment": {"departure": {"iataCode": "KUL", "at": "2023-02-06T01:05:00Z"}, "arrival": {"iataCode": "PEN", "at": "2023-02-06T02:05:00Z"}, "number": "1138", "carrierCode": "MH", "class": "N", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "1138"}, "status": "HK", "id": "MH-1138-2023-02-06-KUL-PEN"}, "dcsProductType": "PASSIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6C9-KUL", "departure": {"iataCode": "KUL", "at": "2023-02-06"}, "arrival": {"iataCode": "PEN"}, "operatingFlight": {"carrierCode": "MH", "number": "1138"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "associatedPassengerSegments": {"infantId": "10BFC200064A1CA8"}, "previousTicket": {"primaryDocumentNumber": "2322466644050", "conjunctiveDocumentNumbers": ["2322466644050"], "associationStatus": "UNASSOCIATED"}, "services": [{"id": "1000000009D65F0E", "code": "INFT", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "PRETHESHA/THANESKUMAR 02JAN22"}], "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}], "services": [{"id": "1000000009D65F0F", "code": "CEID", "subType": "SPECIAL_KEYWORD", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "STIACK48GXH6RDIPDI5C97RPC"}], "passengerLinks": [{"collection": [{"id": "6107617C000052A1", "type": "CUSTOMER_LINK"}, {"id": "6107617C000052A2", "type": "CUSTOMER_LINK"}, {"id": "6107617C000052AD", "type": "CUSTOMER_LINK"}], "id": "1000000007481719"}], "lastModification": {"dateTime": "2023-01-31T21:25:58.855Z", "triggerEventName": "CFLICQ-Flight Creation", "user": {"officeId": "NCEMH06TI"}}}, "correlations": [{"name": "DCSPAX-TKT", "relation": {"rel": "related"}}, {"name": "DCSPAX-PNR", "relation": {"rel": "related"}}, {"name": "DCSPAX-EMD", "relation": {"rel": "related"}}, {"name": "DCSPAX-DCSBAG", "relation": {"rel": "missing"}}, {"name": "DCSPAX-SKD", "relation": {"rel": "missing"}}]}, "previous": {"image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.dcscml.dcspax.DCSPassenger", "id": "6107617C000052AD", "version": "1675199414586", "passenger": {"name": {"firstName": "SLVNTHN", "lastName": "THANESKUMAR"}, "passengerType": "ADULT"}, "recordLocator": "5GJZBK", "cprFeedType": "MARKETING_SBR", "isMasterRecord": true, "segmentDeliveries": [{"id": "6007B17C0002A6BF", "segment": {"departure": {"iataCode": "PEN", "at": "2023-02-03T22:00:00Z"}, "arrival": {"iataCode": "KUL", "at": "2023-02-03T23:00:00Z"}, "number": "1133", "carrierCode": "MH", "class": "S", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "1133"}, "status": "HK", "id": "MH-1133-2023-02-03-PEN-KUL"}, "dcsProductType": "ACTIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6BF-PEN", "departure": {"iataCode": "PEN", "at": "2023-02-04"}, "arrival": {"iataCode": "KUL"}, "operatingFlight": {"carrierCode": "MH", "number": "1133"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}, {"regulatoryProgram": {"name": "AQQ", "countryCode": "USA"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}, {"regulatoryProgram": {"name": "iAPP", "countryCode": "KOR"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "associatedPassengerSegments": {"infantId": "10BFC200064A1CA5"}, "associatedAirTravelDocuments": [{"id": "2322466644050", "documentType": "ETICKET", "primaryDocumentNumber": "2322466644050", "conjunctiveDocumentNumbers": ["2322466644050"], "associationStatus": "ASSOCIATED", "coupons": [{"id": "2322466644050-1", "number": 1, "status": "OPEN_FOR_USE"}]}], "services": [{"id": "1000000009D65F03", "code": "INFT", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "PRETHESHA/THANESKUMAR 02JAN22"}], "revenueIntegrity": {"checks": [{"id": "6007B17C0002A6BF-BOARD_OFF_POINTS_CHECK", "checkType": "BOARD_OFF_POINTS_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-CLASS_CHECK", "checkType": "CLASS_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-VALIDITY_DATE_CHECK", "checkType": "VALIDITY_DATE_CHECK", "status": "NOT_REQUIRED"}, {"id": "6007B17C0002A6BF-EXACT_FLIGHT_CHECK", "checkType": "EXACT_FLIGHT_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-SEQUENCE_CHECK", "checkType": "SEQUENCE_CHECK", "status": "NOT_REQUIRED"}, {"id": "6007B17C0002A6BF-CARRIER_CHECK", "checkType": "CARRIER_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-CUSTOMER_TYPE_CHECK", "checkType": "CUSTOMER_TYPE_CHECK", "status": "NOT_REQUIRED"}, {"id": "6007B17C0002A6BF-GO_SHOW_CHECK", "checkType": "GO_SHOW_CHECK", "status": "NOT_REQUIRED"}, {"id": "6007B17C0002A6BF-TRAVEL_DATE_CHECK", "checkType": "TRAVEL_DATE_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-FLIGHT_NUMBER_CHECK", "checkType": "FLIGHT_NUMBER_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-FULL_FIRST_NAME_CHECK", "checkType": "FULL_FIRST_NAME_CHECK", "status": "PASSED"}, {"id": "6007B17C0002A6BF-NO_SHOW_CHECK", "checkType": "NO_SHOW_CHECK", "status": "IGNORED"}]}, "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}, {"id": "6007B17C0002A6C2", "segment": {"departure": {"iataCode": "KUL", "at": "2023-02-04T00:25:00Z"}, "arrival": {"iataCode": "BKI", "at": "2023-02-04T03:10:00Z"}, "number": "2612", "carrierCode": "MH", "class": "S", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "2612"}, "status": "HK", "id": "MH-2612-2023-02-04-KUL-BKI"}, "dcsProductType": "PASSIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6C2-KUL", "departure": {"iataCode": "KUL", "at": "2023-02-04"}, "arrival": {"iataCode": "BKI"}, "operatingFlight": {"carrierCode": "MH", "number": "2612"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "associatedPassengerSegments": {"infantId": "10BFC200064A1CA6"}, "previousTicket": {"primaryDocumentNumber": "2322466644050", "conjunctiveDocumentNumbers": ["2322466644050"], "associationStatus": "UNASSOCIATED"}, "services": [{"id": "1000000009D65F04", "code": "INFT", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "PRETHESHA/THANESKUMAR 02JAN22"}], "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}, {"id": "6007B17C0002A6C6", "segment": {"departure": {"iataCode": "BKI", "at": "2023-02-05T21:20:00Z"}, "arrival": {"iataCode": "KUL", "at": "2023-02-06T00:00:00Z"}, "number": "2637", "carrierCode": "MH", "class": "N", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "2637"}, "status": "HK", "id": "MH-2637-2023-02-05-BKI-KUL"}, "dcsProductType": "PASSIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6C6-BKI", "departure": {"iataCode": "BKI", "at": "2023-02-06"}, "arrival": {"iataCode": "KUL"}, "operatingFlight": {"carrierCode": "MH", "number": "2637"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "associatedPassengerSegments": {"infantId": "10BFC200064A1CA7"}, "previousTicket": {"primaryDocumentNumber": "2322466644050", "conjunctiveDocumentNumbers": ["2322466644050"], "associationStatus": "UNASSOCIATED"}, "services": [{"id": "1000000009D65F05", "code": "INFT", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "PRETHESHA/THANESKUMAR 02JAN22"}], "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}, {"id": "6007B17C0002A6C9", "segment": {"departure": {"iataCode": "KUL", "at": "2023-02-06T01:05:00Z"}, "arrival": {"iataCode": "PEN", "at": "2023-02-06T02:05:00Z"}, "number": "1138", "carrierCode": "MH", "class": "N", "cabin": "Y", "operating": {"carrierCode": "MH", "number": "1138"}, "status": "HK", "id": "MH-1138-2023-02-06-KUL-PEN"}, "dcsProductType": "PASSIVE_SYNCHRONISED", "legDeliveries": [{"id": "6007B17C0002A6C9-KUL", "departure": {"iataCode": "KUL", "at": "2023-02-06"}, "arrival": {"iataCode": "PEN"}, "operatingFlight": {"carrierCode": "MH", "number": "1138"}, "travelCabinCode": "Y", "acceptance": {"status": "NOT_ACCEPTED"}, "boarding": {"boardingStatus": "NOT_BOARDED", "boardingPassPrint": {"status": "NOT_PRINTED", "channel": "NONE"}}, "regulatoryChecks": [{"regulatoryProgram": {"name": "ADC"}, "statuses": [{"statusType": "SECURITY", "statusCode": "X"}]}]}], "associatedPassengerSegments": {"infantId": "10BFC200064A1CA8"}, "previousTicket": {"primaryDocumentNumber": "2322466644050", "conjunctiveDocumentNumbers": ["2322466644050"], "associationStatus": "UNASSOCIATED"}, "services": [{"id": "1000000009D65F0E", "code": "INFT", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "PRETHESHA/THANESKUMAR 02JAN22"}], "passengerDisruption": {"productState": {"status": "NOT_DISRUPTED"}}}], "services": [{"id": "1000000009D65F0F", "code": "CEID", "subType": "SPECIAL_KEYWORD", "serviceProvider": {"code": "MH"}, "statusCode": "HK", "nIP": 1, "description": "STIACK48GXH6RDIPDI5C97RPC"}], "passengerLinks": [{"collection": [{"id": "6107617C000052A1", "type": "CUSTOMER_LINK"}, {"id": "6107617C000052A2", "type": "CUSTOMER_LINK"}, {"id": "6107617C000052AD", "type": "CUSTOMER_LINK"}], "id": "1000000007481719"}], "lastModification": {"dateTime": "2023-01-31T21:10:14.563Z", "triggerEventName": "1AINTERNAL", "user": {"officeId": "LON1A0955"}}}, "correlations": [{"name": "DCSPAX-TKT", "relation": {"rel": "related"}}, {"name": "DCSPAX-PNR", "relation": {"rel": "related"}}, {"name": "DCSPAX-EMD", "relation": {"rel": "related"}}, {"name": "DCSPAX-DCSBAG", "relation": {"rel": "missing"}}, {"name": "DCSPAX-SKD", "relation": {"rel": "missing"}}]}}, "correlatedResourcesCurrent": {"DCSPAX-TKT": {"id": "6107617C000052AD", "version": "TKT-3", "isFullUpdate": true, "fromFullVersion": "1675200359279", "fromDomain": "DCSPAX", "toDomain": "TKT"}, "DCSPAX-EMD": {"id": "6107617C000052AD", "version": "EMD-3", "isFullUpdate": true, "fromFullVersion": "1675200359279", "fromDomain": "DCSPAX", "toDomain": "EMD"}, "DCSPAX-PNR": {"id": "6107617C000052AD", "version": "PNR-2", "isFullUpdate": true, "fromFullVersion": "1675200359279", "correlations": [{"fromVersion": "1675198820352", "toId": "5GJZBK-2023-01-11", "toVersion": "8", "corrDcspaxPnr": {"items": [{"dcsPassengerSegmentDeliveryId": "6007B17C0002A6C2", "pnrTravelerId": "5GJZBK-2023-01-11-PT-4", "pnrAirSegmentId": "5GJZBK-2023-01-11-ST-2"}, {"dcsPassengerSegmentDeliveryId": "6007B17C0002A6C9", "pnrTravelerId": "5GJZBK-2023-01-11-PT-4", "pnrAirSegmentId": "5GJZBK-2023-01-11-ST-4"}]}}], "fromDomain": "DCSPAX", "toDomain": "PNR"}}}