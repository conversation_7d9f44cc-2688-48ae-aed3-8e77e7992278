﻿{
    "correlatedResourcesCurrent": {
        "PNR-EMD": {
            "fromDomain": "PNR",
            "fromFullVersion": "12",
            "id": "NP84R4-2022-08-19",
            "isFullUpdate": true,
            "toDomain": "EMD",
            "version": "EMD-7"
        },
        "PNR-TKT": {
            "correlations": [
                {
                    "corrTktPnr": {
                        "items": [
                            {
                                "pnrAirSegmentId": "NP84R4-2022-08-19-ST-3",
                                "pnrTicketingReferenceId": "NP84R4-2022-08-19-OT-28",
                                "pnrTravelerId": "NP84R4-2022-08-19-PT-2",
                                "ticketCouponId": "6072401543235-2022-08-19-1"
                            }
                        ]
                    },
                    "fromVersion": "11",
                    "toId": "6072401543235-2022-08-19",
                    "toVersion": "2"
                }
            ],
            "fromDomain": "PNR",
            "fromFullVersion": "12",
            "id": "NP84R4-2022-08-19",
            "isFullUpdate": true,
            "toDomain": "TKT",
            "version": "TKT-8"
        }
    },
    "correlatedResourcesPrevious": {
        "PNR-EMD": {
            "fromDomain": "PNR",
            "fromFullVersion": "11",
            "id": "NP84R4-2022-08-19",
            "isFullUpdate": true,
            "toDomain": "EMD",
            "version": "EMD-6"
        },
        "PNR-TKT": {
            "correlations": [
                {
                    "corrTktPnr": {
                        "items": [
                            {
                                "pnrAirSegmentId": "NP84R4-2022-08-19-ST-3",
                                "pnrTicketingReferenceId": "NP84R4-2022-08-19-OT-28",
                                "pnrTravelerId": "NP84R4-2022-08-19-PT-2",
                                "ticketCouponId": "6072401543235-2022-08-19-1"
                            }
                        ]
                    },
                    "fromVersion": "11",
                    "toId": "6072401543235-2022-08-19",
                    "toVersion": "2"
                }
            ],
            "fromDomain": "PNR",
            "fromFullVersion": "11",
            "id": "NP84R4-2022-08-19",
            "isFullUpdate": true,
            "toDomain": "TKT",
            "version": "TKT-7"
        }
    },
    "mainResource": {
        "current": {
            "correlations": [
                {
                    "name": "PNR-EMD",
                    "relation": {
                        "rel": "related"
                    }
                },
                {
                    "name": "PNR-TKT",
                    "relation": {
                        "rel": "related"
                    }
                }
            ],
            "image": {
                "@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr",
                "automatedProcesses": [
                    {
                        "applicableCarrierCode": "EY",
                        "code": "OK",
                        "dateTime": "2022-08-19T00:00:00",
                        "id": "NP84R4-2022-08-19-OT-18",
                        "isApplicableToInfants": false,
                        "office": {
                            "id": "RKTEY08WC"
                        },
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-1",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-2",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-3",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-4",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "automated-process"
                    }
                ],
                "contacts": [
                    {
                        "email": {
                            "address": "<EMAIL>"
                        },
                        "id": "NP84R4-2022-08-19-OT-3",
                        "purpose": [
                            "STANDARD"
                        ],
                        "travelerRefs": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "contact"
                    }
                ],
                "creation": {
                    "comment": "DAPI-EY/USEYBKG",
                    "dateTime": "2022-08-19T14:14:00Z",
                    "pointOfSale": {
                        "login": {
                            "cityCode": "RKT",
                            "countryCode": "AE",
                            "initials": "WS",
                            "numericSign": "9999"
                        },
                        "office": {
                            "agentType": "AIRLINE",
                            "iataNumber": "86493503",
                            "id": "RKTEY08WC",
                            "systemCode": "EY"
                        }
                    }
                },
                "fareElements": [
                    {
                        "code": "FE",
                        "id": "NP84R4-2022-08-19-OT-4",
                        "text": "PAX NON ENDO/ REF",
                        "type": "fare-element"
                    },
                    {
                        "code": "FV",
                        "id": "NP84R4-2022-08-19-OT-5",
                        "text": "PAX EY",
                        "type": "fare-element"
                    }
                ],
                "flightItinerary": [
                    {
                        "destinationIataCode": "DOH",
                        "flights": [
                            {
                                "flightSegment": {
                                    "id": "NP84R4-2022-08-19-ST-1",
                                    "ref": "processedPnr.products",
                                    "type": "product"
                                },
                                "id": "ST-1"
                            }
                        ],
                        "id": "SELL_AVAILABILITY-ST-1",
                        "originIataCode": "AUH",
                        "pointOfCommencement": {
                            "address": {
                                "countryCode": "AE"
                            }
                        },
                        "type": "SELL_AVAILABILITY",
                        "yield": {
                            "amount": "308.0",
                            "elementaryPriceType": "ORIGIN_AND_DESTINATION_YIELD"
                        }
                    },
                    {
                        "destinationIataCode": "AUH",
                        "flights": [
                            {
                                "flightSegment": {
                                    "id": "NP84R4-2022-08-19-ST-2",
                                    "ref": "processedPnr.products",
                                    "type": "product"
                                },
                                "id": "ST-2"
                            }
                        ],
                        "id": "SELL_AVAILABILITY-ST-2",
                        "originIataCode": "DOH",
                        "pointOfCommencement": {
                            "address": {
                                "countryCode": "AE"
                            }
                        },
                        "type": "SELL_AVAILABILITY",
                        "yield": {
                            "amount": "308.0",
                            "elementaryPriceType": "ORIGIN_AND_DESTINATION_YIELD"
                        }
                    }
                ],
                "id": "NP84R4-2022-08-19",
                "keywords": [
                    {
                        "code": "PROT",
                        "id": "NP84R4-2022-08-19-OT-32",
                        "nip": 1,
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-4",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "serviceProvider": {
                            "code": "EY"
                        },
                        "status": "HK",
                        "subType": "SPECIAL_KEYWORD",
                        "text": "REACC BKG PROTECTION",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "service"
                    }
                ],
                "lastModification": {
                    "comment": "1APUB/ATL-0001AA/NCE1A0955",
                    "dateTime": "2022-09-20T12:38:00Z",
                    "pointOfSale": {
                        "login": {
                            "cityCode": "NCE",
                            "countryCode": "FR",
                            "dutyCode": "SU",
                            "initials": "AA",
                            "numericSign": "0001"
                        },
                        "office": {
                            "agentType": "AIRLINE",
                            "iataNumber": "12345675",
                            "id": "NCE1A0955",
                            "systemCode": "1A"
                        }
                    }
                },
                "nip": 1,
                "owner": {
                    "login": {
                        "cityCode": "RKT",
                        "countryCode": "AE",
                        "dutyCode": "SU",
                        "initials": "AA"
                    },
                    "office": {
                        "agentType": "AIRLINE",
                        "iataNumber": "86493503",
                        "id": "RKTEY08WC",
                        "systemCode": "EY"
                    }
                },
                "products": [
                    {
                        "id": "NP84R4-2022-08-19-OT-24",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-1",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-2",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-3",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-4",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "service": {
                            "code": "OTHS",
                            "creation": {
                                "dateTime": "2022-09-06T06:06:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "initials": "AA",
                                        "numericSign": "0001"
                                    },
                                    "office": {
                                        "id": "MUC1A0701"
                                    }
                                }
                            },
                            "isChargeable": false,
                            "serviceProvider": {
                                "code": "1A"
                            },
                            "subType": "SPECIAL_SERVICE_REQUEST",
                            "text": "EY399 CANCELED DUE TO OTHER REASON"
                        },
                        "subType": "SERVICE",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "id": "NP84R4-2022-08-19-OT-30",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-1",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-2",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-3",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-4",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "service": {
                            "code": "OTHS",
                            "creation": {
                                "dateTime": "2022-09-20T12:38:00Z",
                                "pointOfSale": {
                                    "office": {
                                        "id": "MUC1A0701"
                                    }
                                }
                            },
                            "isChargeable": false,
                            "serviceProvider": {
                                "code": "1A"
                            },
                            "subType": "SPECIAL_SERVICE_REQUEST",
                            "text": "EY390 CANCELED DUE TO OTHER REASON"
                        },
                        "subType": "SERVICE",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "airSegment": {
                            "aircraft": {
                                "aircraftType": "32A"
                            },
                            "arrival": {
                                "dateTime": "2023-01-05T23:15:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-06T02:15:00"
                            },
                            "bookingStatusCode": "UN",
                            "creation": {
                                "dateTime": "2022-08-19T14:14:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "cityCode": "RKT",
                                        "countryCode": "AE"
                                    },
                                    "office": {
                                        "agentType": "WEB",
                                        "iataNumber": "86493503",
                                        "id": "RKTEY08WC",
                                        "systemCode": "00"
                                    }
                                }
                            },
                            "deliveries": [
                                {
                                    "distributionId": "6004D0EC00016018",
                                    "id": "NP84R4-2022-08-19-OT-1",
                                    "traveler": {
                                        "id": "NP84R4-2022-08-19-PT-2",
                                        "ref": "processedPnr.travelers",
                                        "type": "stakeholder"
                                    },
                                    "type": "segment-delivery"
                                }
                            ],
                            "departure": {
                                "dateTime": "2023-01-05T22:10:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-06T02:10:00",
                                "terminal": "3"
                            },
                            "id": "2023-01-06-AUH-DOH",
                            "isInformational": false,
                            "marketing": {
                                "bookingClass": {
                                    "cabin": {
                                        "bidPrice": {
                                            "amount": "1.0",
                                            "elementaryPriceType": "BID_PRICE"
                                        },
                                        "code": "Y"
                                    },
                                    "code": "Q",
                                    "levelOfService": "ECONOMY",
                                    "subClass": {
                                        "code": 0,
                                        "pointOfSale": {
                                            "login": {
                                                "countryCode": "AE"
                                            },
                                            "office": {
                                                "systemCode": "EY"
                                            }
                                        },
                                        "yields": [
                                            {
                                                "amount": "308.0",
                                                "elementaryPriceType": "ADJUSTED_YIELD"
                                            },
                                            {
                                                "amount": "307.0",
                                                "elementaryPriceType": "EFFECTIVE_YIELD"
                                            },
                                            {
                                                "amount": "308.0",
                                                "elementaryPriceType": "OND_YIELD_AUH_DOH"
                                            }
                                        ]
                                    }
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "399"
                                },
                                "id": "EY-399-2023-01-06-AUH-DOH",
                                "isOpenNumber": false,
                                "isPrime": true
                            }
                        },
                        "id": "NP84R4-2022-08-19-ST-1",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-OT-24",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-OT-30",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "subType": "AIR",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "airSegment": {
                            "aircraft": {
                                "aircraftType": "32A"
                            },
                            "arrival": {
                                "dateTime": "2023-01-26T01:30:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-26T05:30:00",
                                "terminal": "3"
                            },
                            "bookingStatusCode": "UN",
                            "creation": {
                                "dateTime": "2022-08-19T14:14:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "cityCode": "RKT",
                                        "countryCode": "AE"
                                    },
                                    "office": {
                                        "agentType": "WEB",
                                        "iataNumber": "86493503",
                                        "id": "RKTEY08WC",
                                        "systemCode": "00"
                                    }
                                }
                            },
                            "deliveries": [
                                {
                                    "distributionId": "6004D0EC00015FB5",
                                    "id": "NP84R4-2022-08-19-OT-2",
                                    "traveler": {
                                        "id": "NP84R4-2022-08-19-PT-2",
                                        "ref": "processedPnr.travelers",
                                        "type": "stakeholder"
                                    },
                                    "type": "segment-delivery"
                                }
                            ],
                            "departure": {
                                "dateTime": "2023-01-26T00:30:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-26T03:30:00"
                            },
                            "id": "2023-01-26-DOH-AUH",
                            "isInformational": false,
                            "marketing": {
                                "bookingClass": {
                                    "cabin": {
                                        "bidPrice": {
                                            "amount": "1.0",
                                            "elementaryPriceType": "BID_PRICE"
                                        },
                                        "code": "Y"
                                    },
                                    "code": "Q",
                                    "levelOfService": "ECONOMY",
                                    "subClass": {
                                        "code": 0,
                                        "pointOfSale": {
                                            "login": {
                                                "countryCode": "AE"
                                            },
                                            "office": {
                                                "systemCode": "EY"
                                            }
                                        },
                                        "yields": [
                                            {
                                                "amount": "308.0",
                                                "elementaryPriceType": "ADJUSTED_YIELD"
                                            },
                                            {
                                                "amount": "307.0",
                                                "elementaryPriceType": "EFFECTIVE_YIELD"
                                            },
                                            {
                                                "amount": "308.0",
                                                "elementaryPriceType": "OND_YIELD_DOH_AUH"
                                            }
                                        ]
                                    }
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "390"
                                },
                                "id": "EY-390-2023-01-26-DOH-AUH",
                                "isOpenNumber": false,
                                "isPrime": true
                            }
                        },
                        "id": "NP84R4-2022-08-19-ST-2",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-OT-24",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-OT-30",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "subType": "AIR",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "airSegment": {
                            "aircraft": {
                                "aircraftType": "32A"
                            },
                            "arrival": {
                                "dateTime": "2023-01-05T18:35:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-05T21:35:00"
                            },
                            "bookingStatusCode": "TK",
                            "creation": {
                                "dateTime": "2022-09-06T06:06:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "cityCode": "RKT",
                                        "countryCode": "AE"
                                    },
                                    "office": {
                                        "agentType": "NON_IATA_AGENT",
                                        "iataNumber": "00000000",
                                        "id": "RKTEY08WC",
                                        "systemCode": "EY"
                                    }
                                }
                            },
                            "deliveries": [
                                {
                                    "distributionId": "014093800171DC78",
                                    "id": "NP84R4-2022-08-19-OT-23",
                                    "traveler": {
                                        "id": "NP84R4-2022-08-19-PT-2",
                                        "ref": "processedPnr.travelers",
                                        "type": "stakeholder"
                                    },
                                    "type": "segment-delivery"
                                }
                            ],
                            "departure": {
                                "dateTime": "2023-01-05T17:35:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-05T21:35:00",
                                "terminal": "3"
                            },
                            "id": "2023-01-05-AUH-DOH",
                            "isInformational": false,
                            "marketing": {
                                "bookingClass": {
                                    "cabin": {
                                        "code": "Y"
                                    },
                                    "code": "Q",
                                    "levelOfService": "ECONOMY",
                                    "subClass": {
                                        "code": 0,
                                        "pointOfSale": {
                                            "login": {
                                                "countryCode": "AE"
                                            },
                                            "office": {
                                                "systemCode": "EY"
                                            }
                                        }
                                    }
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "391"
                                },
                                "id": "EY-391-2023-01-05-AUH-DOH",
                                "isOpenNumber": false,
                                "isPrime": true
                            }
                        },
                        "id": "NP84R4-2022-08-19-ST-3",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-OT-24",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-OT-30",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "subType": "AIR",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "airSegment": {
                            "aircraft": {
                                "aircraftType": "321"
                            },
                            "arrival": {
                                "dateTime": "2023-01-25T08:10:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-25T12:10:00",
                                "terminal": "3"
                            },
                            "bookingStatusCode": "TK",
                            "creation": {
                                "dateTime": "2022-09-20T12:38:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "cityCode": "RKT",
                                        "countryCode": "AE"
                                    },
                                    "office": {
                                        "agentType": "NON_IATA_AGENT",
                                        "iataNumber": "00000000",
                                        "id": "RKTEY08WC",
                                        "systemCode": "EY"
                                    }
                                }
                            },
                            "deliveries": [
                                {
                                    "distributionId": "014093800171E95B",
                                    "id": "NP84R4-2022-08-19-OT-29",
                                    "traveler": {
                                        "id": "NP84R4-2022-08-19-PT-2",
                                        "ref": "processedPnr.travelers",
                                        "type": "stakeholder"
                                    },
                                    "type": "segment-delivery"
                                }
                            ],
                            "departure": {
                                "dateTime": "2023-01-25T07:10:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-25T10:10:00"
                            },
                            "id": "2023-01-25-DOH-AUH",
                            "isInformational": false,
                            "marketing": {
                                "bookingClass": {
                                    "cabin": {
                                        "code": "Y"
                                    },
                                    "code": "Q",
                                    "levelOfService": "ECONOMY",
                                    "subClass": {
                                        "code": 0,
                                        "pointOfSale": {
                                            "login": {
                                                "countryCode": "AE"
                                            },
                                            "office": {
                                                "systemCode": "EY"
                                            }
                                        }
                                    }
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "394"
                                },
                                "id": "EY-394-2023-01-25-DOH-AUH",
                                "isOpenNumber": false,
                                "isPrime": true
                            }
                        },
                        "id": "NP84R4-2022-08-19-ST-4",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-OT-24",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-OT-30",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "subType": "AIR",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    }
                ],
                "purgeDate": {
                    "date": "2023-01-30"
                },
                "queuingOffice": {
                    "id": "RKTEY08WC"
                },
                "quotations": [
                    {
                        "coupons": [
                            {
                                "baggageAllowance": {
                                    "weight": {
                                        "unit": "KILOGRAMS",
                                        "value": 30
                                    }
                                },
                                "fareBasis": {
                                    "fareBasisCode": "2AE",
                                    "primaryCode": "QLC"
                                },
                                "id": "NP84R4-2022-08-19-QT-1-1",
                                "product": {
                                    "id": "NP84R4-2022-08-19-ST-1",
                                    "ref": "processedPnr.products",
                                    "type": "product"
                                },
                                "validityDates": {
                                    "notValidAfterDate": "2023-01-06",
                                    "notValidBeforeDate": "2023-01-06"
                                }
                            },
                            {
                                "baggageAllowance": {
                                    "weight": {
                                        "unit": "KILOGRAMS",
                                        "value": 30
                                    }
                                },
                                "fareBasis": {
                                    "fareBasisCode": "2AE",
                                    "primaryCode": "QLC"
                                },
                                "id": "NP84R4-2022-08-19-QT-1-2",
                                "product": {
                                    "id": "NP84R4-2022-08-19-ST-2",
                                    "ref": "processedPnr.products",
                                    "type": "product"
                                },
                                "validityDates": {
                                    "notValidAfterDate": "2023-01-26",
                                    "notValidBeforeDate": "2023-01-26"
                                }
                            }
                        ],
                        "creation": {
                            "dateTime": "2022-08-19T00:00:00Z",
                            "pointOfSale": {
                                "office": {
                                    "id": "RKTEY08WC"
                                }
                            }
                        },
                        "destinationCityIataCode": "AUH",
                        "documentType": "TICKET",
                        "id": "NP84R4-2022-08-19-QT-1",
                        "isManual": false,
                        "issuanceType": "FIRST_ISSUE",
                        "lastModification": {
                            "dateTime": "2022-08-19T00:00:00Z",
                            "pointOfSale": {
                                "office": {
                                    "id": "RKTEY08WC"
                                }
                            }
                        },
                        "originCityIataCode": "AUH",
                        "price": {
                            "detailedPrices": [
                                {
                                    "amount": "2755",
                                    "currency": "AED",
                                    "elementaryPriceType": "TOTAL"
                                },
                                {
                                    "amount": "2100",
                                    "currency": "AED",
                                    "elementaryPriceType": "BASE_FARE"
                                },
                                {
                                    "amount": "2755",
                                    "currency": "AED",
                                    "elementaryPriceType": "GRAND_TOTAL"
                                }
                            ],
                            "taxes": [
                                {
                                    "amount": "360",
                                    "category": "NEW",
                                    "code": "YQ",
                                    "currency": "AED",
                                    "nature": "AC"
                                },
                                {
                                    "amount": "75",
                                    "category": "NEW",
                                    "code": "AE",
                                    "currency": "AED",
                                    "nature": "AD"
                                },
                                {
                                    "amount": "35",
                                    "category": "NEW",
                                    "code": "F6",
                                    "currency": "AED",
                                    "nature": "TO"
                                },
                                {
                                    "amount": "5",
                                    "category": "NEW",
                                    "code": "TP",
                                    "currency": "AED",
                                    "nature": "SE"
                                },
                                {
                                    "amount": "10",
                                    "category": "NEW",
                                    "code": "ZR",
                                    "currency": "AED",
                                    "nature": "AP"
                                },
                                {
                                    "amount": "70",
                                    "category": "NEW",
                                    "code": "G4",
                                    "currency": "AED",
                                    "nature": "AF"
                                },
                                {
                                    "amount": "10",
                                    "category": "NEW",
                                    "code": "PZ",
                                    "currency": "AED",
                                    "nature": "SE"
                                },
                                {
                                    "amount": "10",
                                    "category": "NEW",
                                    "code": "PZ",
                                    "currency": "AED",
                                    "nature": "AV"
                                },
                                {
                                    "amount": "70",
                                    "category": "NEW",
                                    "code": "QA",
                                    "currency": "AED",
                                    "nature": "AP"
                                },
                                {
                                    "amount": "10",
                                    "category": "NEW",
                                    "code": "R9",
                                    "currency": "AED",
                                    "nature": "SE"
                                }
                            ]
                        },
                        "pricingConditions": {
                            "fareCalculation": {
                                "isManual": false,
                                "pricingIndicator": "0",
                                "text": "AUH EY DOH Q50.00 260.02EY AUH260.02NUC570.04END ROE3.672750"
                            },
                            "isInternationalSale": true
                        },
                        "subType": "TST",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "quotation-record"
                    }
                ],
                "reference": "NP84R4",
                "sourcePublicationId": "2269",
                "ticketingReferences": [
                    {
                        "documents": [
                            {
                                "coupons": [
                                    {
                                        "product": {
                                            "id": "NP84R4-2022-08-19-ST-3",
                                            "ref": "processedPnr.products",
                                            "type": "product"
                                        },
                                        "sequenceNumber": 1
                                    }
                                ],
                                "creation": {
                                    "dateTime": "2022-09-06T00:00:00Z",
                                    "pointOfSale": {
                                        "office": {
                                            "iataNumber": "00145725",
                                            "id": "AUHEY0ERS",
                                            "systemCode": "EY"
                                        }
                                    }
                                },
                                "documentType": "ETICKET",
                                "price": {
                                    "currency": "AED",
                                    "total": "2755"
                                },
                                "primaryDocumentNumber": "6072401543235",
                                "status": "ISSUED"
                            }
                        ],
                        "id": "NP84R4-2022-08-19-OT-28",
                        "isInfant": false,
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-3",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "referenceTypeCode": "FA",
                        "traveler": {
                            "id": "NP84R4-2022-08-19-PT-2",
                            "ref": "processedPnr.travelers",
                            "type": "stakeholder"
                        },
                        "type": "ticketing-reference"
                    }
                ],
                "travelers": [
                    {
                        "contacts": [
                            {
                                "id": "NP84R4-2022-08-19-OT-3",
                                "ref": "processedPnr.contacts",
                                "type": "contact"
                            }
                        ],
                        "gender": "MALE",
                        "id": "NP84R4-2022-08-19-PT-2",
                        "names": [
                            {
                                "firstName": "KARL",
                                "lastName": "GREEN",
                                "title": "MR"
                            }
                        ],
                        "passenger": {
                            "uniqueIdentifier": "6104B0EC0000A675"
                        },
                        "passengerTypeCode": "ADT",
                        "type": "stakeholder"
                    }
                ],
                "type": "pnr",
                "version": "12"
            }
        },
        "id": "NP84R4-2022-08-19",
        "previous": {
            "correlations": [
                {
                    "name": "PNR-EMD",
                    "relation": {
                        "rel": "related"
                    }
                },
                {
                    "name": "PNR-TKT",
                    "relation": {
                        "rel": "related"
                    }
                }
            ],
            "image": {
                "@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr",
                "automatedProcesses": [
                    {
                        "applicableCarrierCode": "EY",
                        "code": "OK",
                        "dateTime": "2022-08-19T00:00:00",
                        "id": "NP84R4-2022-08-19-OT-18",
                        "isApplicableToInfants": false,
                        "office": {
                            "id": "RKTEY08WC"
                        },
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-1",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-2",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-3",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-4",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "automated-process"
                    }
                ],
                "contacts": [
                    {
                        "email": {
                            "address": "<EMAIL>"
                        },
                        "id": "NP84R4-2022-08-19-OT-3",
                        "purpose": [
                            "STANDARD"
                        ],
                        "travelerRefs": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "contact"
                    }
                ],
                "creation": {
                    "comment": "DAPI-EY/USEYBKG",
                    "dateTime": "2022-08-19T14:14:00Z",
                    "pointOfSale": {
                        "login": {
                            "cityCode": "RKT",
                            "countryCode": "AE",
                            "initials": "WS",
                            "numericSign": "9999"
                        },
                        "office": {
                            "agentType": "AIRLINE",
                            "iataNumber": "86493503",
                            "id": "RKTEY08WC",
                            "systemCode": "EY"
                        }
                    }
                },
                "fareElements": [
                    {
                        "code": "FE",
                        "id": "NP84R4-2022-08-19-OT-4",
                        "text": "PAX NON ENDO/ REF",
                        "type": "fare-element"
                    },
                    {
                        "code": "FV",
                        "id": "NP84R4-2022-08-19-OT-5",
                        "text": "PAX EY",
                        "type": "fare-element"
                    }
                ],
                "flightItinerary": [
                    {
                        "destinationIataCode": "DOH",
                        "flights": [
                            {
                                "flightSegment": {
                                    "id": "NP84R4-2022-08-19-ST-1",
                                    "ref": "processedPnr.products",
                                    "type": "product"
                                },
                                "id": "ST-1"
                            }
                        ],
                        "id": "SELL_AVAILABILITY-ST-1",
                        "originIataCode": "AUH",
                        "pointOfCommencement": {
                            "address": {
                                "countryCode": "AE"
                            }
                        },
                        "type": "SELL_AVAILABILITY",
                        "yield": {
                            "amount": "308.0",
                            "elementaryPriceType": "ORIGIN_AND_DESTINATION_YIELD"
                        }
                    },
                    {
                        "destinationIataCode": "AUH",
                        "flights": [
                            {
                                "flightSegment": {
                                    "id": "NP84R4-2022-08-19-ST-2",
                                    "ref": "processedPnr.products",
                                    "type": "product"
                                },
                                "id": "ST-2"
                            }
                        ],
                        "id": "SELL_AVAILABILITY-ST-2",
                        "originIataCode": "DOH",
                        "pointOfCommencement": {
                            "address": {
                                "countryCode": "AE"
                            }
                        },
                        "type": "SELL_AVAILABILITY",
                        "yield": {
                            "amount": "308.0",
                            "elementaryPriceType": "ORIGIN_AND_DESTINATION_YIELD"
                        }
                    }
                ],
                "flightTransfer": {
                    "fromSegments": [
                        {
                            "arrival": {
                                "dateTime": "2023-01-26T01:30:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-26T05:30:00"
                            },
                            "departure": {
                                "dateTime": "2023-01-26T00:30:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-26T03:30:00"
                            },
                            "id": "NP84R4-2022-08-19-ST-2",
                            "marketing": {
                                "bookingClass": {
                                    "code": "Q"
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "390"
                                }
                            }
                        }
                    ],
                    "id": "31376087",
                    "subType": "TRAVEL_READY_REACCOMODATION",
                    "toSegments": [
                        {
                            "arrival": {
                                "dateTime": "2023-01-25T08:10:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-25T12:10:00"
                            },
                            "departure": {
                                "dateTime": "2023-01-25T07:10:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-25T10:10:00"
                            },
                            "id": "NP84R4-2022-08-19-ST-4",
                            "marketing": {
                                "bookingClass": {
                                    "code": "Q"
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "394"
                                }
                            }
                        }
                    ],
                    "travelers": [
                        {
                            "gender": "MALE",
                            "id": "NP84R4-2022-08-19-PT-2",
                            "names": [
                                {
                                    "firstName": "KARL",
                                    "fullName": "KARL GREEN",
                                    "lastName": "GREEN",
                                    "title": "MR"
                                }
                            ],
                            "passenger": {
                                "uniqueIdentifier": "6104B0EC0000A675"
                            },
                            "passengerTypeCode": "ADT",
                            "type": "stakeholder"
                        }
                    ],
                    "type": "flight-disruption-transfer"
                },
                "id": "NP84R4-2022-08-19",
                "lastModification": {
                    "comment": "REACC EY Q-0001AA/MUC1A0701",
                    "dateTime": "2022-09-20T12:38:00Z",
                    "pointOfSale": {
                        "login": {
                            "cityCode": "MUC",
                            "countryCode": "DE",
                            "dutyCode": "SU",
                            "initials": "AA",
                            "numericSign": "0001"
                        },
                        "office": {
                            "agentType": "AIRLINE",
                            "iataNumber": "00000000",
                            "id": "MUC1A06EY",
                            "systemCode": "1A"
                        }
                    }
                },
                "nip": 1,
                "owner": {
                    "login": {
                        "cityCode": "RKT",
                        "countryCode": "AE",
                        "dutyCode": "RM",
                        "initials": "EY"
                    },
                    "office": {
                        "agentType": "AIRLINE",
                        "iataNumber": "86493503",
                        "id": "RKTEY08WC",
                        "systemCode": "EY"
                    }
                },
                "products": [
                    {
                        "id": "NP84R4-2022-08-19-OT-24",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-1",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-2",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-3",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-4",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "service": {
                            "code": "OTHS",
                            "creation": {
                                "dateTime": "2022-09-06T06:06:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "initials": "AA",
                                        "numericSign": "0001"
                                    },
                                    "office": {
                                        "id": "MUC1A0701"
                                    }
                                }
                            },
                            "isChargeable": false,
                            "serviceProvider": {
                                "code": "1A"
                            },
                            "subType": "SPECIAL_SERVICE_REQUEST",
                            "text": "EY399 CANCELED DUE TO OTHER REASON"
                        },
                        "subType": "SERVICE",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "id": "NP84R4-2022-08-19-OT-30",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-1",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-2",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-3",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-ST-4",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "service": {
                            "code": "OTHS",
                            "creation": {
                                "dateTime": "2022-09-20T12:38:00Z",
                                "pointOfSale": {
                                    "office": {
                                        "id": "MUC1A0701"
                                    }
                                }
                            },
                            "isChargeable": false,
                            "serviceProvider": {
                                "code": "1A"
                            },
                            "subType": "SPECIAL_SERVICE_REQUEST",
                            "text": "EY390 CANCELED DUE TO OTHER REASON"
                        },
                        "subType": "SERVICE",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "airSegment": {
                            "aircraft": {
                                "aircraftType": "32A"
                            },
                            "arrival": {
                                "dateTime": "2023-01-05T23:15:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-06T02:15:00"
                            },
                            "bookingStatusCode": "UN",
                            "creation": {
                                "dateTime": "2022-08-19T14:14:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "cityCode": "RKT",
                                        "countryCode": "AE"
                                    },
                                    "office": {
                                        "agentType": "WEB",
                                        "iataNumber": "86493503",
                                        "id": "RKTEY08WC",
                                        "systemCode": "00"
                                    }
                                }
                            },
                            "deliveries": [
                                {
                                    "distributionId": "6004D0EC00016018",
                                    "id": "NP84R4-2022-08-19-OT-1",
                                    "traveler": {
                                        "id": "NP84R4-2022-08-19-PT-2",
                                        "ref": "processedPnr.travelers",
                                        "type": "stakeholder"
                                    },
                                    "type": "segment-delivery"
                                }
                            ],
                            "departure": {
                                "dateTime": "2023-01-05T22:10:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-06T02:10:00",
                                "terminal": "3"
                            },
                            "id": "2023-01-06-AUH-DOH",
                            "isInformational": false,
                            "marketing": {
                                "bookingClass": {
                                    "cabin": {
                                        "bidPrice": {
                                            "amount": "1.0",
                                            "elementaryPriceType": "BID_PRICE"
                                        },
                                        "code": "Y"
                                    },
                                    "code": "Q",
                                    "levelOfService": "ECONOMY",
                                    "subClass": {
                                        "code": 0,
                                        "pointOfSale": {
                                            "login": {
                                                "countryCode": "AE"
                                            },
                                            "office": {
                                                "systemCode": "EY"
                                            }
                                        },
                                        "yields": [
                                            {
                                                "amount": "308.0",
                                                "elementaryPriceType": "ADJUSTED_YIELD"
                                            },
                                            {
                                                "amount": "307.0",
                                                "elementaryPriceType": "EFFECTIVE_YIELD"
                                            },
                                            {
                                                "amount": "308.0",
                                                "elementaryPriceType": "OND_YIELD_AUH_DOH"
                                            }
                                        ]
                                    }
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "399"
                                },
                                "id": "EY-399-2023-01-06-AUH-DOH",
                                "isOpenNumber": false,
                                "isPrime": true
                            }
                        },
                        "id": "NP84R4-2022-08-19-ST-1",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-OT-24",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-OT-30",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "subType": "AIR",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "airSegment": {
                            "aircraft": {
                                "aircraftType": "32A"
                            },
                            "arrival": {
                                "dateTime": "2023-01-26T01:30:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-26T05:30:00",
                                "terminal": "3"
                            },
                            "bookingStatusCode": "UN",
                            "creation": {
                                "dateTime": "2022-08-19T14:14:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "cityCode": "RKT",
                                        "countryCode": "AE"
                                    },
                                    "office": {
                                        "agentType": "WEB",
                                        "iataNumber": "86493503",
                                        "id": "RKTEY08WC",
                                        "systemCode": "00"
                                    }
                                }
                            },
                            "deliveries": [
                                {
                                    "distributionId": "6004D0EC00015FB5",
                                    "id": "NP84R4-2022-08-19-OT-2",
                                    "traveler": {
                                        "id": "NP84R4-2022-08-19-PT-2",
                                        "ref": "processedPnr.travelers",
                                        "type": "stakeholder"
                                    },
                                    "type": "segment-delivery"
                                }
                            ],
                            "departure": {
                                "dateTime": "2023-01-26T00:30:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-26T03:30:00"
                            },
                            "id": "2023-01-26-DOH-AUH",
                            "isInformational": false,
                            "marketing": {
                                "bookingClass": {
                                    "cabin": {
                                        "bidPrice": {
                                            "amount": "1.0",
                                            "elementaryPriceType": "BID_PRICE"
                                        },
                                        "code": "Y"
                                    },
                                    "code": "Q",
                                    "levelOfService": "ECONOMY",
                                    "subClass": {
                                        "code": 0,
                                        "pointOfSale": {
                                            "login": {
                                                "countryCode": "AE"
                                            },
                                            "office": {
                                                "systemCode": "EY"
                                            }
                                        },
                                        "yields": [
                                            {
                                                "amount": "308.0",
                                                "elementaryPriceType": "ADJUSTED_YIELD"
                                            },
                                            {
                                                "amount": "307.0",
                                                "elementaryPriceType": "EFFECTIVE_YIELD"
                                            },
                                            {
                                                "amount": "308.0",
                                                "elementaryPriceType": "OND_YIELD_DOH_AUH"
                                            }
                                        ]
                                    }
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "390"
                                },
                                "id": "EY-390-2023-01-26-DOH-AUH",
                                "isOpenNumber": false,
                                "isPrime": true
                            }
                        },
                        "id": "NP84R4-2022-08-19-ST-2",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-OT-24",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-OT-30",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "subType": "AIR",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "airSegment": {
                            "aircraft": {
                                "aircraftType": "32A"
                            },
                            "arrival": {
                                "dateTime": "2023-01-05T18:35:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-05T21:35:00"
                            },
                            "bookingStatusCode": "TK",
                            "creation": {
                                "dateTime": "2022-09-06T06:06:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "cityCode": "RKT",
                                        "countryCode": "AE"
                                    },
                                    "office": {
                                        "agentType": "NON_IATA_AGENT",
                                        "iataNumber": "00000000",
                                        "id": "RKTEY08WC",
                                        "systemCode": "EY"
                                    }
                                }
                            },
                            "deliveries": [
                                {
                                    "distributionId": "014093800171DC78",
                                    "id": "NP84R4-2022-08-19-OT-23",
                                    "traveler": {
                                        "id": "NP84R4-2022-08-19-PT-2",
                                        "ref": "processedPnr.travelers",
                                        "type": "stakeholder"
                                    },
                                    "type": "segment-delivery"
                                }
                            ],
                            "departure": {
                                "dateTime": "2023-01-05T17:35:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-05T21:35:00",
                                "terminal": "3"
                            },
                            "id": "2023-01-05-AUH-DOH",
                            "isInformational": false,
                            "marketing": {
                                "bookingClass": {
                                    "cabin": {
                                        "code": "Y"
                                    },
                                    "code": "Q",
                                    "levelOfService": "ECONOMY",
                                    "subClass": {
                                        "code": 0,
                                        "pointOfSale": {
                                            "login": {
                                                "countryCode": "AE"
                                            },
                                            "office": {
                                                "systemCode": "EY"
                                            }
                                        }
                                    }
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "391"
                                },
                                "id": "EY-391-2023-01-05-AUH-DOH",
                                "isOpenNumber": false,
                                "isPrime": true
                            }
                        },
                        "id": "NP84R4-2022-08-19-ST-3",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-OT-24",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-OT-30",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "subType": "AIR",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    },
                    {
                        "airSegment": {
                            "aircraft": {
                                "aircraftType": "321"
                            },
                            "arrival": {
                                "dateTime": "2023-01-25T08:10:00Z",
                                "iataCode": "AUH",
                                "localDateTime": "2023-01-25T12:10:00",
                                "terminal": "3"
                            },
                            "bookingStatusCode": "TK",
                            "creation": {
                                "dateTime": "2022-09-20T12:38:00Z",
                                "pointOfSale": {
                                    "login": {
                                        "cityCode": "RKT",
                                        "countryCode": "AE"
                                    },
                                    "office": {
                                        "agentType": "NON_IATA_AGENT",
                                        "iataNumber": "00000000",
                                        "id": "RKTEY08WC",
                                        "systemCode": "EY"
                                    }
                                }
                            },
                            "deliveries": [
                                {
                                    "distributionId": "014093800171E95B",
                                    "id": "NP84R4-2022-08-19-OT-29",
                                    "traveler": {
                                        "id": "NP84R4-2022-08-19-PT-2",
                                        "ref": "processedPnr.travelers",
                                        "type": "stakeholder"
                                    },
                                    "type": "segment-delivery"
                                }
                            ],
                            "departure": {
                                "dateTime": "2023-01-25T07:10:00Z",
                                "iataCode": "DOH",
                                "localDateTime": "2023-01-25T10:10:00"
                            },
                            "id": "2023-01-25-DOH-AUH",
                            "isInformational": false,
                            "marketing": {
                                "bookingClass": {
                                    "cabin": {
                                        "code": "Y"
                                    },
                                    "code": "Q",
                                    "levelOfService": "ECONOMY",
                                    "subClass": {
                                        "code": 0,
                                        "pointOfSale": {
                                            "login": {
                                                "countryCode": "AE"
                                            },
                                            "office": {
                                                "systemCode": "EY"
                                            }
                                        }
                                    }
                                },
                                "flightDesignator": {
                                    "carrierCode": "EY",
                                    "flightNumber": "394"
                                },
                                "id": "EY-394-2023-01-25-DOH-AUH",
                                "isOpenNumber": false,
                                "isPrime": true
                            }
                        },
                        "id": "NP84R4-2022-08-19-ST-4",
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-OT-24",
                                "ref": "processedPnr.products",
                                "type": "product"
                            },
                            {
                                "id": "NP84R4-2022-08-19-OT-30",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "subType": "AIR",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "product"
                    }
                ],
                "purgeDate": {
                    "date": "2023-01-30"
                },
                "queuingOffice": {
                    "id": "RKTEY08WC"
                },
                "quotations": [
                    {
                        "coupons": [
                            {
                                "baggageAllowance": {
                                    "weight": {
                                        "unit": "KILOGRAMS",
                                        "value": 30
                                    }
                                },
                                "fareBasis": {
                                    "fareBasisCode": "2AE",
                                    "primaryCode": "QLC"
                                },
                                "id": "NP84R4-2022-08-19-QT-1-1",
                                "product": {
                                    "id": "NP84R4-2022-08-19-ST-1",
                                    "ref": "processedPnr.products",
                                    "type": "product"
                                },
                                "validityDates": {
                                    "notValidAfterDate": "2023-01-06",
                                    "notValidBeforeDate": "2023-01-06"
                                }
                            },
                            {
                                "baggageAllowance": {
                                    "weight": {
                                        "unit": "KILOGRAMS",
                                        "value": 30
                                    }
                                },
                                "fareBasis": {
                                    "fareBasisCode": "2AE",
                                    "primaryCode": "QLC"
                                },
                                "id": "NP84R4-2022-08-19-QT-1-2",
                                "product": {
                                    "id": "NP84R4-2022-08-19-ST-2",
                                    "ref": "processedPnr.products",
                                    "type": "product"
                                },
                                "validityDates": {
                                    "notValidAfterDate": "2023-01-26",
                                    "notValidBeforeDate": "2023-01-26"
                                }
                            }
                        ],
                        "creation": {
                            "dateTime": "2022-08-19T00:00:00Z",
                            "pointOfSale": {
                                "office": {
                                    "id": "RKTEY08WC"
                                }
                            }
                        },
                        "destinationCityIataCode": "AUH",
                        "documentType": "TICKET",
                        "id": "NP84R4-2022-08-19-QT-1",
                        "isManual": false,
                        "issuanceType": "FIRST_ISSUE",
                        "lastModification": {
                            "dateTime": "2022-08-19T00:00:00Z",
                            "pointOfSale": {
                                "office": {
                                    "id": "RKTEY08WC"
                                }
                            }
                        },
                        "originCityIataCode": "AUH",
                        "price": {
                            "detailedPrices": [
                                {
                                    "amount": "2755",
                                    "currency": "AED",
                                    "elementaryPriceType": "TOTAL"
                                },
                                {
                                    "amount": "2100",
                                    "currency": "AED",
                                    "elementaryPriceType": "BASE_FARE"
                                },
                                {
                                    "amount": "2755",
                                    "currency": "AED",
                                    "elementaryPriceType": "GRAND_TOTAL"
                                }
                            ],
                            "taxes": [
                                {
                                    "amount": "360",
                                    "category": "NEW",
                                    "code": "YQ",
                                    "currency": "AED",
                                    "nature": "AC"
                                },
                                {
                                    "amount": "75",
                                    "category": "NEW",
                                    "code": "AE",
                                    "currency": "AED",
                                    "nature": "AD"
                                },
                                {
                                    "amount": "35",
                                    "category": "NEW",
                                    "code": "F6",
                                    "currency": "AED",
                                    "nature": "TO"
                                },
                                {
                                    "amount": "5",
                                    "category": "NEW",
                                    "code": "TP",
                                    "currency": "AED",
                                    "nature": "SE"
                                },
                                {
                                    "amount": "10",
                                    "category": "NEW",
                                    "code": "ZR",
                                    "currency": "AED",
                                    "nature": "AP"
                                },
                                {
                                    "amount": "70",
                                    "category": "NEW",
                                    "code": "G4",
                                    "currency": "AED",
                                    "nature": "AF"
                                },
                                {
                                    "amount": "10",
                                    "category": "NEW",
                                    "code": "PZ",
                                    "currency": "AED",
                                    "nature": "SE"
                                },
                                {
                                    "amount": "10",
                                    "category": "NEW",
                                    "code": "PZ",
                                    "currency": "AED",
                                    "nature": "AV"
                                },
                                {
                                    "amount": "70",
                                    "category": "NEW",
                                    "code": "QA",
                                    "currency": "AED",
                                    "nature": "AP"
                                },
                                {
                                    "amount": "10",
                                    "category": "NEW",
                                    "code": "R9",
                                    "currency": "AED",
                                    "nature": "SE"
                                }
                            ]
                        },
                        "pricingConditions": {
                            "fareCalculation": {
                                "isManual": false,
                                "pricingIndicator": "0",
                                "text": "AUH EY DOH Q50.00 260.02EY AUH260.02NUC570.04END ROE3.672750"
                            },
                            "isInternationalSale": true
                        },
                        "subType": "TST",
                        "travelers": [
                            {
                                "id": "NP84R4-2022-08-19-PT-2",
                                "ref": "processedPnr.travelers",
                                "type": "stakeholder"
                            }
                        ],
                        "type": "quotation-record"
                    }
                ],
                "reference": "NP84R4",
                "sourcePublicationId": "2269",
                "ticketingReferences": [
                    {
                        "documents": [
                            {
                                "coupons": [
                                    {
                                        "product": {
                                            "id": "NP84R4-2022-08-19-ST-3",
                                            "ref": "processedPnr.products",
                                            "type": "product"
                                        },
                                        "sequenceNumber": 1
                                    }
                                ],
                                "creation": {
                                    "dateTime": "2022-09-06T00:00:00Z",
                                    "pointOfSale": {
                                        "office": {
                                            "iataNumber": "00145725",
                                            "id": "AUHEY0ERS",
                                            "systemCode": "EY"
                                        }
                                    }
                                },
                                "documentType": "ETICKET",
                                "price": {
                                    "currency": "AED",
                                    "total": "2755"
                                },
                                "primaryDocumentNumber": "6072401543235",
                                "status": "ISSUED"
                            }
                        ],
                        "id": "NP84R4-2022-08-19-OT-28",
                        "isInfant": false,
                        "products": [
                            {
                                "id": "NP84R4-2022-08-19-ST-3",
                                "ref": "processedPnr.products",
                                "type": "product"
                            }
                        ],
                        "referenceTypeCode": "FA",
                        "traveler": {
                            "id": "NP84R4-2022-08-19-PT-2",
                            "ref": "processedPnr.travelers",
                            "type": "stakeholder"
                        },
                        "type": "ticketing-reference"
                    }
                ],
                "travelers": [
                    {
                        "contacts": [
                            {
                                "id": "NP84R4-2022-08-19-OT-3",
                                "ref": "processedPnr.contacts",
                                "type": "contact"
                            }
                        ],
                        "gender": "MALE",
                        "id": "NP84R4-2022-08-19-PT-2",
                        "names": [
                            {
                                "firstName": "KARL",
                                "lastName": "GREEN",
                                "title": "MR"
                            }
                        ],
                        "passenger": {
                            "uniqueIdentifier": "6104B0EC0000A675"
                        },
                        "passengerTypeCode": "ADT",
                        "type": "stakeholder"
                    }
                ],
                "type": "pnr",
                "version": "11"
            }
        },
        "type": "com.amadeus.pulse.message.Pnr"
    }
}