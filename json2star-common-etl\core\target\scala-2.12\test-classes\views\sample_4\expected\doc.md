
### Tables overview
#### Fact Tables
| Table | Description | GDPR Zone | Granularity | Primary key | Subdomain |
| ---- | ---- | ---- | ---- | ---- | ---- |
| FACT_RESERVATION_HISTO | It contains information related to the booking on PNR-level. |  | 1 PNR, version | RESERVATION_ID-VERSION |  |

#### Dimension Tables
| Table | Description | GDPR Zone | Granularity | Primary key |
| ---- | ---- | ---- | ---- | ---- |
| DIM_CARRIER |  | green |  | CARRIER_ID |
| DIM_POINT_OF_SALE |  | green |  | POINT_OF_SALE_ID |

#### Association Tables
No Table is available
### Tables details with fields
#### Fact Tables Fields
| Table | Field | Description | Example | GDPR Zone | Format | Mandatory | In FK to | Source Path |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| FACT_RESERVATION_HISTO | RESERVATION_ID |  |  | <p>green</p> | STRING | PK |  | <p>hashM(.id)</p><p>hashM(.dummy.id)</p> |
| FACT_RESERVATION_HISTO | VERSION |  |  | <p>green</p> | INT | PK |  | <p>.version</p><p>.dummy.version</p> |
| FACT_RESERVATION_HISTO | PNR_CREATION_DATE |  |  |  | TIMESTAMP | N |  | <p>.creation.dateTime</p><p>.dummy.creation.dateTime</p> |
| FACT_RESERVATION_HISTO | DATE_BEGIN |  |  |  | TIMESTAMP | N |  | <p>.lastModification.dateTime</p><p>.dummy.lastModification.dateTime</p> |
| FACT_RESERVATION_HISTO | DATE_END |  |  |  | TIMESTAMP | N |  |  |
| FACT_RESERVATION_HISTO | IS_LAST_VERSION |  |  |  | BOOLEAN | N |  |  |
| FACT_RESERVATION_HISTO | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |

#### Dimension Tables Fields
| Table | Field | Description | Example | GDPR Zone | Format | Mandatory | In FK to | Source Path |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| DIM_CARRIER | CARRIER_ID |  |  | <p>green</p> | BIGINT | PK |  | <p>hashXS(.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode.value)</p><p>hashXS(.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode.value)</p><p>hashXS(.products[?(@.subType == 'AIR')].airSegment.deliveries[\*].flightSegment.marketing.flightDesignator.carrierCode.value)</p> |
| DIM_CARRIER | CARRIER |  |  | <p>green</p> | STRING | N |  | <p>.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode.value</p><p>.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode.value</p><p>.products[?(@.subType == 'AIR')].airSegment.deliveries[\*].flightSegment.marketing.flightDesignator.carrierCode.value</p> |
| DIM_CARRIER | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |
| DIM_POINT_OF_SALE | COMMON_COLUMN |  |  | <p>green</p> | STRING | N |  | <p>.owner.office.id</p><p>.creation.pointOfSale.office.id</p><p>.lastModification.pointOfSale.office.id</p> |
| DIM_POINT_OF_SALE | POINT_OF_SALE_ID |  |  | <p>green</p> | STRING | PK |  | <p>hashM(.owner.office.id - .owner.login.cityCode)</p><p>hashM(.creation.pointOfSale.office.id - .creation.pointOfSale.login.cityCode)</p><p>hashM(.lastModification.pointOfSale.office.id - .lastModification.pointOfSale.login.iataNumber)</p> |
| DIM_POINT_OF_SALE | COL_PRESENT_ONLY_IN_A_ROOT |  |  | <p>green</p> | STRING | N |  | <p>.owner.office.id</p> |
| DIM_POINT_OF_SALE | LOAD_DATE | <p>Technical field: date/time UTC when the source message was received in the Amadeus Big Data Platform</p> |  | <p>green</p> | TIMESTAMP | N |  |  |

#### Association Tables Fields
No Table is available

