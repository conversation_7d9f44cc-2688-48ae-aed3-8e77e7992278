import java.time.OffsetDateTime
import scala.collection.mutable.ListBuffer
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import com.amadeus.airbi.json2star.common.validation.config.{
  CheckType,
  ValidationConf,
  ValidationConfig,
  ValidationRecord
}

//WIDGETS

val exclusions = dbutils.widgets.get("exclusions")
val exclusionList = exclusions.toUpperCase().split(';')
val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val vConfig = ValidationConf.apply(dbutils)

var testRecords: ListBuffer[ValidationRecord] = ListBuffer()
var testNum = 1

spark.catalog.setCurrentDatabase(vConfig.appConfig.common.outputDatabase)

def tableIsEligible(tableName: String): Boolean = {
  tableName.toUpperCase().contains("DIM") || tableName.toUpperCase().contains("INTERNAL") || tableName
    .toUpperCase()
    .contains("HISTO")
}

spark.catalog
  .listTables()
  .collect()
  .foreach(table => {
    if (tableIsEligible(table.name)  && ! exclusionList.contains(table.name.toUpperCase())) {
      val cols: Array[String] = spark.sql(s"select * from ${table.name} limit 1").columns

      // select statement construction
      val queryPart = cols.map(c => s"sum(case when $c is null then 0 else 1 end) as $c").mkString(", ")

      // get the null cols
      val nullColsFinder = spark.sql(s"select ${queryPart} from ${table.name}").collect()(0).toSeq

      // merge with column name to be able to do filter on column
      val nullCols = cols.zip(nullColsFinder).filter(_._2 == 0)

      println(table.name + " " + nullCols.length)
      val testRecord = ValidationRecord(
        vConfig.appConfig.common.domain,
        vConfig.appConfig.common.domainVersion,
        vConfig.appConfig.common.shard,
        vConfig.phase,
        currentTimestamp,
        task,
        s"Test $testNum - Check no null columns in ${table.name}",
        nullCols.length == 0,
        nullCols.length,
        cols.length,
        nullCols.length.toFloat / cols.length,
        nullCols.map(_._1).mkString(", ")
      )
      testRecords += testRecord
      testNum += 1
    }
  })

val df = testRecords.toSeq.toDF()
df.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
  .write
  .insertInto(s"${vConfig.validationDatabase}.${vConfig.validationTable}")