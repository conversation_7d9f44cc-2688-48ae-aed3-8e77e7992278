# Readme

## TKT only side correlations

The files in the data/pnr and data/tkt folders correspond to different versions of PNR N389UA and TKT 7492400944169.
It is a scenario where only TKT side contains correlations to PNR side.
The scenario contains a closure on TKT coupons (the coupon association is no longer there on v2 and v3).
In chronological order, the scenario is as follows:

```
PNR v6 (0 items)    2022-05-13T13:48:00
                    2022-05-13T13:48:31   TKT v0 (2 items: ST-1,PT-2,C1; ST-2,PT-2,C2)
PNR v7 (0 items)    2022-05-13T13:50:00
PNR v8 (0 items)    2022-05-13T13:50:00
                    2022-05-13T13:50:59   TKT v1 (1 item:                ST-2,PT-2,C2)
PNR v9 (0 items)    2022-05-13T13:51:00
                    2022-05-13T13:51:01   TKT v2 (1 item: PT-2)
                    2022-05-13T13:51:02   TKT v3 (1 item: PT-2)
```

Expected high level associations: 

```
PNR v6 - v0 TKT (2 association rows at coupon level)
PNR v8 - v0 TKT (2 association rows at coupon level)
PNR v8 - v1 TKT (1 association row at coupon level)
PNR v9 - v1 TKT (1 association row at coupon level)
PNR v9 - v2 TKT (arguably, 1 association row containing only PT-2 information)
PNR v9 - v3 TKT (arguably, 1 association row containing only PT-2 information, marked as latest)
```

Note: 
```
PNR v7 - v0 TKT (2 items) not shown because PNR v7 and PNR v8 have the same timestamp
``

This data has been extracted from 4Z PDT data.

```

# Correlation IDS Table
This scenario is the correlation between these domains
- A: PNR
- B: TKT

The table ```internal_corr_ids_asso_air_segment_pax_coupon_histo``` contains the following data:
- DOMAIN_A_ID: RESERVATION_ID from updated PNR tables 
- DOMAIN_B_ID: TRAVEL_DOCUMENT_ID from updated TKT tables