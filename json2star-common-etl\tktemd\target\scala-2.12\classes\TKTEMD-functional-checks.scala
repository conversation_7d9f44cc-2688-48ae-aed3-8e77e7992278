// MAGIC %md
// MAGIC #Notebook Information
// MAGIC This notebook contains functional checks for TKTEMD.

// COMMAND ----------

import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

// COMMAND ----------

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, daysBack, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard
val db = vConfig.appConfig.common.outputDatabase

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val date_formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val minTime = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(daysBack.toInt)

// COMMAND ----------

case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()


// COMMAND ----------

// MAGIC %md
// MAGIC ## Travel Document Level
// MAGIC - percent of travel docs with a fare calc remark (IT/BT, noadc ??????)
// MAGIC - percent of travel documents with a numeric price total that are fully prorated
// MAGIC - percent of travel docs with sum of proration = ( base fare +- 5%)
// MAGIC - percent of travel documents with IT/BT, NO ADC, or A (or any letters) or **** null in price total
// MAGIC - percent of partially-prorated travel docs that have a prorated currency different than price_currency (should be )

// COMMAND ----------

// MAGIC %md
// MAGIC ### test1

// COMMAND ----------

//percent of travel documents with fare calc remark
//this is the key element for computing proration

val totalCount = spark.sql(s"""
  select *
  from $db.fact_travel_document
  where load_date > to_date($minTime)
""").count()

val failCount = totalCount - spark.sql(s"""
  select *
  from $db.FACT_PRICING_CONDITION
  where FARE_CALCULATION_TEXT is not null
    and load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 1 : Percent of documents in with no fare calculation text field",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of documents with no fare calculation text field : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test2

// COMMAND ----------

//percent of travel documents that are fully prorated
//percent of travel documents with numeric base fare > 0 where all the coupons have ESTIMATED_PRORATED_FARE_ALGORITHM != "NONE"
val priceThreshold = .05

//nb of travel docs
val totalCount = spark.sql(s"""
  select *
  from $db.fact_travel_document
  where (price_total_original - price_taxes_original) > 0
    and load_date > to_date($minTime)
""").count()

val nbNotProrated = spark.sql(s"""
  select a.travel_document_id
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where ((price_total_original - price_taxes_original) > 0
    and ESTIMATED_PRORATED_FARE_ALGORITHM not in("FARE CALC","DISTANCE")
    or ESTIMATED_PRORATED_FARE_ALGORITHM is null)
    and b.load_date > to_date($minTime)
  group by a.travel_document_id
""").count()

val percentFail = nbNotProrated.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 2 : Percent of travel documents that have all coupons prorated",
  nbNotProrated > 0,
  nbNotProrated,
  totalCount,
  percentFail,
  if ( nbNotProrated > 0) f"Percent of travel documents with a price_total value where not all coupons are prorate : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test3

// COMMAND ----------

//check that sum of prorated amount across all tickets equals the total ticket amount, for converted currency
//only for records that have a price_total and where all coupons of the travel document are prorated
val priceThreshold = .05

// to do: check with
val failCount = spark.sql(s"""
    select
      b.travel_document_id,
      max(price_total - price_taxes) trvDocTotal,
      collect_set(ESTIMATED_PRORATED_FARE_ALGORITHM) tdProrationAlgos,
      sum(ESTIMATED_PRORATED_FARE) coupTotal
    from $db.fact_travel_document a
      join $db.fact_coupon b on a.travel_document_id = b.travel_document_id
    where (price_total_original - price_taxes_original) > 0
      and a.load_date > to_date($minTime)
    group by b.travel_document_id
    having !array_contains(tdProrationAlgos,"NONE")
      and coupTotal not between (trvDocTotal*(1-$priceThreshold)) and (trvDocTotal*(1+$priceThreshold))
""").count()

val tdAllCouponsProrated = (totalCount - nbNotProrated)
val percentFail = failCount.toFloat/tdAllCouponsProrated

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 3 : Check that the sum of prorated values equals total amount in converted home currency",
  failCount > 0,
  failCount,
  tdAllCouponsProrated,
  percentFail,
  if ( failCount > 0) f"Percent of travel documents with a price_total value and all coupons prorate not within ${(priceThreshold*100)}%.0f%% : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test4

// COMMAND ----------

//Percent of travel documents with base_fare containing nonnumeric characters

val totalCount = spark.sql(s"""
  select * from $db.fact_travel_document where load_date > to_date($minTime)
  """).count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_travel_document
  where (price_total_original RLIKE '.*[a-zA-Z]+.*'
    or price_total_original is null)
    and load_date > to_date($minTime)
  """).count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 4 : Percent of travel documents with price_total_original containing nonnumeric characters",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of travel documents with price_total_original containing nonnumeric characters : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ## Coupon Level
// MAGIC - percent of coupons prorated by fareCalc
// MAGIC - percent of remaining coupons prorated by distance
// MAGIC - percent of coupons not prorated

// COMMAND ----------

// MAGIC %md
// MAGIC ### test5

// COMMAND ----------

//percent of coupons prorated by farecalc

val totalCount = spark.sql(s"""
  select *
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where (price_total-price_taxes) > 0
    and b.load_date > to_date($minTime)
  """).count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where (price_total-price_taxes) > 0
    and ESTIMATED_PRORATED_FARE_ALGORITHM = "FARE CALC"
    and b.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 5 : Percent of coupons prorated by fare calc",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of coupons prorated by fare calc : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test6

// COMMAND ----------

//percent of remaining coupons prorated by distance

val totalCount = spark.sql(s"""
  select *
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where (price_total-price_taxes) > 0
    and ESTIMATED_PRORATED_FARE_ALGORITHM != "FARE CALC"
    and b.load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where (price_total-price_taxes) > 0
    and ESTIMATED_PRORATED_FARE_ALGORITHM = "DISTANCE"
    and b.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 6 : Percent of remaining coupons prorated by distance",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of remaining coupons prorated by distance : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test7

// COMMAND ----------

//percent of coupons not prorated

val totalCount = spark.sql(s"""
  select *
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where (price_total-price_taxes) > 0
    and b.load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where ((price_total-price_taxes) > 0
    and (ESTIMATED_PRORATED_FARE_ALGORITHM = "NONE"
    or ESTIMATED_PRORATED_FARE_ALGORITHM is null))
    and b.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 7 : Percent of coupons not prorated",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of coupons not prorated : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test8

// COMMAND ----------

//percent of fare calc prorated coupons that have a prorated currency different than base fare currency

val totalCount = spark.sql(s"""
  select *
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where (price_total-price_taxes) > 0
    and b.load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_coupon a
    join $db.fact_travel_document b on a.travel_document_id = b.travel_document_id
  where (price_total-price_taxes) > 0
    and ESTIMATED_PRORATED_FARE_CURRENCY_ORIGINAL != PRICE_CURRENCY_ORIGINAL
    and b.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 8 : Percent of fare calc prorated coupons that have a prorated currency different than base fare currency",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of fare calc prorated coupons that have a prorated currency different than base fare currency : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ## BDS-14075
// MAGIC - if >=5 coupons then conjunctive ticket number(s) shall be filled
// MAGIC - for a given document and its type (e.g. TICKET or EMD_ASSOCIATED), there shall be no associated document of that same type
// MAGIC - all associated documents' document numbers shall be present as travel documents' primary document number
// MAGIC - all travel documents shall have price details for at least the "BaseFare" and "TotalFare" elementary price type
// MAGIC - all travel documents shall have at least 1 FOP

// COMMAND ----------

// MAGIC %md
// MAGIC ### test9

// COMMAND ----------

// if >=5 coupons then conjunctive ticket number(s) shall be filled

val totalCount = spark.sql(s"""
  select doc.reference_key, count(1) as nbrcpns
  from $db.fact_travel_document doc
    inner join $db.fact_coupon cpn on cpn.travel_document_id = doc.travel_document_id
  where doc.load_date > to_date($minTime)
  group by doc.reference_key
  having nbrcpns >= 5
""").count()

val failCount = spark.sql(s"""
  select doc.reference_key, count(1) as nbrcpns
  from $db.fact_travel_document doc
    inner join $db.fact_coupon cpn on cpn.travel_document_id = doc.travel_document_id
  where doc.conjunctive_document_1 is null
    and doc.conjunctive_document_2 is null
    and doc.conjunctive_document_3 is null
    and doc.load_date > to_date($minTime)
  group by doc.reference_key
  having nbrcpns >= 5
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 9 : Percent of tickets that have at least 5 coupons and that do not have conjuctive documents",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of tickets that have at least 5 coupons and that do not have conjuctive documents : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test10

// COMMAND ----------

// for a given document and its type (e.g. TICKET or EMD_ASSOCIATED), there shall be no associated document of that same type

val totalCount = spark.sql(s"""
  select *
  from $db.fact_travel_document doc
    inner join $db.fact_associated_travel_document assdoc on assdoc.travel_document_id = doc.travel_document_id
  where doc.load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_travel_document doc
    inner join $db.fact_associated_travel_document assdoc on assdoc.travel_document_id = doc.travel_document_id
  where doc.document_type = assdoc.associated_document_type
    and doc.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 10 : Percent of tickets that have an associated document with the same document type",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of tickets that have an associated document with the same document type : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test11

// COMMAND ----------

// all associated documents' document numbers shall be present as travel documents' primary document number

val totalCount = spark.sql(s"""
  select *
  from $db.fact_travel_document doc
    inner join $db.fact_associated_travel_document assdoc on assdoc.travel_document_id = doc.travel_document_id
  where doc.load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_travel_document doc
    inner join $db.fact_associated_travel_document assdoc on assdoc.travel_document_id = doc.travel_document_id
  where assdoc.document_number not in (
    select distinct primary_document_number from $db.fact_travel_document
  ) and doc.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 11 : Percent of tickets that have an associated document whose document number is not the primary document number of any other document",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of tickets that have an associated document whose document number is not the primary document number of any other document : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test12

// COMMAND ----------

// all travel documents shall have price details for at least the "BaseFare" and "TotalFare" elementary price type

val totalCount = spark.sql(s"""
  select doc.reference_key
  from $db.fact_travel_document doc
    inner join $db.fact_price_detail det on det.travel_document_id = doc.travel_document_id
  where doc.load_date > to_date($minTime)
  group by 1
""").count()

val failCount = spark.sql(s"""
  select
    doc.reference_key,
    array_join(collect_set(distinct det.elementary_price_type), '-') as price_types
  from $db.fact_travel_document doc
    inner join $db.fact_price_detail det on det.travel_document_id = doc.travel_document_id
  where doc.load_date > to_date($minTime)
  group by 1
  having price_types not like '%BaseFare%'
    or price_types not like '%TotalFare%'
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 12 : Percent of tickets that do not have any price detail for the 'BaseFare' nor the 'TotalFare' elementary price type",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of tickets that do not have any price detail for the 'BaseFare' nor the 'TotalFare' elementary price type : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test13

// COMMAND ----------

// all travel documents shall have at least 1 FOP

val totalCount = spark.sql(s"""
  select * from $db.fact_travel_document where load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $db.fact_travel_document doc
    left outer join $db.fact_form_of_payment fop on fop.travel_document_id = doc.travel_document_id
  where fop.form_of_payment_id is null
    and doc.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 13 : Percent of tickets that do not have any form of payment",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of tickets that do not have any form of payment : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ### test14

// COMMAND ----------

// less than 2% of tickets with additional collection amounts that do not have any original document reference

val tc1Threshold = 0.02

val results = spark.sql(s"""
  select
    count(1) as exc_tkt_total,
    sum(case when original_document_number is null then 1 else 0 end) as exc_tkt_without_parent_total,
    exc_tkt_without_parent_total / exc_tkt_total as exc_tkt_without_parent_percentage
  from $db.fact_travel_document
  where price_total_original in ('NO_ADC', 'NO ADC', 'NOADC') or price_total_original rlike '^[0-9.]+A'
    and load_date > to_date($minTime)
""").first().toString.replace("[","").replace("]","").split(",")

val (totalCount, failCount, percentFail) = (results(0).toLong, results(1).toLong, results(2).toFloat)

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 14 : Percent of tickets with additional collection amounts that do not have any original document reference",
  percentFail < tc1Threshold,
  failCount,
  totalCount,
  percentFail,
  if (failCount > 0) f"Percent of tickets with additional collection amounts that do not have any original document reference : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ##write to db

// COMMAND ----------

val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")