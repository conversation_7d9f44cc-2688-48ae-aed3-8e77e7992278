Goal: integration test to verify the generators with the mapping and latest addons

D: Databricks Delta
S: Snowflake

The mapping file contain these tables:
- FACT_PASSENGER_DUMMY: it is the latest VIEW of FACT_PASSENGER_HISTO - it must not be created its table selector never used 
- FACT_PASSENGER: it is the latest VIEW of FACT_PASSENGER_HISTO - it must be created in D and S
- FACT_PASSENGER_HISTO: it is a FACT TABLE - it must be created in D and S
- FACT_SECONDARY_HISTO: it is a FACT TABLE - it must be created in D and S
- INTERNAL_MY_TABLE it is an INTERNAL TABLE - it must be created in D, but not in S

Note: the order of tables and columns is kept in the generated views

The schema is consolidated using the Table Addons Logic

The goal is to generate the following views for this scenario
 - Delta create table sql statement
 - Delta copy table from previous version sql statement
 - Snowflake sql statement for create and alter to add primary keys constraints
 - Confluence doc markdown: default and full version with all columns
 - J<PERSON> views sql statement
 - Plant UML global diagram input

Note: the config "table-snowflake" is optional and is used to provide additional info for Snowflake.
- cluster-by to define how to cluster the table