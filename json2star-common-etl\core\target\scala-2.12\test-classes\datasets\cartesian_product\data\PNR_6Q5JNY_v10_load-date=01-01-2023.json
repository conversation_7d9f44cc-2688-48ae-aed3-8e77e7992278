{"mainResource": {"id": "6Q5JNY-2023-06-01", "type": "com.amadeus.pulse.message.Pnr", "current": {"image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "id": "6Q5JNY-2023-06-01", "type": "pnr", "reference": "6Q5JNY", "version": "10", "purgeDate": {"date": "2023-06-16"}, "owner": {"office": {"id": "CAN131", "iataNumber": "08304564", "systemCode": "1E", "agentType": "TRAVEL_AGENT"}, "login": {"initials": "AA", "dutyCode": "SU", "countryCode": "CN", "cityCode": "CAN"}}, "creation": {"dateTime": "2023-06-01T10:21:00Z", "pointOfSale": {"office": {"id": "CAN131", "iataNumber": "08304564", "systemCode": "1E", "agentType": "TRAVEL_AGENT"}, "login": {"numericSign": "0000", "initials": "1E", "countryCode": "CN", "cityCode": "CAN"}}, "comment": "PEKRM1E 011020 DKP237263AA 302E4C707ECB7B//PEK1EHFDYDE/CAN131/08304564/CAN/1E/T/CN"}, "lastModification": {"dateTime": "2023-06-02T07:14:00Z", "pointOfSale": {"office": {"id": "NCE1A0955", "iataNumber": "12345675", "systemCode": "1A", "agentType": "AIRLINE"}, "login": {"numericSign": "0001", "initials": "AA", "dutyCode": "SU", "countryCode": "FR", "cityCode": "NCE"}}, "comment": "1APUB/ATL-0001AA/NCE1A0955"}, "pnrProperties": ["OTHER_AIRLINE"], "queuingOffice": {"id": "PEK1EHFDYDE/CAN131/08304564/CAN/1E/T/CN"}, "nip": 2, "associatedPnrs": [{"reference": "HFDYDE", "associationType": "OTHER_AIRLINE", "creation": {"pointOfSale": {"office": {"systemCode": "1E"}}}}], "travelers": [{"type": "stakeholder", "id": "6Q5JNY-2023-06-01-PT-1", "names": [{"firstName": "PENGLONG", "lastName": "JIN", "title": "MR", "id": "MR-PENGLONG-JIN"}], "gender": "MALE", "identityDocuments": [{"type": "service", "id": "6Q5JNY-2023-06-01-OT-19", "code": "DOCS", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "status": "HK", "nip": 1, "document": {"documentType": "PASSPORT", "number": "*********", "expiryDate": "2029-03-11", "issuanceCountry": "CN", "nationality": "CN", "gender": "MALE", "name": {"fullName": "PENGLONG JIN", "firstName": "PENGLONG", "lastName": "JIN"}, "birthDate": "1993-03-08"}, "text": "P/CN/*********/CN/08MAR93/M/11MAR29/JIN/PENGLONG"}], "contacts": [{"type": "contact", "id": "6Q5JNY-2023-06-01-OT-9", "ref": "processedPnr.contacts"}], "passenger": {"uniqueIdentifier": "3105F20A001F3DCE"}}, {"type": "stakeholder", "id": "6Q5JNY-2023-06-01-PT-2", "names": [{"firstName": "KUNHUI", "lastName": "ZHANG", "title": "MR", "id": "MR-KUNHUI-ZHANG"}], "gender": "MALE", "identityDocuments": [{"type": "service", "id": "6Q5JNY-2023-06-01-OT-20", "code": "DOCS", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "status": "HK", "nip": 1, "document": {"documentType": "PASSPORT", "number": "E88049401", "expiryDate": "2026-10-25", "issuanceCountry": "CN", "nationality": "CN", "gender": "MALE", "name": {"fullName": "KUNHUI ZHANG", "firstName": "KUNHUI", "lastName": "ZHANG"}, "birthDate": "1998-10-06"}, "text": "P/CN/E88049401/CN/06OCT98/M/25OCT26/ZHANG/KUNHUI"}], "contacts": [{"type": "contact", "id": "6Q5JNY-2023-06-01-OT-8", "ref": "processedPnr.contacts"}], "passenger": {"uniqueIdentifier": "3105F20A001F3DCF"}}], "products": [{"type": "product", "subType": "SERVICE", "id": "6Q5JNY-2023-06-01-OT-10", "service": {"code": "CTCM", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "status": "HK", "nip": 1, "text": "18520035300", "isChargeable": false}, "products": [{"type": "product", "id": "6Q5JNY-2023-06-01-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "6Q5JNY-2023-06-01-ST-2", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "6Q5JNY-2023-06-01-PT-2", "ref": "processedPnr.travelers"}]}, {"type": "product", "subType": "SERVICE", "id": "6Q5JNY-2023-06-01-OT-11", "service": {"code": "CTCM", "subType": "SPECIAL_SERVICE_REQUEST", "serviceProvider": {"code": "MH"}, "status": "HK", "nip": 1, "text": "18802092975", "isChargeable": false}, "products": [{"type": "product", "id": "6Q5JNY-2023-06-01-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "6Q5JNY-2023-06-01-ST-2", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "6Q5JNY-2023-06-01-PT-1", "ref": "processedPnr.travelers"}]}, {"type": "product", "subType": "AIR", "id": "6Q5JNY-2023-06-01-ST-1", "airSegment": {"departure": {"iataCode": "CMB", "localDateTime": "2023-06-12T00:05:00", "dateTime": "2023-06-11T18:35:00Z"}, "arrival": {"iataCode": "KUL", "terminal": "1", "localDateTime": "2023-06-12T06:20:00", "dateTime": "2023-06-11T22:20:00Z"}, "aircraft": {"aircraftType": "738"}, "marketing": {"flightDesignator": {"carrierCode": "MH", "flightNumber": "178"}, "bookingClass": {"code": "V", "cabin": {"code": "Y", "bidPrice": {"amount": "608.0", "elementaryPriceType": "BID_PRICE"}}, "subClass": {"code": 0, "pointOfSale": {"office": {"systemCode": "1E"}, "login": {"countryCode": "CN"}}, "yields": [{"amount": "1484.0", "elementaryPriceType": "ADJUSTED_YIELD"}, {"amount": "429.0", "elementaryPriceType": "EFFECTIVE_YIELD"}, {"amount": "1484.0", "elementaryPriceType": "OND_YIELD_CMB_HKG"}]}, "levelOfService": "ECONOMY"}, "isOpenNumber": false, "id": "MH-178-2023-06-12-CMB-KUL"}, "creation": {"dateTime": "2023-06-01T10:18:00Z", "pointOfSale": {"office": {"id": "BJS1E2RAS", "iataNumber": "08304564", "systemCode": "1E", "agentType": "TRAVEL_AGENT"}, "login": {"countryCode": "CN", "cityCode": "CAN"}}}, "isInformational": false, "isDominantInMarriage": true, "bookingStatusCode": "HK", "id": "2023-06-12-CMB-KUL"}, "products": [{"type": "product", "id": "6Q5JNY-2023-06-01-OT-10", "ref": "processedPnr.products"}, {"type": "product", "id": "6Q5JNY-2023-06-01-OT-11", "ref": "processedPnr.products"}], "travelers": [{"type": "stakeholder", "id": "6Q5JNY-2023-06-01-PT-1", "ref": "processedPnr.travelers"}, {"type": "stakeholder", "id": "6Q5JNY-2023-06-01-PT-2", "ref": "processedPnr.travelers"}]}], "flightItinerary": [{"flights": [{"connectedFlights": {"connectionType": "MARRIAGE", "flightSegments": [{"type": "product", "id": "6Q5JNY-2023-06-01-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "6Q5JNY-2023-06-01-ST-2", "ref": "processedPnr.products"}], "connectionTimeDuration": "2H50M"}, "id": "ST-1-ST-2"}], "id": "CONNECTION-ST-1-ST-2", "type": "CONNECTION"}, {"originIataCode": "CMB", "destinationIataCode": "HKG", "yield": {"amount": "1484.0", "elementaryPriceType": "ORIGIN_AND_DESTINATION_YIELD"}, "flights": [{"connectedFlights": {"connectionType": "MARRIAGE", "flightSegments": [{"type": "product", "id": "6Q5JNY-2023-06-01-ST-1", "ref": "processedPnr.products"}, {"type": "product", "id": "6Q5JNY-2023-06-01-ST-2", "ref": "processedPnr.products"}], "connectionTimeDuration": "2H50M"}, "id": "ST-1-ST-2"}], "id": "SELL_AVAILABILITY-ST-1-ST-2", "pointOfCommencement": {"address": {"countryCode": "CN"}}, "type": "SELL_AVAILABILITY"}]}}}}