[[{"${BASE}/refdata/src/main/resources/refdata/REGULATORY_CHECK_STATUS.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/REGULATORY_CHECK_STATUS.csv"], "${BASE}/refdata/src/main/resources/refdata/PASSENGER_TYPE.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/PASSENGER_TYPE.csv"], "${BASE}/refdata/src/main/resources/refdata/AIRPORT_AREA.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/AIRPORT_AREA.csv"], "${BASE}/refdata/src/main/resources/refdata/FORM_OF_PAYMENT_TYPE.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/FORM_OF_PAYMENT_TYPE.csv"], "${BASE}/refdata/src/main/resources/refdata/FARE_CALC_PRICING_INDICATOR.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/FARE_CALC_PRICING_INDICATOR.csv"], "${BASE}/refdata/src/main/resources/refdata/TRIGGER_EVENT_NAME.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/TRIGGER_EVENT_NAME.csv"], "${BASE}/refdata/src/main/resources/refdata/BOOKING_STATUS.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/BOOKING_STATUS.csv"], "${BASE}/refdata/src/main/resources/refdata/CARD_VENDOR.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/CARD_VENDOR.csv"], "${BASE}/refdata/src/main/resources/refdata/TAX_CODE.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/TAX_CODE.csv"], "${BASE}/refdata/src/main/resources/refdata/COUPON_VOLUNTARY_INDICATOR.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/COUPON_VOLUNTARY_INDICATOR.csv"], "${BASE}/refdata/src/main/resources/refdata/REASON_FOR_ISSUANCE.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/REASON_FOR_ISSUANCE.csv"], "${BASE}/refdata/src/main/resources/refdata/COUNTRY.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/COUNTRY.csv"], "${BASE}/refdata/src/main/resources/refdata/FORM_OF_PAYMENT_APPROVAL_CODE.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/FORM_OF_PAYMENT_APPROVAL_CODE.csv"], "${BASE}/refdata/src/main/resources/refdata/SSR_CODE.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/SSR_CODE.csv"], "${BASE}/refdata/src/main/resources/refdata/IATA_SERVICE_TYPE.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/IATA_SERVICE_TYPE.csv"], "${BASE}/refdata/src/main/resources/refdata/FARE_ELEMENT.csv": ["${BASE}/refdata/target/scala-2.12/classes/refdata/FARE_ELEMENT.csv"]}, {"${BASE}/refdata/target/scala-2.12/classes/refdata/IATA_SERVICE_TYPE.csv": ["${BASE}/refdata/src/main/resources/refdata/IATA_SERVICE_TYPE.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/TRIGGER_EVENT_NAME.csv": ["${BASE}/refdata/src/main/resources/refdata/TRIGGER_EVENT_NAME.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/FARE_ELEMENT.csv": ["${BASE}/refdata/src/main/resources/refdata/FARE_ELEMENT.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/COUPON_VOLUNTARY_INDICATOR.csv": ["${BASE}/refdata/src/main/resources/refdata/COUPON_VOLUNTARY_INDICATOR.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/COUNTRY.csv": ["${BASE}/refdata/src/main/resources/refdata/COUNTRY.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/SSR_CODE.csv": ["${BASE}/refdata/src/main/resources/refdata/SSR_CODE.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/REGULATORY_CHECK_STATUS.csv": ["${BASE}/refdata/src/main/resources/refdata/REGULATORY_CHECK_STATUS.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/BOOKING_STATUS.csv": ["${BASE}/refdata/src/main/resources/refdata/BOOKING_STATUS.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/AIRPORT_AREA.csv": ["${BASE}/refdata/src/main/resources/refdata/AIRPORT_AREA.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/PASSENGER_TYPE.csv": ["${BASE}/refdata/src/main/resources/refdata/PASSENGER_TYPE.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/FORM_OF_PAYMENT_APPROVAL_CODE.csv": ["${BASE}/refdata/src/main/resources/refdata/FORM_OF_PAYMENT_APPROVAL_CODE.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/FORM_OF_PAYMENT_TYPE.csv": ["${BASE}/refdata/src/main/resources/refdata/FORM_OF_PAYMENT_TYPE.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/FARE_CALC_PRICING_INDICATOR.csv": ["${BASE}/refdata/src/main/resources/refdata/FARE_CALC_PRICING_INDICATOR.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/REASON_FOR_ISSUANCE.csv": ["${BASE}/refdata/src/main/resources/refdata/REASON_FOR_ISSUANCE.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/CARD_VENDOR.csv": ["${BASE}/refdata/src/main/resources/refdata/CARD_VENDOR.csv"], "${BASE}/refdata/target/scala-2.12/classes/refdata/TAX_CODE.csv": ["${BASE}/refdata/src/main/resources/refdata/TAX_CODE.csv"]}], {"${BASE}/refdata/src/main/resources/refdata/REGULATORY_CHECK_STATUS.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/REGULATORY_CHECK_STATUS.csv", "lastModified": 1749205498273}, "${BASE}/refdata/src/main/resources/refdata/PASSENGER_TYPE.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/PASSENGER_TYPE.csv", "lastModified": 1749205498272}, "${BASE}/refdata/src/main/resources/refdata/AIRPORT_AREA.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/AIRPORT_AREA.csv", "lastModified": 1749205498268}, "${BASE}/refdata/src/main/resources/refdata/FORM_OF_PAYMENT_TYPE.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/FORM_OF_PAYMENT_TYPE.csv", "lastModified": 1749205498272}, "${BASE}/refdata/src/main/resources/refdata/FARE_CALC_PRICING_INDICATOR.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/FARE_CALC_PRICING_INDICATOR.csv", "lastModified": 1749205498271}, "${BASE}/refdata/src/main/resources/refdata/TRIGGER_EVENT_NAME.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/TRIGGER_EVENT_NAME.csv", "lastModified": 1749205498275}, "${BASE}/refdata/src/main/resources/refdata/BOOKING_STATUS.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/BOOKING_STATUS.csv", "lastModified": 1749205498269}, "${BASE}/refdata/src/main/resources/refdata/CARD_VENDOR.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/CARD_VENDOR.csv", "lastModified": 1749205498269}, "${BASE}/refdata/src/main/resources/refdata/TAX_CODE.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/TAX_CODE.csv", "lastModified": 1749205498273}, "${BASE}/refdata/src/main/resources/refdata/COUPON_VOLUNTARY_INDICATOR.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/COUPON_VOLUNTARY_INDICATOR.csv", "lastModified": 1749205498269}, "${BASE}/refdata/src/main/resources/refdata/REASON_FOR_ISSUANCE.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/REASON_FOR_ISSUANCE.csv", "lastModified": 1749205498273}, "${BASE}/refdata/src/main/resources/refdata/COUNTRY.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/COUNTRY.csv", "lastModified": 1749205498269}, "${BASE}/refdata/src/main/resources/refdata/FORM_OF_PAYMENT_APPROVAL_CODE.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/FORM_OF_PAYMENT_APPROVAL_CODE.csv", "lastModified": 1749205498271}, "${BASE}/refdata/src/main/resources/refdata/SSR_CODE.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/SSR_CODE.csv", "lastModified": 1749205498273}, "${BASE}/refdata/src/main/resources/refdata/IATA_SERVICE_TYPE.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/IATA_SERVICE_TYPE.csv", "lastModified": 1749205498272}, "${BASE}/refdata/src/main/resources/refdata/FARE_ELEMENT.csv": {"file": "file:///C:/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/refdata/src/main/resources/refdata/FARE_ELEMENT.csv", "lastModified": 1749205498271}}]