{"mainResource": {"current": {"correlations": [{"name": "TKT-PNR", "relation": {"rel": "missing"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.AirTravelDocument", "associatedPnrs": [{"creation": {"pointOfSale": {"office": {"systemCode": "AC"}}}, "reference": "22USLC"}, {"creation": {"pointOfSale": {"office": {"systemCode": "1A"}}}, "reference": "22USLC"}], "conjunctiveDocumentNumbers": ["0142172648268", "0142172648269"], "coupons": [{"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "AUH"}, "bookingClass": {"code": "N"}, "carrierCode": "EY", "departure": {"iataCode": "YYZ", "localDate": "2022-09-22", "localDateTime": "2022-09-22T21:40:00"}, "number": "140"}, "documentNumber": "0142172648268", "fareBasis": {"fareBasisCode": "NBP00TG/AE4"}, "id": "0142172648268-2022-08-16-1", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 1, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 1, "soldSegment": {"arrival": {"iataCode": "AUH"}, "bookingClass": {"code": "N"}, "carrierCode": "EY", "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "number": "140"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "ICN"}, "bookingClass": {"code": "N"}, "carrierCode": "EY", "departure": {"iataCode": "AUH", "localDate": "2022-09-23", "localDateTime": "2022-09-23T22:15:00"}, "number": "856"}, "documentNumber": "0142172648268", "fareBasis": {"fareBasisCode": "NBP00TG/AE4"}, "id": "0142172648268-2022-08-16-2", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 2, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 2, "soldSegment": {"arrival": {"iataCode": "ICN"}, "bookingClass": {"code": "N"}, "carrierCode": "EY", "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "number": "856"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "YVR"}, "bookingClass": {"code": "T"}, "carrierCode": "AC", "departure": {"iataCode": "ICN", "localDate": "2022-09-30", "localDateTime": "2022-09-30T17:45:00"}, "number": "64"}, "documentNumber": "0142172648268", "fareBasis": {"fareBasisCode": "TAEROTG/AE1"}, "id": "0142172648268-2022-08-16-3", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 3, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 3, "soldSegment": {"arrival": {"iataCode": "YVR"}, "bookingClass": {"code": "T"}, "carrierCode": "AC", "departure": {"iataCode": "ICN", "localDateTime": "2022-09-30T17:45:00"}, "number": "64"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "YEG"}, "bookingClass": {"code": "X"}, "carrierCode": "AC", "departure": {"iataCode": "YVR", "localDate": "2022-09-30", "localDateTime": "2022-09-30T16:30:00"}, "number": "242"}, "documentNumber": "0142172648268", "fareBasis": {"fareBasisCode": "TAEROTG/AE1"}, "id": "0142172648268-2022-08-16-4", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 4, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 4, "soldSegment": {"arrival": {"iataCode": "YEG"}, "bookingClass": {"code": "X"}, "carrierCode": "AC", "departure": {"iataCode": "YVR", "localDateTime": "2022-09-30T16:30:00"}, "number": "242"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "YYZ"}, "bookingClass": {"code": "T"}, "carrierCode": "AC", "departure": {"iataCode": "YEG", "localDate": "2022-10-01", "localDateTime": "2022-10-01T00:55:00"}, "number": "176"}, "documentNumber": "0142172648269", "fareBasis": {"fareBasisCode": "TAEROTG/AE1"}, "id": "0142172648268-2022-08-16-5", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 1, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 5, "soldSegment": {"arrival": {"iataCode": "YYZ"}, "bookingClass": {"code": "T"}, "carrierCode": "AC", "departure": {"iataCode": "YEG", "localDateTime": "2022-10-01T00:55:00"}, "number": "176"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-10-01", "notValidBeforeDate": "2022-10-01"}}], "creation": {"dateTime": "2022-08-16", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA", "currencyCode": "CAD", "dutyCode": "SU", "initials": "DA", "numericSign": "9999"}, "office": {"agentType": "AIRLINE_AGENT", "iataNumber": "69991305", "id": "YVRAC08ST", "inHouseIdentification": "495561"}}}, "destinationCityIataCode": "YTO", "documentRefund": {}, "documentType": "TICKET", "endorsementFreeFlow": "BG EY", "formsOfPayment": [{"code": "MS", "displayedAmount": {"amount": "0.00", "currency": "CAD"}, "fopIndicator": "NEW", "freeText": "FFAAC400031639-PTS66100*A"}, {"code": "MS", "displayedAmount": {"amount": "0.00", "currency": "CAD"}, "fopIndicator": "NEW", "freeText": "FPRAC400031639-PRWWPY*A"}, {"authorization": {"approvalCode": "094855", "sourceOfApprovalCode": "S"}, "code": "CC", "displayedAmount": {"amount": "140.21", "currency": "CAD"}, "fopIndicator": "NEW", "freeText": "CCCAXXXXXXXXXXXX0007/0330", "paymentCard": {"expiryDate": "0330", "maskedCardNumber": "XXXXXXXXXXXX0007", "vendorCode": "CA"}}], "id": "0142172648268-2022-08-16", "issuanceType": "FIRST_ISSUE", "latestEvent": {"dateTime": "2022-09-26T18:15:02.*********", "id": "9", "pointOfSale": {"login": {}, "office": {"systemCode": "AC"}}, "triggerEventName": "142"}, "numberOfBooklets": 2, "originCityIataCode": "YTO", "price": {"currency": "CAD", "detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TotalFare"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BaseFare"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "25.91", "category": "NEW", "code": "CA", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "3.90", "category": "NEW", "code": "RC", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "30.00", "category": "NEW", "code": "SQ", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "12.20", "category": "NEW", "code": "F6", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "1.70", "category": "NEW", "code": "ZR", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "27.50", "category": "NEW", "code": "BP", "ticketReportedStatuses": ["DISPLAYED"]}], "total": "140.21", "totalTaxes": "140.21"}, "pricingConditions": {"fareCalculation": {"pricingIndicator": "5", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "isInternationalSale": true, "isNonEndorsable": false, "isNonExchangeable": false, "isNonRefundable": false, "isPenaltyRestriction": false}, "primaryDocumentNumber": "0142172648268", "traveler": {"contact": {"phone": {"number": "44221100448"}}, "name": {"firstName": "JOHN", "lastName": "BILL"}, "passengerTypeCode": "ADT"}, "validatingCarrierCode": "AC", "validatingCarrierPnr": {"reference": "22USLC"}, "version": "9", "void": {}}}, "id": "0142172648268-2022-08-16", "previous": {"correlations": [{"name": "TKT-PNR", "relation": {"rel": "missing"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.AirTravelDocument", "associatedPnrs": [{"creation": {"pointOfSale": {"office": {"systemCode": "AC"}}}, "reference": "22USLC"}, {"creation": {"pointOfSale": {"office": {"systemCode": "1A"}}}, "reference": "22USLC"}], "conjunctiveDocumentNumbers": ["0142172648268", "0142172648269"], "coupons": [{"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "AUH"}, "bookingClass": {"code": "N"}, "carrierCode": "EY", "departure": {"iataCode": "YYZ", "localDate": "2022-09-22", "localDateTime": "2022-09-22T21:40:00"}, "number": "140"}, "documentNumber": "0142172648268", "fareBasis": {"fareBasisCode": "NBP00TG/AE4"}, "id": "0142172648268-2022-08-16-1", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 1, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 1, "soldSegment": {"arrival": {"iataCode": "AUH"}, "bookingClass": {"code": "N"}, "carrierCode": "EY", "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "number": "140"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "ICN"}, "bookingClass": {"code": "N"}, "carrierCode": "EY", "departure": {"iataCode": "AUH", "localDate": "2022-09-23", "localDateTime": "2022-09-23T22:15:00"}, "number": "856"}, "documentNumber": "0142172648268", "fareBasis": {"fareBasisCode": "NBP00TG/AE4"}, "id": "0142172648268-2022-08-16-2", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 2, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 2, "soldSegment": {"arrival": {"iataCode": "ICN"}, "bookingClass": {"code": "N"}, "carrierCode": "EY", "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "number": "856"}, "status": "OPEN_FOR_USE", "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "YVR"}, "bookingClass": {"code": "T"}, "carrierCode": "AC", "departure": {"iataCode": "ICN", "localDate": "2022-09-30", "localDateTime": "2022-09-30T17:45:00"}, "number": "64"}, "documentNumber": "0142172648268", "fareBasis": {"fareBasisCode": "TAEROTG/AE1"}, "id": "0142172648268-2022-08-16-3", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 3, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 3, "soldSegment": {"arrival": {"iataCode": "YVR"}, "bookingClass": {"code": "T"}, "carrierCode": "AC", "departure": {"iataCode": "ICN", "localDateTime": "2022-09-30T17:45:00"}, "number": "64"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "YEG"}, "bookingClass": {"code": "X"}, "carrierCode": "AC", "departure": {"iataCode": "YVR", "localDate": "2022-09-30", "localDateTime": "2022-09-30T16:30:00"}, "number": "242"}, "documentNumber": "0142172648268", "fareBasis": {"fareBasisCode": "TAEROTG/AE1"}, "id": "0142172648268-2022-08-16-4", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 4, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 4, "soldSegment": {"arrival": {"iataCode": "YEG"}, "bookingClass": {"code": "X"}, "carrierCode": "AC", "departure": {"iataCode": "YVR", "localDateTime": "2022-09-30T16:30:00"}, "number": "242"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "currentSegment": {"arrival": {"iataCode": "YYZ"}, "bookingClass": {"code": "T"}, "carrierCode": "AC", "departure": {"iataCode": "YEG", "localDate": "2022-10-01", "localDateTime": "2022-10-01T00:55:00"}, "number": "176"}, "documentNumber": "0142172648269", "fareBasis": {"fareBasisCode": "TAEROTG/AE1"}, "id": "0142172648268-2022-08-16-5", "isCodeshare": false, "isFromConnection": false, "isNonExchangeable": false, "isNonRefundable": false, "number": 1, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 5, "soldSegment": {"arrival": {"iataCode": "YYZ"}, "bookingClass": {"code": "T"}, "carrierCode": "AC", "departure": {"iataCode": "YEG", "localDateTime": "2022-10-01T00:55:00"}, "number": "176"}, "status": "COUPON_NOTIFICATION", "validityDates": {"notValidAfterDate": "2022-10-01", "notValidBeforeDate": "2022-10-01"}}], "creation": {"dateTime": "2022-08-16", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA", "currencyCode": "CAD", "dutyCode": "SU", "initials": "DA", "numericSign": "9999"}, "office": {"agentType": "AIRLINE_AGENT", "iataNumber": "69991305", "id": "YVRAC08ST", "inHouseIdentification": "495561"}}}, "destinationCityIataCode": "YTO", "documentRefund": {}, "documentType": "TICKET", "endorsementFreeFlow": "BG EY", "formsOfPayment": [{"code": "MS", "displayedAmount": {"amount": "0.00", "currency": "CAD"}, "fopIndicator": "NEW", "freeText": "FFAAC400031639-PTS66100*A"}, {"code": "MS", "displayedAmount": {"amount": "0.00", "currency": "CAD"}, "fopIndicator": "NEW", "freeText": "FPRAC400031639-PRWWPY*A"}, {"authorization": {"approvalCode": "094855", "sourceOfApprovalCode": "S"}, "code": "CC", "displayedAmount": {"amount": "140.21", "currency": "CAD"}, "fopIndicator": "NEW", "freeText": "CCCAXXXXXXXXXXXX0007/0330", "paymentCard": {"expiryDate": "0330", "maskedCardNumber": "XXXXXXXXXXXX0007", "vendorCode": "CA"}}], "id": "0142172648268-2022-08-16", "issuanceType": "FIRST_ISSUE", "latestEvent": {"dateTime": "2022-09-26T18:15:02.*********", "id": "8", "pointOfSale": {"login": {}, "office": {"systemCode": "EY"}}, "triggerEventName": "142"}, "numberOfBooklets": 2, "originCityIataCode": "YTO", "price": {"currency": "CAD", "detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TotalFare"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BaseFare"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "25.91", "category": "NEW", "code": "CA", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "3.90", "category": "NEW", "code": "RC", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "30.00", "category": "NEW", "code": "SQ", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "12.20", "category": "NEW", "code": "F6", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "1.70", "category": "NEW", "code": "ZR", "ticketReportedStatuses": ["DISPLAYED"]}, {"amount": "27.50", "category": "NEW", "code": "BP", "ticketReportedStatuses": ["DISPLAYED"]}], "total": "140.21", "totalTaxes": "140.21"}, "pricingConditions": {"fareCalculation": {"pricingIndicator": "5", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "isInternationalSale": true, "isNonEndorsable": false, "isNonExchangeable": false, "isNonRefundable": false, "isPenaltyRestriction": false}, "primaryDocumentNumber": "0142172648268", "traveler": {"contact": {"phone": {"number": "44221100448"}}, "name": {"firstName": "JOHN", "lastName": "BILL"}, "passengerTypeCode": "ADT"}, "validatingCarrierCode": "AC", "validatingCarrierPnr": {"reference": "22USLC"}, "version": "8", "void": {}}}, "type": "com.amadeus.pulse.message.AirTravelDocument"}}