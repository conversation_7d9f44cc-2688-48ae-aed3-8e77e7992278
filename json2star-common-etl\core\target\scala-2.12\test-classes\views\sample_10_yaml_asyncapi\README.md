Integration tests to verify the metadata generation using the YAML lookup with AsyncAPI file and mapping file
These tests are based on the following input files:

- yaml: order-asyncapi.yaml - version 0.2.1
- mapping:
    - order_sample_1.conf: a sample mapping that models order
    - order_sample_2.conf: a sample mapping that models orderAgreement
    - order_sample_3.conf: a sample mapping that models orderOffer

Source of mapping
https://rndwww.nce.amadeus.net/git/projects/DDL/repos/json2star-nevio-etl/browse?at=refs%2Ftags%2Fv2.0.106

The output is

- lookup_results.csv: results of the lookup at table, column, json path level
- lookup_results_not_found.csv: results of distinct json paths that are not found in the AsyncAPI YAML file
- lookup_stats.csv: statistics of the lookup

# Analysis on lookup_results_not_found.csv

The lookup_results_not_found.csv contains the json paths that are not found in the AsyncAPI YAML file.
This can be caused by the following reasons:

- Empty --> the json path is found, but the corresponding Yaml Schema has no "description/example" field
- Missing --> the json path is not found, this can be caused by path mismatch between Json/Yaml

For example:

- `$.attachments[*].payload.offerData.offer.lifecycle.version` is Empty, because it is found, but the property `version`
  has no description/example in YAML
- `$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].arcNumber` is `Missing` in the YAML file (
  missing `offices.arcNumber` in YAML)
- `$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr`
  is `Missing` in the YAML file, `externalRecordReferences` is json-pointer (not supported)
  and `Order_ExternalRecordReference_Pnr.v1` schema is missing in the YAML file)

