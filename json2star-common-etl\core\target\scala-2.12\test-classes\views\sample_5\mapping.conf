{"defaultComment": "A coment here", "partition-spec": {"key": "PNR_CREATION_DATE", "column-name": "PART_PNR_CREATION_MONTH", "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"}, "tables": [{"name": "ASSO_COUPON_SERVICE_DELIVERY_HISTO", "source-correlation": {"domain-a": {"name": "DCSPAX", "table-name": "INTERNAL_ASSO_COUPON_SERVICE_DELIVERY_HISTO", "version-column-name": "VERSION"}, "domain-b": "PNR", "target": {"columns": [{"name": "COUPON_ID", "fk": [{"schema": "TKT", "table": "FACT_COUPON_HISTO", "column": "COUPON_ID"}], "is-mandatory": true, "column-type": "strColumn", "sources": {}, "meta": {"description": {"value": "CUSTOM DESCRIPTION FOR COUPON_ID", "rule": "replace"}, "example": {"value": "CUSTOM EXAMPLE FOR COUPON_ID", "rule": "concat"}, "pii-type": {"value": "CUSTOM PII TYPE FOR COUPON_ID", "rule": "concat"}, "gdpr-zone": "red"}}, {"name": "SERVICE_DELIVERY_ID", "fk": [{"schema": "DCSPAX", "table": "FACT_SERVICE_DELIVERY_HISTO", "column": "SERVICE_DELIVERY_ID"}], "is-mandatory": true, "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "red"}}, {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "red"}}, {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "red"}}, {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "red"}}, {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}}, {"name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKT", "table": "FACT_COUPON_HISTO", "column": "VERSION"}], "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}}, {"name": "VERSION_PASSENGER", "fk": [{"schema": "DCSPAX", "table": "FACT_SERVICE_DELIVERY_HISTO", "column": "VERSION"}], "column-type": "strColumn", "sources": {}, "meta": {"gdpr-zone": "green"}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}, "meta": {"gdpr-zone": "green"}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}, "meta": {"gdpr-zone": "green"}}], "domain-a-key": "SERVICE_DELIVERY_ID", "domain-b-key": "COUPON_ID", "domain-a-version": "VERSION_PASSENGER", "domain-b-version": "VERSION_TRAVEL_DOCUMENT", "start-date": "DATE_BEGIN", "end-date": "DATE_END", "is-last": "IS_LAST_VERSION", "if-dupe-take-highest": ["DATE_BEGIN"]}}}, {"name": "ASSO_COUPON_SERVICE_DELIVERY", "latest": {"histo-table-name": "ASSO_COUPON_SERVICE_DELIVERY_HISTO"}}]}