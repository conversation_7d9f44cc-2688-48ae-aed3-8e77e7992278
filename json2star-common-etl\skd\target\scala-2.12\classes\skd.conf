//Version Field cast to bigint
versionExprVal: "bigint({0})"
//Version Field type
versionTypeVal: "longColumn"
//Date_Id hashed FK
dateExprVal: "hashS(date_format({0},\"yyyy-MM-dd\"))"
// in case of fk_id created from multiple fields, check if end point Id is not null - ex : ARRIVAL_GATES_ID
createIdCheckEndNotNull: "if(element_at(split({0},'-'),array_size(split({0},'-'))) == '_', NULL,hashS({0}) )"
createZorderKey: "substring({0}, -10)|| '-' || {0}"
sanitizeIdentifier: "regexp_replace({0}, '-_|T..:..:..Z', '')"

//if contains an ARN with source=MVT exists, then select this number (hyphenate if multiple distinct),
// else select number from source=NULL (hyphenate if multiple distinct)
// ex:  [{"number=1234"},{"number=5678,source=MVT"}] -> "5678"
//      [{"number=1234"}] -> "1234"
//      [{"number=1234"},{"number=5678,source=MVT"},{"number=5678,source=MVT"},{"number=9991,source=MVT"}] -> "5678-9991"
//      [{"number=1234"},{"number=1255"}] -> "1234-1255"
getARNs: "if(contains({0}, 'number='),concat_ws('-',array_distinct(regexp_extract_all({0},'number=(.+?(?=(,|})))',1))),NULL)"
ARNSelector: "if(contains({0}, 'source=MVT'),concat_ws('-',array_distinct(transform(split({0}, \"}-\"), x -> if(contains(x, 'source=MVT'),regexp_extract(x,'number=(.+?(?=(,|})))',1),NULL)))),"${getARNs}")"
ARNSource: "if(contains({0}, 'source='), regexp_extract({0},'source=(.+?(?=(,|})))',1), NULL)"

"defaultComment": "SKD mapping file",

"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.mainResource.current.image": "ScheduleFlightDataPush.scheduleProcessedFlight"
    }
  }
},

"tables": [
  {
    "name": "FACT_FLIGHT_DATE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_DATE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_DATE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Flight Date",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains the various flight dates (defined by carrier, flight number with suffix and departure date) from the airline's flight schedule.",
        "granularity": "1 flight occurrence per date"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        //@TODO: define this column with "blocks" syntax after cartesian product is merged
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "FLIGHT_DATE_ID", "is-mandatory": "true", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base":"$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "is-mandatory": "true", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.id"}]}
          , "meta": {"description": {"value": "Functional key: carrier-flightNum-suffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.flightDesignator.carrierCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_NUMBER", "column-type": "intColumn", "sources": {"blocks": [{"base":"$.flightDesignator.flightNumber"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.flightDesignator.operationalSuffix"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_FLIGHT_DEPARTURE_DATE_LOCAL", "column-type": "dateColumn", "sources": {"blocks": [{"base":"$.scheduledDepartureDate"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DAY_OF_OPERATION", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.dayOfOperation"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CODESHARE_ROLE", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.codeshare"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_CHARACTERISTICS", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.characteristics[*]"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"description": {"value": "The list of flight characteristics which apply to this flight date.", "rule": "replace"}, "example": {"value": "PSEUDO, HIDDEN, INTERNATIONAL, DOMESTIC, ADHOC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_CANCELLED", "column-type": "strColumn", "sources": {"blocks": [{"base":"$.isCancelled"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "INTERNAL_CORR_ID", "is-mandatory": "true", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base":"$.flightDesignator.carrierCode"}, {"base":"$.flightDesignator.flightNumber"}, {"base":"$.flightDesignator.operationalSuffix"}, {"base":"$.segments[0].scheduledDepartureDateTimeUtc"}]}, "expr": "hashM("${sanitizeIdentifier}")"
          , "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SKD_FLIGHT_DATE_ID", "is-mandatory": "true", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base":"$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base":"$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "is-mandatory": "true", "column-type": "timestampColumn", "sources": {"blocks": [{"base":"$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_VERSION"
        }
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains the different flight segments (commercial view) which are marketed for a given flight date. For multi-leg flights there can be multiple segments.",
        "granularity": "1 flight segment for 1 flight date"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}]
        }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: (flight date key)-depDate-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_FLIGHT_POINT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.boardFlightPointId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.boardPointIataCode"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_TERMINALS", "column-type": "strColumn",
          "sources": {"blocks": [{"base": "$.flightPoints[?(@.id=='{DEPARTURE_FLIGHT_POINT_IDENTIFIER}')].departure.terminals[*].code"}]},
          "has-variable": true,
          "meta": {"description": {"value": "The terminal (or list of, concatenated by -) of the segment departure", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_GATES", "column-type": "strColumn",
          "sources": {"blocks": [{"base": "$.flightPoints[?(@.id=='{DEPARTURE_FLIGHT_POINT_IDENTIFIER}')].departure.gates[*].mainGate"}]},
          "has-variable": true,
          "meta": {"description": {"value": "The gate (or list of, concatenated by -) of the segment departure", "rule": "replace"}, "example": {"value": "A38", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_SEGMENT_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.scheduledDepartureDateTime"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": ${dateExprVal}, "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_SEGMENT_DEPARTURE_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.scheduledDepartureDateTimeUtc"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": ${dateExprVal}, "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"description": {"value": "The scheduled segment arrival date time in UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_FLIGHT_POINT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.offFlightPointId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.offPointIataCode"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_TERMINALS", "column-type": "strColumn",
          "sources": {"blocks": [{"base": "$.flightPoints[?(@.id=='{ARRIVAL_FLIGHT_POINT_IDENTIFIER}')].arrival.terminals[*].code"}]},
          "has-variable": true,
          "meta": {"description": {"value": "The terminal (or list of, concatenated by -) of the segment arrival", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_SEGMENT_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.scheduledArrivalDateTime"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": ${dateExprVal}, "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_SEGMENT_ARRIVAL_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"seg": "$.scheduledArrivalDateTimeUtc"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": ${dateExprVal}, "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"description": {"value": "The scheduled segment arrival date time in UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "NUMBER_OF_LEGS", "column-type": "intColumn", "sources": {"blocks": [{"seg": "$.numberOfLegs"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ALLIANCE", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.partnerships.alliance"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]}
          , "meta": {"description": {"value": "The airline alliance to which the codeshare segment belongs to.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRAFFIC_RESTRICTIONS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.trafficRestrictions[*]"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CABINS", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.bookingClassList[*].cabin"}]}, "expr": "array_join(array_distinct(split({0},'-')),'-')", "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS(array_join(array_distinct(split({0},'-')),'-'))"}
          , "meta": {"description": {"value": "The cabins available on the flight segment", "rule": "replace"}, "example": {"value": "F-J-Y", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "BOOKING_CLASSES", "column-type": "strColumn", "sources": {"blocks": [{"seg": "$.bookingClassList[*].code"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"description": {"value": "The booking classes available on the flight segment", "rule": "replace"}, "example": {"value": "J-C-D-W-Y-B-H-K-M-Q-L-V", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_DATE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}
          , "meta": {"description": {"value": "Foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_FLIGHT_POINT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.boardFlightPointId"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_POINT_HISTO", "column": "FLIGHT_POINT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_TERMINALS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.boardPointIataCode"}, {"base": "$.flightPoints[?(@.id=='{DEPARTURE_FLIGHT_POINT_IDENTIFIER}')].departure.terminals[*].code"}]}, "expr": ${createIdCheckEndNotNull}, "has-variable": true
          , "fk": [{"table": "DIM_AIRPORT_TERMINALS", "column": "AIRPORT_TERMINALS_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_GATES_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.boardPointIataCode"}, {"base": "$.flightPoints[?(@.id=='{DEPARTURE_FLIGHT_POINT_IDENTIFIER}')].departure.gates[*].mainGate"}]}, "expr": ${createIdCheckEndNotNull}, "has-variable": true
          , "fk": [{"table": "DIM_GATES", "column": "GATES_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_FLIGHT_POINT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.offFlightPointId"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_POINT_HISTO", "column": "FLIGHT_POINT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_TERMINALS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"seg": "$.offPointIataCode"}, {"base": "$.flightPoints[?(@.id=='{ARRIVAL_FLIGHT_POINT_IDENTIFIER}')].arrival.terminals[*].code"}]}, "expr": ${createIdCheckEndNotNull}, "has-variable": true
          , "fk": [{"table": "DIM_AIRPORT_TERMINALS", "column": "AIRPORT_TERMINALS_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.flightDesignator.carrierCode"}, {"base": "$.flightDesignator.flightNumber"}, {"base": "$.flightDesignator.operationalSuffix"}, {"seg": "$.scheduledDepartureDateTimeUtc"}, {"seg": "$.scheduledDepartureDateTimeUtc"}, {"seg": "$.boardPointIataCode"}, {"seg": "$.offPointIataCode"}]}, "expr": "hashM("${sanitizeIdentifier}")"
          , "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SKD_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "is-mandatory": "true", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CODESHARE_FLIGHT_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment",
    "mapping": {
      "description": {
        "description": "Contains the corresponding codeshare flights (either operating, or marketing) for a given flight segment, depending if the flight segment itself is marketing or operating).",
        "granularity": "1 codeshare flight for 1 flight segment"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "mark", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}, {"mkt": "$.partnerships.marketingFlights[*]"},{"shouldNorBeEmpty" : "$.id"}]}},
        {"name": "oper", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}, {"ope": "$.partnerships.operatingFlight"},{"shouldNorBeEmpty" : "$.id"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"mkt": "$.id"}]},
              {"rs-name": "oper", "blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"ope": "$.id"}]}
            ]
          },
          "expr":  "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"mkt": "$.id"}]},
              {"rs-name": "oper", "blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"ope": "$.id"}]}
            ]
          },
          "meta": {"description": {"value": "Functional key: (flight segment key)-partnerCarrier-partnerFlightNum-partnerSuffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG-8X-5900-D-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "PARTNER_CARRIER", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "blocks": [{"mkt": "$.flightDesignator.carrierCode"}]},
              {"rs-name": "oper", "blocks": [{"ope": "$.flightDesignator.carrierCode"}]}
            ]
          },
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PARTNER_FLIGHT_NUMBER", "column-type": "intColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "blocks": [{"mkt": "$.flightDesignator.flightNumber"}]},
              {"rs-name": "oper", "blocks": [{"ope": "$.flightDesignator.flightNumber"}]}
            ]
          },
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PARTNER_OPERATIONAL_SUFFIX", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "blocks": [{"mkt": "$.flightDesignator.operationalSuffix"}]},
              {"rs-name": "oper", "blocks": [{"ope": "$.flightDesignator.operationalSuffix"}]}
            ]
          },
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PARTNER_CODESHARE_ROLE", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "literal": "MARKETING"},
              {"rs-name": "oper", "literal": "OPERATING"}
            ]
          },
          "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CODESHARE_ROLE", "column": "CODESHARE_ROLE_ID"}]},
          "meta": {"description": {"value": "The codeshare role of the partner's codeshare flight", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "FLIGHT_DATE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}
          , "meta": {"description": {"value": "Foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "blocks": [{"base": "$.flightDesignator.carrierCode"}, {"base": "$.flightDesignator.flightNumber"}, {"base": "$.flightDesignator.operationalSuffix"}, {"seg": "$.scheduledDepartureDateTimeUtc"}, {"seg": "$.scheduledDepartureDateTimeUtc"}, {"seg": "$.boardPointIataCode"}, {"seg": "$.offPointIataCode"}, {"mkt": "$.flightDesignator.carrierCode"}, {"mkt": "$.flightDesignator.flightNumber"}, {"mkt": "$.flightDesignator.suffix"}, {"seg": "$.scheduledDepartureDateTimeUtc"}]},
              {"rs-name": "oper", "blocks": [{"base": "$.flightDesignator.carrierCode"}, {"base": "$.flightDesignator.flightNumber"}, {"base": "$.flightDesignator.operationalSuffix"}, {"seg": "$.scheduledDepartureDateTimeUtc"}, {"seg": "$.scheduledDepartureDateTimeUtc"}, {"seg": "$.boardPointIataCode"}, {"seg": "$.offPointIataCode"}, {"ope": "$.flightDesignator.carrierCode"}, {"ope": "$.flightDesignator.flightNumber"}, {"ope": "$.flightDesignator.suffix"}, {"seg": "$.scheduledDepartureDateTimeUtc"}]}
            ]
          },
          "expr": "hashM("${sanitizeIdentifier}")"
          "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"mkt": "$.id"}]},
              {"rs-name": "oper", "blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"ope": "$.id"}]}
            ]
          },
          "expr":  "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr" : ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "is-mandatory": "true", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"base": "$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_LEG",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_LEG_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_LEG_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains the different flight legs (operational view) which are flown for a given flight date.",
        "granularity": "1 flight leg for 1 flight date"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}]
        }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: (flight date key)-board-off", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-LHR-CDG", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_LEG_UTC_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.flightDesignator.carrierCode"}, {"base": "$.flightDesignator.flightNumber"}, {"base": "$.flightDesignator.operationalSuffix"}, {"leg": "$.scheduledDepartureDateTimeUtc"}, {"leg": "$.id"}]}, "expr": "hashM("${sanitizeIdentifier}")"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_FLIGHT_POINT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.boardFlightPointId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.boardPointIataCode"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_TERMINALS", "column-type": "strColumn",
          "sources": {"blocks": [{"base": "$.flightPoints[?(@.id=='{DEPARTURE_FLIGHT_POINT_IDENTIFIER}')].departure.terminals[*].code"}]},
          "has-variable": true,
          "meta": {"description": {"value": "The terminal (or list of, concatenated by -) of the leg departure", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_GATES", "column-type": "strColumn",
          "sources": {"blocks": [{"base": "$.flightPoints[?(@.id=='{DEPARTURE_FLIGHT_POINT_IDENTIFIER}')].departure.gates[*].mainGate"}]},
          "has-variable": true,
          "meta": {"description": {"value": "The gate (or list of, concatenated by -) of the leg departure", "rule": "replace"}, "example": {"value": "A38", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_LEG_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"leg": "$.scheduledDepartureDateTime"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": ${dateExprVal}, "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_LEG_DEPARTURE_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"leg": "$.scheduledDepartureDateTimeUtc"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": ${dateExprVal}, "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"description": {"value": "The scheduled leg departure time in UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_FLIGHT_POINT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.offFlightPointId"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.offPointIataCode"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_TERMINALS", "column-type": "strColumn",
          "sources": {"blocks": [{"base": "$.flightPoints[?(@.id=='{ARRIVAL_FLIGHT_POINT_IDENTIFIER}')].arrival.terminals[*].code"}]},
          "has-variable": true,
          "meta": {"description": {"value": "The terminal (or list of, concatenated by -) of the leg arrival", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_LEG_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"leg": "$.scheduledArrivalDateTime"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": ${dateExprVal}, "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "SCHEDULED_LEG_ARRIVAL_DATETIME_UTC", "column-type": "timestampColumn", "sources": {"blocks": [{"leg": "$.scheduledArrivalDateTimeUtc"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": ${dateExprVal}, "fk": [{"table": "DIM_DATE", "column": "DATE_ID"}]}
          , "meta": {"description": {"value": "The scheduled leg arrival time in UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FRANCHISEE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.aircraftEquipment.realAircraftOwner"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRLINE", "column": "AIRLINE_ID"}]}
          , "meta": {"description": {"value": "The IATA code of the franchisee carrier performing the flight leg", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SERVICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.serviceType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "GENERAL_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.legDCSStatuses.generalStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ACCEPTANCE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.legDCSStatuses.acceptanceStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "LOAD_CONTROL_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.legDCSStatuses.loadControlStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "BOARDING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.legDCSStatuses.boardingStatus"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "ROUTING_ORDER", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.routingOrder"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "AIRCRAFT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.aircraftEquipment.aircraftType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "AIRCRAFT_REGISTRATION_NUMBERS", "column-type": "strColumn",
          "sources": {"blocks": [{"leg": "$.aircraftEquipment.registrationNumbers"}]}, "expr": ${ARNSelector},
          "meta": {"description": {"value": "The aircraft registration number (or list of, concatenated by -)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "AIRCRAFT_REGISTRATION_SOURCES", "column-type": "strColumn",
          "sources": {"blocks": [{"leg": "$.aircraftEquipment.registrationNumbers"}]}, "expr": ${ARNSource},
          "meta": {"description": {"value": "The aircraft registration number (or list of, concatenated by -)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_DATE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}
          , "meta": {"description": {"value": "Foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_FLIGHT_POINT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.boardFlightPointId"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_POINT_HISTO", "column": "FLIGHT_POINT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_TERMINALS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"leg": "$.boardPointIataCode"}, {"base": "$.flightPoints[?(@.id=='{DEPARTURE_FLIGHT_POINT_IDENTIFIER}')].departure.terminals[*].code"}]}, "expr": ${createIdCheckEndNotNull}, "has-variable": true
          , "fk": [{"table": "DIM_AIRPORT_TERMINALS", "column": "AIRPORT_TERMINALS_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_GATES_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"leg": "$.boardPointIataCode"}, {"base": "$.flightPoints[?(@.id=='{DEPARTURE_FLIGHT_POINT_IDENTIFIER}')].departure.gates[*].mainGate"}]}, "expr": ${createIdCheckEndNotNull}, "has-variable": true
          , "fk": [{"table": "DIM_GATES", "column": "GATES_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_FLIGHT_POINT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.offFlightPointId"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_POINT_HISTO", "column": "FLIGHT_POINT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_TERMINALS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"leg": "$.offPointIataCode"}, {"base": "$.flightPoints[?(@.id=='{ARRIVAL_FLIGHT_POINT_IDENTIFIER}')].arrival.terminals[*].code"}]}, "expr": ${createIdCheckEndNotNull}, "has-variable": true
          , "fk": [{"table": "DIM_AIRPORT_TERMINALS", "column": "AIRPORT_TERMINALS_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "SKD_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "is-mandatory": "true", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_AIRCRAFT_CONFIGURATION_CABIN",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_AIRCRAFT_CONFIGURATION_CABIN_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_AIRCRAFT_CONFIGURATION_CABIN_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg",
    "mapping": {
      "description": {
        "description": "Contains the aircraft configurations and cabin capacities for the different perspectives: fitted/saleable, scheduled/operational.",
        "granularity": "1 cabin for 1 flight leg"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIRCRAFT_CONFIGURATION_CABIN_ID", "CONFIGURATION_TYPE", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "opeFit", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"opeFit": "$.aircraftEquipment.operationalFittedConfiguration"}, {"cont": "$.configurationContents[*]"}]}},
        {"name": "opeSal", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"opeSal": "$.aircraftEquipment.operationalSaleableConfiguration"}, {"cont": "$.configurationContents[*]"}]}},
        {"name": "schFit", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"schFit": "$.aircraftEquipment.scheduledFittedConfiguration"}, {"cont": "$.configurationContents[*]"}]}},
        {"name": "schSal", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"schSal": "$.aircraftEquipment.scheduledSaleableConfiguration"}, {"cont": "$.configurationContents[*]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "AIRCRAFT_CONFIGURATION_CABIN_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.id"}, {"cont": "$.cabin"}]},
          "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.id"},{"cont": "$.cabin"}]}
          , "meta": {"description": {"value": "Functional key: (flight leg key)-cabin", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-LHR-CDG-OPERATIONAL_FITTED-F", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "AIRCRAFT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"leg": "$.aircraftEquipment.aircraftType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "TOTAL_SCHEDULED_FITTED_CAPACITY", "column-type": "intColumn", "sources": {"blocks": [{"leg": "$.aircraftEquipment.totalScheduledFittedCapacity"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CONFIGURATION_TYPE", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "opeFit", "literal": "OPERATIONAL_FITTED"},
              {"rs-name": "opeSal", "literal": "OPERATIONAL_SALEABLE"},
              {"rs-name": "schFit", "literal": "SCHEDULED_FITTED"},
              {"rs-name": "schSal", "literal": "SCHEDULED_SALEABLE"}
            ]
          },
          "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRCRAFT_CONFIGURATION_TYPE", "column": "AIRCRAFT_CONFIGURATION_TYPE_ID"}]},
          "meta": {"description": {"value": "The aircraft configuration type, a combination between fitted/saleable and scheduled/operational", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "CONFIGURATION_CODE", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "opeFit", "blocks": [{"opeFit": "$.code"}]},
              {"rs-name": "opeSal", "blocks": [{"opeSal": "$.code"}]},
              {"rs-name": "schFit", "blocks": [{"schFit": "$.code"}]},
              {"rs-name": "schSal", "blocks": [{"schSal": "$.code"}]}
            ]
          },
          "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_AIRCRAFT_CONFIGURATION_CODE", "column": "AIRCRAFT_CONFIGURATION_CODE_ID"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"cont": "$.cabin"}]}
          , "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABINS", "column": "CABINS_ID"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CAPACITY", "column-type": "intColumn", "sources": {"blocks": [{"cont": "$.capacity"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_LEG_HISTO", "column": "FLIGHT_LEG_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "is-mandatory": "true", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_POINT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_POINT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_POINT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Flight Point",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Contains the different airports involved in the flight date (segments or legs) as board and/or off points, with the corresponding flight timings.",
        "granularity": "1 flight point for 1 flight date"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_POINT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"flightpoint": "$.flightPoints[*]"}]}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "FLIGHT_POINT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightpoint": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"flightpoint": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: (flight date key)-airport_order", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-LHR_1", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"flightpoint": "$.iataCode"}]}, "create-fk": {"name": "AIRPORT_ID", "column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CITY", "column-type": "strColumn", "sources": {"blocks": [{"flightpoint": "$.city"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"flightpoint": "$.country"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "REGION", "column-type": "strColumn", "sources": {"blocks": [{"flightpoint": "$.region"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "CONTINENT", "column-type": "strColumn", "sources": {"blocks": [{"flightpoint": "$.continent"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_TERMINALS", "column-type": "strColumn", "sources": {"blocks": [{"flightpoint": "$.departure.terminals[*].code"}]}
          , "meta": {"description": {"value": "The terminal (or list of, concatenated by -) of the flight point", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_GATES", "column-type": "strColumn", "sources": {"blocks": [{"flightpoint": "$.departure.gates[*].mainGate"}]}
          , "meta": {"description": {"value": "The gate (or list of, concatenated by -) involved on departure at this flight point.", "example": {"value": "A38", "rule": "replace"}, "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_TERMINALS", "column-type": "strColumn", "sources": {"blocks": [{"flightpoint": "$.arrival.terminals[*].code"}]}
          , "meta": {"description": {"value": "The terminal (or list of, concatenated by -) of the flight point", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_TERMINALS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"flightpoint": "$.iataCode"}, {"flightpoint": "$.departure.terminals[*].code"}]}, "expr": ${createIdCheckEndNotNull}
          , "fk": [{"table": "DIM_AIRPORT_TERMINALS", "column": "AIRPORT_TERMINALS_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DEPARTURE_GATES_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"flightpoint": "$.iataCode"}, {"flightpoint": "$.departure.gates[*].mainGate"}]}, "expr": ${createIdCheckEndNotNull}
          , "fk": [{"table": "DIM_GATES", "column": "GATES_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "ARRIVAL_TERMINALS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"flightpoint": "$.iataCode"}, {"flightpoint": "$.arrival.terminals[*].code"}]}, "expr": ${createIdCheckEndNotNull}
          , "fk": [{"table": "DIM_AIRPORT_TERMINALS", "column": "AIRPORT_TERMINALS_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "is-mandatory": "true", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_POINT_TIMING",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_POINT_TIMING_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_POINT_TIMING_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Flight Point",
    "mapping": {
      "description": {
        "description": "Contains the list of qualified flight timings (departure or arrival) on a given flight point, either in local time or in UTC",
        "granularity": "1 flight timing in 1 flight point"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_POINT_TIMING_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "arrival", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"flightpoint": "$.flightPoints[*]"}, {"timing": "$.arrival.timings[?(!(@.qualifier=~/.*ZULU/))]"}]}},
        {"name": "arrzulu", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"flightpoint": "$.flightPoints[*]"}, {"timing": "$.arrival.timings[?(@.qualifier=~/.*ZULU/)]"}]}},
        {"name": "departure", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"flightpoint": "$.flightPoints[*]"}, {"timing": "$.departure.timings[?(!(@.qualifier=~/.*ZULU/))]"}]}},
        {"name": "depzulu", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"flightpoint": "$.flightPoints[*]"}, {"timing": "$.departure.timings[?(@.qualifier=~/.*ZULU/)]"}]}}
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "FLIGHT_POINT_TIMING_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"timing": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"timing": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: (flight point key)-timingQualifier", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-LHR_1-STD", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TIMING_TYPE", "column-type": "strColumn", "sources": {
          "root-specific": [
            {"rs-name": "arrival", "literal": "ARRIVAL"},
            {"rs-name": "arrzulu", "literal": "ARRIVAL"},
            {"rs-name": "departure", "literal": "DEPARTURE"},
            {"rs-name": "depzulu", "literal": "DEPARTURE"}
          ]
        },
          "meta": {"description": {"value": "Indicates if the timing corresponds to departure or arrival", "rule": "replace"}, "example": {"value": "DEPARTURE, ARRIVAL", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "QUALIFIER", "column-type": "strColumn", "sources": {"blocks": [{"timing": "$.qualifier"}]}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DATETIME_LOCAL", "column-type": "timestampColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "arrival", "blocks": [{"timing": "$.value"}]},
              {"rs-name": "departure", "blocks": [{"timing": "$.value"}]}
            ]
          },
          "meta": {"description": {"value": "The flight timing value in local time", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATETIME_UTC", "column-type": "timestampColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "arrzulu", "blocks": [{"timing": "$.value"}]},
              {"rs-name": "depzulu", "blocks": [{"timing": "$.value"}]}
            ]
          },
          "meta": {"description": {"value": "The flight timing value in UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FLIGHT_POINT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"flightpoint": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_POINT_HISTO", "column": "FLIGHT_POINT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr" : ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"base": "$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SYSTEM_USER_ACTION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SYSTEM_USER_ACTION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SYSTEM_USER_ACTION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "System User Action",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {
        "description": "Captures information on the trigger event of the flight date update.",
        "granularity": "1 system action for 1 flight date"
      },
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SYSTEM_USER_ACTION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [{"base": "$.mainResource.current.image"}]
        }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "SYSTEM_USER_ACTION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}
          , "meta": {"description": {"value": "Functional key: (flight date key)", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.event.qualifiers[*]"}]}, "create-fk": {"name": "TRIGGER_EVENT_ID", "column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"description": {"value": "The name of the triggering event of the flight update", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRIGGER_EVENT_SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.event.source"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})"}
          , "meta": {"description": {"value": "The source of the triggering event of the flight update", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "is-mandatory": "true", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "DIM_FLIGHT_CHARACTERISTICS",
    "mapping": {
      "description": {"description": "Lists the flight characteristics used on flight dates.", "granularity": "1 combination of flight characteristics (concatenated if multiple values)"},
      "merge": {
        "key-columns": ["FLIGHT_CHARACTERISTICS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}]}
      ],
      "columns": [
        {
          "name": "FLIGHT_CHARACTERISTICS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.characteristics[*]"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of the flight characteristics", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_CHARACTERISTICS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.characteristics[*]"}]}
          , "meta": {"description": {"value": "A combination of flight characteristics", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRLINE",
    "mapping": {
      "description": {"description": "Lists the airlines used as marketing/operating/franchisee carriers on flight segments or flight legs.", "granularity": "1 airline"},
      "merge": {
        "key-columns": ["AIRLINE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"car": "$.mainResource.current.image.flightDesignator.carrierCode"}]},
        {"blocks": [{"car": "$.mainResource.current.image.segments[*].partnerships.marketingFlights[*].flightDesignator.carrierCode"}]},
        {"blocks": [{"car": "$.mainResource.current.image.segments[*].partnerships.operatingFlight.flightDesignator.carrierCode"}]},
        {"blocks": [{"car": "$.mainResource.current.image.legs[*].aircraftEquipment.realAircraftOwner"}]}
      ],
      "columns": [
        {
          "name": "AIRLINE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"car": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of airline IATA code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "AIRLINE_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"car": "$.value"}]}},
        {
          "name": "AIRLINE_NAME", "column-type": "strColumn", "sources": {}
          , "meta": {"description": {"value": "The name of the airline", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_CODESHARE_ROLE",
    "mapping": {
      "description": {"description": "Lists the codeshare roles for flight dates and codeshare flight segments", "granularity": "1 codeshare role"},
      "merge": {
        "key-columns": ["CODESHARE_ROLE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"cs": "$.mainResource.current.image.codeshare"}]}
      ],
      "columns": [
        {
          "name": "CODESHARE_ROLE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"cs": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of codeshare role", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "CODESHARE_ROLE", "column-type": "strColumn", "sources": {"blocks": [{"cs": "$.value"}]}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_DATE",
    "mapping": {
      "description": {"description": "Lists all calendar days (and corresponding coarse-grained date elements such as month, quarter, year) for which the database contains at least one flight occurrence.", "granularity": "1 calendar day"},
      "merge": {
        "key-columns": ["DATE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"date": "$.mainResource.current.image.scheduledDepartureDate"}]},
        {"blocks": [{"date": "$.mainResource.current.image.segments[*].scheduledDepartureDateTime"}]},
        {"blocks": [{"date": "$.mainResource.current.image.segments[*].scheduledArrivalDateTime"}]},
        {"blocks": [{"date": "$.mainResource.current.image.legs[*].scheduledDepartureDateTime"}]},
        {"blocks": [{"date": "$.mainResource.current.image.legs[*].scheduledArrivalDateTime"}]}
      ],
      "columns": [
        {
          "name": "DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"date": "$.value"}]}, "expr": ${dateExprVal}
          , "meta": {"description": {"value": "Hash of calendar date", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DAY_OF_MONTH", "column-type": "intColumn", "sources": {"blocks": [{"date": "$.value"}]}, "expr": "date_format({0},\"d\")"
          , "meta": {"description": {"value": "Indicates the day within the month of the calendar date", "rule": "replace"}, "example": {"value": "31", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "MONTH", "column-type": "intColumn", "sources": {"blocks": [{"date": "$.value"}]}, "expr": "date_format({0},\"M\")"
          , "meta": {"description": {"value": "Indicates the month of the calendar date", "rule": "replace"}, "example": {"value": "12", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "QUARTER", "column-type": "intColumn", "sources": {"blocks": [{"date": "$.value"}]}, "expr": "date_format({0},\"Q\")"
          , "meta": {"description": {"value": "Indicates the quarter of the calendar date", "rule": "replace"}, "example": {"value": "4", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "YEAR", "column-type": "intColumn", "sources": {"blocks": [{"date": "$.value"}]}, "expr": "date_format({0},\"y\")"
          , "meta": {"description": {"value": "Indicates the year of the calendar date", "rule": "replace"}, "example": {"value": "2022", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "WEEKDAY", "column-type": "strColumn", "sources": {"blocks": [{"date": "$.value"}]}, "expr": "date_format({0},\"E\")"
          , "meta": {"description": {"value": "Indicates the day of the week of the calendar date", "rule": "replace"}, "example": {"value": "Mon", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT",
    "mapping": {
      "description": {"description": "Lists the airports used in departure/arrival points of flight segments or legs", "granularity": "1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"point": "$.mainResource.current.image.flightPoints[*]"}]},
      ],
      "columns": [
        {
          "name": "AIRPORT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"point": "$.iataCode"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of airport IATA code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"point": "$.iataCode"}]}},
        {"name": "CITY", "column-type": "strColumn", "sources": {"blocks": [{"point": "$.city"}]}},
        {"name": "COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"point": "$.country"}]}},
        {"name": "REGION", "column-type": "strColumn", "sources": {"blocks": [{"point": "$.region"}]}},
        {"name": "CONTINENT", "column-type": "strColumn", "sources": {"blocks": [{"point": "$.continent"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_CABINS",
    "mapping": {
      "description": {"description": "Lists the cabins used to indicate cabin capacities and available bookings classes (lists).", "granularity": "1 cabin in 1 cabin configuration, 1 list of cabins for 1 list of bookings classes in 1 flight segment"},
      "merge": {
        "key-columns": ["CABINS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "opeFit", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"opeFit": "$.aircraftEquipment.operationalFittedConfiguration.configurationContents[*].cabin"}]}},
        {"name": "opeSal", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"opeSal": "$.aircraftEquipment.operationalSaleableConfiguration.configurationContents[*].cabin"}]}},
        {"name": "schFit", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"schFit": "$.aircraftEquipment.scheduledFittedConfiguration.configurationContents[*].cabin"}]}},
        {"name": "schSal", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"schSal": "$.aircraftEquipment.scheduledSaleableConfiguration.configurationContents[*].cabin"}]}},
        {"name": "segment", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}]}}
      ],
      "columns": [
        {
          "name": "CABINS_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "opeFit", "blocks": [{"opeFit": "$.value"}]},
              {"rs-name": "opeSal", "blocks": [{"opeSal": "$.value"}]},
              {"rs-name": "schFit", "blocks": [{"schFit": "$.value"}]},
              {"rs-name": "schSal", "blocks": [{"schSal": "$.value"}]},
              {"rs-name": "segment", "blocks": [{"seg": "$.bookingClassList[*].cabin"}]}
            ]
          },
          "expr": "hashS(array_join(array_distinct(split({0},'-')),'-'))",
          "meta": {"description": {"value": "Hash of cabins  (potentially concatenated list)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "CABIN_CODES", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "opeFit", "blocks": [{"opeFit": "$.value"}]},
              {"rs-name": "opeSal", "blocks": [{"opeSal": "$.value"}]},
              {"rs-name": "schFit", "blocks": [{"schFit": "$.value"}]},
              {"rs-name": "schSal", "blocks": [{"schSal": "$.value"}]},
              {"rs-name": "segment", "blocks": [{"seg": "$.bookingClassList[*].cabin"}]}
            ]
          },
          "expr": "array_join(array_distinct(split({0},'-')),'-')",
          "meta": {"description": {"value": "Cabin code (or ordered list of) used in flight segments or aircraft configurations", "rule": "replace"}, "example": {"value": "F-J-Y", "rule": "replace"}, "gdpr-zone": "green"}
        },
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_BOOKING_CLASSES",
    "mapping": {
      "description": {"description": "Lists the booking classes (ordered list) available on flight segments", "granularity": "1 list of bookings classes (concatenated) in 1 flight segment"},
      "merge": {
        "key-columns": ["BOOKING_CLASSES_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "segment", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}]}}
      ],
      "columns": [
        {
          "name": "BOOKING_CLASSES_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "segment", "blocks": [{"seg": "$.bookingClassList[*].code"}]}
            ]
          },
          "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of booking classes (potentially concatenated list)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "BOOKING_CLASS_CODES", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segment", "blocks": [{"seg": "$.bookingClassList[*].code"}]}
            ]
          },
          "meta": {"description": {"value": "The ordered list of booking classes which are available on flight segments", "rule": "replace"}, "example": {"value": "J-C-D-W-Y-B-H-K-M-Q-L-V", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "CABIN_CODES", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segment", "blocks": [{"seg": "$.bookingClassList[*].cabin"}]}
            ]
          },
          "meta": {"description": {"value": "The ordered list of cabins which are available on flight segments, or applicable to flight legs.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "CABINS_ID", "column-type": "binaryStrColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "segment", "blocks": [{"seg": "$.bookingClassList[*].cabin"}]}
            ]
          },
          "expr": "hashS(array_join(array_distinct(split({0},'-')),'-'))",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_TRAFFIC_RESTRICTIONS",
    "mapping": {
      "description": {"description": "Lists the traffic restrictions which apply to flight segments", "granularity": "1 combination of restrictions (concatenated if multiple values) for 1 flight segment"},
      "merge": {
        "key-columns": ["TRAFFIC_RESTRICTIONS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"rest": "$.mainResource.current.image.segments[*]"}]}
      ],
      "columns": [
        {
          "name": "TRAFFIC_RESTRICTIONS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"rest": "$.trafficRestrictions[*]"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of traffic restrictions (potentially concatenated list)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "TRAFFIC_RESTRICTIONS", "column-type": "strColumn", "sources": {"blocks": [{"rest": "$.trafficRestrictions[*]"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRPORT_TERMINALS",
    "mapping": {
      "description": {"description": "Lists the airport terminals used in departure/arrival of flight segments or legs", "granularity": "1 list of terminals (concatenated by -) in 1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_TERMINALS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"point": "$.mainResource.current.image.flightPoints[*]"}, {"term": "$.departure"}, {"filteringPurpose": "$.terminals[*]"}]},
        {"blocks": [{"point": "$.mainResource.current.image.flightPoints[*]"}, {"term": "$.arrival"}, {"filteringPurpose": "$.terminals[*]"}]}
      ],
      "columns": [
        {
          "name": "AIRPORT_TERMINALS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"point": "$.iataCode"}, {"term": "$.terminals[*].code"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of airport terminals (potentially concatenated list) and airport code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"point": "$.iataCode"}]}},
        {"name": "TERMINALS", "column-type": "strColumn", "sources": {"blocks": [{"term": "$.terminals[*].code"}]}},
        {
          "name": "AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"point": "$.iataCode"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_GATES",
    "mapping": {
      "description": {"description": "Lists the gates used in departure/arrival of flight segments or legs", "granularity": "1 list of gates (concatenated by -) in 1 airport"},
      "merge": {
        "key-columns": ["GATES_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"point": "$.mainResource.current.image.flightPoints[*]"}, {"gate": "$.departure"}, {"filteringPurpose": "$.gates[*]"}]},
        {"blocks": [{"point": "$.mainResource.current.image.flightPoints[*]"}, {"gate": "$.arrival"}, {"filteringPurpose": "$.gates[*]"}]}
      ],
      "columns": [
        {
          "name": "GATES_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"point": "$.iataCode"}, {"gate": "$.gates[*].mainGate"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of gates value (potentially concatenated list)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"point": "$.iataCode"}]}},
        {
          "name": "GATE_NAMES", "column-type": "strColumn", "sources": {"blocks": [{"gate": "$.gates[*].mainGate"}]}
          , "meta": {"description": {"value": "The gates involved in flight segments or legs", "rule": "replace"}, "example": {"value": "A38", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "AIRPORT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"point": "$.iataCode"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_SERVICE_TYPE",
    "mapping": {
      "description": {"description": "Lists the different IATA service types used on flight legs", "granularity": "1 service type"},
      "merge": {
        "key-columns": ["SERVICE_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.legs[*].serviceType"}]}
      ],
      "columns": [
        {
          "name": "SERVICE_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of service type code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "SERVICE_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}},
        {"name": "SERVICE_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the service type code", "rule": "replace"}, "example": {"value": "Passenger/Cargo in Cabin (mixed configuration aircraft)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SERVICE_TYPE_OPERATION", "column-type": "strColumn", "sources": {"literal": "UNKNOWN"}
          , "meta": {"description": {"value": "The category of operations corresponding to the service type code", "rule": "replace"}, "example": {"value": "Passenger/Cargo", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [ {
      "data-source-key": "IATA_SERVICE_TYPE",
      "column-filler" : [
        {"dim-col" : "SERVICE_TYPE_ID", "src-col" : "CODE"},
        {"dim-col" :  "SERVICE_TYPE_CODE", "src-col" : "CODE"},
        {"dim-col" :  "SERVICE_TYPE_LABEL", "src-col" : "LABEL"},
        {"dim-col" :  "SERVICE_TYPE_OPERATION", "src-col" : "TYPE_OF_OPERATION"},
      ]
    } ]
  },
  {
    "name": "DIM_GENERAL_STATUS",
    "mapping": {
      "description": {"description": "Lists the different general statuses used on flight legs", "granularity": "1 general status"},
      "merge": {
        "key-columns": ["GENERAL_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.legs[*].legDCSStatuses.generalStatus"}]}
      ],
      "columns": [
        {
          "name": "GENERAL_STATUS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of general flight status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "GENERAL_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_ACCEPTANCE_STATUS",
    "mapping": {
      "description": {"description": "Lists the acceptance statuses used on segment deliveries", "granularity": "1 acceptance status"},
      "merge": {
        "key-columns": ["ACCEPTANCE_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.legs[*].legDCSStatuses.acceptanceStatus"}]}
      ],
      "columns": [
        {
          "name": "ACCEPTANCE_STATUS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of acceptance status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "ACCEPTANCE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_BOARDING_STATUS",
    "mapping": {
      "description": {"description": "Lists the boarding statuses used on leg deliveries", "granularity": "1 boarding status"},
      "merge": {
        "key-columns": ["BOARDING_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.legs[*].legDCSStatuses.boardingStatus"}]}
      ],
      "columns": [
        {
          "name": "BOARDING_STATUS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of boarding status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "BOARDING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_LOAD_CONTROL_STATUS",
    "mapping": {
      "description": {"description": "Lists the different load control statuses of leg deliveries", "granularity": "1 load control status"},
      "merge": {
        "key-columns": ["LOAD_CONTROL_STATUS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.legs[*].legDCSStatuses.loadControlStatus"}]}
      ],
      "columns": [
        {
          "name": "LOAD_CONTROL_STATUS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of load control status", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "LOAD_CONTROL_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRCRAFT_TYPE",
    "mapping": {
      "description": {"description": "Lists the aircraft types used on flight legs", "granularity": "1 aircraft type"},
      "merge": {
        "key-columns": ["AIRCRAFT_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.legs[*].aircraftEquipment.aircraftType"}]}
      ],
      "columns": [
        {
          "name": "AIRCRAFT_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"dim": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of aircraft type", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "AIRCRAFT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_TRIGGER_EVENT",
    "mapping": {
      "description": {"description": "Lists the different trigger events for the Schedule update", "granularity": "1 event"},
      "merge": {
        "key-columns": ["TRIGGER_EVENT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.event"}]}
      ],
      "columns": [
        {
          "name": "TRIGGER_EVENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"dim": "$.qualifiers[*]"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of trigger event", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.qualifiers[*]"}]}
          , "meta": {"description": {"value": "The name of the triggering event of the flight update", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_TRIGGER_EVENT_SOURCE",
    "mapping": {
      "description": {"description": "Lists the different trigger event sources for the Schedule update", "granularity": "1 event source"},
      "merge": {
        "key-columns": ["TRIGGER_EVENT_SOURCE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"dim": "$.mainResource.current.image.event"}]}
      ],
      "columns": [
        {
          "name": "TRIGGER_EVENT_SOURCE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"dim": "$.source"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of trigger event source", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "TRIGGER_EVENT_SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"dim": "$.source"}]}
          , "meta": {"description": {"value": "The source of the triggering event of the flight update", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRCRAFT_CONFIGURATION_TYPE",
    "mapping": {
      "description": {"description": "Lists the different combinations of aircraft configuration types (fitted/saleable + scheduled/operational).", "granularity": "1 configuration type"},
      "merge": {
        "key-columns": ["AIRCRAFT_CONFIGURATION_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      }
      "root-sources": [
        {"name": "opeFit", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"opeFit": "$.aircraftEquipment.operationalFittedConfiguration"}, {"cont": "$.configurationContents[*]"}]}},
        {"name": "opeSal", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"opeSal": "$.aircraftEquipment.operationalSaleableConfiguration"}, {"cont": "$.configurationContents[*]"}]}},
        {"name": "schFit", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"schFit": "$.aircraftEquipment.scheduledFittedConfiguration"}, {"cont": "$.configurationContents[*]"}]}},
        {"name": "schSal", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"schSal": "$.aircraftEquipment.scheduledSaleableConfiguration"}, {"cont": "$.configurationContents[*]"}]}}
      ],
      "columns": [
        {
          "name": "AIRCRAFT_CONFIGURATION_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "opeFit", "literal": "OPERATIONAL_FITTED"},
              {"rs-name": "opeSal", "literal": "OPERATIONAL_SALEABLE"},
              {"rs-name": "schFit", "literal": "SCHEDULED_FITTED"},
              {"rs-name": "schSal", "literal": "SCHEDULED_SALEABLE"}
            ]
          },
          "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of aircraft configuration type", "rule": "replace"}, "gdpr-zone": "green"}
        }
        {
          "name": "AIRCRAFT_CONFIGURATION_TYPE", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "opeFit", "literal": "OPERATIONAL_FITTED"},
              {"rs-name": "opeSal", "literal": "OPERATIONAL_SALEABLE"},
              {"rs-name": "schFit", "literal": "SCHEDULED_FITTED"},
              {"rs-name": "schSal", "literal": "SCHEDULED_SALEABLE"}
            ]
          },
          "meta": {"description": {"value": "The aircraft configuration type, a combination between fitted/saleable and scheduled/operational", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_AIRCRAFT_CONFIGURATION_CODE",
    "mapping": {
      "description": {"description": "Lists the different codes for aircraft configuration.", "granularity": "1 configuration code"},
      "merge": {
        "key-columns": ["AIRCRAFT_CONFIGURATION_CODE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      }
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"conf": "$.aircraftEquipment.operationalFittedConfiguration"}, {"code": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"conf": "$.aircraftEquipment.operationalSaleableConfiguration"}, {"code": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"conf": "$.aircraftEquipment.scheduledFittedConfiguration"}, {"code": "$.code"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"leg": "$.legs[*]"}, {"conf": "$.aircraftEquipment.scheduledSaleableConfiguration"}, {"code": "$.code"}]}
      ],
      "columns": [
        {
          "name": "AIRCRAFT_CONFIGURATION_CODE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "blocks": [{"code": "$.value"}]
          },
          "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of aircraft configuration code", "rule": "replace"}, "gdpr-zone": "green"}
        }
        {
          "name": "AIRCRAFT_CONFIGURATION_CODE", "column-type": "strColumn",
          "sources": {
            "blocks": [{"code": "$.value"}]
          },
          "meta": {"description": {"value": "The aircraft configuration code", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "ASSO_FLIGHT_SEGMENT_LEG",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "ASSO_FLIGHT_SEGMENT_LEG_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_FLIGHT_SEGMENT_LEG_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains the relationships between flight segments and flight legs, especially relevant for multi-leg flights.", "granularity": "1 relationship between 1 flight segment and 1 flight leg"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_SEGMENT_ID", "FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}, {"leg": "$.legIds[*]"}]
        }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"leg": "$.value"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_LEG_HISTO", "column": "FLIGHT_LEG_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column": "FLIGHT_DATE_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "is-mandatory": "true", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.event.timestamp"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },


  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_DATE_DCS_PAX",  // SKD-PAX #1/4
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-DCSPAX"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },

  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_DATE_DCS_PAX",  // SKD-PAX #1/4
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PASSENGER_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.mainResource.current.image"}
        ]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_SEGMENT_DCSPAX_SEGMENT_DEL",  // SKD-PAX #2/4
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE","DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-DCSPAX"}, // TODO no data available now
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"shouldNotBeEmpty" : "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPaxSegmentDeliveryId"}]}, "expr":  "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}]}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.dcsPaxSegmentDeliveryId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_SEGMENT_DCSPAX_SEGMENT_DEL",  // SKD-PAX #2/4
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE","DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-DCSPAX"}, // TODO no data available now
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"shouldNotBeEmpty" : "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr":  "hashM({0})"},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_CODESHARE_DCSPAX_FLIGHT_SEGMENT_DEL",   // SKD-PAX #3/4
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-DCSPAX"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"shouldNotBeEmpty": "$.partnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",  "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"item": "$.partnershipFlightId"}]}, "expr":  "hashM({0})"},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",  "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.dcsPaxSegmentDeliveryId"}]}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.dcsPaxSegmentDeliveryId"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"item": "$.partnershipFlightId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_CODESHARE_DCSPAX_FLIGHT_SEGMENT_DEL",   // SKD-PAX #3/4
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SEGMENT_DELIVERY_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-DCSPAX"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"shouldNotBeEmpty": "$.partnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",  "sources": {}, "expr":  "hashM({0})"},
        {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",  "sources": {}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_SEGMENT_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_LEG_DCSPAX_LEG_DEL",   // SKD-PAX #4/4
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DIH_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_LEG_ID", "LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-DCSPAX"}, // TODO no data available now
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"legs": "$.correlatedLegs[*]"},
          {"shouldNotBeEmpty": "$.flightLegId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"legs": "$.flightLegId"}]}, "expr": "hashM({0})"},
        {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.dcsPaxSegmentDeliveryId"}, {"legs": "$.dcsPaxLegDeliveryId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_LEG_DELIVERY", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.dcsPaxSegmentDeliveryId"}, {"legs": "$.dcsPaxLegDeliveryId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_LEG", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"legs": "$.flightLegId"}]}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_LEG_DCSPAX_LEG_DEL",   // SKD-PAX #4/4
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["SKD_ACTIVE", "DCSPAX_ACTIVE", "DAAS_CORRELATION"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INTERNAL_CORR_ID", "LEG_DELIVERY_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-DCSPAX"}, // TODO no data available now
          {"corr": "$.correlations[*]"},
          {"item": "$.corrDcspaxSkd.items[*]"},
          {"legs": "$.correlatedLegs[*]"},
          {"shouldNotBeEmpty": "$.flightLegId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_LEG_DELIVERY", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_PASSENGER", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  //      **** SKD TKT ****
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO",
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_PASSIVE", "DIH_CORRELATION"],["SKD_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-TKT"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_FLIGHT_DATE_HISTO",
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_PASSIVE", "DAAS_CORRELATION"],["SKD_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["SKD_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-TKT"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_FLIGHT_SEGMENT_HISTO",
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_PASSIVE"],["SKD_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-TKT"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrTktSkd.items[*]"},
          {"shouldNotBeEmpty": "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_FLIGHT_SEGMENT_HISTO",
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["SKD_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-TKT"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrTktSkd.items[*]"},
          {"shouldNotBeEmpty": "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_PASSIVE"],["SKD_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-TKT"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrTktSkd.items[*]"},
          {"shouldNotBeEmpty": "$.partnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"item": "$.partnershipFlightId"}]}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"item": "$.partnershipFlightId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": [["SKD_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["SKD_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-TKT"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrTktSkd.items[*]"},
          {"shouldNotBeEmpty": "$.partnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },

  //**** SKD PNR ****
  {
    "name": "INTERNAL_ASSO_RESERVATION_FLIGHT_DATE_HISTO", // SKD-PNR #1/3
    "table-selectors": [["SKD_ACTIVE", "PNR_ACTIVE","DIH_CORRELATION"],["SKD_ACTIVE", "PNR_PASSIVE"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-PNR"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources":{"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_RESERVATION_FLIGHT_DATE_HISTO", // SKD-PNR #1/3
    "table-selectors": ["SKD_ACTIVE", "PNR_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-PNR"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources":{}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources":{}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "TECHNICAL_CUST_OPE_OR_MKT", "column-type": "strColumn", "is-mandatory": "true","sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO", // SKD-PNR #2/3
    "table-selectors": [["SKD_ACTIVE", "PNR_ACTIVE","DIH_CORRELATION"],["SKD_ACTIVE", "PNR_PASSIVE"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-PNR"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrSkdPnr.items[*]"},
          {"codeshare": "$.partnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources":  {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"item": "$.partnershipFlightId"}]}, "expr": "hashM({0})"},
        {"name": "AIR_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.pnrAirsegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources":  {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"item": "$.partnershipFlightId"}]}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.pnrAirsegmentId"}]}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources":{"blocks": [{"base": "$.id"}]}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO", // SKD-PNR #2/3
    "table-selectors": ["SKD_ACTIVE", "PNR_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.SKD-PNR"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources":  {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources":  {}, "expr": "hashM({0})"},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "TECHNICAL_CUST_OPE_OR_MKT", "column-type": "strColumn", "is-mandatory": "true","sources": {}},
        {"name": "TECHNICAL_CUST_ISPRIME", "column-type": "strColumn", "is-mandatory": "true","sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO", // SKD-PNR #3/3
    "table-selectors": [["SKD_ACTIVE", "PNR_ACTIVE","DIH_CORRELATION"],["SKD_ACTIVE", "PNR_PASSIVE"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-PNR"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrSkdPnr.items[*]"},
          {"shouldNotBeEmpty": "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}]}, "expr": "hashM({0})"},
        {"name": "AIR_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.pnrAirsegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}]}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.pnrAirsegmentId"}]}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources":{"blocks": [{"base": "$.id"}]}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO", // SKD-PNR #3/3
    "table-selectors": ["SKD_ACTIVE", "PNR_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-PNR"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrSkdPnr.items[*]"},
          {"shouldNotBeEmpty": "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "AIR_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_SKD_INV_FLIGHT_DATE", // SKD-INV #1/4
    "table-selectors": [["SKD_ACTIVE","INV_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SKD_FLIGHT_DATE_ID", "INV_FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-INV"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "SKD_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "INV_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_INV_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION_INV_FLIGHT_DATE", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_SKD_INV_FLIGHT_DATE", // SKD-INV #1/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SKD_FLIGHT_DATE_ID", "INV_FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-INV"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "SKD_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INV_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_INV_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION_INV_FLIGHT_DATE", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_SEGMENT", // SKD-INV #2/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_FLIGHT_SEGMENT_ID", "SKD_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-INV"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"shouldNotBeEmpty": "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "SKD_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}]}, "expr": "hashM({0})"},
        {"name": "INV_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}]}},
        {"name": "REFERENCE_KEY_INV_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_SEGMENT", // SKD-INV #2/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_FLIGHT_SEGMENT_ID", "SKD_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-INV"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"shouldNotBeEmpty": "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "SKD_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INV_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_INV_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_CODESHARE_FLIGHT_SEGMENT", // SKD-INV #3/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_CODESHARE_FLIGHT_SEGMENT_ID", "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-INV"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"shouldNotBeEmpty": "$.inventoryPartnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"item": "$.inventoryPartnershipFlightId"}]}, "expr": "hashM({0})"},
        {"name": "INV_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"}, {"item": "$.inventoryPartnershipFlightId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SKD_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"item": "$.inventoryPartnershipFlightId"}]}},
        {"name": "REFERENCE_KEY_INV_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"}, {"item": "$.inventoryPartnershipFlightId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_CODESHARE_FLIGHT_SEGMENT", // SKD-INV #3/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_CODESHARE_FLIGHT_SEGMENT_ID", "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-INV"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"shouldNotBeEmpty": "$.inventoryPartnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INV_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SKD_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_INV_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_LEG", // SKD-INV #2/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_FLIGHT_LEG_ID", "SKD_FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-INV"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"legs": "$.correlatedLegs[*]"},
          {"shouldNotBeEmpty": "$.flightLegId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "SKD_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"legs": "$.flightLegId"}]}, "expr": "hashM({0})"},
        {"name": "INV_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"}, {"legs": "$.inventoryLegId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_LEG", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.flightSegmentId"}, {"legs": "$.flightLegId"}]}},
        {"name": "REFERENCE_KEY_INV_FLIGHT_LEG", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.inventorySegmentId"}, {"legs": "$.inventoryLegId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.event.timestamp"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_SKD_FLIGHT_LEG", // SKD-INV #2/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_FLIGHT_LEG_ID", "SKD_FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.SKD-INV"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"legs": "$.correlatedLegs[*]"},
          {"shouldNotBeEmpty": "$.flightLegId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}, "expr": ${createZorderKey}},
        {"name": "SKD_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INV_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_LEG", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_INV_FLIGHT_LEG", "column-type": "strColumn", "sources": {}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  //
]
