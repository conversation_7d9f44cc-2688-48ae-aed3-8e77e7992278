[0m[[0m[0mdebug[0m] [0m[0m[zinc] IncrementalCompile -----------[0m
[0m[[0m[0mdebug[0m] [0m[0mIncrementalCompile.incrementalCompile[0m
[0m[[0m[0mdebug[0m] [0m[0mprevious = Stamps for: 0 products, 0 sources, 0 libraries[0m
[0m[[0m[0mdebug[0m] [0m[0mcurrent source = Set(${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/ColSources.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/executors/ValidationExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/CloudEvent.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/JDFEventData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeActuator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/PorsExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/DimPrefillerParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/PreRowGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/SnowflakePushApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationColumns.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/PitTransformation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/CorrelationIdsTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/PlantUmlGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamPlanner.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/ModelComparisonExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamCheckpointsTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/correlation/CorrelationLibrary.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/JsonFileInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/Mapping.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/PartialReprocessingParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/ColumnTypeDatabricks.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AutoLoaderAvroInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingAddonParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/ColumnConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/BlockResults.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/FileSystemDiscovery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/ExpressionManager.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/ChangeDataFeedUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/InternalOptimizeMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/PlantUmlExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/StreamExecutorInputFormat.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Merge.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/ValidationRecord.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/ColumnDetails.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/RootConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/Blocks.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/executors/ValidationSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DataDictExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/ValidationConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/currency/CurrencyConversionAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/PurgeApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/AccessTokenProvider.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/DistanceProration.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/TablesToDf.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/EnrichedSchemaMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunQualityMetrics.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/SourceCorrelation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/TableGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/package.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/Correlation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeConnector.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/FatalJson2StarAppException.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/OptimizeResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/lookups/YamlLookup.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/DeltaInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/ConsistentQuery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/SourceCorrelation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/SnowflakeConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationFullyProcessedGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/PreRowQuery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/latest/Latest.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AutoLoaderMDMJsonInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/package.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/RefDataPrefillerApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/TemporaryTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/AddonConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/ExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/PitLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/EventGridParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TablesConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/dummy/DummyAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/TableAccess.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/SnowflakeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaTableCopySchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/CorrelationPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddonConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/RowGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddons.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/lookups/MetadataFields.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/TableDef.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DeltaTableCopySchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaTableCopyConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunMetadataDetails.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/ProcessingContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/LatestConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/DummyExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DocGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/prefiller/PrefillerConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/EventGridClient.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/ResizeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/PlantUmlDSL.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeRootConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/PipelineContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/CheckType.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/InputFormat.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/io/AvroFile.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/ModelConfigLoader.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationOutput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/DatabricksUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AvroDecoder.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/purge/PurgeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/correlation/Correlation2Ways.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/EtlConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationNoDupesGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Json2StarApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/GDPRZone.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/DimPrefiller.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/ProcessingParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Display.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/AddonTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/EventsListener.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DeltaSchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamStep.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/Table.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/SchemaManager.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/OriginResolver.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/SourceCorrelationPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/MapAccumulator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/JDFMetadataGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SnowflakeTableCopySchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableSnowflake.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/DeltaMergeStatus.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/EventPublisher.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SnowflakeSchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/OptimizeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableDescription.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/TablesDef.scala, ${BASE}/core/src/main/scala/net/snowflake/spark/snowflake/SnowflakeUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/EventToStar.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/hashers/StarSchemaHasher.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddonValidationException.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/ExtDataType.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/Unparsed.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Metrics.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeSettings.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/JsonProcessor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/Schema.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/weight/WeightConversionAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/FareCalcProration.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/SnowflakeSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/Optimize.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Correlation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingMerge.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeQueryRunner.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/MarkdownDSL.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationForeignKeyGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/FieldProcessor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeActuatorConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/AppConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Pit.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/MappingConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/Addon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/ColumnDef.scala)[0m
[0m[[0m[0mdebug[0m] [0m[0m> initialChanges = InitialChanges(Changes(added = Set(${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/DimPrefillerParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/BlockResults.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/StreamExecutorInputFormat.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/purge/PurgeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AutoLoaderMDMJsonInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/ColumnTypeDatabricks.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/EventGridClient.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/ProcessingParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/Optimize.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingAddonParams.scala, ${BASE}/core/src/main/scala/net/snowflake/spark/snowflake/SnowflakeUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Metrics.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/prefiller/PrefillerConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/FileSystemDiscovery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeConnector.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DocGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/ValidationRecord.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/CheckType.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/ExtDataType.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamPlanner.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/PartialReprocessingParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/correlation/CorrelationLibrary.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/ExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/TableDef.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/ConsistentQuery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/EventsListener.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/Addon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/lookups/MetadataFields.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/PurgeApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/ColumnDef.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamStep.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DeltaSchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/CorrelationPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/Mapping.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/TablesToDf.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DeltaTableCopySchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/DistanceProration.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/JsonFileInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/PlantUmlGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/ChangeDataFeedUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/TableGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/SnowflakeSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationColumns.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/executors/ValidationSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationNoDupesGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/JsonProcessor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableDescription.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeActuator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SnowflakeTableCopySchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/Schema.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/PlantUmlDSL.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/SnowflakeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/JDFEventData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableSnowflake.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeSettings.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Pit.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/ResizeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaTableCopySchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddonConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/SnowflakePushApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/RootConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Merge.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationForeignKeyGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/MappingConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddonValidationException.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/ModelConfigLoader.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/ExpressionManager.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/PreRowGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/InputFormat.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/TableAccess.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/package.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/TablesDef.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/ColumnDetails.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TablesConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/PitTransformation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeRootConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/EventGridParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/lookups/YamlLookup.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/DimPrefiller.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/ColSources.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/AppConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddons.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DataDictExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/AccessTokenProvider.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/TemporaryTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/OptimizeResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/ColumnConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SnowflakeSchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/SourceCorrelation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/PorsExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunMetadataDetails.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunQualityMetrics.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/DummyExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/package.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AvroDecoder.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeActuatorConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/dummy/DummyAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/DeltaInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/SnowflakeConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/EnrichedSchemaMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/EventToStar.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/SourceCorrelation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/EventPublisher.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/PipelineContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/DeltaMergeStatus.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/RefDataPrefillerApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Display.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamCheckpointsTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/CloudEvent.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/Correlation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/io/AvroFile.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/Unparsed.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/PitLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/executors/ValidationExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeQueryRunner.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/MapAccumulator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/AddonConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingMerge.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Json2StarApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/Blocks.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/FatalJson2StarAppException.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/EtlConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationOutput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/weight/WeightConversionAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/PlantUmlExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/correlation/Correlation2Ways.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Correlation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/PreRowQuery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/ValidationConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/Table.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/hashers/StarSchemaHasher.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationFullyProcessedGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/AddonTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/CorrelationIdsTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/MarkdownDSL.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/FareCalcProration.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/InternalOptimizeMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/ProcessingContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/OptimizeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/FieldProcessor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/DatabricksUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/latest/Latest.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/ModelComparisonExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/currency/CurrencyConversionAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/SchemaManager.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/SourceCorrelationPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AutoLoaderAvroInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/LatestConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaTableCopyConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/JDFMetadataGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/RowGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/GDPRZone.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/OriginResolver.scala), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())[0m
[0m[[0m[0mdebug[0m] [0m[0mFull compilation, no sources in previous analysis.[0m
[0m[[0m[0mdebug[0m] [0m[0mall 166 sources are invalidated[0m
[0m[[0m[0mdebug[0m] [0m[0mInitial set of included nodes: [0m
[0m[[0m[0mdebug[0m] [0m[0mRecompiling all sources: number of invalidated sources > 50.0 percent of all sources[0m
[0m[[0m[0mdebug[0m] [0m[0mcompilation cycle 1[0m
[0m[[0m[0minfo[0m] [0m[0mcompiling 166 Scala sources to /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/core/target/scala-2.12/classes ...[0m
[0m[[0m[0mdebug[0m] [0m[0mGetting org.scala-sbt:compiler-bridge_2.12:1.9.5:compile for Scala 2.12.15[0m
[0m[[0m[31merror[0m] [0m[0m'jvm-11' is not a valid choice for '-target'[0m
[0m[[0m[31merror[0m] [0m[0mbad option: '-target:jvm-11'[0m
[0m[[0m[0mdebug[0m] [0m[0mCompilation failed (CompilerInterface)[0m
[0m[[0m[31merror[0m] [0m[0m(core / Compile / [31mcompileIncremental[0m) Compilation failed[0m
