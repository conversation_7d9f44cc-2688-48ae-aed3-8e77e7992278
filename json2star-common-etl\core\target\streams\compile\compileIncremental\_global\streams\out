[debug] [zinc] IncrementalCompile -----------
[debug] IncrementalCompile.incrementalCompile
[debug] previous = Stamps for: 0 products, 0 sources, 0 libraries
[debug] current source = Set(${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/ColSources.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/executors/ValidationExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/CloudEvent.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/JDFEventData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeActuator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/PorsExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/DimPrefillerParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/PreRowGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/SnowflakePushApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationColumns.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/PitTransformation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/CorrelationIdsTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/PlantUmlGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamPlanner.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/ModelComparisonExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamCheckpointsTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/correlation/CorrelationLibrary.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/JsonFileInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/Mapping.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/PartialReprocessingParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/ColumnTypeDatabricks.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AutoLoaderAvroInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingAddonParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/ColumnConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/BlockResults.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/FileSystemDiscovery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/ExpressionManager.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/ChangeDataFeedUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/InternalOptimizeMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/PlantUmlExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/StreamExecutorInputFormat.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Merge.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/ValidationRecord.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/ColumnDetails.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/RootConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/Blocks.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/executors/ValidationSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DataDictExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/ValidationConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/currency/CurrencyConversionAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/PurgeApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/AccessTokenProvider.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/DistanceProration.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/TablesToDf.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/EnrichedSchemaMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunQualityMetrics.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/SourceCorrelation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/TableGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/package.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/Correlation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeConnector.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/FatalJson2StarAppException.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/OptimizeResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/lookups/YamlLookup.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/DeltaInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/ConsistentQuery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/SourceCorrelation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/SnowflakeConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationFullyProcessedGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/PreRowQuery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/latest/Latest.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AutoLoaderMDMJsonInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/package.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/RefDataPrefillerApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/TemporaryTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/AddonConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/ExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/PitLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/EventGridParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TablesConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/dummy/DummyAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/TableAccess.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/SnowflakeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaTableCopySchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/CorrelationPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddonConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/RowGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddons.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/lookups/MetadataFields.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/TableDef.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DeltaTableCopySchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaTableCopyConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunMetadataDetails.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/ProcessingContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/LatestConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/DummyExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DocGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/prefiller/PrefillerConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/EventGridClient.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/ResizeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/PlantUmlDSL.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeRootConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/PipelineContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/CheckType.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/InputFormat.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/io/AvroFile.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/ModelConfigLoader.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationOutput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/DatabricksUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AvroDecoder.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/purge/PurgeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/correlation/Correlation2Ways.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/EtlConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationNoDupesGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Json2StarApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/GDPRZone.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/DimPrefiller.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/ProcessingParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Display.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/AddonTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/EventsListener.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DeltaSchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamStep.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/Table.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/SchemaManager.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/OriginResolver.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/SourceCorrelationPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/MapAccumulator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/JDFMetadataGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SnowflakeTableCopySchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableSnowflake.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/DeltaMergeStatus.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/EventPublisher.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SnowflakeSchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/OptimizeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableDescription.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/TablesDef.scala, ${BASE}/core/src/main/scala/net/snowflake/spark/snowflake/SnowflakeUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/EventToStar.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/hashers/StarSchemaHasher.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddonValidationException.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/ExtDataType.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/Unparsed.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Metrics.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeSettings.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/JsonProcessor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/Schema.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/weight/WeightConversionAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/FareCalcProration.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/SnowflakeSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/Optimize.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Correlation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingMerge.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeQueryRunner.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/MarkdownDSL.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationForeignKeyGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/FieldProcessor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeActuatorConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/AppConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Pit.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/MappingConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/Addon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/ColumnDef.scala)
[debug] > initialChanges = InitialChanges(Changes(added = Set(${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/DimPrefillerParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/BlockResults.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/StreamExecutorInputFormat.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/purge/PurgeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AutoLoaderMDMJsonInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/EventGridClient.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/ColumnTypeDatabricks.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/ProcessingParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/Optimize.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingAddonParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/prefiller/PrefillerConfig.scala, ${BASE}/core/src/main/scala/net/snowflake/spark/snowflake/SnowflakeUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Metrics.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/FileSystemDiscovery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeConnector.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DocGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/CheckType.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/ValidationRecord.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/ExtDataType.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamPlanner.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/correlation/CorrelationLibrary.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/PartialReprocessingParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/ExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/TableDef.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/ConsistentQuery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/EventsListener.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/Addon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/lookups/MetadataFields.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/PurgeApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/ColumnDef.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamStep.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DeltaSchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/CorrelationPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/Mapping.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/TablesToDf.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/DeltaTableCopySchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/DistanceProration.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/JsonFileInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/PlantUmlGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/ChangeDataFeedUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/TableGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/SnowflakeSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationColumns.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/executors/ValidationSchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/JsonProcessor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationNoDupesGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableDescription.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeActuator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SnowflakeTableCopySchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/PlantUmlDSL.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/Schema.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/SnowflakeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/JDFEventData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TableSnowflake.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeSettings.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/ResizeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaTableCopySchemaExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddonConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Pit.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/SnowflakePushApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/RootConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Merge.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationForeignKeyGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/MappingConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddonValidationException.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/ModelConfigLoader.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/PreRowGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/ExpressionManager.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/InputFormat.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/TableAccess.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/package.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/TablesDef.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/ColumnDetails.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/TablesConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/PitTransformation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeRootConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/EventGridParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/lookups/YamlLookup.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/ColSources.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/DimPrefiller.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/AppConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddons.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DataDictExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/TemporaryTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/AccessTokenProvider.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/OptimizeResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/ColumnConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SnowflakeSchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/SourceCorrelation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/PorsExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunMetadataDetails.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/metadata/JobRunQualityMetrics.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/package.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/extdata/DummyExtData.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AvroDecoder.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/dummy/DummyAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeActuatorConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/DeltaInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/SnowflakeConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/SourceCorrelation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/EnrichedSchemaMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/EventToStar.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/EventPublisher.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/PipelineContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/RefDataPrefillerApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/DeltaMergeStatus.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Display.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/checkpoints/StreamCheckpointsTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/eventgrid/CloudEvent.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/Correlation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/io/AvroFile.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/PitLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/Unparsed.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/executors/ValidationExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/snowflake/SnowflakeQueryRunner.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/MapAccumulator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/AddonConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingMerge.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/Json2StarApp.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/Blocks.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/SchemaGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/FatalJson2StarAppException.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/ResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/EtlConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/ProrationOutput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/weight/WeightConversionAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/correlation/Correlation2Ways.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/PlantUmlExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/Correlation.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/PreRowQuery.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/StackableAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/config/ValidationConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/Table.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/hashers/StarSchemaHasher.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/MappingResizeLogic.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/validation/generators/ValidationFullyProcessedGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/AddonTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/CorrelationIdsTable.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/MarkdownDSL.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/proration/FareCalcProration.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/optimize/InternalOptimizeMetadata.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/app/ProcessingContext.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/config/OptimizeParams.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/processors/FieldProcessor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/resize/DatabricksUtils.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/latest/Latest.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/ModelComparisonExecutor.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/stackable/currency/CurrencyConversionAddon.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/correlation/SourceCorrelationPipeline.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/spark/SchemaManager.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/input/AutoLoaderAvroInput.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/application/config/LatestConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/executors/DeltaTableCopyConfig.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/views/generators/JDFMetadataGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/vault/generators/RowGenerator.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/rawvault/common/config/GDPRZone.scala, ${BASE}/core/src/main/scala/com/amadeus/airbi/json2star/common/addons/base/mapping/OriginResolver.scala), removed = Set(), changed = Set(), unmodified = ...),Set(),Set(),API Changes: Set())
[debug] Full compilation, no sources in previous analysis.
[debug] all 166 sources are invalidated
[debug] Initial set of included nodes: 
[debug] Recompiling all sources: number of invalidated sources > 50.0 percent of all sources
[debug] compilation cycle 1
[info] compiling 166 Scala sources to C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\core\target\scala-2.12\classes ...
[debug] Getting org.scala-sbt:compiler-bridge_2.12:1.9.5:compile for Scala 2.12.15
[info] Non-compiled module 'compiler-bridge_2.12' for Scala 2.12.15. Compiling...
[debug] Creating plain compiler interface for 2.12.15.
[debug]   > Arguments: 
[debug] 	-d
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_3e1accc7
[debug] 	-nowarn
[debug] 	-bootclasspath
[debug] 	C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repository.rnd.amadeus.net\sbt-sonatype-remote\org\scala-lang\scala-library\2.12.15\scala-library-2.12.15.jar
[debug] 	-classpath
[debug] 	C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repository.rnd.amadeus.net\sbt-sonatype-remote\org\scala-sbt\compiler-interface\1.9.5\compiler-interface-1.9.5.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repository.rnd.amadeus.net\sbt-sonatype-remote\org\scala-sbt\util-interface\1.9.4\util-interface-1.9.4.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repository.rnd.amadeus.net\sbt-sonatype-remote\org\scala-sbt\compiler-bridge_2.12\1.9.5\compiler-bridge_2.12-1.9.5-sources.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repository.rnd.amadeus.net\sbt-sonatype-remote\org\scala-lang\scala-compiler\2.12.15\scala-compiler-2.12.15.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repository.rnd.amadeus.net\sbt-sonatype-remote\org\scala-lang\scala-reflect\2.12.15\scala-reflect-2.12.15.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repository.rnd.amadeus.net\sbt-sonatype-remote\org\scala-lang\modules\scala-xml_2.12\1.0.6\scala-xml_2.12-1.0.6.jar;C:\Users\<USER>\AppData\Local\Coursier\Cache\v1\https\repository.rnd.amadeus.net\sbt-sonatype-remote\jline\jline\2.14.6\jline-2.14.6.jar
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\scala\ZincCompat.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\API.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\AbstractZincFile.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\Analyzer.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\CallbackGlobal.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\ClassName.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\Compat.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\CompilerBridge.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\ConsoleBridge.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\DelegatingReporter.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\Dependency.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\ExtractAPI.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\ExtractUsedNames.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\GlobalHelpers.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\InteractiveConsoleBridge.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\InteractiveConsoleFactoryBridge.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\InteractiveConsoleHelper.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\InteractiveConsoleResponse.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\JarUtils.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\JavaUtils.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\LocalToNonLocalClass.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\LocateClassFile.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\Log.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\Message.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\PlainNioFile.scala
[debug] 	C:\Users\<USER>\AppData\Local\Temp\sbt_355b582f\xsbt\ScaladocBridge.scala
[debug]       
[info]   Compilation completed in 6.815s.
[error] 'jvm-11' is not a valid choice for '-target'
[error] bad option: '-target:jvm-11'
[debug] Compilation failed (CompilerInterface)
[error] (core / Compile / compileIncremental) Compilation failed
