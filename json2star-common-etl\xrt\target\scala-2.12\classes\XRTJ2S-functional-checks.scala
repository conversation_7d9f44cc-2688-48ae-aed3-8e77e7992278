// Databricks notebook source
// MAGIC %md
// MAGIC #Notebook Information
// MAGIC This notebook contains 1 functional test for XRTJ2S.

// COMMAND ----------

import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils
import org.apache.spark.sql.functions.{col, abs, when,coalesce, lit }
import org.apache.spark.sql.functions._
import scala.collection.Map
import scala.util.parsing.json.JSON

// COMMAND ----------

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, daysBack, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard
val db = vConfig.appConfig.common.outputDatabase

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val date_formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val minTime = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(daysBack.toInt)

// COMMAND ----------

case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()

// COMMAND ----------

val metaTableName = "metadata_job_run"

val tableMeta  = spark.read.table(s"$db.$metaTableName")

// COMMAND ----------

// DBTITLE 1,TC-XRTJ2S-001: All data have been processed


val df = tableMeta
  .orderBy(desc("END_TIMESTAMP"))
  .limit(1)


val latestDetailsJson = df.select("DETAILS").collect()(0).getAs[String]("DETAILS")
val latestDetails = JSON.parseFull(latestDetailsJson).get.asInstanceOf[Map[String, Map[String, String]]]

val droppedRows: Double = latestDetails("mapping")("transformErrors").asInstanceOf[Double]
val processededRows: Double = latestDetails("mapping")("totProcessed").asInstanceOf[Double]

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "TC-XRTJ2S-001: All data have been processed",
  droppedRows == 0,
  droppedRows.toLong,
  processededRows.toLong,
  if (processededRows != 0) droppedRows.toFloat / processededRows.toFloat else 0,
  if (droppedRows != 0) "Some rows raised a transformErrors." else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ##write to db

// COMMAND ----------

val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")