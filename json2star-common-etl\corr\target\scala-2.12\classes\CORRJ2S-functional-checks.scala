// MAGIC %md
// MAGIC #Correlation checks

// COMMAND ----------

import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import org.apache.spark.sql.functions.col
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

// COMMAND ----------

case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()

// COMMAND ----------

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val days_back = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, days_back, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

// COMMAND ----------

val inputDatabases = vConfig.appConfig.inputDatabases.getOrElse(Map.empty)
val pnr = inputDatabases("PNR")
val tkt = inputDatabases("TKTEMD")
val skd = inputDatabases("SKD")
val dcspax = inputDatabases("DCSPAX")
val dcsbag = inputDatabases("DCSBAG")
val corr = vConfig.appConfig.common.outputDatabase

val possible_correlations  = Map(
  pnr -> List(skd), //tkt, dcspax, dcsbag,
  tkt -> List(skd),  //pnr, dcspax,
  skd -> List(pnr, tkt, dcspax),
  dcspax -> List(dcsbag, skd), //pnr, tkt,
  dcsbag -> List(dcspax) //pnr
)

val feeds = Map(pnr->"pnr",tkt->"tkt",skd->"skd",dcspax->"dcspax",dcsbag->"dcsbag")
val primary_tables = Map("pnr"->"reservation","tkt" -> "travel_document","skd" -> "flight_date","dcspax" -> "dcs_passenger","dcsbag" -> "bag_group")
val primary_tables_id = Map("pnr"->"reservation","tkt" -> "travel_document","skd" -> "flight_date","dcspax" -> "passenger","dcsbag" -> "bag_group")
val primary_tables_id2 = Map("pnr"->"reservation","tkt" -> "travel_document","skd" -> "flight_date","dcspax" -> "passenger","dcsbag" -> "baggage")

// COMMAND ----------
//tests to ensure records in the main CORRJ2S asso tables exist in the corresponding domain tables, checks whether the correlation is computed or source based based on if the domain database exists (ei PNR exists if reservation database exists)
for ((db_a,v) <- possible_correlations) {
  val feed_a = feeds(db_a)
  for (db_b <- v){
    val feed_b = feeds(db_b)
    val active = spark.catalog.databaseExists(db_b)
    val corr_type = if (active) "computed" else "source-based"
    val table_to_check = if(active) db_b + ".fact_" + primary_tables_id(feed_b) + "_histo" else db_a + ".internal_asso_" + primary_tables(feed_b) + "_" + primary_tables(feed_a) + "_histo"
    val version_to_check = if(active) {if(feeds(db_b)=="pnr") {"ENVELOP_NB"}else "VERSION" }else "VERSION_" + primary_tables(feed_b)
    val reference_to_check = if(active) "REFERENCE_KEY" else "REFERENCE_KEY_" + primary_tables(feed_b)

    //check to ensure all values reference in the corr table exist in the corresponding feed table
    val feedOrder = spark.catalog.tableExists(corr+".asso_"+primary_tables(feed_b)+"_"+primary_tables(feed_a))
    val feed1 = if (feedOrder) feed_a else feed_b
    val feed2 = if (feedOrder) feed_b else feed_a
    val requestFailed = s"""select * from ${corr}.asso_${primary_tables(feed2)}_${primary_tables(feed1)} corr
    where not exists (
      select * from ${table_to_check} table1
      where table1.${primary_tables_id(feed_b)}_ID = corr.${primary_tables_id(feed_b)}_ID
      and table1.${version_to_check} = corr.VERSION_${primary_tables_id2(feed_b)}
      and table1.${reference_to_check} = corr.REFERENCE_KEY_${primary_tables_id(feed_b)}
    );"""
    val requestTotal = s"""select * from ${corr}.asso_${primary_tables(feed2)}_${primary_tables(feed1)};"""

    val failCount=try{spark.sql(requestFailed).count()} catch{case _: Throwable =>0}
    val totalCount=try{spark.sql(requestTotal).count()} catch{case _: Throwable =>1}
    if (totalCount==1) {println(s"Looking for $corr_type correlation, none found between $feed_a and $feed_b. Can't find ${corr}.asso_${primary_tables(feed2)}_${primary_tables(feed1)}")}
    else{println(s"$corr_type correlation found from $feed_a to $feed_b. Number of failed & total records : $failCount & $totalCount")}

    //add to df to load into DB
    testRecords += TestRecord(
      domain,
      domain_version,
      customer,
      phase,
      currentTimestamp,
      task,
      s"Check that all fields in correlation from $feed_a to $feed_b exist in main $feed_b FACT tables",
      failCount == 0,
      failCount,
      totalCount,
      failCount.toFloat / totalCount,
      if (failCount > 0) {s"Number of records that are in CORRJ2S but do not appear in $feed_b main FACT table : $failCount"} else {s"$corr_type correlation found from $feed_a to $feed_b."}
    )
  }
}

// COMMAND ----------
//tests each histo table in CORRJ2S database to see how many times versions between domain_a and domain_b are out of order, filters to only test records for the past $days_back

spark.catalog.setCurrentDatabase(corr)

spark.catalog.listTables().where(col("name").like("asso_air%histo")).collect().foreach(table => {
  val table_name = table.name
  val tableColumns = spark.sql(s"""SHOW COLUMNS IN $table_name;""").map(f=>f.getString(0)).collect.toList
  //since the id's are both used in the partition by and the versions are used to filter and check the order, it doesn't matter the order of the id's and versions
  val id_1 = tableColumns.apply(tableColumns.indexWhere(element => element.endsWith("ID")))
  val id_2 = tableColumns.apply(tableColumns.lastIndexWhere(element => element.endsWith("ID")))
  val version_a = tableColumns.apply(tableColumns.indexWhere(element => element.startsWith("VERSION")))
  val version_b = tableColumns.apply(tableColumns.lastIndexWhere(element => element.startsWith("VERSION")))

  val request_totalRecords = s"""select * from $table_name where datediff(now(),date_begin) >= $days_back"""
  //lead function is used to compare the version from one row to the version from the next row within the same id_1 and id_2, ordered by the other version
  val request_failedRecords = s"""select * from (
      select *,
      ($version_a-lead($version_a,1) OVER(PARTITION BY $id_1, $id_2 ORDER BY $version_b)) domain_a_diff
      from $table_name
      where datediff(now(),date_begin) >= $days_back
      )
    where domain_a_diff > 0;"""

  val totalCount = spark.sql(request_totalRecords).count()
  val failCount = spark.sql(request_failedRecords).count()
  println(s"$failCount out of $totalCount versions out of order for $version_a and $version_b in $table_name")

  //add to df to load into DB
  testRecords += TestRecord(
    domain,
    domain_version,
    customer,
    phase,
    currentTimestamp,
    task,
    s"Check how many versions are out of order between $version_a and $version_b in $table_name.",
    failCount == 0,
    failCount,
    totalCount,
    failCount.toFloat / totalCount,
    if (failCount > 0) {s"Number of versions that are out of order : $failCount"} else {s"All records in order"}
  )
}
)

// COMMAND ----------

// MAGIC %md
// MAGIC ##write to db

// COMMAND ----------

val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")