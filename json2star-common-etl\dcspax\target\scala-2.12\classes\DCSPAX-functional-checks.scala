// MAGIC %md
// MAGIC #Notebook Information
// MAGIC This notebook contains functional tests for DCSPAX.

// COMMAND ----------

import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

// COMMAND ----------

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, daysBack, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard
val dbName = vConfig.appConfig.common.outputDatabase

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val date_formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val minTime = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(daysBack.toInt)

// COMMAND ----------

case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()

// COMMAND ----------

// MAGIC %md
// MAGIC ##begin tests

// COMMAND ----------

// MAGIC %md
// MAGIC ###test1

// COMMAND ----------

//at least one segment in operational window should have ACTIVE
//
val tc1Threshold = 0.01

val results = spark.sql(s"""
    select
      count(*) countAll,
      count_if(!exists(prodTypes, x -> x like 'ACTIVE%')) countNotActive,
      count_if(!exists(prodTypes, x -> x like 'ACTIVE%'))/count(*) percentNotActive
    from (
      select a.FLIGHT_SEGMENT_ID, collect_set(a.DCS_PRODUCT_TYPE) prodTypes
      from $dbName.FACT_SEGMENT_DELIVERY_HISTO a
      join $dbName.fact_flight_segment_histo b
      on a.FLIGHT_SEGMENT_ID = b.FLIGHT_SEGMENT_ID
      and a.version = b.version
      where a.load_date > to_date($minTime)
      and a.DEPARTURE_DATETIME_UTC < (current_date() - INTERVAL '2' DAY)
      and date_diff(a.DEPARTURE_DATETIME_UTC,b.LOAD_DATE) not between -3 and 6
      group by a.flight_segment_id
    )
""").first().toString.replace("[","").replace("]","").split(",")

val (totalCount, failCount, failPercent) = (results(0).toLong, results(1).toLong, results(2).toFloat)

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 1 : All segments in the operational windows (-3 to +6 days of flight) should have at least one ACTIVE",
  failPercent < tc1Threshold,
  failCount,
  totalCount,
  failPercent,
  if ( failPercent > tc1Threshold) f"Percent of records in operational window that don't contain at least one active : ${failPercent*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ###test2

// COMMAND ----------

//all segments in operational window are "active synchronized"
//
val tc1Threshold = 0.01

val results = spark.sql(s"""
    select
      count(*) countAll,
      count_if(exists(prodTypes, x -> x like 'ACTIVE%')) countNotActive,
      count_if(exists(prodTypes, x -> x like 'ACTIVE%'))/count(*) percentNotActive
    from (
      select a.FLIGHT_SEGMENT_ID, collect_set(a.DCS_PRODUCT_TYPE) prodTypes
      from $dbName.FACT_SEGMENT_DELIVERY_HISTO a
      join $dbName.fact_flight_segment_histo b
      on a.FLIGHT_SEGMENT_ID = b.FLIGHT_SEGMENT_ID
      and a.version = b.version
      where a.load_date > to_date($minTime)
      and date_diff(a.DEPARTURE_DATETIME_UTC,b.LOAD_DATE) not between -3 and 6
      group by a.flight_segment_id
    )
""").first().toString.replace("[","").replace("]","").split(",")

val (totalCount, failCount, failPercent) = (results(0).toLong, results(1).toLong, results(2).toFloat)


testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 2 : Segments outside of the operational windows (-3 to +6 days of flight) should be PASSIVE",
  failPercent < tc1Threshold,
  failCount,
  totalCount,
  failPercent,
  if ( failPercent > tc1Threshold) f"Percent of records outside operational window that have an ACTIVE message : ${failPercent*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ###test3

// COMMAND ----------

//infants who are not associated with an adult
//
val tc1Threshold = 0.01

val results = spark.sql(s"""
  select
    count(*) countInfants,
    count_if(!array_contains(allAssoPaxTypes,"ADULT")) countLonelyInfants,
    count_if(!array_contains(allAssoPaxTypes,"ADULT"))/count(*) percentLonelyInfants
  from (
    select a.PASSENGER_ID, max(a.passenger_type) paxType, collect_set(c.PASSENGER_TYPE) allAssoPaxTypes
    from $dbName.fact_passenger a
    left join $dbName.asso_passenger_association b
    on a.PASSENGER_ID = b.PASSENGER_ID
    left join $dbName.fact_passenger c
    on b.PASSENGER_ASSO_ID = c.PASSENGER_ID
    where a.load_date > to_date($minTime)
    and a.PASSENGER_TYPE in ("INFANT", "INFANT_WITH_SEAT")
    group by a.PASSENGER_ID
  )
""").first().toString.replace("[","").replace("]","").split(",")

val (totalCount, failCount, failPercent) = (results(0).toLong, results(1).toLong, results(2).toFloat)


testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 3 : All infants should be associated to an adult",
  failPercent < tc1Threshold,
  failCount,
  totalCount,
  failPercent,
  if ( failPercent > tc1Threshold) f"Percent of infants not associated to an adult : ${failPercent*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ###test4

// COMMAND ----------

//children who are not associated with an adult
val tc1Threshold = 0.01

val results = spark.sql(s"""
  select
    count(*) countInfants,
    count_if(!array_contains(allAssoPaxTypes,"ADULT")) countLonelyInfants,
    count_if(!array_contains(allAssoPaxTypes,"ADULT"))/count(*) percentLonelyInfants
  from (
    select a.PASSENGER_ID, max(a.passenger_type) paxType, collect_set(c.PASSENGER_TYPE) allAssoPaxTypes
    from $dbName.fact_passenger a
    left join $dbName.asso_passenger_association b
    on a.PASSENGER_ID = b.PASSENGER_ID
    left join $dbName.fact_passenger c
    on b.PASSENGER_ASSO_ID = c.PASSENGER_ID
    where a.load_date > to_date($minTime)
    and a.PASSENGER_TYPE in ("CHILD")
    group by a.PASSENGER_ID
  )
""").first().toString.replace("[","").replace("]","").split(",")

val (totalCount, failCount, failPercent) = (results(0).toLong, results(1).toLong, results(2).toFloat)

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 4 : Most children should be associated to an adult",
  failPercent < tc1Threshold,
  failCount,
  totalCount,
  failPercent,
  if ( failPercent > tc1Threshold) f"Percent of children not associated to an adult : ${failPercent*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ##write to DB

// COMMAND ----------


val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")