@startuml star-schema_PNR_04Ticketing_v1_0_0

' Header
hide circle
hide <<assotable>> stereotype
hide <<dimtable>> stereotype
hide <<maintable>> stereotype
hide <<mainsubdomain>> stereotype
hide methods
left to right direction

!define TABLE_GRADIENT_BACKGROUND #F2F2F2-fcffd6
!define MAIN_TABLE_GRADIENT_HEADER Orange-%lighten("Yellow", 60)

!function $pk($content)
!return "<color:#ff0000>" + $content + "</color>"
!endfunction

!function $fk($content)
!return "<color:#0000ff>" + $content + "</color>"
!endfunction

skinparam {
    DefaultFontName Monospaced
    ranksep 250
    linetype polyline
    tabSize 4
    HyperlinkUnderline false
    HyperlinkColor #0000ff
}

skinparam frame {
    FontSize 28
    FontSize<<mainsubdomain>> 34
    BorderThickness<<mainsubdomain>> 3
}

skinparam class {
    BackgroundColor TABLE_GRADIENT_BACKGROUND
    HeaderBackgroundColor %lighten("Crimson", 40)
    HeaderBackgroundColor<<maintable>> MAIN_TABLE_GRADIENT_HEADER
    HeaderBackgroundColor<<dimtable>> LightBlue
    HeaderBackgroundColor<<assotable>> %lighten("LimeGreen", 30)
    ColorArrowSeparationSpace Red
    BorderColor Black
    BorderColor<<maintable>> MediumBlue
    BorderThickness<<maintable>> 3
    ArrowColor Blue
    FontSize 16
    FontSize<<maintable>> 20
    FontStyle Bold
}

' Frames
frame "Ticketing"<<mainsubdomain>> #FFFEB0 {

entity "FACT_AUTOMATED_PROCESS_HISTO" {
$pk("AUTOMATED_PROCESS_ID           Binary     NN  PK")
REFERENCE_KEY                  String     NN    
CODE                           String           
SOURCE                         String           
APPLICABLE_CARRIER             String           
ACTION_DATETIME_UTC            Timestamp        
OFFICE_IDENTIFIER              String           
QUEUE_CATEGORY                 String           
QUEUE_NUMBER                   String           
IS_APPLICABLE_TO_INFANTS       Boolean          
DOCUMENT_DELIVERY_OPTIONS      String           
TEXT                           String           
CANCELLATION_RULES_IDENTIFIER  String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
$fk("[[#{DIM_AIRLINE} APPLICABLE_CARRIER_ID]]          Binary         FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "FACT_TRAVEL_DOCUMENT_HISTO"<<maintable>> {
$pk("TRAVEL_DOCUMENT_ID             Binary     NN  PK")
REFERENCE_KEY                  String     NN    
DOCUMENT_NUMBER_CONSOLIDATED   String           
DOCUMENT_NUMBER                String           
PRIMARY_DOCUMENT_NUMBER        String           
DOCUMENT_TYPE                  String           
DOCUMENT_STATUS                String           
ASSOCIATION_STATUS             String           
NUMBER_OF_BOOKLETS             Integer          
TOTAL_AMOUNT                   Float            
CURRENCY                       String           
TOTAL_AMOUNT_ORIGINAL          Float            
CURRENCY_ORIGINAL              String           
EXCHANGE_RATE_DATE_NEEDED      Date             
EXCHANGE_RATE_DATE_TAKEN       Date             
IS_INFANT                      Boolean          
BLACKLIST_CATEGORY             String           
TICKETING_REFERENCE_TYPE       String           
TICKETING_REFERENCE_STATUS     String           
TICKETING_REFERENCE_TEXT       String           
CREATION_DATETIME_UTC          Timestamp        
CREATION_OFFICE_IDENTIFIER     String           
CREATION_OFFICE_IATA_NUMBER    String           
CREATION_OFFICE_SYSTEM_CODE    String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]      Binary         FK")
$fk("[[#{DIM_BOOKING_STATUS} TICKETING_REFERENCE_STATUS_ID]]  Binary         FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "FACT_COUPON_HISTO" {
$pk("COUPON_ID                        Binary     NN  PK")
REFERENCE_KEY                    String     NN    
COUPON_NUMBER_CONSOLIDATED       Integer          
COUPON_NUMBER                    Integer          
SEQUENCE_NUMBER                  Integer          
REASON_FOR_ISSUANCE_CODE         String           
REASON_FOR_ISSUANCE_SUB_CODE     String           
REASON_FOR_ISSUANCE_DESCRIPTION  String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                   Binary     NN  FK")
$fk("[[#{Ticketing.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]               Binary     NN  FK")
$fk("[[#{DIM_REASON_FOR_ISSUANCE} REASON_FOR_ISSUANCE_ID]]           Binary         FK")
RECORD_LOCATOR                   String     NN    
PNR_CREATION_DATE                Timestamp  NN    
$pk("VERSION                          Integer    NN  PK")
DATE_BEGIN                       Timestamp  NN    
DATE_END                         Timestamp        
IS_LAST_VERSION                  Boolean          
LOAD_DATE                        Timestamp        
}
}

frame "Reservation" #E4C7FF {

entity "FACT_RESERVATION_HISTO"<<maintable>> {
RESERVATION_ID                 Binary     NN    
REFERENCE_KEY                  String     NN    
RECORD_LOCATOR                 String     NN    
PNR_PROPERTIES                 String           
NUMBER_IN_PARTY                Integer          
GROUP_SIZE                     Integer          
GROUP_NAME                     String           
GROUP_SIZE_TAKEN               Integer          
GROUP_SIZE_REMAINING           Integer          
OWNER_OFFICE_IDENTIFIER        String           
CREATION_DATETIME_UTC          Timestamp  NN    
CREATION_OFFICE_IDENTIFIER     String           
CREATION_COMMENT               String           
LAST_UPDATE_DATETIME_UTC       Timestamp        
LAST_UPDATE_OFFICE_IDENTIFIER  String           
LAST_UPDATE_COMMENT            String           
QUEUING_OFFICE_IDENTIFIER      String           
$fk("[[#{DIM_POINT_OF_SALE} OWNER_POINT_OF_SALE_ID]]         Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]      Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} LAST_UPDATE_POINT_OF_SALE_ID]]   Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} QUEUING_POINT_OF_SALE_ID]]       Binary         FK")
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}
}

frame "Segment Booking" #C8FFC0 {

entity "FACT_AIR_SEGMENT_PAX_HISTO"<<maintable>> {
$pk("AIR_SEGMENT_PAX_ID                     Binary     NN  PK")
REFERENCE_KEY                          String     NN    
DEPARTURE_AIRPORT                      String           
DEPARTURE_TERMINAL                     String           
ARRIVAL_AIRPORT                        String           
ARRIVAL_TERMINAL                       String           
DEPARTURE_DATETIME_UTC                 Timestamp        
DEPARTURE_DATETIME_LOCAL               Timestamp        
ARRIVAL_DATETIME_UTC                   Timestamp        
ARRIVAL_DATETIME_LOCAL                 Timestamp        
MKT_CARRIER                            String           
MKT_FLIGHT_NUMBER                      String           
MKT_OPERATIONAL_SUFFIX                 String           
OPE_CARRIER                            String           
OPE_FLIGHT_NUMBER                      String           
OPE_OPERATIONAL_SUFFIX                 String           
BOOKING_STATUS                         String           
MKT_CABIN                              String           
MKT_BOOKING_CLASS                      String           
MKT_BOOKING_SUB_CLASS                  String           
MKT_LEVEL_OF_SERVICE                   String           
MKT_OVERBOOK_REASON                    String           
MKT_CABIN_BID_PRICE_AMOUNT_ORIGINAL    Float            
MKT_CABIN_BID_PRICE_CURRENCY_ORIGINAL  String           
OPE_CABIN                              String           
OPE_BOOKING_CLASS                      String           
OPE_BOOKING_SUB_CLASS                  String           
OPE_LEVEL_OF_SERVICE                   String           
OPE_OVERBOOK_REASON                    String           
OPE_CODESHARE_AGREEMENT                String           
OPE_CABIN_BID_PRICE_AMOUNT_ORIGINAL    Float            
OPE_CABIN_BID_PRICE_CURRENCY_ORIGINAL  String           
IS_INFORMATIONAL                       Boolean          
IS_DOMINANT_IN_MARRIAGE                Boolean          
IS_OPEN_SEGMENT                        Boolean          
AIRCRAFT_TYPE                          String           
CREATION_DATETIME_UTC                  Timestamp        
CREATION_OFFICE_IDENTIFIER             String           
CREATION_COMMENT                       String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                         Binary     NN  FK")
AIR_SEGMENT_ID                         Binary     NN    
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} TRAVELER_ID]]                            Binary     NN  FK")
$fk("[[#{DIM_BOOKING_STATUS} BOOKING_STATUS_ID]]                      Binary         FK")
$fk("[[#{DIM_AIRPORT} DEPARTURE_AIRPORT_ID]]                   Binary         FK")
$fk("[[#{DIM_AIRPORT} ARRIVAL_AIRPORT_ID]]                     Binary         FK")
$fk("[[#{DIM_AIRLINE} MKT_CARRIER_ID]]                         Binary         FK")
$fk("[[#{DIM_AIRLINE} OPE_CARRIER_ID]]                         Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]              Binary         FK")
RECORD_LOCATOR                         String     NN    
PNR_CREATION_DATE                      Timestamp  NN    
$pk("VERSION                                Integer    NN  PK")
DATE_BEGIN                             Timestamp  NN    
DATE_END                               Timestamp        
IS_LAST_VERSION                        Boolean          
LOAD_DATE                              Timestamp        
}
}

frame "Service Booking" #FFC5F9 {

entity "FACT_SERVICE_PAX_HISTO"<<maintable>> {
$pk("SERVICE_PAX_ID                   Binary     NN  PK")
REFERENCE_KEY                    String           
SERVICE_CODE                     String           
SERVICE_SUB_TYPE                 String           
SERVICE_PROVIDER                 String           
TEXT                             String           
STATUS                           String           
IS_CHARGEABLE                    Boolean          
REASON_FOR_ISSUANCE_CODE         String           
REASON_FOR_ISSUANCE_SUB_CODE     String           
REASON_FOR_ISSUANCE_DESCRIPTION  String           
CREATION_DATETIME_UTC            Timestamp        
CREATION_OFFICE_IDENTIFIER       String           
CREATION_COMMENT                 String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                   Binary     NN  FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} TRAVELER_ID]]                      Binary         FK")
$fk("[[#{DIM_SERVICE} SERVICE_ID]]                       Binary         FK")
$fk("[[#{DIM_AIRLINE} SERVICE_PROVIDER_ID]]              Binary         FK")
$fk("[[#{DIM_BOOKING_STATUS} STATUS_ID]]                        Binary         FK")
$fk("[[#{DIM_REASON_FOR_ISSUANCE} REASON_FOR_ISSUANCE_ID]]           Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]        Binary         FK")
RECORD_LOCATOR                   String     NN    
PNR_CREATION_DATE                Timestamp  NN    
$pk("VERSION                          Integer    NN  PK")
DATE_BEGIN                       Timestamp  NN    
DATE_END                         Timestamp        
IS_LAST_VERSION                  Boolean          
LOAD_DATE                        Timestamp        
}

entity "FACT_SEATING_PAX_HISTO" {
$pk("SEATING_PAX_ID                   Binary     NN  PK")
REFERENCE_KEY                    String     NN    
SERVICE_CODE                     String           
SERVICE_SUB_TYPE                 String           
SERVICE_PROVIDER                 String           
SEAT_NUMBER                      String           
SEAT_CHARACTERISTICS             String           
TEXT                             String           
STATUS                           String           
IS_CHARGEABLE                    Boolean          
REASON_FOR_ISSUANCE_CODE         String           
REASON_FOR_ISSUANCE_SUB_CODE     String           
REASON_FOR_ISSUANCE_DESCRIPTION  String           
CREATION_DATETIME_UTC            Timestamp        
CREATION_OFFICE_IDENTIFIER       String           
CREATION_COMMENT                 String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                   Binary     NN  FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} TRAVELER_ID]]                      Binary         FK")
$fk("[[#{DIM_SERVICE} SERVICE_ID]]                       Binary         FK")
$fk("[[#{DIM_AIRLINE} SERVICE_PROVIDER_ID]]              Binary         FK")
$fk("[[#{DIM_BOOKING_STATUS} STATUS_ID]]                        Binary         FK")
$fk("[[#{DIM_REASON_FOR_ISSUANCE} REASON_FOR_ISSUANCE_ID]]           Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]        Binary         FK")
RECORD_LOCATOR                   String     NN    
PNR_CREATION_DATE                Timestamp  NN    
$pk("VERSION                          Integer    NN  PK")
DATE_BEGIN                       Timestamp  NN    
DATE_END                         Timestamp        
IS_LAST_VERSION                  Boolean          
LOAD_DATE                        Timestamp        
}
}

' Free entities
entity "DIM_POINT_OF_SALE"<<dimtable>> {
$pk("POINT_OF_SALE_ID    Binary     NN  PK")
OFFICE_IDENTIFIER   String           
OFFICE_IATA_NUMBER  String           
OFFICE_SYSTEM_CODE  String           
OFFICE_AGENT_TYPE   String           
LOGIN_CITY_CODE     String           
LOGIN_COUNTRY_CODE  String           
LOGIN_NUMERIC_SIGN  String           
LOGIN_INITIALS      String           
LOGIN_DUTY_CODE     String           
LOAD_DATE           Timestamp        
}

entity "DIM_AIRLINE"<<dimtable>> {
$pk("AIRLINE_ID         Binary     NN  PK")
AIRLINE_IATA_CODE  String     NN    
LOAD_DATE          Timestamp        
}

entity "DIM_BOOKING_STATUS"<<dimtable>> {
$pk("BOOKING_STATUS_ID     Binary     NN  PK")
BOOKING_STATUS_CODE   String     NN    
BOOKING_STATUS_LABEL  String           
BOOKING_MACRO_STATUS  String           
BOOKING_STATUS_TYPE   String           
RECORD_SOURCE         String           
LOAD_DATE             Timestamp        
}

entity "DIM_REASON_FOR_ISSUANCE"<<dimtable>> {
$pk("REASON_FOR_ISSUANCE_ID     Binary         PK")
REASON_FOR_ISSUANCE_CODE   String           
REASON_FOR_ISSUANCE_LABEL  String           
RECORD_SOURCE              String           
LOAD_DATE                  Timestamp        
}

entity "ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS_HISTO"<<assotable>> {
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]               Binary     NN  FK")
$fk("[[#{Ticketing.FACT_AUTOMATED_PROCESS_HISTO} AUTOMATED_PROCESS_ID]]             Binary     NN  FK")
REFERENCE_KEY_AIR_SEGMENT_PAX    String     NN    
REFERENCE_KEY_AUTOMATED_PROCESS  String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                   Binary     NN  FK")
RECORD_LOCATOR                   String     NN    
PNR_CREATION_DATE                Timestamp  NN    
$pk("VERSION                          Integer    NN  PK")
DATE_BEGIN                       Timestamp  NN    
DATE_END                         Timestamp        
IS_LAST_VERSION                  Boolean          
LOAD_DATE                        Timestamp        
}

entity "ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX_HISTO"<<assotable>> {
$fk("[[#{Ticketing.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary     NN  FK")
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary     NN  FK")
REFERENCE_KEY_TRAVEL_DOCUMENT  String     NN    
REFERENCE_KEY_AIR_SEGMENT_PAX  String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_TRAVEL_DOCUMENT_SERVICE_PAX_HISTO"<<assotable>> {
$fk("[[#{Ticketing.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary     NN  FK")
$fk("[[#{Service Booking.FACT_SERVICE_PAX_HISTO} SERVICE_PAX_ID]]                 Binary     NN  FK")
REFERENCE_KEY_TRAVEL_DOCUMENT  String     NN    
REFERENCE_KEY_SERVICE_PAX      String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_TRAVEL_DOCUMENT_SEATING_PAX_HISTO"<<assotable>> {
$fk("[[#{Ticketing.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary     NN  FK")
$fk("[[#{Service Booking.FACT_SEATING_PAX_HISTO} SEATING_PAX_ID]]                 Binary     NN  FK")
REFERENCE_KEY_TRAVEL_DOCUMENT  String     NN    
REFERENCE_KEY_SEATING_PAX      String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

' Relationships
FACT_AUTOMATED_PROCESS_HISTO::APPLICABLE_CARRIER_ID --> DIM_AIRLINE::AIRLINE_ID
FACT_TRAVEL_DOCUMENT_HISTO::RESERVATION_ID --> FACT_RESERVATION_HISTO::RESERVATION_ID
FACT_TRAVEL_DOCUMENT_HISTO::CREATION_POINT_OF_SALE_ID --> DIM_POINT_OF_SALE::POINT_OF_SALE_ID
FACT_TRAVEL_DOCUMENT_HISTO::TICKETING_REFERENCE_STATUS_ID --> DIM_BOOKING_STATUS::BOOKING_STATUS_ID
FACT_COUPON_HISTO::TRAVEL_DOCUMENT_ID --> FACT_TRAVEL_DOCUMENT_HISTO::TRAVEL_DOCUMENT_ID
FACT_COUPON_HISTO::REASON_FOR_ISSUANCE_ID --> DIM_REASON_FOR_ISSUANCE::REASON_FOR_ISSUANCE_ID
ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS_HISTO::AUTOMATED_PROCESS_ID --> FACT_AUTOMATED_PROCESS_HISTO::AUTOMATED_PROCESS_ID
ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX_HISTO::TRAVEL_DOCUMENT_ID --> FACT_TRAVEL_DOCUMENT_HISTO::TRAVEL_DOCUMENT_ID
ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_TRAVEL_DOCUMENT_SERVICE_PAX_HISTO::TRAVEL_DOCUMENT_ID --> FACT_TRAVEL_DOCUMENT_HISTO::TRAVEL_DOCUMENT_ID
ASSO_TRAVEL_DOCUMENT_SERVICE_PAX_HISTO::SERVICE_PAX_ID --> FACT_SERVICE_PAX_HISTO::SERVICE_PAX_ID
ASSO_TRAVEL_DOCUMENT_SEATING_PAX_HISTO::TRAVEL_DOCUMENT_ID --> FACT_TRAVEL_DOCUMENT_HISTO::TRAVEL_DOCUMENT_ID
ASSO_TRAVEL_DOCUMENT_SEATING_PAX_HISTO::SEATING_PAX_ID --> FACT_SEATING_PAX_HISTO::SEATING_PAX_ID

@enduml
