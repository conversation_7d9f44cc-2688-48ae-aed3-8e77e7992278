{"version": "[VERSION]", "path": "output/path", "shard": "[SHARD]", "schemaName": "DEFAULT_DB.db", "status": "ACTIVATED", "tables": [{"name": "ASSO_COUPON_SERVICE_DELIVERY_HISTO", "type": "ASSO", "description": "Source Correlation table between DCSPAX and PNR", "gdprZone": "red", "kind": "MATERIALIZED", "columns": [{"name": "COUPON_ID", "description": "CUSTOM DESCRIPTION FOR COUPON_ID", "type": "STRING", "primaryKey": true, "gdprZone": "red", "piiType": "CUSTOM PII TYPE FOR COUPON_ID", "example": "CUSTOM EXAMPLE FOR COUPON_ID", "fkRelationships": [{"schemaName": "TKT_DATABASE_NAME", "tableName": "FACT_COUPON_HISTO", "columnName": "COUPON_ID"}]}, {"name": "SERVICE_DELIVERY_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": true, "gdprZone": "red", "piiType": "", "fkRelationships": [{"schemaName": "[DCSPAX]", "tableName": "FACT_SERVICE_DELIVERY_HISTO", "columnName": "SERVICE_DELIVERY_ID"}]}, {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "", "fkRelationships": []}, {"name": "REFERENCE_KEY_COUPON", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "", "fkRelationships": []}, {"name": "REFERENCE_KEY_PASSENGER", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "", "fkRelationships": []}, {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}, {"name": "VERSION_TRAVEL_DOCUMENT", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [{"schemaName": "TKT_DATABASE_NAME", "tableName": "FACT_COUPON_HISTO", "columnName": "VERSION"}]}, {"name": "VERSION_PASSENGER", "description": "", "type": "STRING", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [{"schemaName": "[DCSPAX]", "tableName": "FACT_SERVICE_DELIVERY_HISTO", "columnName": "VERSION"}]}, {"name": "DATE_BEGIN", "description": "", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}, {"name": "DATE_END", "description": "", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}, {"name": "IS_LAST_VERSION", "description": "", "type": "BOOLEAN", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "ASSO_COUPON_SERVICE_DELIVERY", "type": "ASSO", "description": "Latest view of ASSO_COUPON_SERVICE_DELIVERY_HISTO", "gdprZone": "red", "kind": "VIEW", "query": "SELECT COUPON_ID,SERVICE_DELIVERY_ID,REFERENCE_KEY_TRAVEL_DOCUMENT,REFERENCE_KEY_COUPON,REFERENCE_KEY_PASSENGER,REFERENCE_KEY_SERVICE_DELIVERY,VERSION_TRAVEL_DOCUMENT,VERSION_PASSENGER FROM ASSO_COUPON_SERVICE_DELIVERY_HISTO WHERE IS_LAST_VERSION=true", "columns": [{"name": "COUPON_ID", "description": "CUSTOM DESCRIPTION FOR COUPON_ID", "type": "STRING", "primaryKey": true, "gdprZone": "red", "piiType": "CUSTOM PII TYPE FOR COUPON_ID", "example": "CUSTOM EXAMPLE FOR COUPON_ID", "fkRelationships": [{"schemaName": "TKT_DATABASE_NAME", "tableName": "FACT_COUPON", "columnName": "COUPON_ID"}]}, {"name": "SERVICE_DELIVERY_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": true, "gdprZone": "red", "piiType": "", "fkRelationships": [{"schemaName": "[DCSPAX]", "tableName": "FACT_SERVICE_DELIVERY", "columnName": "SERVICE_DELIVERY_ID"}]}, {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "", "fkRelationships": []}, {"name": "REFERENCE_KEY_COUPON", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "", "fkRelationships": []}, {"name": "REFERENCE_KEY_PASSENGER", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "red", "piiType": "", "fkRelationships": []}, {"name": "REFERENCE_KEY_SERVICE_DELIVERY", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}, {"name": "VERSION_TRAVEL_DOCUMENT", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}, {"name": "VERSION_PASSENGER", "description": "", "type": "STRING", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}]}