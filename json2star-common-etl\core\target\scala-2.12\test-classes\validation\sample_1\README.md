Goal: integration test to verify the generators with the mapping and latest addons

The mapping file contain these tables:
- FACT_PASSENGER: it is the latest table of FACT_PASSENGER_HISTO
- FACT_PASSENGER_HISTO

Note: the order of tables and columns is kept in the generated views

The schema is consolidated using the Table Addons Logic

The goal is to generate the following validations tests for this scenario
 - no dupes checks
 - all processed checks

