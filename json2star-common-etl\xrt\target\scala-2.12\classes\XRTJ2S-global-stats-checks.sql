-- Databricks notebook source
--The threshold value can be set individually for each validation test.
SET db.task = "XRTJ2S-global-stats-checks.sql";
SET db.validation_database = ${valDatabase};
SET db.validation_table = ${valTableName};
SET db.default_threshold = 0;
SET db.now_datetime = current_timestamp();
SET db.table_fields = (domain, domain_version, customer, phase, test_time, task, covered_functional_case,status,nb_failed_records,nb_total_records,fail_ratio,result_details);


-- COMMAND ----------

--test 1
--set variables for this test
SET db.threshold_min = 190;
SET db.threshold_max = 250;
SET db.func_case = "Test 1 - Check the distinct number of currencies";
SET db.result_details = CONCAT('The number of distinct currencies  is less than ', ${db.threshold_min}, ' or higher than ', ${db.threshold_max},'. ');


WITH dist_CURR AS (
    select count(DISTINCT (from_currency)) as from_currencies_count,
        count(DISTINCT (to_currency))   as to_currencies_count
        from ${DB_NAME}.exchange_rate_histo
)

--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select "${DOMAIN}", "${DOMAIN_VERSION}", "${CUSTOMER}", "${PHASE}", ${db.now_datetime}, ${db.task}, ${db.func_case},
       (
        (from_currencies_count > ${db.threshold_min})
         AND (from_currencies_count < ${db.threshold_max})
         AND (to_currencies_count > ${db.threshold_min})
         AND (to_currencies_count < ${db.threshold_max})
        )    as status,
       null, null, null,
    if((from_currencies_count < ${db.threshold_min}) OR (from_currencies_count > ${db.threshold_max}) OR
          (to_currencies_count < ${db.threshold_min}) OR (to_currencies_count > ${db.threshold_max}),
          CONCAT(${db.result_details}, 'From: ', from_currencies_count, ', To: ', to_currencies_count),
          CONCAT('From: ', from_currencies_count, ', To: ', to_currencies_count)) AS result_details
from dist_CURR;

-- COMMAND ----------

--test 2
-- If no data have been created in the past 3 days, raise an error
-- This test doesn't write to the validation tracking table as it is only supposed to raise an error if the condition is met
select  raise_error('No data created in exchange_rate_histo for 3 days !!') from (
    select count(*) as cpt from ${DB_NAME}.exchange_rate_histo
    where REFERENCE_DATE >= date_sub(now(), 3)
)
where cpt = 0