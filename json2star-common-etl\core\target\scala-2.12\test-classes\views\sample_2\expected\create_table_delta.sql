CREATE TABLE IF NOT EXISTS MY_DB.ASSO_AIR_SEGMENT_PAX_COUPON_HISTO (
	AIR_SEGMENT_PAX_ID STRING,
	COUPON_ID STRING,
	VERSION_PNR BIGINT NOT NULL,
	VERSION_TRAVEL_DOCUMENT BIGINT NOT NULL,
	<PERSON><PERSON><PERSON>_<PERSON>EGIN TIMESTAMP,
	DATE_END TIMESTAMP,
	IS_LAST_VERSION BOOLEAN
)
USING DELTA PARTITIONED BY(IS_LAST_VERSION)
TBLPROPERTIES(delta.autoOptimize.optimizeWrite = true, delta.autoOptimize.autoCompact = true, delta.enableChangeDataFeed = true);

CREATE VIEW IF NOT EXISTS MY_DB.ASSO_AIR_SEGMENT_PAX_COUPON
AS SELECT AIR_SEGMENT_PAX_ID,COUPON_ID,VERSION_PNR,VERSION_TRAVEL_DOCUMENT FROM ASSO_AIR_SEGMENT_PAX_COUPON_HISTO WHERE IS_LAST_VERSION=true
;
