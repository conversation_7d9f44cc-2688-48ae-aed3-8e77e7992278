{
  "ruleset": {
    "data-dictionary": {
      "json-to-yaml-paths": {
        "$.mainResource.current.image": "DcsPassengerPush.processedDcsPassenger"
      }
    }
  },
  "tables": [
    {
        "name": "FACT_PASSENGER_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["PASSENGER_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "CPR_FEED_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.cprFeedType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "ETAG", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.etag"}]}},
          {"name": "GROUP_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.groupName"}]}},
          {"name": "IS_MASTER_RECORD", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isMasterRecord"}]}},
          {"name": "IS_SAME_PHYSICAL_CUSTOMER", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isSamePhysicalCustomer"}]}},
          {"name": "IS_SYSTEM_MARKED_SPC", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isSystemMarkedSPC"}]}},
          {"name": "BIRTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.passenger.dateOfBirth"}]}},
          {"name": "BIRTH_PLACE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.placeOfBirth"}]}},
          {"name": "PASSENGER_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.passengerType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "GENDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.gender"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_GENDER"}]}},
          {"name": "NATIONALITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.nationality"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COUNTRY"}]}},
          {"name": "SPECIAL_SEAT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.specialSeat"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.firstName"}]}},
          {"name": "LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.lastName"}]}},
          {"name": "TITLE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.name.title"}]}},
          {"name": "RESIDENCE_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.passenger.countryOfResidence"}]}},
          {"name": "AGE", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.passenger.age"}]}},
          {"name": "STAFF_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.category"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "STAFF_COMPANY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.companyCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "STAFF_COMPANY_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.companyName"}]}},
          {"name": "STAFF_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.id"}]}},
          {"name": "STAFF_BOOKING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.idType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "STAFF_JOINING_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.staff.joiningDate"}]}},
          {"name": "STAFF_RELATIONSHIP", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.staff.relationshipType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "STAFF_RETIREMENT_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.staff.retirementDate"}]}},
          {"name": "STAFF_TRANSFER_DAYS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.staff.transferDays"}]}},
          {"name": "STAFF_TRANSFERS_DURING_DAYS", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.staff.transfersDuringDay"}]}},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master" : {
            "pit-key": "PASSENGER_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        }}
    },
    {
        "name": "FACT_TRAVEL_DOCUMENT_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["TRAVEL_DOCUMENT_ID", "PASSENGER_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}]
          },
          { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedAirTravelDocuments[*]"}, {"atd": "$.associatedDocuments[*]"}]
          }],
        "columns": [
          {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"atd": "$.id"} ]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"atd": "$.id"} ]}},
          {"name": "ASSOCIATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"atd": "$.associationStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_TRAVEL_DOCUMENT_ASSOCIATION_STATUS"}]}},
          {"name": "BLACKLIST_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [ {"atd": "$.blacklistCategory"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "CONJUNCTIVE_DOCUMENT_NUMBERS", "column-type": "strColumn", "sources": {"blocks": [ {"atd": "$.conjunctiveDocumentNumbers[*]"}]}},
          {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"atd": "$.documentType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "PRIMARY_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"atd": "$.primaryDocumentNumber"}]}},
          {"name": "VALIDATING_CARRIER", "column-type": "strColumn", "sources": {"blocks": [ {"atd": "$.validatingCarrierCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "SOURCE_SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_COMPENSATION_VOUCHER_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["COMPENSATION_VOUCHER_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "name": "vouch", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"comp": "$.compensations[*]"}, {"voucher": "$.vouchers[*]"}]}},
          { "name": "compensation", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"comp": "$.compensations[*]"}]}},
        ],
        "columns": [
          {"name": "COMPENSATION_VOUCHER_ID", "column-type": "binaryStrColumn", "sources": {
            "root-specific": [
              {"rs-name": "vouch","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"comp": "$.id"}, {"voucher": "$.id"}]},
              {"rs-name": "compensation","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"comp": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {
            "root-specific": [
              {"rs-name": "vouch","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"comp": "$.id"}, {"voucher": "$.id"}]},
              {"rs-name": "compensation","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"comp": "$.id"}]}
              ]}},
          {"name": "COMPENSATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"comp": "$.id"}]}},
          {"name": "COMPENSATION_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [ {"comp": "$.categoryDescription"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "COMPENSATION_AUTH_AUTHORISER", "column-type": "strColumn", "sources": {"blocks": [ {"comp": "$.authorisation.authorizer"}]}},
          {"name": "COMPENSATION_AUTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [ {"comp": "$.authorisation.date"}]}},
          {"name": "COMPENSATION_AUTH_QUANTITY", "column-type": "intColumn", "sources": {"blocks": [ {"comp": "$.authorisation.quantity"}]}},
          {"name": "COMPENSATION_AUTH_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"comp": "$.authorisation.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COMPENSATION_AUTHORIZATION_STATUS"}]}},
          {"name": "COMPENSATION_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"comp": "$.reason.code"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COMPENSATION_REASON"}]}},
          {"name": "COMPENSATION_REASON_LABEL", "column-type": "strColumn", "sources": {"blocks": [ {"comp": "$.reason.label"}]}},
          {"name": "VOUCHER_IDENTIFIER", "column-type": "strColumn", "sources": {
            "root-specific": [{"rs-name": "vouch","blocks": [ {"voucher": "$.id"}]}]}},
          {"name": "VOUCHER_COMMENT", "column-type": "strColumn", "sources": {
            "root-specific": [{"rs-name": "vouch","blocks": [ {"voucher": "$.comment"}]}]}},
          {"name": "VOUCHER_DESCRIPTION", "column-type": "strColumn", "sources": {
            "root-specific": [{"rs-name": "vouch","blocks": [ {"voucher": "$.description"}]}]}},
          {"name": "VOUCHER_IS_REPRINTABLE", "column-type": "booleanColumn", "sources": {
            "root-specific": [{"rs-name": "vouch","blocks": [ {"voucher": "$.isReprintable"}]}]}},
          {"name": "VOUCHER_PRINT_STATUS", "column-type": "strColumn", "sources": {
            "root-specific": [{"rs-name": "vouch","blocks": [ {"voucher": "$.printStatus"}]}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "VOUCHER_PROVIDER", "column-type": "strColumn", "sources": {
            "root-specific": [{"rs-name": "vouch","blocks": [ {"voucher": "$.providerCode"}]}]}},
          {"name": "VOUCHER_STATUS", "column-type": "strColumn", "sources": {
            "root-specific": [{"rs-name": "vouch","blocks": [ {"voucher": "$.status"}]}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "VOUCHER_TYPE", "column-type": "strColumn", "sources": {
            "root-specific": [{"rs-name": "vouch","blocks": [ {"voucher": "$.voucherType"}]}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_CONTACT_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["CONTACT_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "name": "pax", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"contact": "$.passenger.contacts[*]"}]}},
          { "name": "segDel", "rs" :{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"contact": "$.contacts[*]"}]}}
        ],
        "columns": [
          {"name": "IS_DECLINED", "column-type": "booleanColumn", "sources": {"blocks": [ {"contact": "$.isDeclined"}]}},
          {"name": "CARRIER", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.carrierCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "PURPOSES", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.purpose[*]"}]}},
          {"name": "ADDRESS_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.address.category"}]}},
          {"name": "ADDRESS_CITY", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.address.cityName"}]}},
          {"name": "ADDRESS_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.address.countryCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COUNTRY"}]}},
          {"name": "ADDRESS_POSTAL_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.address.postalCode"}]}},
          {"name": "ADDRESS_STATE", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.address.stateCode"}]}},
          {"name": "ADDRESS_LINES", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.address.lines[*]"}]}},
          {"name": "ADDRESSEE_FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.addresseeName.firstName"}]}},
          {"name": "ADDRESSEE_LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.addresseeName.lastName"}]}},
          {"name": "ADDRESSEE_TITLE", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.addresseeName.title"}]}},
          {"name": "PHONE", "column-type": "strColumn", "sources": {"blocks": [ {"contact": "$.phone.text"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          {"name": "CONTACT_ID", "column-type": "binaryStrColumn",
            "sources": { "root-specific": [
              {"rs-name": "pax","blocks": [ {"base": "$.id"}, {"contact": "$.id"}, {"contact": "$.purpose"}]},
              {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"contact": "$.id"}, {"contact": "$.purpose"}]}
          ]}, "expr": "hashM({0})"},

          {"name": "REFERENCE_KEY", "column-type": "strColumn",
            "sources": { "root-specific": [
              {"rs-name": "pax","blocks": [ {"base": "$.id"}, {"contact": "$.id"}, {"contact": "$.purpose"}]},
              {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"contact": "$.id"}, {"contact": "$.purpose"}]}
          ]}},
          {"name": "RELATES_TO", "column-type": "strColumn",
            "sources": { "root-specific": [
              {"rs-name": "pax","literal": "PASSENGER"},
              {"rs-name": "segDel","literal": "SEGMENT_DELIVERY"}
          ]}},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
            "sources": { "root-specific": [
              {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}
          ]}, "expr": "hashM({0})"}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_COUPON_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["COUPON_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}, {"coupon": "$.coupons[*]"}]}],
        "columns": [
          {"name": "COUPON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"atd": "$.id"}, {"coupon": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"atd": "$.id"}, {"coupon": "$.id"}]}},
          {"name": "DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"coupon": "$.documentNumber"}]}},
          {"name": "NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"coupon": "$.number"}]}},
          {"name": "STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"coupon": "$.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COUPON_STATUS"}]}},
          {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"coupon": "$.reasonForIssuance.code"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REASON_FOR_ISSUANCE_CODE"}]}},
          {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [ {"coupon": "$.reasonForIssuance.rfiscDescription"}]}},
          {"name": "REASON_FOR_ISSUANCE_SUBCODE", "column-type": "strColumn", "sources": {"blocks": [ {"coupon": "$.reasonForIssuance.subCode"}]}},
          {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"atd": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          {"name": "REASON_FOR_ISSUANCE_SUBCODE_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [ {"coupon": "$.reasonForIssuance.code"},{"coupon": "$.reasonForIssuance.subCode"},{"coupon": "$.reasonForIssuance.rfiscDescription"}]},
            "fk" : [{"table":"DIM_REASON_FOR_ISSUANCE_SUBCODE"}], "expr": "hashM({0})"
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_FLIGHT_TRANSFER_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["FLIGHT_TRANSFER_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"ftrans": "$.flightTransfers[*]"}]}],
        "columns": [
          {"name": "FLIGHT_TRANSFER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"ftrans": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"ftrans": "$.id"}]}},
          {"name": "DATA_TRANSFER_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"ftrans": "$.dataTransferStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "DCS_TRANSFER_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"ftrans": "$.dcsTransferStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "DISRUPTION_TRANSFER_REASON", "column-type": "strColumn", "sources": {"blocks": [ {"ftrans": "$.disruptionTransferReason[*]"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [ {"ftrans": "$.subType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})", "fk" : [{"table":"DIM_TRANSFER_SUBTYPE"}]}},
          {"name": "TRANSFER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"ftrans": "$.id"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_MEMBERSHIP_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["MEMBERSHIP_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
            { "name": "pax", "rs" :  { "blocks": [ {"base": "$.mainResource.current.image"}, {"freq": "$.frequentFlyer[*]"}]}},
            { "name": "seg", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"freq": "$.frequentFlyer[*]"}]}}
        ],
        "columns": [
          {"name": "APPLICABLE_AIRLINE", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.applicableAirlineCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "CONFIRMATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.confirmationStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_MEMBERSHIP_STATUS"}]}},
          {"name": "CUSTOMER_VALUE", "column-type": "intColumn", "sources": {"blocks": [ {"freq": "$.customerValue"}]}},
          {"name": "MEMBERSHIP_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.frequentFlyerNumber"}]}},
          {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.serviceCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_SERVICE"}]}},
          {"name": "AIRLINE_TIER_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.airlineLevel.code"}]}},
          {"name": "AIRLINE", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.airlineLevel.companyCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "AIRLINE_TIER_NAME", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.airlineLevel.name"}]}},
          {"name": "AIRLINE_TIER_PRIORITY_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.airlineLevel.priorityCode"}]}},
          {"name": "ALLIANCE_TIER_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.allianceLevel.code"}]}},
          {"name": "ALLIANCE", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.allianceLevel.companyCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "ALLIANCE_TIER_NAME", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.allianceLevel.name"}]}},
          {"name": "ALLIANCE_TIER_PRIORITY_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"freq": "$.allianceLevel.priorityCode"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          {"name": "MEMBERSHIP_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "pax", "blocks": [ {"base": "$.id"}, {"freq": "$.id"}]},
                {"rs-name": "seg", "blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"freq": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn",
              "sources": { "root-specific": [
                {"rs-name": "pax","blocks": [ {"base": "$.id"}, {"freq": "$.id"}]},
                {"rs-name": "seg","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"freq": "$.id"}]}
              ]}},
          {"name": "RELATES_TO", "column-type": "strColumn",
            "sources": { "root-specific": [
              {"rs-name": "pax","literal": "PASSENGER"},
              {"rs-name": "seg","literal": "SEGMENT_DELIVERY"}
            ]}},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
              "sources":{ "root-specific": [
                {"rs-name": "seg","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "AIRLINE_TIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"freq": "$.airlineLevel.companyCode"},{"freq": "$.airlineLevel.code"},{"freq": "$.airlineLevel.name"},{"freq": "$.airlineLevel.priorityCode"}]}, "expr": "hashM({0})",
            "fk" : [{"table":"DIM_MEMBERSHIP_TIER"}]
          },
          {"name": "ALLIANCE_TIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"freq": "$.allianceLevel.companyCode"},{"freq": "$.allianceLevel.code"},{"freq": "$.allianceLevel.name"},{"freq": "$.allianceLevel.priorityCode"}]}, "expr": "hashM({0})",
            "fk" : [{"table":"DIM_MEMBERSHIP_TIER"}]
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_FLIGHT_SEGMENT_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["FLIGHT_SEGMENT_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
            { "name": "segDel", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"middle": "$.segmentDeliveries[*]"}, {"seg": "$.segment"}]}},
            { "name": "fromFlt","rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"middle": "$.flightTransfers[*]"}, {"seg": "$.fromSegments[*]"}]}},
            { "name": "toFlt",  "rs" : {"blocks": [ {"base": "$.mainResource.current.image"}, {"middle": "$.flightTransfers[*]"}, {"seg": "$.toSegments[*]"}]}}
        ],
        "columns": [
          {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"middle": "$.id"}, {"seg": "$.id"},{"seg": "$.carrierCode"}, {"seg": "$.number"},{"seg": "$.suffix"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"middle": "$.id"}, {"seg": "$.id"},{"seg": "$.carrierCode"}, {"seg": "$.number"},{"seg": "$.suffix"}]}},
          {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.cabin"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_CABIN"}]}},
          {"name": "BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.class"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_BOOKING_CLASS"}]}},
          {"name": "MKT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.carrierCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "DURATION", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.duration"}]}},
          {"name": "MKT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.number"}]}},
          {"name": "BOOKING_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_BOOKING_STATUS"}]}},
          {"name": "MKT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.suffix"}]}},
          {"name": "ARRIVAL_DATETIME", "column-type": "timestampColumn", "sources": {"blocks": [ {"seg": "$.arrival.at"}]}},
          {"name": "ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.arrival.iataCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT"}]}},
          {"name": "DEPARTURE_DATETIME", "column-type": "timestampColumn", "sources": {"blocks": [ {"seg": "$.departure.at"}]}},
          {"name": "DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.departure.iataCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT"}]}},
          {"name": "OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.operating.carrierCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.operating.number"}]}},
          {"name": "OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [ {"seg": "$.operating.suffix"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          { "name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
            "sources": { "root-specific": [
              {"rs-name": "segDel", "blocks" : [ {"base": "$.id"}, {"middle": "$.id"}]}
            ]}, "expr": "hashM({0})"},
          {"name": "RELATES_TO", "column-type": "strColumn",
            "sources": { "root-specific": [
              {"rs-name": "segDel", "literal": "SEGMENT_DELIVERY"},
              {"rs-name": "fromFlt", "literal": "TRANSFER_FROM"},
              {"rs-name": "toFlt", "literal": "TRANSFER_TO"}
            ]}},
          {"name": "FLIGHT_TRANSFER_FROM_ID", "column-type": "binaryStrColumn",
            "sources": { "root-specific": [
              {"rs-name": "fromFlt", "blocks" : [ {"base": "$.id"}, {"middle": "$.id"}]}
            ]}, "expr": "hashM({0})"},
          {"name": "FLIGHT_TRANSFER_TO_ID", "column-type": "binaryStrColumn",
            "sources": { "root-specific": [
              {"rs-name": "toFlt", "blocks" : [ {"base": "$.id"}, {"middle": "$.id"}]}
            ]}, "expr": "hashM({0})"}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_IDENTITY_DOCUMENT_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["IDENTITY_DOCUMENT_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
            { "name": "servDoc", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"},  {"serv": "$.services[*]"}, {"doc": "$.document"}]}},
            { "name": "segServDoc", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "segmentDeliveries[*]"}, {"serv": "$.services[*]"}, {"doc": "$.document"}]}},
            { "name": "segLegSeatDoc", "rs" : {  "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "segmentDeliveries[*]"},  {"legdel": "$.legDeliveries[*]"}, {"serv": "$.seating.chargeableSeat"}, {"doc": "$.document"}]}},
            { "name": "segRegDoc", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"regDoc": "$.regulatoryDocuments[*]"}, {"doc": "$.document"}]}},
            { "name": "regDoc", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"regDoc": "$.regulatoryDocuments[*]"}, {"doc": "$.document"}]}}
        ],
        "columns": [
          {"name": "NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.number"}]}},
          {"name": "AGE", "column-type": "intColumn", "sources": {"blocks": [ {"doc": "$.age"}]}},
          {"name": "BIRTH_DATE", "column-type": "dateColumn", "sources": {"blocks": [ {"doc": "$.birthDate"}]}},
          {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.documentType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_IDENTITY_DOCUMENT_TYPE"}]}},
          {"name": "EFFECTIVE_DATE", "column-type": "dateColumn", "sources": {"blocks": [ {"doc": "$.effectiveDate"}]}},
          {"name": "EXPIRY_DATE", "column-type": "dateColumn", "sources": {"blocks": [ {"doc": "$.expiryDate"}]}},
          {"name": "GENDER", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.gender"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_GENDER"}]}},
          {"name": "ISSUANCE_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.issuanceCountry"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COUNTRY"}]}},
          {"name": "ISSUANCE_DATE", "column-type": "dateColumn", "sources": {"blocks": [ {"doc": "$.issuanceDate"}]}},
          {"name": "ISSUANCE_LOCATION", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.issuanceLocation"}]}},
          {"name": "VALIDITY_DATE", "column-type": "dateColumn", "sources": {"blocks": [ {"doc": "$.validToDate"}]}},
          {"name": "FIRST_NAME", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.name.firstName"}]}},
          {"name": "LAST_NAME", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.name.lastName"}]}},
          {"name": "TITLE", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.name.title"}]}},
          {"name": "REMARKS", "column-type": "strColumn", "sources": {"blocks": [ {"doc": "$.remarks[*]"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          {"name": "IDENTITY_DOCUMENT_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "servDoc","blocks": [ {"base": "$.id"},  {"serv": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
                {"rs-name": "segServDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"serv": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
                {"rs-name": "segLegSeatDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
                {"rs-name": "segRegDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
                {"rs-name": "regDoc","blocks": [ {"base": "$.id"},  {"regDoc": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn",
              "sources": { "root-specific": [
                {"rs-name": "servDoc","blocks": [ {"base": "$.id"},  {"serv": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
                {"rs-name": "segServDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"serv": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
                {"rs-name": "segLegSeatDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
                {"rs-name": "segRegDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]},
                {"rs-name": "regDoc","blocks": [ {"base": "$.id"},  {"regDoc": "$.id"}, {"doc": "$.issuanceCountry"}, {"doc": "$.number"}]}
              ]}},
          {"name": "SERVICE_DELIVERY_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "servDoc","blocks": [ {"base": "$.id"},  {"serv": "$.id"}]},
                {"rs-name": "segServDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"serv": "$.id"}]},
                {"rs-name": "segLegSeatDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "RELATES_TO", "column-type": "strColumn",
            "sources": { "root-specific": [
              {"rs-name": "servDoc","literal": "SERVICE_DOCUMENT"},
              {"rs-name": "segServDoc","literal": "SEGMENT_SERVICE_DOCUMENT"},
              {"rs-name": "segLegSeatDoc","literal": "SEGMENT_LEG_SEATSERVICE_DOCUMENT"},
              {"rs-name": "segRegDoc","literal": "SEGMENT_REGULATORY_DOCUMENT"},
              {"rs-name": "regDoc","literal": "REGULATORY_DOCUMENT"}
            ]}},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "segServDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]},
                {"rs-name": "segLegSeatDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]},
                {"rs-name": "segRegDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "segLegSeatDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "REGULATORY_DOCUMENT_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "segRegDoc","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}]},
                {"rs-name": "regDoc","blocks": [ {"base": "$.id"},  {"regDoc": "$.id"}]}
              ]}, "expr": "hashM({0})"}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_LEG_DELIVERY_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["LEG_DELIVERY_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}]}],
        "columns": [
          {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}},
          {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.travelCabinCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_CABIN"}]}},
          {"name": "ACCEPTANCE_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.acceptanceType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_ACCEPTANCE_TYPE"}]}},
          {"name": "ACCEPTANCE_CANCELLATION_REASON", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.cancellationReason"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_CANCELLATION_REASON"}]}},
          {"name": "ACCEPTANCE_CHANNEL", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.channel"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_ACCEPTANCE_CHANNEL"}]}},
          {"name": "ACCEPTANCE_FORCE_REASON", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.forceAcceptanceReason"}]}},
          {"name": "ACCEPTANCE_IS_ADVANCE_ACCEPTED", "column-type": "booleanColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.isAdvanceAccepted"}]}},
          {"name": "ACCEPTANCE_IS_FORCE_ACCEPTED", "column-type": "booleanColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.isForceAccepted"}]}},
          {"name": "ACCEPTANCE_IS_FROZEN", "column-type": "booleanColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.isFrozen"}]}},
          {"name": "ACCEPTANCE_PHYSICAL_LOCATION", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.physicalAcceptanceLocation"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_ACCEPTANCE_PHYSICAL_LOCATION"}]}},
          {"name": "ACCEPTANCE_SECURITY_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.securityNumber"}]}},
          {"name": "ACCEPTANCE_STANDBY_REASON", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.standbyReason"}]}},
          {"name": "ACCEPTANCE_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.acceptance.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_ACCEPTANCE_STATUS"}]}},
          {"name": "ARRIVAL_DATE", "column-type": "dateColumn", "sources": {"blocks": [ {"legdel": "$.arrival.at"}]}},
          {"name": "ARRIVAL_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.arrival.iataCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT"}]}},
          {"name": "DEPARTURE_DATE", "column-type": "dateColumn", "sources": {"blocks": [ {"legdel": "$.departure.at"}]}},
          {"name": "DEPARTURE_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.departure.iataCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT"}]}},
          {"name": "BOARDING_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.boardingStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_BOARDING_STATUS"}]}},
          {"name": "BOARDING_ZONE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.boardingZone"}]}},
          {"name": "BOARDING_ERROR", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.error"}]}},
          {"name": "BOARDING_WARNING", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.warning"}]}},
          {"name": "BOARDING_PRINT_CHANNEL", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.boardingPassPrint.channel"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_BOARDING_PRINT_CHANNEL"}]}},
          {"name": "BOARDING_PRINT_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.boardingPassPrint.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_BOARDING_PRINT_STATUS"}]}},
          {"name": "BOARDING_LOG_DEVICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.deviceId"}]}},
          {"name": "BOARDING_LOG_DEVICE_USER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.deviceUser"}]}},
          {"name": "BOARDING_LOG_LOCAL_TIMESTAMP", "column-type": "timestampColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.localDateTime"}]}},
          {"name": "BOARDING_LOG_UTC_TIMESTAMP", "column-type": "timestampColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.utcDateTime"}]}},
          {"name": "BOARDING_LOG_SOURCE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.logSource"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_DEVICE_TYPE"}]}},
          {"name": "BOARDING_LOG_DEVICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.referenceDeviceType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_LOG_SOURCE"}]}},
          {"name": "BOARDING_LOG_LOC_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.trackedLocation.airportCode"}]}},
          {"name": "BOARDING_LOG_LOC_AREA", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.trackedLocation.areaCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT_AREA"}]}},
          {"name": "BOARDING_LOG_LOC_BUILDING", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.trackedLocation.buildingId"}]}},
          {"name": "BOARDING_LOG_LOC_CITY", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.trackedLocation.cityCode"}]}},
          {"name": "BOARDING_LOG_LOC_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.trackedLocation.description"}]}},
          {"name": "BOARDING_LOG_LOC_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.trackedLocation.identifier"}]}},
          {"name": "BOARDING_LOG_LOC_TERMINAL", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.trackedLocation.terminalId"}]}},
          {"name": "ONLOAD_CABIN", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.onload.cabinCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_CABIN"}]}},
          {"name": "ONLOAD_PRIORITY", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.onload.priority"}]}},
          {"name": "ONLOAD_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.onload.status"}]}},
          {"name": "ONLOAD_EDIT_CABIN", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.onload.edit.cabinCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_CABIN"}]}},
          {"name": "ONLOAD_EDIT_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.onload.edit.reasonCode"}]}},
          {"name": "ONLOAD_EDIT_REASON_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.onload.edit.reasonFreeText"}]}},
          {"name": "OPE_CARRIER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.operatingFlight.carrierCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "OPE_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.operatingFlight.number"}]}},
          {"name": "OPE_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.operatingFlight.suffix"}]}},
          {"name": "REGRADE_DELIVERED_AUTHORISER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeDelivered.authoriserReference"}]}},
          {"name": "REGRADE_DELIVERED_CABIN", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeDelivered.cabinCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_CABIN"}]}},
          {"name": "REGRADE_DELIVERED_PRIORITY", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeDelivered.priority"}]}},
          {"name": "REGRADE_DELIVERED_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeDelivered.reasonCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGRADE_REASON"}]}},
          {"name": "REGRADE_DELIVERED_REASON_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeDelivered.reasonFreeText"}]}},
          {"name": "REGRADE_DELIVERED_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeDelivered.regradeType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGRADE_TYPE"}]}},
          {"name": "REGRADE_DELIVERED_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeDelivered.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGRADE_STATUS"}]}},
          {"name": "REGRADE_DELIVERED_DIRECTION", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeDelivered.direction"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGRADE_DIRECTION"}]}},
          {"name": "REGRADE_PROPOSED_AUTHORISER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeProposed.authoriserReference"}]}},
          {"name": "REGRADE_PROPOSED_CABIN", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeProposed.cabinCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_CABIN"}]}},
          {"name": "REGRADE_PROPOSED_PRIORITY", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeProposed.priority"}]}},
          {"name": "REGRADE_PROPOSED_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeProposed.reasonCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGRADE_REASON"}]}},
          {"name": "REGRADE_PROPOSED_REASON_FREETEXT", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeProposed.reasonFreeText"}]}},
          {"name": "REGRADE_PROPOSED_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeProposed.regradeType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGRADE_TYPE"}]}},
          {"name": "REGRADE_PROPOSED_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeProposed.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGRADE_STATUS"}]}},
          {"name": "REGRADE_PROPOSED_DIRECTION", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.regradeProposed.direction"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGRADE_DIRECTION"}]}},
          {"name": "SEATING_EMERGENCY_EXIT_SUITABILITY", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.seating.emergencyExitSuitability"}]}},
          {"name": "SEATING_IS_CHARGEABLE_SEAT", "column-type": "booleanColumn", "sources": {"blocks": [ {"legdel": "$.seating.isChargeableSeat"}]}},
          {"name": "SEATING_SEAT_PRODUCT_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.seating.seatProductStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_BOOKING_STATUS"}]}},
          {"name": "SEATING_SEAT_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.seating.seatStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_SEAT_STATUS"}]}},
          {"name": "SEATING_PREFERENCES_SEAT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.seating.seatPreferences.number"}]}},
          {"name": "SEATING_PREFERENCES_SEAT_CHARACS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.seating.seatPreferences.characteristicsCodes[*]"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_SEAT_CHARACTERISTICS"}]}},
          {"name": "SEATING_SEAT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.seating.seat.number"}]}},
          {"name": "SEATING_SEAT_CHARACTERISTICS", "column-type": "strColumn", "sources": {"blocks": [ {"legdel": "$.seating.seat.characteristicsCodes[*]"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_SEAT_CHARACTERISTICS"}]}},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          {"name": "BOARDING_LOG_LOC_TERMINAL_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [ {"legdel": "$.boarding.trackingLog.trackedLocation.airportCode"},{"legdel": "$.boarding.trackingLog.trackedLocation.terminalId"}]}, "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT_TERMINAL"}]}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_LEG_DELIVERY_COMMENT_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["LEG_DELIVERY_COMMENT_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"com": "$.comments[*]"}]}],
        "columns": [
          {"name": "LEG_DELIVERY_COMMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"com": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"com": "$.id"}]}},
          {"name": "PRIORITY", "column-type": "strColumn", "sources": {"blocks": [ {"com": "$.priority"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_DELIVERY_COMMENT_PRIORITY"}]}},
          {"name": "TEXT", "column-type": "strColumn", "sources": {"blocks": [ {"com": "$.text"}]}},
          {"name": "USAGE", "column-type": "strColumn", "sources": {"blocks": [ {"com": "$.usage"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_DELIVERY_COMMENT_USAGE"}]}},
          {"name": "DELIVERY_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"com": "$.delivery.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_DELIVERY_COMMENT_STATUS"}]}},
          {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_REGULATORY_CHECK_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["REGULATORY_CHECK_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"check": "$.regulatoryChecks[*]"}, {"status": "$.statuses[*]"}]}],
        "columns": [
          {"name": "REGULATORY_CHECK_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"check": "$.id"}, {"status": "$.id"}, {"check": "$.regulatoryProgram.name"}, {"check": "$.regulatoryProgram.countryCode"}, {"status": "$.statusType"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"check": "$.id"}, {"status": "$.id"}, {"check": "$.regulatoryProgram.name"}, {"check": "$.regulatoryProgram.countryCode"}, {"status": "$.statusType"}]}},
          {"name": "ASSESSMENT_TIME", "column-type": "timestampColumn", "sources": {"blocks": [ {"check": "$.assesmentTime"}]}},
          {"name": "CHECK_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"check": "$.id"}]}},
          {"name": "REGULATORY_PROGRAM_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [ {"check": "$.regulatoryProgram.countryCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COUNTRY"}]}},
          {"name": "REGULATORY_PROGRAM_NAME", "column-type": "strColumn", "sources": {"blocks": [ {"check": "$.regulatoryProgram.name"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGULATORY_PROGRAM"}]}},
          {"name": "STATUS_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.id"}]}},
          {"name": "STATUS_IS_VERIFIED_IDENTIFIER", "column-type": "booleanColumn", "sources": {"blocks": [ {"status": "$.isVerifiedId"}]}},
          {"name": "STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.statusCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGULATORY_CHECK_STATUS_CODE"}]}},
          {"name": "STATUS_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.statusType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGULATORY_CHECK_STATUS_TYPE"}]}},
          {"name": "STATUS_MESSAGE_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.message.code"}]}},
          {"name": "STATUS_MESSAGE_COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.message.commentType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGULATORY_CHECK_COMMENT_CODE"}]}},
          {"name": "STATUS_MESSAGE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.message.description"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGULATORY_CHECK_COMMENT_TYPE"}]}},
          {"name": "OVERRIDE_MESSAGE_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.override.code"}]}},
          {"name": "OVERRIDE_MESSAGE_COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.override.commentType"}]}},
          {"name": "OVERRIDE_MESSAGE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [ {"status": "$.override.description"}]}},
          {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_REGULATORY_DOCUMENT_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["REGULATORY_DOCUMENT_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
            { "name": "segDel", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"regDoc": "$.regulatoryDocuments[*]"}]}},
            { "name": "pax", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"regDoc": "$.regulatoryDocuments[*]"}, {"doc": "$.document"}]}}
        ],
        "columns": [
          {"name": "ENTRY_PORT_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [ {"regDoc": "$.countryOfPortOfEntry"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COUNTRY"}]}},
          {"name": "REGISTRATION_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [ {"regDoc": "$.countryOfRegistration"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COUNTRY"}]}},
          {"name": "IS_BEARER", "column-type": "booleanColumn", "sources": {"blocks": [ {"regDoc": "$.isBearer"}]}},
          {"name": "IS_CARRIED", "column-type": "booleanColumn", "sources": {"blocks": [ {"regDoc": "$.isCarried"}]}},
          {"name": "PRESENTED_AT_PORT", "column-type": "strColumn", "sources": {"blocks": [ {"regDoc": "$.presentedAtPort"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT"}]}},
          {"name": "RECORDED_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"regDoc": "$.recordedDocumentType"}]}},
          {"name": "USED_FOR", "column-type": "strColumn", "sources": {"blocks": [ {"regDoc": "$.usedFor"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REGULATORY_DOCUMENT_USAGE"}]}},
          {"name": "VALID_FOR_CARRIER", "column-type": "strColumn", "sources": {"blocks": [ {"regDoc": "$.validForCarrier"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "INPUT_ENTRY_METHOD", "column-type": "strColumn", "sources": {"blocks": [ {"regDoc": "$.inputSource.entryMethod"}]}},
          {"name": "INPUT_IS_HISTORICAL", "column-type": "booleanColumn", "sources": {"blocks": [ {"regDoc": "$.inputSource.isHistorical"}]}},
          {"name": "INPUT_IS_OCR", "column-type": "booleanColumn", "sources": {"blocks": [ {"regDoc": "$.inputSource.isOCR"}]}},
          {"name": "INPUT_IS_SWIPE_OVERRIDDEN", "column-type": "booleanColumn", "sources": {"blocks": [ {"regDoc": "$.inputSource.isSwipeOverriden"}]}},
          {"name": "INPUT_SCANNED_METHOD", "column-type": "strColumn", "sources": {"blocks": [ {"regDoc": "$.inputSource.scannedMethod"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          {"name": "REGULATORY_DOCUMENT_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}]},
                {"rs-name": "pax","blocks":  [ {"base": "$.id"}, {"regDoc": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn",
              "sources": { "root-specific": [
                {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"regDoc": "$.id"}]},
                {"rs-name": "pax","blocks":  [ {"base": "$.id"}, {"regDoc": "$.id"}]}
              ]}},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "RELATES_TO", "column-type": "strColumn",
            "sources": { "root-specific": [
              {"rs-name": "segDel","literal": "SEGMENT_DELIVERY"},
              {"rs-name": "pax","literal": "PASSENGER"}
            ]}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_REVENUE_INTEGRITY_CHECK_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["REVENUE_INTEGRITY_CHECK_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"ric": "$.revenueIntegrity.checks[*]"}]}],
        "columns": [
          {"name": "REVENUE_INTEGRITY_CHECK_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"ric": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"ric": "$.id"}]}},
          {"name": "CHECK_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"ric": "$.id"}]}},
          {"name": "CHECK_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"ric": "$.checkType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REVENUE_INTEGRITY_CHECK_TYPE"}]}},
          {"name": "CHECK_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"ric": "$.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REVENUE_INTEGRITY_CHECK_STATUS"}]}},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_REVENUE_INTEGRITY_COMMENT_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["REVENUE_INTEGRITY_COMMENT_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"ric": "$.revenueIntegrity.comments[*]"}]}],
        "columns": [
          {"name": "REVENUE_INTEGRITY_COMMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"ric": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"ric": "$.id"}]}},
          {"name": "COMMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"ric": "$.id"}]}},
          {"name": "COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"ric": "$.commentType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REVENUE_INTEGRITY_COMMENT_TYPE"}]}},
          {"name": "COMMENT_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [ {"ric": "$.description"}]}},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_SEGMENT_DELIVERY_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["SEGMENT_DELIVERY_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}]}],
        "columns": [
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}},
          {"name": "UPI", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.id"}]}},
          {"name": "DCS_PRODUCT_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.dcsProductType"}]}},
          {"name": "NATIONALITY", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.nationality"}]}},
          {"name": "RECORD_LOCATOR_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.recordLocator"}]}},
          {"name": "TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.type"}]}},
          {"name": "UCI", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.uniqueCustomerIdentifier"}]}},
          {"name": "VOLUNTEER_DENIED_BOARDING", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.volunteerDeniedBoarding"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "VOLUNTEER_DOWNGRADE", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.volunteerDowngrade"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "DISRUPTION_ORIGINAL_DESTINATION", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.passengerDisruption.originalDestination"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT"}]}},
          {"name": "DISRUPTION_REASONS", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.passengerDisruption.productState.reason[*]"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashM({0})"}},
          {"name": "DISRUPTION_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"segdel": "$.passengerDisruption.productState.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "STAFF_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.staff.category"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_STAFF_CATEGORY"}]}},
          {"name": "STAFF_COMPANY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.staff.companyCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}},
          {"name": "STAFF_COMPANY_NAME", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.staff.companyName"}]}},
          {"name": "STAFF_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.staff.id"}]}},
          {"name": "STAFF_BOOKING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.staff.idType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_STAFF_BOOKING_TYPE"}]}},
          {"name": "STAFF_JOINING_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"segdel": "$.staff.joiningDate"}]}},
          {"name": "STAFF_RELATIONSHIP", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.staff.relationshipType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_STAFF_RELATIONSHIP"}]}},
          {"name": "STAFF_RETIREMENT_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"segdel": "$.staff.retirementDate"}]}},
          {"name": "STAFF_TRANSFER_DAYS", "column-type": "intColumn", "sources": {"blocks": [{"segdel": "$.staff.transferDays"}]}},
          {"name": "STAFF_TRANSFERS_DURING_DAY", "column-type": "intColumn", "sources": {"blocks": [{"segdel": "$.staff.transfersDuringDay"}]}},
          {"name": "ASSO_ACCEPTED_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.acceptedOnwardId"}]}},
          {"name": "ASSO_ALTERNATE_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.alternateOnwardId"}]}},
          {"name": "ASSO_CBBG_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.cbbgId"}]}},
          {"name": "ASSO_DISRUPTED_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.disruptedOnwardId"}]}},
          {"name": "ASSO_EXST_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.exstId"}]}},
          {"name": "ASSO_INFANT_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.infantId"}]}},
          {"name": "ASSO_INFORMATIVE_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.informativeOnwardId"}]}},
          {"name": "ASSO_MISCONNECTED_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.misconnectedOnwardId"}]}},
          {"name": "ASSO_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.onwardId"}]}},
          {"name": "ASSO_TCI_ONWARD_SEGMENT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"segdel": "$.associatedPassengerSegments.tciOnwardId"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "FACT_SERVICE_DELIVERY_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["SERVICE_DELIVERY_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
            { "name": "pax", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"serv": "$.services[*]"}]}},
            { "name": "segDel", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"serv": "$.services[*]"}]}},
            { "name": "segLegSeat", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"serv": "$.seating.chargeableSeat"}]}}
        ],
        "columns": [
          {"name": "SERVICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.id"}]}},
          {"name": "CHARGEABLE_SERVICE_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.chargeableServiceId"}]}},
          {"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.code"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_SERVICE"}]}},
          {"name": "DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.description"}]}},
          {"name": "IS_ISSUANCE_REQUIRED", "column-type": "booleanColumn", "sources": {"blocks": [ {"serv": "$.isIssuanceRequired"}]}},
          {"name": "IS_SUBJECT_TO_QUOTA", "column-type": "booleanColumn", "sources": {"blocks": [ {"serv": "$.isSubjectToQuota"}]}},
          {"name": "NUMBER_IN_PARTY", "column-type": "intColumn", "sources": {"blocks": [ {"serv": "$.nIP"}]}},
          {"name": "PAYMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.paymentStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "QUOTA_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.quotaCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_SERVICE_QUOTA"}]}},
          {"name": "STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.statusCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_BOOKING_STATUS"}]}},
          {"name": "SUB_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.subType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_SERVICE_SUBTYPE"}]}},
          {"name": "ADDRESS_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.address.category"}]}},
          {"name": "ADDRESS_CITY", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.address.cityName"}]}},
          {"name": "ADDRESS_COUNTRY", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.address.countryCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_COUNTRY"}]}},
          {"name": "ADDRESS_POSTAL_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.address.postalCode"}]}},
          {"name": "ADDRESS_STATE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.address.stateCode"}]}},
          {"name": "ADDRESS_LINES", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.address.lines[*]"}]}},
          {"name": "CHARGEABLE_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.chargeableServiceDocument.documentType"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "CHARGEABLE_DOCUMENT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.chargeableServiceDocument.number"}]}},
          {"name": "CHARGEABLE_DOCUMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.chargeableServiceDocument.status"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})"}},
          {"name": "REASON_FOR_ISSUANCE_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.priceCategory.code"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REASON_FOR_ISSUANCE_CODE"}]}},
          {"name": "REASON_FOR_ISSUANCE_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.priceCategory.rfiscDescription"}]}},
          {"name": "REASON_FOR_ISSUANCE_SUBCODE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.priceCategory.subCode"}]}},
          {"name": "SERVICE_PROVIDER", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.serviceProvider.code"}]}},
          {"name": "SERVICE_DESCRIPTION_LABEL", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.text.label"}]}},
          {"name": "SERVICE_DESCRIPTION_VALUE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.text.value"}]}},
          {"name": "WAIVER_AUTHORISER", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.waiver.authoriser"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashM({0})"}},
          {"name": "WAIVER_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.waiver.reasonCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashM({0})"}},
          {"name": "WAIVER_REASON_TEXT", "column-type": "strColumn", "sources": {"blocks": [ {"serv": "$.waiver.reasonText"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          {"name": "SERVICE_DELIVERY_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "pax","blocks": [ {"base": "$.id"}, {"serv": "$.id"}]},
                {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"serv": "$.id"}]},
                {"rs-name": "segLegSeat","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn",
              "sources": { "root-specific": [
                {"rs-name": "pax","blocks": [ {"base": "$.id"}, {"serv": "$.id"}]},
                {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"serv": "$.id"}]},
                {"rs-name": "segLegSeat","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}]}
              ]}},
          {"name": "RELATES_TO", "column-type": "strColumn",
            "sources": { "root-specific": [
              {"rs-name": "pax","literal": "PASSENGER"},
              {"rs-name": "segDel","literal": "SEGMENT_DELIVERY"},
              {"rs-name": "segLegSeat","literal": "LEG_DELIVERY_SEATING"}
            ]}},
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "segLegSeat","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]},
                {"rs-name": "segDel","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                {"rs-name": "segLegSeat","blocks": [ {"base": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "REASON_FOR_ISSUANCE_SUBCODE_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [ {"serv": "$.priceCategory.code"},{"serv": "$.priceCategory.subCode"},{"serv": "$.priceCategory.rfiscDescription"}]},
            "fk" : [{"table":"DIM_REASON_FOR_ISSUANCE_SUBCODE"}], "expr": "hashM({0})"
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "ASSO_PASSENGER_ASSOCIATION_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["PASSENGER_ID", "LINK_ID", "PASSENGER_ASSO_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
            { "blocks": [ {"base": "$.mainResource.current.image"}, {"plink": "$.passengerLinks[*]"}, {"coll": "$.collection[*]"}]}
        ],
        "columns": [
          {"name": "LINK_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"plink": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ASSO_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"coll": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ASSO_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"coll": "$.id"}]}},
          {"name": "ASSO_TYPE", "column-type": "strColumn", "sources": {"blocks": [ {"coll": "$.type"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "ASSO_TRAVEL_DOCUMENT_ASSOCIATION_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["TRAVEL_DOCUMENT_FROM_ID", "TRAVEL_DOCUMENT_TO_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
            { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}, {"asso": "$.associatedDocuments[*]"}]}
        ],
        "columns": [
          {"name": "TRAVEL_DOCUMENT_FROM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"atd": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "TRAVEL_DOCUMENT_TO_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"asso": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "ASSOCIATION_STATUS_FROM", "column-type": "strColumn", "sources": {"blocks": [ {"atd": "$.associationStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_TRAVEL_DOCUMENT_ASSOCIATION_STATUS"}]}},
          {"name": "ASSOCIATION_STATUS_TO", "column-type": "strColumn", "sources": {"blocks": [ {"asso": "$.associationStatus"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_TRAVEL_DOCUMENT_ASSOCIATION_STATUS"}]}},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "ASSO_SEGMENT_DELIVERY_TRAVEL_DOCUMENT_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["TRAVEL_DOCUMENT_ID", "SEGMENT_DELIVERY_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"atd": "$.associatedAirTravelDocuments[*]"}]
          },
          { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedAirTravelDocuments[*]"}, {"atd": "$.associatedDocuments[*]"}]
          }],
        "columns": [
          {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"atd": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
        "name": "ASSO_SEGMENT_DELIVERY_ASSOCIATION_HISTO",
        "mapping": {
        "merge": {
          "key-columns": ["SEGMENT_DELIVERY_FROM_ID", "SEGMENT_DELIVERY_TO_ID", "ASSO_TYPE", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
            { "name": "accOnw", "rs" : {  "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.acceptedOnwardId"}]}},
            { "name": "altOwn", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.alternateOnwardId"}]}},
            { "name": "cbbg", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.cbbgId"}]}},
            { "name": "disOnw", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.disruptedOnwardId"}]}},
            { "name": "exst", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.exstId"}]}},
            { "name": "inft", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.infantId"}]}},
            { "name": "infoOnw", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.informativeOnwardId"}]}},
            { "name": "misOnw", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.misconnectedOnwardId"}]}},
            { "name": "onw", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.onwardId"}]}},
            { "name": "tciOnw", "rs" : { "blocks": [ {"base": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"asso": "$.associatedPassengerSegments.tciOnwardId"}]}}
          ],
        "columns": [
          {"name": "SEGMENT_DELIVERY_FROM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}, {"segdel": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
          {"name": "SEGMENT_DELIVERY_TO_ID", "column-type": "binaryStrColumn",
              "sources": { "root-specific": [
                  { "rs-name": "accOnw", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "altOwn", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "cbbg", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "disOnw", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "exst", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "inft", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "infoOnw", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "misOnw", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "onw", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]},
                  { "rs-name": "tciOnw", "blocks": [ {"base": "$.id"}, {"asso": "$.value"}]}
              ]}, "expr": "hashM({0})"},
          {"name": "ASSO_TYPE", "column-type": "strColumn",
            "sources": { "root-specific": [
              { "rs-name": "accOnw", "literal": "ACCEPTED_ONWARD"},
              { "rs-name": "altOwn", "literal": "ALTERNATE_ONWARD"},
              { "rs-name": "cbbg", "literal": "CBBG"},
              { "rs-name": "disOnw", "literal": "DISRUPTED_ONWARD"},
              { "rs-name": "exst", "literal": "EXST"},
              { "rs-name": "inft", "literal": "INFANT"},
              { "rs-name": "infoOnw", "literal": "INFORMATIVE_ONWARD"},
              { "rs-name": "misOnw", "literal": "MISCONNECTED_ONWARD"},
              { "rs-name": "onw", "literal": "ONWARD"},
              { "rs-name": "tciOnw", "literal": "TCI_ONWARD"}
            ]}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }}
    },
    {
      "name": "FACT_SYSTEM_USER_ACTION_HISTO",
      "mapping": {
      "merge": {
        "key-columns": ["SYSTEM_USER_ACTION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
        },
      "root-sources": [
        { "blocks": [ {"base": "$.mainResource.current.image"}, {"modif": "$.lastModification"}, {"user": "$.user"}]}
      ],
      "columns": [
        {"name": "SYSTEM_USER_ACTION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [ {"base": "$.id"}]}},
        {"name": "TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [ {"modif": "$.triggerEventName"}]}},
        {"name": "USER_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"user": "$.id"}]}},
        {"name": "OFFICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"user": "$.officeId"}]}},
        {"name": "WORKSTATION_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [ {"user": "$.workStation.id"}]}},
        {"name": "WORKSTATION_LOC_CITY", "column-type": "strColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.cityCode"}]}},
        {"name": "WORKSTATION_LOC_AIRPORT", "column-type": "strColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.airportCode"}]}},
        {"name": "WORKSTATION_LOC_TERMINAL", "column-type": "strColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.terminalId"}]}},
        {"name": "WORKSTATION_LOC_AREA", "column-type": "strColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.areaCode"}]}},
        {"name": "WORKSTATION_LOC_BUILDING", "column-type": "strColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.buildingId"}]}},
        {"name": "WORKSTATION_LOC_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.description"}]}},
        {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
        {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}},
        {"name": "TRIGGER_EVENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [ {"modif": "$.triggerEventName"}]}, "expr": "hashS({0})"},
        {"name": "WORKSTATION_LOC_AIRPORT_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.airportCode"}]}, "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT"}]},
        {"name": "WORKSTATION_LOC_TERMINAL_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.airportCode"},{"user": "$.workStation.fullLocation.terminalId"}]}, "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT_TERMINAL"}]},
        {"name": "WORKSTATION_LOC_AREA_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [ {"user": "$.workStation.fullLocation.areaCode"}]}, "expr": "hashS({0})","fk" : [{"table":"DIM_AIRPORT_AREA"}]}
      ],
      "pit": {
      "type": "secondary-pit-table"
      }}
    },
    {
      "name": "DIM_AIRLINE",
      "mapping": {
        "merge": {
          "key-columns": ["AIRLINE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"companyCode": "$.mainResource.current.image.staff.companyCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].staff.companyCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].validatingCarrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.passenger.contacts[*].carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].contacts[*].carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].segment.carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.flightTransfers[*].toSegments[*].carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].segment.operating.carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].operating.carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.flightTransfers[*].toSegments[*].operating.carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].operatingFlight.carrierCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].validForCarrier"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image..regulatoryDocuments[*].validForCarrier"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.services[*].serviceProvider.code"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].services[*].serviceProvider.code"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.chargeableSeat.serviceProvider.code"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.frequentFlyer[*].applicableAirlineCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].applicableAirlineCode.code"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.frequentFlyer[*].allianceLevel.companyCode"}]},
          { "blocks": [{"companyCode": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].allianceLevel.companyCode"}]}

        ],
        "columns": [
          {"name": "AIRLINE_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"companyCode": "$.value"}]},"expr": "hashS({0})" },
          {"name": "AIRLINE_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"companyCode": "$.value"}]}}
        ],
        "pit": {
         "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_AIRPORT",
      "mapping": {
        "merge": {
          "key-columns": ["AIRPORT_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].segment.arrival.iataCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].arrival.iataCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.flightTransfers[*].toSegments[*].arrival.iataCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].segment.departure.iataCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].departure.iataCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.flightTransfers[*].toSegments[*].departure.iataCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].arrival.iataCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].departure.iataCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.trackingLog.trackedLocation.airportCode"}]},
          {"blocks": [ {"airportCode": "$.mainResource.current.image.lastModification.user.workStation.fullLocation.airportCode"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].passengerDisruption.originalDestination"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].presentedAtPort"}]},
          { "blocks": [{"airportCode": "$.mainResource.current.image.regulatoryDocuments[*].presentedAtPort"}]}
        ],
        "columns": [
          {"name": "AIRPORT_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"airportCode": "$.value"}]},"expr": "hashS({0})" },
          {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"airportCode": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_AIRPORT_TERMINAL",
      "mapping": {
        "merge": {
          "key-columns": ["AIRPORT_TERMINAL_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*]..boarding.trackingLog.trackedLocation"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.lastModification.user.workStation.fullLocation"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.lastModification.user.workStation.fullLocation"}]}
        ],
        "columns": [
          {"name": "AIRPORT_TERMINAL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.airportCode"},{"base": "$.terminalId"}]},"expr": "hashM({0})" },
          {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.airportCode"}]}},
          {"name": "TERMINAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.terminalId"}]}},
          {"name": "AIRPORT_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.airportCode"}]},"expr": "hashS({0})"}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_TRIGGER_EVENT",
      "mapping": {
        "merge": {
          "key-columns": ["TRIGGER_EVENT_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"event": "$.mainResource.current.image.lastModification"}]}
        ],
        "columns": [
          {"name": "TRIGGER_EVENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"event": "$.triggerEventName"}]},"expr": "hashS({0})" },
          {"name": "TRIGGER_EVENT_NAME", "column-type": "strColumn", "sources": {"blocks": [{"event": "$.triggerEventName"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_AIRPORT_AREA",
      "mapping": {
        "merge": {
          "key-columns": ["AIRPORT_AREA_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"event": "$.mainResource.current.image.lastModification.user.workStation.fullLocation"}]}
        ],
        "columns": [
          {"name": "AIRPORT_AREA_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"event": "$.areaCode"}]},"expr": "hashS({0})" },
          {"name": "AIRPORT_AREA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"event": "$.areaCode"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_BOOKING_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["BOOKING_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].segment.status"}]},
          { "blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].status"}]},
          { "blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].toSegments[*].status"}]},
          { "blocks": [{"status": "$.mainResource.current.image.services[*].statusCode"}]},
          { "blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].services[*].statusCode"}]},
          { "blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.statusCode"}]},
          { "blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.seatProductStatus"}]}
        ],
        "columns": [

          {"name": "BOOKING_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]},"expr": "hashS({0})" },
          {"name": "BOOKING_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_BOOKING_CLASS",
      "mapping": {
        "merge": {
          "key-columns": ["BOOKING_CLASS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"class": "$.mainResource.current.image.segmentDeliveries[*].segment.class"}]},
          { "blocks": [{"class": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].class"}]},
          { "blocks": [{"class": "$.mainResource.current.image.flightTransfers[*].toSegments[*].class"}]}
        ],
        "columns": [

          {"name": "BOOKING_CLASS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"class": "$.value"}]},"expr": "hashS({0})" },
          {"name": "BOOKING_CLASS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CABIN",
      "mapping": {
        "merge": {
          "key-columns": ["CABIN_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].segment.cabin"}]},
          { "blocks": [{"cabin": "$.mainResource.current.image.flightTransfers[*].fromSegments[*].cabin"}]},
          { "blocks": [{"cabin": "$.mainResource.current.image.flightTransfers[*].toSegments[*].cabin"}]},
          { "blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].travelCabinCode"}]},
          { "blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].onload.cabinCode"}]},
          { "blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].onload.edit.cabinCode"}]},
          { "blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.cabinCode"}]},
          { "blocks": [{"cabin": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.cabinCode"}]}
        ],
        "columns": [

          {"name": "CABIN_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"cabin": "$.value"}]},"expr": "hashS({0})" },
          {"name": "CABIN_CODE", "column-type": "strColumn", "sources": {"blocks": [{"cabin": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DCS_TRANSFER_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["DCS_TRANSFER_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].dcsTransferStatus"}]}
        ],
        "columns": [
          {"name": "DCS_TRANSFER_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]},"expr": "hashS({0})" },
          {"name": "DCS_TRANSFER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DATA_TRANSFER_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["DATA_TRANSFER_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].dataTransferStatus"}]}
        ],
        "columns": [
          {"name": "DATA_TRANSFER_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]},"expr": "hashS({0})" },
          {"name": "DATA_TRANSFER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DISRUPTION_TRANSFER_REASON",
      "mapping": {
        "merge": {
          "key-columns": ["DISRUPTION_TRANSFER_REASON_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].disruptionTransferReason"}]}
        ],
        "columns": [
          {"name": "DISRUPTION_TRANSFER_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]},"expr": "hashS({0})" },
          {"name": "DISRUPTION_TRANSFER_REASON", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_TRANSFER_SUBTYPE",
      "mapping": {
        "merge": {
          "key-columns": ["TRANSFER_SUBTYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"status": "$.mainResource.current.image.flightTransfers[*].subType"}]}
        ],
        "columns": [
          {"name": "TRANSFER_SUBTYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]},"expr": "hashS({0})" },
          {"name": "TRANSFER_SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_MEMBERSHIP_TIER",
      "mapping": {
        "merge": {
          "key-columns": ["MEMBERSHIP_TIER_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].airlineLevel"}]},
          { "blocks": [{"base": "$.mainResource.current.image.frequentFlyer[*].airlineLevel"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].allianceLevel"}]},
          { "blocks": [{"base": "$.mainResource.current.image.frequentFlyer[*].allianceLevel"}]}
        ],
        "columns": [

          {"name": "MEMBERSHIP_TIER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.companyCode"},{"base": "$.code"},{"base": "$.name"},{"base": "$.priorityCode"}]},"expr": "hashM({0})" },
          {"name": "TIER_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.code"}]}},
          {"name": "TIER_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.name"}]}},
          {"name": "PRIORITY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.priorityCode"}]}},
          {"name": "COMPANY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.companyCode"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_AIRLINE"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_MEMBERSHIP_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["MEMBERSHIP_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"class": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].confirmationStatus"}]},
          { "blocks": [{"class": "$.mainResource.current.image.frequentFlyer[*].confirmationStatus"}]}
        ],
        "columns": [

          {"name": "MEMBERSHIP_STATUS_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"class": "$.value"}]},"expr": "hashS({0})" },
          {"name": "MEMBERSHIP_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_IDENTITY_DOCUMENT_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["IDENTITY_DOCUMENT_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"status": "$.mainResource.current.image.regulatoryDocuments[*].document.documentType"}]},
          { "blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document.documentType"}]},
          { "blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].services[*].document.documentType"}]},
          { "blocks": [{"status": "$.mainResource.current.image.services[*].document.documentType"}]},
          { "blocks": [{"status": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.document.documentType"}]}
        ],
        "columns": [
          {"name": "IDENTITY_DOCUMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"status": "$.value"}]},"expr": "hashS({0})" },
          {"name": "IDENTITY_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"status": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_COUNTRY",
      "mapping": {
        "merge": {
          "key-columns": ["COUNTRY_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"country": "$.mainResource.current.image.passenger.contacts[*].address.countryCode"}]},
          { "blocks": [{"country": "$.mainResource.current.image.passenger.nationality"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].contacts[*].address.countryCode"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document.issuanceCountry"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document.countryOfPortOfEntry"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document.countryOfRegistration"}]},
          { "blocks": [{"country": "$.mainResource.current.image.regulatoryDocuments[*].document.countryOfPortOfEntry"}]},
          { "blocks": [{"country": "$.mainResource.current.image.regulatoryDocuments[*].document.countryOfRegistration"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].services[*].document.issuanceCountry"}]},
          { "blocks": [{"country": "$.mainResource.current.image.services[*].document.issuanceCountry"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.document.issuanceCountry"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].regulatoryProgram.countryCode"}]},
          { "blocks": [{"country": "$.mainResource.current.image.services[*].address.countryCode"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].services[*].address.countryCode"}]},
          { "blocks": [{"country": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.address.countryCode"}]}
        ],
        "columns": [

          {"name": "COUNTRY_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"country": "$.value"}]},"expr": "hashS({0})" },
          {"name": "COUNTRY_CODE", "column-type": "strColumn", "sources": {"blocks": [{"country": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_GENDER",
      "mapping": {
        "merge": {
          "key-columns": ["GENDER_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.passenger"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].document"}]},
          { "blocks": [{"base": "$.mainResource.current.image.regulatoryDocuments[*].document"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].document"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].document"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.document"}]}
        ],
        "columns": [
          {"name": "GENDER_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.gender"}]},"expr": "hashS({0})" },
          {"name": "GENDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.gender"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_SPECIAL_SEAT",
      "mapping": {
        "merge": {
          "key-columns": ["SPECIAL_SEAT_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.passenger.specialSeat"}]}
        ],
        "columns": [

          {"name": "SPECIAL_SEAT_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "SPECIAL_SEAT_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_PASSENGER_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["PASSENGER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.passenger.passengerType"}]}
        ],
        "columns": [

          {"name": "PASSENGER_TYPE_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "PASSENGER_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CPR_FEED_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["CPR_FEED_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.cprFeedType"}]}
        ],
        "columns": [

          {"name": "CPR_FEED_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "CPR_FEED_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_STAFF_RELATIONSHIP",
      "mapping": {
        "merge": {
          "key-columns": ["STAFF_RELATIONSHIP_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.staff.relationshipType"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].staff.relationshipType"}]}
        ],
        "columns": [

          {"name": "STAFF_RELATIONSHIP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "STAFF_RELATIONSHIP", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_STAFF_BOOKING_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["STAFF_BOOKING_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.staff.idType"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].staff.idType"}]}
        ],
        "columns": [

          {"name": "STAFF_BOOKING_TYPE_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "STAFF_BOOKING_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_STAFF_CATEGORY",
      "mapping": {
        "merge": {
          "key-columns": ["STAFF_CATEGORY_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.staff.category"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].staff.category"}]}
        ],
        "columns": [

          {"name": "STAFF_CATEGORY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "STAFF_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_SERVICE",
      "mapping": {
        "merge": {
          "key-columns": ["SERVICE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].code"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].code"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.code"}]},
          { "blocks": [{"base": "$.mainResource.current.image.frequentFlyer[*].serviceCode"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].frequentFlyer[*].serviceCode"}]}
        ],
        "columns": [
          {"name": "SERVICE_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_SERVICE_SUBTYPE",
      "mapping": {
        "merge": {
          "key-columns": ["SERVICE_SUBTYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].subType"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].subType"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.subType"}]}
        ],
        "columns": [
          {"name": "SERVICE_SUBTYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "SERVICE_SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_PAYMENT_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["PAYMENT_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].paymentStatus"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].paymentStatus"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.paymentStatus"}]}
        ],
        "columns": [
          {"name": "PAYMENT_STATUS_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "PAYMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_SERVICE_QUOTA",
      "mapping": {
        "merge": {
          "key-columns": ["SERVICE_QUOTA_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].quotaCode"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].quotaCode"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.quotaCode"}]}
        ],
        "columns": [
          {"name": "SERVICE_QUOTA_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "SERVICE_QUOTA_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CHARGEABLE_DOCUMENT_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["CHARGEABLE_DOCUMENT_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].chargeableServiceDocument.documentType"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].chargeableServiceDocument.documentType"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.chargeableServiceDocument.documentType"}]}
        ],
        "columns": [
          {"name": "CHARGEABLE_DOCUMENT_TYPE_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "CHARGEABLE_DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CHARGEABLE_DOCUMENT_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["CHARGEABLE_DOCUMENT_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].chargeableServiceDocument.status"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].chargeableServiceDocument.status"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.chargeableServiceDocument.status"}]}
        ],
        "columns": [
          {"name": "CHARGEABLE_DOCUMENT_STATUS_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "CHARGEABLE_DOCUMENT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_WAIVER_AUTHORIZER",
      "mapping": {
        "merge": {
          "key-columns": ["WAIVER_AUTHORIZER_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].waiver.authoriser"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].waiver.authoriser"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.waiver.authoriser"}]}
        ],
        "columns": [
          {"name": "WAIVER_AUTHORIZER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashM({0})" },
          {"name": "WAIVER_AUTHORIZER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_WAIVER_REASON",
      "mapping": {
        "merge": {
          "key-columns": ["WAIVER_REASON_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].waiver.reasonCode"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].waiver.reasonCode"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.waiver.reasonCode"}]}
        ],
        "columns": [
          {"name": "WAIVER_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashM({0})" },
          {"name": "WAIVER_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REASON_FOR_ISSUANCE_CODE",
      "mapping": {
        "merge": {
          "key-columns": ["RFIC_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].priceCategory.code"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].priceCategory.code"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.priceCategory.code"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].coupons[*]"}]}
        ],
        "columns": [
          {"name": "RFIC_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "RFIC", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REASON_FOR_ISSUANCE_SUBCODE",
      "mapping": {
        "merge": {
          "key-columns": ["RFISC_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].services[*].priceCategory"}]},
          { "blocks": [{"base": "$.mainResource.current.image.services[*].priceCategory"}]},
          { "blocks": [{"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].service.chargeableSeat.priceCategory"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].coupons[*]"}]}
        ],
        "columns": [
          {"name": "RFISC_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.code"},{"base": "$.subCode"},{"base": "$.rfiscDescription"}]},"expr": "hashM({0})" },
          {"name": "RFIC", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.code"}]},"create-fk" : {"column-type" : "binaryStrColumn", "expr": "hashS({0})","fk" : [{"table":"DIM_REASON_FOR_ISSUANCE_CODE"}]}},
          {"name": "RFISC", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.subCode"}]}},
          {"name": "RFISC_DESCRIPTION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.rfiscDescription"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_COUPON_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["COUPON_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].coupons[*].status"}]}
        ],
        "columns": [
          {"name": "COUPON_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "COUPON_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DOCUMENT_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["DOCUMENT_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].documentType"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].associatedDocuments[*].documentType"}]}
        ],
        "columns": [
          {"name": "DOCUMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "DOCUMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_BLACKLIST_CATEGORY",
      "mapping": {
        "merge": {
          "key-columns": ["BLACKLIST_CATEGORY_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].blacklistCategory"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].associatedDocuments[*].blacklistCategory"}]}
        ],
        "columns": [
          {"name": "BLACKLIST_CATEGORY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "BLACKLIST_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_TRAVEL_DOCUMENT_ASSOCIATION_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["TRAVEL_DOCUMENT_ASSOCIATION_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].associationStatus"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].associatedAirTravelDocuments[*].associatedDocuments[*].associationStatus"}]}
        ],
        "columns": [
          {"name": "TRAVEL_DOCUMENT_ASSOCIATION_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "TRAVEL_DOCUMENT_ASSOCIATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_VOLUNTEER_DENIED_BOARDING",
      "mapping": {
        "merge": {
          "key-columns": ["VOLUNTEER_DENIED_BOARDING_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].volunteerDeniedBoarding"}]}
        ],
        "columns": [
          {"name": "VOLUNTEER_DENIED_BOARDING_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "VOLUNTEER_DENIED_BOARDING", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_VOLUNTEER_DOWNGRADE",
      "mapping": {
        "merge": {
          "key-columns": ["VOLUNTEER_DOWNGRADE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].volunteerDowngrade"}]}
        ],
        "columns": [
          {"name": "VOLUNTEER_DOWNGRADE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "VOLUNTEER_DOWNGRADE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DISRUPTION_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["DISRUPTION_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].passengerDisruption.productState.status"}]}
        ],
        "columns": [
          {"name": "DISRUPTION_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "DISRUPTION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DISRUPTION_REASONS",
      "mapping": {
        "merge": {
          "key-columns": ["DISRUPTION_REASONS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].passengerDisruption.productState.reason[*]"}]}
        ],
        "columns": [
          {"name": "DISRUPTION_REASONS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashM({0})" },
          {"name": "DISRUPTION_REASONS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGULATORY_DOCUMENT_USAGE",
      "mapping": {
        "merge": {
          "key-columns": ["REGULATORY_DOCUMENT_USAGE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].regulatoryDocuments[*].usedFor"}]},
          { "blocks": [ {"base": "$.mainResource.current.imageregulatoryDocuments[*].document.usedFor"}]}

        ],
        "columns": [
          {"name": "REGULATORY_DOCUMENT_USAGE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGULATORY_DOCUMENT_USAGE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REVENUE_INTEGRITY_CHECK_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["REVENUE_INTEGRITY_CHECK_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].revenueIntegrity.checks[*].checkType"}]}

        ],
        "columns": [
          {"name": "REVENUE_INTEGRITY_CHECK_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REVENUE_INTEGRITY_CHECK_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REVENUE_INTEGRITY_CHECK_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["REVENUE_INTEGRITY_CHECK_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].revenueIntegrity.checks[*].status"}]}

        ],
        "columns": [
          {"name": "REVENUE_INTEGRITY_CHECK_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REVENUE_INTEGRITY_CHECK_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REVENUE_INTEGRITY_COMMENT_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["REVENUE_INTEGRITY_COMMENT_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].revenueIntegrity.comments[*].commentType"}]}

        ],
        "columns": [
          {"name": "REVENUE_INTEGRITY_COMMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REVENUE_INTEGRITY_COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DELIVERY_COMMENT_PRIORITY",
      "mapping": {
        "merge": {
          "key-columns": ["DELIVERY_COMMENT_PRIORITY_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].comments[*].priority"}]}

        ],
        "columns": [
          {"name": "DELIVERY_COMMENT_PRIORITY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "DELIVERY_COMMENT_PRIORITY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DELIVERY_COMMENT_USAGE",
      "mapping": {
        "merge": {
          "key-columns": ["DELIVERY_COMMENT_USAGE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].comments[*].usage"}]}

        ],
        "columns": [
          {"name": "DELIVERY_COMMENT_USAGE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "DELIVERY_COMMENT_USAGE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DELIVERY_COMMENT_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["COMMENT_DELIVERY_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].comments[*].delivery.status"}]}

        ],
        "columns": [
          {"name": "COMMENT_DELIVERY_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "COMMENT_DELIVERY_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_VOUCHER_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["VOUCHER_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].vouchers[*].voucherType"}]}

        ],
        "columns": [
          {"name": "VOUCHER_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "VOUCHER_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_VOUCHER_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["VOUCHER_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].vouchers[*].status"}]}

        ],
        "columns": [
          {"name": "VOUCHER_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "VOUCHER_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_VOUCHER_PRINT_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["VOUCHER_PRINT_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].vouchers[*].printStatus"}]}

        ],
        "columns": [
          {"name": "VOUCHER_PRINT_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "VOUCHER_PRINT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_COMPENSATION_CATEGORY",
      "mapping": {
        "merge": {
          "key-columns": ["COMPENSATION_CATEGORY_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].categoryDescription"}]}

        ],
        "columns": [
          {"name": "COMPENSATION_CATEGORY_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "COMPENSATION_CATEGORY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_COMPENSATION_AUTHORIZATION_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["COMPENSATION_AUTHORIZATION_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].authorisation.status"}]}

        ],
        "columns": [
          {"name": "COMPENSATION_AUTHORIZATION_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "COMPENSATION_AUTHORIZATION_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_COMPENSATION_REASON",
      "mapping": {
        "merge": {
          "key-columns": ["COMPENSATION_REASON_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].compensations[*].reason.code"}]}

        ],
        "columns": [
          {"name": "COMPENSATION_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "COMPENSATION_REASON", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGULATORY_PROGRAM",
      "mapping": {
        "merge": {
          "key-columns": ["REGULATORY_PROGRAM_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].regulatoryProgram.name"}]}

        ],
        "columns": [
          {"name": "REGULATORY_PROGRAM_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGULATORY_PROGRAM", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGULATORY_CHECK_STATUS_CODE",
      "mapping": {
        "merge": {
          "key-columns": ["REGULATORY_CHECK_STATUS_CODE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].statusCode"}]}

        ],
        "columns": [
          {"name": "REGULATORY_CHECK_STATUS_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGULATORY_CHECK_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGULATORY_CHECK_STATUS_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["REGULATORY_CHECK_STATUS_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].statusType"}]}

        ],
        "columns": [
          {"name": "REGULATORY_CHECK_STATUS_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGULATORY_CHECK_STATUS_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGULATORY_CHECK_COMMENT_CODE",
      "mapping": {
        "merge": {
          "key-columns": ["REGULATORY_CHECK_COMMENT_CODE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].message.code"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].override.code"}]}

        ],
        "columns": [
          {"name": "REGULATORY_CHECK_COMMENT_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGULATORY_CHECK_COMMENT_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGULATORY_CHECK_COMMENT_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["REGULATORY_CHECK_COMMENT_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].message.commentType"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regulatoryChecks[*].statuses[*].override.commentType"}]}

        ],
        "columns": [
          {"name": "REGULATORY_CHECK_COMMENT_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGULATORY_CHECK_COMMENT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGRADE_REASON",
      "mapping": {
        "merge": {
          "key-columns": ["REGRADE_REASON_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.reasonCode"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.reasonCode"}]}

        ],
        "columns": [
          {"name": "REGRADE_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGRADE_REASON_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGRADE_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["REGRADE_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.status"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.status"}]}

        ],
        "columns": [
          {"name": "REGRADE_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGRADE_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGRADE_DIRECTION",
      "mapping": {
        "merge": {
          "key-columns": ["REGRADE_DIRECTION_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.direction"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.direction"}]}

        ],
        "columns": [
          {"name": "REGRADE_DIRECTION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGRADE_DIRECTION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_REGRADE_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["REGRADE_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeProposed.regradeType"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].regradeDelivered.regradeType"}]}

        ],
        "columns": [
          {"name": "REGRADE_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "REGRADE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_SEAT_CHARACTERISTICS",
      "mapping": {
        "merge": {
          "key-columns": ["SEAT_CHARACTERISTICS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.seat"}]},
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.seatPreferences"}]}

        ],
        "columns": [
          {"name": "SEAT_CHARACTERISTICS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.characteristicsCodes[*]"}]},"expr": "hashS({0})" },
          {"name": "SEAT_CHARACTERISTICS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.characteristicsCodes[*]"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_SEAT_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["SEAT_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].seating.seatStatus"}]}
        ],
        "columns": [
          {"name": "SEAT_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "SEAT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_ACCEPTANCE_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["ACCEPTANCE_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.acceptanceType"}]}
        ],
        "columns": [
          {"name": "ACCEPTANCE_TYPE_ID", "column-type":  "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "ACCEPTANCE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_ACCEPTANCE_CHANNEL",
      "mapping": {
        "merge": {
          "key-columns": ["ACCEPTANCE_CHANNEL_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.channel"}]}
        ],
        "columns": [
          {"name": "ACCEPTANCE_CHANNEL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "ACCEPTANCE_CHANNEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_ACCEPTANCE_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["ACCEPTANCE_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.status"}]}
        ],
        "columns": [
          {"name": "ACCEPTANCE_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "ACCEPTANCE_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_ACCEPTANCE_PHYSICAL_LOCATION",
      "mapping": {
        "merge": {
          "key-columns": ["ACCEPTANCE_PHYSICAL_LOCATION_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.physicalAcceptanceLocation"}]}
        ],
        "columns": [
          {"name": "ACCEPTANCE_PHYSICAL_LOCATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "ACCEPTANCE_PHYSICAL_LOCATION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_ACCEPTANCE_CANCELLATION_REASON",
      "mapping": {
        "merge": {
          "key-columns": ["ACCEPTANCE_CANCELLATION_REASON_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].acceptance.cancellationReason"}]}
        ],
        "columns": [
          {"name": "ACCEPTANCE_CANCELLATION_REASON_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "ACCEPTANCE_CANCELLATION_REASON_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_BOARDING_PRINT_CHANNEL",
      "mapping": {
        "merge": {
          "key-columns": ["BOARDING_PRINT_CHANNEL_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.boardingPassPrint.channel"}]}
        ],
        "columns": [
          {"name": "BOARDING_PRINT_CHANNEL_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "BOARDING_PRINT_CHANNEL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_BOARDING_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["BOARDING_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.boardingStatus"}]}
        ],
        "columns": [
          {"name": "BOARDING_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "BOARDING_STATUS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_BOARDING_PRINT_STATUS",
      "mapping": {
        "merge": {
          "key-columns": ["BOARDING_PRINT_STATUS_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.boardingPassPrint.status"}]}
        ],
        "columns": [
          {"name": "BOARDING_PRINT_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "BOARDING_PRINT_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_DEVICE_TYPE",
      "mapping": {
        "merge": {
          "key-columns": ["DEVICE_TYPE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.trackingLog.referenceDeviceType"}]}
        ],
        "columns": [
          {"name": "DEVICE_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "DEVICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_LOG_SOURCE",
      "mapping": {
        "merge": {
          "key-columns": ["LOG_SOURCE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "blocks": [ {"base": "$.mainResource.current.image.segmentDeliveries[*].legDeliveries[*].boarding.trackingLog.logSource"}]}
        ],
        "columns": [
          {"name": "LOG_SOURCE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.value"}]},"expr": "hashS({0})" },
          {"name": "LOG_SOURCE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.value"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    }
  ]
}
