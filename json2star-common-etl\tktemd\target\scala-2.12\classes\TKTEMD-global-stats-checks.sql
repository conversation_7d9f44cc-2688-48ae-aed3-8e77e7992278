-- MAGIC %md
-- MAGIC #Notebook Information
-- MAGIC
-- MAGIC Each cell starts by defining the values specific to the test. Then an INSERT INTO is run with a join between tables. For a test to pass, zero records are expected from the join.

-- COMMAND ----------

--The threshold value can be set individually for each validation test.
SET db.task = "TKTEMD-global-stats-checks.sql";
SET db.validation_database = ${valDatabase};
SET db.validation_table = ${valTableName};
SET db.default_threshold = 0;
SET db.now_datetime = current_timestamp();
SET db.table_fields = (domain, domain_version, customer, phase, test_time, task, covered_functional_case,status,nb_failed_records,nb_total_records,fail_ratio,result_details);

-- COMMAND ----------

--test 1
--set variables for this test
SET db.threshold_min = 1.5;
SET db.threshold_max = 2.0;
SET db.func_case = "Test 1 - Check the average number of coupons per ticket";
SET db.result_details = CONCAT('The average number of coupons per ticket is less than ',${db.threshold_min},' or higher than ',${db.threshold_max},'. Average number is ');

WITH avg_CPN AS (
  select avg(nb_cpn) as avg_number
  from (
    select doc.reference_key, count(1) as nb_cpn
    from $DB_NAME.fact_travel_document doc
      inner join $DB_NAME.fact_coupon cpn on cpn.travel_document_id = doc.travel_document_id
    group by 1
  )
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select "${DOMAIN}","${DOMAIN_VERSION}","${CUSTOMER}","${PHASE}",${db.now_datetime},${db.task},${db.func_case},
  ((avg_number>${db.threshold_min}) AND (avg_number<${db.threshold_max})) as status,
  null,null,null,
  if((avg_number<${db.threshold_min}) OR (avg_number>${db.threshold_max}),concat(${db.result_details},round(avg_number, 2)),NULL) as result_details
from avg_CPN;

-- COMMAND ----------

--test 2
--set variables for this test
SET db.threshold_min = 1.0;
SET db.threshold_max = 1.2;
SET db.func_case = "Test 2 - Check the average number of forms of payment per ticket";
SET db.result_details = CONCAT('The average number of forms of payment per ticket is less than ',${db.threshold_min},' or higher than ',${db.threshold_max},'. Average number is ');

WITH avg_FOP AS (
  select avg(nb_fop) as avg_number
  from (
    select doc.reference_key, count(1) as nb_fop
    from $DB_NAME.fact_travel_document doc
      inner join $DB_NAME.fact_form_of_payment fop on fop.travel_document_id = doc.travel_document_id
    group by 1
  )
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select "${DOMAIN}","${DOMAIN_VERSION}","${CUSTOMER}","${PHASE}",${db.now_datetime},${db.task},${db.func_case},
  ((avg_number>${db.threshold_min}) AND (avg_number<${db.threshold_max})) as status,
  null,null,null,
  if((avg_number<${db.threshold_min}) OR (avg_number>${db.threshold_max}),concat(${db.result_details},round(avg_number, 2)),NULL) as result_details
from avg_FOP;
