versionExprVal: "bigint({0})"
floatToIntExprVal: "int({0})"
versionTypeVal: "longColumn"
//these are variables used in the assoSegmentLeg expression
segDepTime: "concat_ws('-', slice(split({0},'-'), 1, 3))"
segArrTime: "concat_ws('-', slice(split({0},'-'), 4, 3))"
legDepTime: "concat_ws('-', slice(split({0},'-'), 7, 3))"
legArrTime: "concat_ws('-', slice(split({0},'-'), 10, 3))"
baseId: "concat_ws('-', slice(split({0},'-'), 13, 5))"
legId: "concat_ws('-', slice(split({0},'-'), -2, 2))"
//if departure_datetime of leg is before segment and arrival_datetime of leg is after segment, then return leg.id, else return null
assoSegmentLeg: "if(("${segDepTime}" <= "${legDepTime}") AND ("${segArrTime}" >= "${legArrTime}"), hashM(concat_ws ('-', "${baseId}", "${legId}")), null)"
sanitizeIdentifier: "regexp_replace({0}, '-_|T..:..:..Z|T..:..:..', '')"
createZorderKey: "substring({0}, -10)|| '-' || {0}"

"ruleset": {
  "data-dictionary": {
    "json-to-yaml-paths": {
      "$.mainResource.current.image": "FlightInventoryDataPush.inventoryProcessedFlight"
    }
  }
},

"tables": [
  {
    "name": "FACT_FLIGHT_DATE",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_DATE_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_DATE_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Flight Date",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {
          "name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CARRIER_CODE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "ROUTE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.route"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SCHEDULED_DEPARTURE_DATE", "column-type": "dateColumn", "sources": {"blocks": [{"base": "$.scheduledDepartureDate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_CHARACTERISTICS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.characteristics"}]},
          "meta": {"description": {"value": "It indicates whether the flightdate is pseudo, hidden, international, domestic, ground handling.", "rule": "replace"}, "example": {"value": "INTERNATIONAL", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_CANCELLED", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.isCancelled"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SYSTEM_FLAGS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.systemFlags"}]},
          "meta": {"description": {"value": "DCS status of the flight.", "rule": "replace"}, "example": {"value": "ACTIVE_FLIGHT_DCS", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "ONGOING_INVENTORY_REBUILD", "column-type": "booleanColumn", "sources": {"blocks": [{"base": "$.ongoingInventoryRebuild"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INV_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "meta": {"gdpr-zone": "green"}},
        {
          "name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the INV message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          "pit-key": "INTERNAL_ZORDER",
          "pit-version": "VERSION",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_VERSION"
        }
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_INVOLUNTARY_CANCELLATION",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["FLIX"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_INVOLUNTARY_CANCELLATION_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_INVOLUNTARY_CANCELLATION_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": ["FLIX"],
    "subdomain": "Flight Date",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_INVOLUNTARY_CANCELLATION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flixes": "$.flixes[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FLIGHT_INVOLUNTARY_CANCELLATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flixes": "$.referenceLegId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flixes": "$.referenceLegId"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "REFERENCE_LEG_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"flixes": "$.referenceLegId"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "QUALIFIER", "column-type": "strColumn", "sources": {"blocks": [{"flixes": "$.qualifier"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },
  //// SEGMENT TABLES
  {
    "name": "FACT_FLIGHT_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightSegment": "$.segments[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BOARD_POINT", "column-type": "strColumn", "sources": {"blocks": [{"flightSegment": "$.boardPointIataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "OFF_POINT", "column-type": "strColumn", "sources": {"blocks": [{"flightSegment": "$.offPointIataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SCHEDULED_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"flightSegment": "$.scheduledDepartureDateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SCHEDULED_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"flightSegment": "$.scheduledArrivalDateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "meta": {"gdpr-zone": "green"}},
        {
          "name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.flightDesignator.*"},{"base": "$.scheduledDepartureDate"}, {"flightSegment": "$.id"}]}, "expr": "hashM("${sanitizeIdentifier}")"
          , "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "INV_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "BOARD_POINT_ID", "column-type": "strColumn", "sources": {"blocks": [{"flightSegment": "$.boardPointIataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "OFF_POINT_ID", "column-type": "strColumn", "sources": {"blocks": [{"flightSegment": "$.offPointIataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },

  {
    "name": "FACT_CODESHARE_FLIGHT_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment",
    "mapping": {
      "description": {"description": "Contains the corresponding codeshare partner flights (either operating, or marketing) for a given flight segment, depending if the flight segment itself is marketing or operating).", "granularity": "1 codeshare flight for 1 flight segment"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "mark", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}, {"flt": "$.partnerships.marketingFlights[*]"},{"id" : "$.id"}]}},
        {"name": "oper", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}, {"flt": "$.partnerships.operatingFlight"},{"id" : "$.id"}]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"id": "$.value"}]},
          "expr":  "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "REFERENCE_KEY", "column-type": "strColumn",
          "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"id": "$.value"}]},
          "meta": {"description": {"value": "Functional key: (flight segment key)-partnerCarrier-partnerFlightNum-partnerSuffix-depDate", "rule": "replace"}, "example": {"value": "6X-835-B-2023-05-01-2023-05-01-LHR-CDG-8X-5900-D-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "PARTNER_CARRIER", "column-type": "strColumn",
          "sources": {"blocks": [{"flt": "$.flightDesignator.carrierCode"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PARTNER_FLIGHT_NUMBER", "column-type": "intColumn",
          "sources": {"blocks": [{"flt": "$.flightDesignator.flightNumber"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PARTNER_OPERATIONAL_SUFFIX", "column-type": "strColumn",
          "sources": {"blocks": [{"flt": "$.flightDesignator.operationalSuffix"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {
          "name": "PARTNER_CODESHARE_ROLE", "column-type": "strColumn",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "literal": "MARKETING"},
              {"rs-name": "oper", "literal": "OPERATING"}
            ]
          },
          //"create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CODESHARE_ROLE", "column": "CODESHARE_ROLE_ID"}]},
          "meta": {"description": {"value": "The codeshare role of the partner's codeshare flight", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "CODESHARE_AGREEMENT", "column-type": "strColumn",
          "sources": {"blocks": [{"flt": "$.flightDesignator.codeshareAgreement"}]},
          "meta": {"gdpr-zone": "green"}
        },
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}]},
        {"name": "FLIGHT_DATE_IDENTIFIER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column":"FLIGHT_SEGMENT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {
            "root-specific": [
              {"rs-name": "mark", "blocks": [{"base": "$.flightDesignator.*"}, {"seg": "$.scheduledDepartureDateTime"}, {"seg": "$.scheduledDepartureDateTime"}, {"seg": "$.boardPointIataCode"}, {"seg": "$.offPointIataCode"}, {"flt": "$.flightDesignator.carrierCode"}, {"flt": "$.flightDesignator.flightNumber"},  {"flt": "$.flightDesignator.suffix"},  {"seg": "$.scheduledDepartureDateTime"}]},
              {"rs-name": "oper", "blocks": [{"base": "$.flightDesignator.*"}, {"seg": "$.scheduledDepartureDateTime"}, {"seg": "$.scheduledDepartureDateTime"}, {"seg": "$.boardPointIataCode"}, {"seg": "$.offPointIataCode"}, {"flt": "$.flightDesignator.carrierCode"}, {"flt": "$.flightDesignator.flightNumber"},  {"flt": "$.flightDesignator.suffix"},  {"seg": "$.scheduledDepartureDateTime"}]}
            ]
          },
          "expr": "hashM("${sanitizeIdentifier}")"
          "meta": {"description": {"value": "Technical field required for correlation computation", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "INV_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"id": "$.value"}]},
          "expr":  "hashM({0})",
          "meta": {"description": {"value": "Hash of the functional key (see REFERENCE_KEY)", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },

  {
    "name": "FACT_CABIN_COUNTERS_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_CABIN_COUNTERS_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CABIN_COUNTERS_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "CABIN_COUNTERS_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightSegment": "$.segments[*]"}, {"cabinCountersSegment": "$.segmentCounters.cabinCounters[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CABIN_COUNTERS_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"cabinCountersSegment": "$.cabinCode"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"cabinCountersSegment": "$.cabinCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.cabinCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]},
          "meta": {"gdpr-zone": "green"}},
//only on class level
//        {"name": "DCS_BOARDED_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.boardedPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_COMMERCIAL_STANDBY_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.commercialStandbyPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.commercialStandbyPassengersLeftAtGate"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_DENIED_BOARDING_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.deniedBoardingPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_DOWNGRADES_INTO", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.downgradesInto"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_DOWNGRADES_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.downgradesOutof"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_UPGRADES_INTO", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.upgradesInto"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_UPGRADES_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.upgradesOutof"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_GO_SHOW_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.goShowPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_NO_SHOW_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.noShowPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_GROUP_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.groupPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_GROUP_NO_SHOWS", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.groupNoShows"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_NO_REC", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.noRec"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_OFFLOADED_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.offloadedPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_VOLUNTEERS_FOR_OFFLOAD_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.volunteersForOffloadPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_ACCEPTED_STANDBY_UPGRADE_INTO", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.staffCounters.acceptedStaffStanbyUpgradeInto"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_ACCEPTED_STANDBY_UPGRADE_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.staffCounters.acceptedStaffStanbyUpgradeOutof"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_ACCEPTED_STANDBY_DOWNGRADE_INTO", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.staffCounters.acceptedStaffStandbyDowngradeInto"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_ACCEPTED_STANDBY_DOWNGRADE_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.staffCounters.acceptedStaffStandbyDowngradeOutof"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_STANDBY_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.staffCounters.staffStandbyPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.counters.staffCounters.staffStandbyPassengersLeftAtGate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_STAFF_STANDBY_MAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.inventoryControls.staffStandbyMax"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_UNSOLD_PROTECTION", "column-type": "floatColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.inventoryControls.unsoldProtection"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_WAITLIST_MAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.inventoryControls.waitListMax"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_WAITLIST_MAX_PERCENTAGE_OF_AUTHORIZED", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.inventoryControls.waitlistMaxPercentageOfAU"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_BOOKINGS", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.inventoryCounters.bookings"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_EXPECTED_TO_BOARD", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.inventoryCounters.expectedToBoard"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_STAFF_STANDBY", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.inventoryCounters.staffStandby"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_WAITLIST", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersSegment": "$.inventoryCounters.waitlist"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column":"FLIGHT_SEGMENT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },

  {
    "name": "FACT_CLASS_COUNTERS_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_CLASS_COUNTERS_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CLASS_COUNTERS_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "CLASS_COUNTERS_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"},{"flightSegment": "$.segments[*]"}, {"class": "$.bookingClassList[*]"}, {"classCountersSegment": "$.counters.dcsCounters"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CLASS_COUNTERS_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}, {"class": "$.code"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}, {"class": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.cabin"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_BOARDED_PAX", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.boardedPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_COMMERCIAL_STANDBY_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.commercialStandbyPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.commercialStandbyPassengersLeftAtGate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_DENIED_BOARDING_PAX", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.deniedBoardingPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_DOWNGRADES_INTO", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.downgradesInto"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_DOWNGRADES_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.downgradesOutof"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_UPGRADES_INTO", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.upgradesInto"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_UPGRADES_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.upgradesOutof"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_GO_SHOW_PAX", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.goShowPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_NO_SHOW_PAX", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.noShowPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_GROUP_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.groupPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_GROUP_NO_SHOWS", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.groupNoShows"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_NO_REC", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.noRec"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_OFFLOADED_PAX", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.offloadedPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_VOLUNTEERS_FOR_OFFLOAD_PAX", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.volunteersForOffloadPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_ACCEPTED_STANDBY_UPGRADE_INTO", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.staffCounters.acceptedStaffStanbyUpgradeInto"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_ACCEPTED_STANDBY_UPGRADE_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.staffCounters.acceptedStaffStanbyUpgradeOutof"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_ACCEPTED_STANDBY_DOWNGRADE_INTO", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.staffCounters.acceptedStaffStandbyDowngradeInto"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_ACCEPTED_STANDBY_DOWNGRADE_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.staffCounters.acceptedStaffStandbyDowngradeOutof"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_STANDBY_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.staffCounters.staffStandbyPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE", "column-type": "intColumn", "sources": {"blocks": [{"classCountersSegment": "$.staffCounters.staffStandbyPassengersLeftAtGate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column":"FLIGHT_SEGMENT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN_COUNTERS_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_CABIN_COUNTERS_SEGMENT_HISTO", "column":"CABIN_COUNTERS_SEGMENT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },

  {
    "name": "FACT_SUB_CLASS_COUNTERS_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SUB_CLASS_COUNTERS_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SUB_CLASS_COUNTERS_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SUB_CLASS_COUNTERS_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"},{"flightSegment": "$.segments[*]"}, {"class": "$.bookingClassList[*]"}, {"subClassCountersSegment": "$.counters.subClassCounters"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SUB_CLASS_COUNTERS_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}, {"class": "$.code"}, {"subClassCountersSegment": "$.subClass"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}, {"class": "$.code"}, {"subClassCountersSegment": "$.subClass"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.cabin"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SUB_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.subClass"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AVAILABILITY_COUNTER_NET", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.availabilityCounters.net"}]}, "expr": ${floatToIntExprVal}, "meta": {"gdpr-zone": "green"}},
        {"name": "AVAILABILITY_COUNTER_NORMAL", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.availabilityCounters.normal"}]}, "expr": ${floatToIntExprVal}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_INHIBIT_WAITLIST", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.inhibitWaitlist"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_INHIBIT_WAITLIST_CLEARANCE", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.inhibitWaitlistClearance"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_UNSOLD_PROTECTION", "column-type": "floatColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.unsoldProtection"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_WAITLIST_MAX", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.waitListMax"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_WAITLIST_MAX_PERCENTAGE_OF_AUTHORIZED", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.waitlistMaxPercentageOfAU"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_MAX_WAITLIST_PERCENTAGE", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.maximumWaitlistPercentage"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_MIN", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.min"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_NETTED_PROTECTION", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.nettedProtection"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_NO_SHOW_PERCENTAGE", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.noshowPercentage"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_OVERBOOKING_PERCENTAGE", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.overBookingPercentage"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_PARENT_BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.parentBookingClass"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_SEGMENT_LIMIT", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryControls.segmentLimit"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_ASSIGNED_NAMES_IN_GROUPS", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.assignedNamesInGroups"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_AUTHORIZATION", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.authorisation"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_BOOKING_CANCELLATION", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.bookingCancellation"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_GROUP_BOOKINGS", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.groupBookings"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_GROUP_CANCELLATIONS", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.groupCancellations"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_PENDING_GROUPS", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.pendingGroups"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_TICKETED_BOOKING", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.ticketedBooking"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_TOTAL_NUMBER_OF_NEGO_BOOKINGS", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.totalNumberOfNegoBookings"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_WAITLIST_GROUPS", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.waitlistGroups"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_BOOKINGS", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.bookings"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_EXPECTED_TO_BOARD", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.expectedToBoard"}]}, "expr": ${floatToIntExprVal}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTER_WAITLIST", "column-type": "intColumn", "sources": {"blocks": [{"subClassCountersSegment": "$.inventoryCounters.waitlist"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column":"FLIGHT_SEGMENT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CLASS_COUNTERS_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}, {"class": "$.code"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_CLASS_COUNTERS_SEGMENT_HISTO", "column":"CLASS_COUNTERS_SEGMENT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },

  {
    "name": "FACT_CODESHARE_CLASS_COUNTERS_SEGMENT",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_CODESHARE_CLASS_COUNTERS_SEGMENT_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CODESHARE_CLASS_COUNTERS_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Segment",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "PARTNER_CLASS_COUNTERS_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightSegment": "$.segments[*]"}, {"class": "$.bookingClassList[*]"}, {"partnerClassCountersSegment": "$.counters.partnerDcsCounters[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "PARTNER_CLASS_COUNTERS_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}, {"class": "$.code"}, {"partnerClassCountersSegment": "$.marketingFlight.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true",
          "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}, {"class": "$.code"}, {"partnerClassCountersSegment": "$.marketingFlight.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.cabin"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BOOKING_CLASS", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.code"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MARKETING_FLIGHT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.marketingFlight.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MARKETING_FLIGHT_CARRIER_CODE", "column-type": "strColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.marketingFlight.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MARKETING_FLIGHT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.marketingFlight.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "MARKETING_FLIGHT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.marketingFlight.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //{"name": "MARKETING_FLIGHT_CODESHARE_AGREEMENT", "column-type": "strColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.marketingFlight.flightDesignator.codeshareAgreement"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_BOARDED_PAX", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.boardedPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_COMMERCIAL_STANDBY_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.commercialStandbyPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.commercialStandbyPassengersLeftAtGate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_DENIED_BOARDING_PAX", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.deniedBoardingPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_DOWNGRADES_INTO", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.downgradesInto"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_DOWNGRADES_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.downgradesOutof"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_UPGRADES_INTO", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.upgradesInto"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_UPGRADES_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.upgradesOutof"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_GO_SHOW_PAX", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.goShowPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_NO_SHOW_PAX", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.noShowPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_GROUP_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.groupPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_GROUP_NO_SHOWS", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.groupNoShows"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_NO_REC", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.noRec"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_OFFLOADED_PAX", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.offloadedPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_VOLUNTEERS_FOR_OFFLOAD_PAX", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.volunteersForOffloadPassengers"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_ACCEPTED_STANDBY_UPGRADE_INTO", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.staffCounters.acceptedStaffStanbyUpgradeInto"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_ACCEPTED_STANDBY_UPGRADE_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.staffCounters.acceptedStaffStanbyUpgradeOutof"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_ACCEPTED_STANDBY_DOWNGRADE_INTO", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.staffCounters.acceptedStaffStandbyDowngradeInto"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_ACCEPTED_STANDBY_DOWNGRADE_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.staffCounters.acceptedStaffStandbyDowngradeOutof"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_STANDBY_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.staffCounters.staffStandbyPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE", "column-type": "intColumn", "sources": {"blocks": [{"partnerClassCountersSegment": "$.dcsCounters.staffCounters.staffStandbyPassengersLeftAtGate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column":"FLIGHT_SEGMENT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "CLASS_COUNTERS_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}, {"class": "$.cabin"}, {"class": "$.code"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_CLASS_COUNTERS_SEGMENT_HISTO", "column":"CLASS_COUNTERS_SEGMENT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },

  ////// LEG TABLES
  {
    "name": "FACT_FLIGHT_LEG",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_FLIGHT_LEG_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_FLIGHT_LEG_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg",
    "subdomain-main-table": "true",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightLeg": "$.legs"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BOARD_POINT", "column-type": "strColumn", "sources": {"blocks": [{"flightLeg": "$.boardPointIataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "OFF_POINT", "column-type": "strColumn", "sources": {"blocks": [{"flightLeg": "$.offPointIataCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SCHEDULED_DEPARTURE_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"flightLeg": "$.scheduledDepartureDateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SCHEDULED_ARRIVAL_DATETIME_LOCAL", "column-type": "timestampColumn", "sources": {"blocks": [{"flightLeg": "$.scheduledArrivalDateTime"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"flightLeg": "$.serviceType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AIRCRAFT_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"flightLeg": "$.aircraftEquipment.aircraftType"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "INV_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"}]}, "expr": "hashM({0})", "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_TYPE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"flightLeg": "$.serviceType"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_SERVICE_TYPE", "column":"SERVICE_TYPE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "BOARD_POINT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"flightSegment": "$.boardPointIataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "OFF_POINT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"flightSegment": "$.offPointIataCode"}]}, "expr": "hashS({0})",
          "fk": [{"table": "DIM_AIRPORT", "column": "AIRPORT_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },
  {
    "name": "DIM_AIRPORT",
    "mapping": {
      "description": {"description": "Lists the airports used in departure/arrival airports of flight segments or legs", "granularity": "1 airport"},
      "merge": {
        "key-columns": ["AIRPORT_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"airport": "$.legs.boardPointIataCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"airport": "$.legs.offPointIataCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"airport": "$.segments[*].boardPointIataCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"airport": "$.segments[*].offPointIataCode"}]}
      ],
      "columns": [
        {"name": "AIRPORT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"airport": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (iataCode)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "AIRPORT_IATA_CODE", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"airport": "$.value"}]}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_SERVICE_TYPE",
    "mapping": {
      "description": {"description": "Lists the different IATA service types used on flight legs", "granularity": "1 service type"},
      "merge": {
        "key-columns": ["SERVICE_TYPE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"flightLeg": "$.legs[*].serviceType"}]}],
      "columns": [
        {
          "name": "SERVICE_TYPE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"flightLeg": "$.value"}]}, "expr": "hashS({0})"
          , "meta": {"description": {"value": "Hash of service type code", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "SERVICE_TYPE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"flightLeg": "$.value"}]}},
        {"name": "SERVICE_TYPE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"flightLeg": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the service type code", "rule": "replace"}, "example": {"value": "Passenger/Cargo in Cabin (mixed configuration aircraft)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SERVICE_TYPE_OPERATION", "column-type": "strColumn", "sources": {"literal": "UNKNOWN"}
          , "meta": {"description": {"value": "The category of operations corresponding to the service type code", "rule": "replace"}, "example": {"value": "Passenger/Cargo", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [ {
      "data-source-key": "IATA_SERVICE_TYPE",
      "column-filler" : [
        {"dim-col" : "SERVICE_TYPE_ID", "src-col" : "CODE"},
        {"dim-col" :  "SERVICE_TYPE_CODE", "src-col" : "CODE"},
        {"dim-col" :  "SERVICE_TYPE_LABEL", "src-col" : "LABEL"},
        {"dim-col" :  "SERVICE_TYPE_OPERATION", "src-col" : "TYPE_OF_OPERATION"}
      ]
    } ]
  },
  {
    "name": "FACT_CABIN_COUNTERS_LEG",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_CABIN_COUNTERS_LEG_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_CABIN_COUNTERS_LEG_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "CABIN_COUNTERS_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightLeg": "$.legs[*]"}, {"cabinCountersLeg": "$.legCounters.cabinCounters[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CABIN_COUNTERS_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"},{"cabinCountersLeg": "$.cabinCode"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"},{"cabinCountersLeg": "$.cabinCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.cabinCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CAPACITY_SALEABLE", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.capacity.saleableCapacity"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CAPACITY_OPERATIONAL", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.capacity.operationalCapacity"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CAPACITY_EFFECTIVE", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.capacity.effectiveCapacity"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AUTHORIZATION_LEVEL", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.capacity.authorizationLevel"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AVAILABILITY_NET", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.availabilityCounters.net"}]}, "expr": ${floatToIntExprVal}, "meta": {"gdpr-zone": "green"}},
        {"name": "AVAILABILITY_GROSS", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.availabilityCounters.gross"}]}, "expr": ${floatToIntExprVal}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_ACCEPTED_STANDBY", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.acceptedStandBy"}]}, "meta": {"gdpr-zone": "green"}},
// only on class level
//        {"name": "DCS_BOARDED_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.boardedPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_COMMERCIAL_STANDBY_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.commercialStandbyPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_COMMERCIAL_STANDBY_PAX_LEFT_AT_GATE", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.commercialStandbyPassengersLeftAtGate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "DCS_ADJUSTMENTS", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.dcsAdjustments"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_DENIED_BOARDING_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.deniedBoardingPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_DOWNGRADES_INTO", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.downgradesInto"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_DOWNGRADES_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.downgradesOutof"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_UPGRADES_INTO", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.upgradesInto"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_UPGRADES_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.upgradesOutof"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_GO_SHOW_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.goShowPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_NO_SHOW_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.noShowPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_GROUP_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.groupPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_GROUP_NO_SHOWS", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.groupNoShows"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_NO_REC", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.noRec"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_OFFLOADED_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.offloadedPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_VOLUNTEERS_FOR_OFFLOAD_PAX", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.volunteersForOffloadPassengers"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_ACCEPTED_STANDBY_UPGRADE_INTO", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.staffCounters.acceptedStaffStanbyUpgradeInto"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_ACCEPTED_STANDBY_UPGRADE_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.staffCounters.acceptedStaffStanbyUpgradeOutof"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_ACCEPTED_STANDBY_DOWNGRADE_INTO", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.staffCounters.acceptedStaffStandbyDowngradeInto"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_ACCEPTED_STANDBY_DOWNGRADE_OUT_OF", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.staffCounters.acceptedStaffStandbyDowngradeOutof"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_STANDBY_PAX_BOARDED", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.staffCounters.staffStandbyPassengersBoarded"}]}, "meta": {"gdpr-zone": "green"}},
//        {"name": "DCS_STAFF_STANDBY_PAX_LEFT_AT_GATE", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.counters.staffCounters.staffStandbyPassengersLeftAtGate"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_BOOKINGS", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryCounters.bookings"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_GROUP_BOOKINGS", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryCounters.groupBookings"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_REGRADE", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryCounters.regrade"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_EXPECTED_TO_BOARD", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryCounters.expectedToBoard"}]}, "expr": ${floatToIntExprVal}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_STAFF_STANDBY", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryCounters.staffStandby"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_COUNTERS_WAITLIST", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryCounters.waitlist"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_MAXI_REGRADE_ADJUSTMENT", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryControls.maxiRegradeAdjustment"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_REGRADE_ADJUSTMENT", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryControls.regradeAdjustment"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_UNBALANCE_ADJUSTMENT", "column-type": "intColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryControls.unbalanceAdjustment"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "INVENTORY_CONTROL_UNSOLD_PROTECTION", "column-type": "floatColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.inventoryControls.unsoldProtection"}]}, "meta": {"gdpr-zone": "green"}},

        {"name": "BLOCKSPACE_MARKETING_FLIGHT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].marketingFlight.id"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BLOCKSPACE_MARKETING_FLIGHT_CARRIER", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].marketingFlight.flightDesignator.carrierCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BLOCKSPACE_MARKETING_FLIGHT_FLIGHT_NUMBER", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].marketingFlight.flightDesignator.flightNumber"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BLOCKSPACE_MARKETING_FLIGHT_OPERATIONAL_SUFFIX", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].marketingFlight.flightDesignator.operationalSuffix"}]}, "meta": {"gdpr-zone": "green"}},
        //{"name": "BLOCKSPACE_MARKETING_FLIGHT_CODESHARE_AGREEMENT", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].marketingFlight.flightDesignator.codeshareAgreement"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BLOCKSPACE_ALLOTMENT", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].allotment"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BLOCKSPACE_CODESHARE", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].codeshare"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BLOCKSPACE_PROTECTED_SEATS", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].protectedSeats"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "BLOCKSPACE_SEATS_BLOCKED", "column-type": "strColumn", "sources": {"blocks": [{"cabinCountersLeg": "$.blockSpaces[*].seatsBlocked"}]}, "meta": {"gdpr-zone": "green"}},

        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_LEG_HISTO", "column":"FLIGHT_LEG_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },


  {
    "name": "FACT_SPECIAL_SERVICE_REQUEST_QUOTAS",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "FACT_SPECIAL_SERVICE_REQUEST_QUOTAS_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "FACT_SPECIAL_SERVICE_REQUEST_QUOTAS_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "subdomain": "Leg",
    "mapping": {
      "description": {"description": "", "granularity": ""},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SPECIAL_SERVICE_REQUEST_QUOTAS_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightLeg": "$.legs[*]"}, {"specialServiceRequestQuota": "$.ssrQuotas[*]"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SPECIAL_SERVICE_REQUEST_QUOTAS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"},{"specialServiceRequestQuota": "$.counterCode"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"},{"specialServiceRequestQuota": "$.counterCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN", "column-type": "strColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.cabinCode"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_CABIN"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.counterCode"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "NEST_FAMILY", "column-type": "strColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.nestFamily"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "QUOTA", "column-type": "intColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.quota"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "AVAILABILITY", "column-type": "intColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.availability"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "COUNT", "column-type": "intColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.count"}]}, "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"},{"flightLeg": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_LEG_HISTO", "column":"FLIGHT_LEG_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.counterCode"}]}, "expr": "hashS({0})", "fk": [{"table": "DIM_SERVICE", "column":"SERVICE_ID"}], "meta": {"gdpr-zone": "green"}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "table-snowflake": {
        "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
      }
    }
  },
  {
    "name": "DIM_SERVICE",
    "mapping": {
      "description": {"description": "Lists the various service codes (SSR/SVC, loyalty requests, keywords, ...)", "granularity": "1 service code"},
      "merge": {
        "key-columns": ["SERVICE_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightLeg": "$.legs[*]"}, {"specialServiceRequestQuota": "$.ssrQuotas[*].counterCode"}]}],
      "columns": [
        {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of the functional key (code)", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "SERVICE_CODE", "column-type": "strColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.value"}]}},
        {"name": "SERVICE_SUBTYPE", "column-type": "strColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.value"}]}},
        {"name": "SERVICE_LABEL", "column-type": "strColumn", "sources": {"blocks": [{"specialServiceRequestQuota": "$.value"}]}
          , "meta": {"description": {"value": "Label corresponding to the service code", "rule": "replace"}, "example": {"value": "Excess baggage", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "RECORD_SOURCE", "column-type": "strColumn", "sources": {"literal": "DOMAIN_FEED"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    },
    "prefiller": [{
      "data-source-key": "SSR_CODE",
      "column-filler" : [
        {"dim-col" : "SERVICE_ID", "src-col" : "CODE"},
        {"dim-col" : "SERVICE_CODE", "src-col" : "CODE"},
        {"dim-col" : "SERVICE_SUBTYPE", "src-col" : "SERVICE_SUBTYPE"},
        {"dim-col" : "SERVICE_LABEL", "src-col" : "LABEL"}
      ]
    }]
  },
  {
    "name": "DIM_CABIN",
    "mapping": {
      "description": {"description": "Cabins of segment and leg deliveries", "granularity": "1 cabin"},
      "merge": {
        "key-columns": ["CABIN_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cabin": "$.segments[*].segmentCounters.cabinCounters[*].cabinCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cabin": "$.segments[*].bookingClassList[*].cabin"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"cabin": "$.legs[*].legCounters.cabinCounters[*].cabinCode"}]},
        {"blocks": [{"base": "$.mainResource.current.image"}, {"flightLeg": "$.legs[*]"}, {"cabin": "$.ssrQuotas[*].cabinCode"}]}
      ],
      "columns": [
        {"name": "CABIN_ID", "column-type": "binaryStrColumn","sources": {"blocks": [{"cabin": "$.value"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of cabins", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "CABIN_CODES", "column-type": "strColumn","sources": {"blocks": [{"cabin": "$.value"}]},
          "meta": {"description": {"value": "Cabin code", "rule": "replace"},"example": {"value": "J", "rule": "replace"}, "gdpr-zone": "green"}
        },
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },
  {
    "name": "DIM_BOOKING_CLASS",
    "mapping": {
      "description": {"description": "Lists the booking classes (ordered list) available on flight segments", "granularity": "1 list of bookings classes (concatenated) in 1 flight segment"},
      "merge": {
        "key-columns": ["BOOKING_CLASS_ID"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"blocks": [{"base": "$.mainResource.current.image"}, {"class": "$.segments[*].bookingClassList[*]"}]}
      ],
      "columns": [
        {"name": "BOOKING_CLASS_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"class": "$.code"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hash of booking class", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "BOOKING_CLASS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.code"}]},
          "meta": {"gdpr-zone": "green"}},
        {"name": "CABIN_CODE", "column-type": "strColumn", "sources": {"blocks": [{"class": "$.cabin"}]},
          "meta": {"description": {"value": "Cabin code", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CABIN_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"class": "$.cabin"}]}, "expr": "hashS({0})",
          "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "no-pit-table"
      }
    }
  },

  //// ASSO TABLE
  {
    "name": "ASSO_FLIGHT_SEGMENT_LEG",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "latest": {
      "histo-table-name": "ASSO_FLIGHT_SEGMENT_LEG_HISTO"
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },
  {
    "name": "ASSO_FLIGHT_SEGMENT_LEG_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "description": {"description": "Contains the relationships between flight segments and flight legs, especially relevant for multi-leg flights.", "granularity": "1 relationship between 1 flight segment and 1 flight leg"},
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_SEGMENT_ID", "FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.mainResource.current.image"},
            {
              "cartesian": [
                [{"flightSegment": "$.segments[*]"}],
                [{"flightLeg": "$.legs[*]"}]
              ]
            }
          ]
        }
      ],

      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})"
          , "fk": [{"table": "FACT_FLIGHT_SEGMENT_HISTO", "column": "FLIGHT_SEGMENT_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true"
          , "sources": {"blocks": [{"flightSegment": "$.scheduledDepartureDateTime"}, {"flightSegment": "$.scheduledArrivalDateTime"}, {"flightLeg": "$.scheduledDepartureDateTime"}, {"flightLeg": "$.scheduledArrivalDateTime"}, {"base": "$.id"}, {"flightLeg": "$.id"}]}
          , "expr":  ${assoSegmentLeg}
          , "fk": [{"table": "FACT_FLIGHT_LEG_HISTO", "column": "FLIGHT_LEG_ID"}]
          , "meta": {"description": {"value": "Hashed foreign key", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})", "fk": [{"table": "FACT_FLIGHT_DATE_HISTO", "column":"FLIGHT_DATE_ID"}]},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}
          , "meta": {"description": {"value": "Version of the Inventory message. Timestamp of the message is used as version.", "rule": "replace"}, "gdpr-zone": "green"}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}
          , "meta": {"description": {"value": "Version validity end date/time UTC, computed: if last version: 9999-01-01, otherwise date_begin of following version", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}
          , "meta": {"description": {"value": "Indicates if this version is the latest version", "rule": "replace"}, "gdpr-zone": "green"}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      }
    },
    "table-snowflake": {
      "cluster-by": ["to_char(DATE_BEGIN, 'yyyy-MM')"]
    }
  },

  //////////////// PNR INV CORRELATION //////////

  //**** INV PNR ****
  {
    "name": "INTERNAL_ASSO_RESERVATION_FLIGHT_DATE_HISTO", // INV-PNR #1/3
    "table-selectors": [["INV_ACTIVE", "PNR_ACTIVE","DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-PNR"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources":{"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_RESERVATION_FLIGHT_DATE_HISTO", // INV-PNR #1/3
    "table-selectors": ["INV_ACTIVE", "PNR_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "RESERVATION_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-PNR"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources":{}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources":{}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "TECHNICAL_CUST_OPE_OR_MKT", "column-type": "strColumn", "is-mandatory": "true","sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO", // INV-PNR #2/3
    "table-selectors": [["INV_ACTIVE", "PNR_ACTIVE","DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-PNR"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvPnr.items[*]"},
          {"codeshare": "$.inventoryPartnershipId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
        "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources":  {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}, {"item": "$.inventoryPartnershipId"}]}, "expr": "hashM({0})"},
        {"name": "AIR_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.pnrAirsegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources":  {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}, {"item": "$.inventoryPartnershipId"}]}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.pnrAirsegmentId"}]}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources":{"blocks": [{"base": "$.id"}]}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_CODESHARE_FLIGHT_SEGMENT_HISTO", // INV-PNR #2/3
    "table-selectors": ["INV_ACTIVE", "PNR_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"base": "$.correlatedResourcesCurrent.INV-PNR"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources":  {}, "expr": "hashM({0})"},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources":  {}, "expr": "hashM({0})"},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "TECHNICAL_CUST_OPE_OR_MKT", "column-type": "strColumn", "is-mandatory": "true","sources": {}},
        {"name": "TECHNICAL_CUST_ISPRIME", "column-type": "strColumn", "is-mandatory": "true","sources": {}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO", // INV-PNR #3/3
    "table-selectors": [["INV_ACTIVE", "PNR_ACTIVE","DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-PNR"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvPnr.items[*]"},
          {"shouldNotBeEmpty": "$.inventorySegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey},
          "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "example": {"value": "6X-835-2023-05-01", "rule": "replace"}, "gdpr-zone": "green"}},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}]}, "expr": "hashM({0})"},
        {"name": "AIR_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.pnrAirsegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}]}},
        {"name": "REFERENCE_KEY_AIR_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.pnrAirsegmentId"}]}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources":{"blocks": [{"base": "$.id"}]}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION_RESERVATION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_AIR_SEGMENT_PAX_FLIGHT_SEGMENT_HISTO", // INV-PNR #3/3
    "table-selectors": ["INV_ACTIVE", "PNR_ACTIVE","DAAS_CORRELATION"],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "AIR_SEGMENT_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-PNR"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvPnr.items[*]"},
          {"shouldNotBeEmpty": "$.flightSegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}},
        {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}},
        {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "AIR_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_AIR_SEGMENT_PAX", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "sources": {}},
        {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },




  ////////////// SKD INV CORRELATION ////////////
  {
    "name": "INTERNAL_PARTIAL_SKD_INV_FLIGHT_DATE", // SKD-INV #1/4
    "table-selectors": [["SKD_ACTIVE","INV_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "FLIGHT_DATE_ID", "SKD_FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-SKD"},
          {"corr": "$.correlations[*]"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": ${createZorderKey}},
        {"name": "INV_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "SKD_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_INV_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "VERSION_SKD_FLIGHT_DATE", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  }
  {
    "name": "INTERNAL_PARTIAL_SKD_INV_FLIGHT_DATE", // SKD-INV #1/4
    "table-selectors": [["SKD_ACTIVE","INV_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "SKD_FLIGHT_DATE_ID", "INV_FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "INV_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "SKD_FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_INV_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION_SKD_FLIGHT_DATE", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_INV_FLIGHT_SEGMENT", // SKD-INV #2/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_FLIGHT_SEGMENT_ID", "SKD_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-SKD"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"shouldNotBeEmpty": "$.inventorySegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "INV_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}]}, "expr": "hashM({0})"},
        {"name": "SKD_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_INV_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}]}},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_INV_FLIGHT_SEGMENT", // SKD-INV #2/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_FLIGHT_SEGMENT_ID", "SKD_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightSegment": "$.segments[*]"}]}],

      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "INV_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "SKD_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_INV_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"flightSegment": "$.id"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_INV_CODESHARE_FLIGHT_SEGMENT", // SKD-INV #3/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_CODESHARE_FLIGHT_SEGMENT_ID", "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-SKD"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"shouldNotBeEmpty": "$.inventoryPartnershipFlightId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "INV_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}, {"item": "$.inventoryPartnershipFlightId"}]}, "expr": "hashM({0})"},
        {"name": "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}, {"item": "$.inventoryPartnershipFlightId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_INV_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}, {"item": "$.inventoryPartnershipFlightId"}]}},
        {"name": "REFERENCE_KEY_SKD_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}, {"item": "$.inventoryPartnershipFlightId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_INV_CODESHARE_FLIGHT_SEGMENT", // SKD-INV #3/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_CODESHARE_FLIGHT_SEGMENT_ID", "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [
        {"name": "mark", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}, {"flt": "$.partnerships.marketingFlights[*]"},{"id" : "$.id"}]}},
        {"name": "oper", "rs": {"blocks": [{"base": "$.mainResource.current.image"}, {"seg": "$.segments[*]"}, {"flt": "$.partnerships.operatingFlight"},{"id" : "$.id"}]}},
      ],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "INV_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"id": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "SKD_CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"id": "$.value"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_INV_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"id": "$.value"}]}},
        {"name": "REFERENCE_KEY_SKD_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"seg": "$.id"}, {"id": "$.value"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_INV_FLIGHT_LEG", // SKD-INV #4/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_FLIGHT_LEG_ID", "SKD_FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-SKD"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvSkd.items[*]"},
          {"legs": "$.correlatedLegs[*]"},
          {"shouldNotBeEmpty": "$.flightLegId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "INV_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}, {"legs": "$.inventoryLegId"}]}, "expr": "hashM({0})"},
        {"name": "SKD_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}, {"legs": "$.flightLegId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_INV_FLIGHT_LEG", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}, {"legs": "$.inventoryLegId"}]}},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_LEG", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}, {"item": "$.flightSegmentId"}, {"legs": "$.flightLegId"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_PARTIAL_CORR_INV_FLIGHT_LEG", // SKD-INV #4/4
    "table-selectors": [["SKD_ACTIVE", "INV_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "INV_FLIGHT_LEG_ID", "SKD_FLIGHT_LEG_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}, {"flightLeg": "$.legs"}]}],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "INV_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightLeg": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "SKD_FLIGHT_LEG_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"flightLeg": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_INV_FLIGHT_LEG", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"flightLeg": "$.id"}]}},
        {"name": "REFERENCE_KEY_SKD_FLIGHT_LEG", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"flightLeg": "$.id"}]}},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"base": "$.version"}]}, "expr": ${versionExprVal}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true",
          "sources": { "blocks": [{"base": "$.inventoryFileSnapshotDateTime"}]}
          , "meta": {"description": {"value": "Version validity start date/time UTC", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains SKD-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_INV_FLIGHT_DATE_HISTO",
    "table-selectors": [["INV_ACTIVE", "TKTEMD_PASSIVE", "DIH_CORRELATION"],["INV_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-TKT"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains TKT-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_TRAVEL_DOCUMENT_INV_FLIGHT_DATE_HISTO",
    "table-selectors": [["INV_ACTIVE", "TKTEMD_PASSIVE", "DAAS_CORRELATION"],["INV_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["INV_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "TRAVEL_DOCUMENT_ID", "FLIGHT_DATE_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-TKT"},
          {"corr": "$.correlations[*]"}
        ]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_INV_FLIGHT_SEGMENT_HISTO",
    "table-selectors": [["INV_ACTIVE", "TKTEMD_PASSIVE"],["INV_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-TKT"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvTkt.items[*]"},
          {"shouldNotBeEmpty": "$.inventorySegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}]}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_INV_FLIGHT_SEGMENT_HISTO",
    "table-selectors": [["INV_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["INV_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "zorder-columns": ["INTERNAL_ZORDER"],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-TKT"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvTkt.items[*]"},
          {"shouldNotBeEmpty": "$.inventorySegmentId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal},  "is-mandatory": "true", "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_INV_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": [["INV_ACTIVE", "TKTEMD_PASSIVE"],["INV_ACTIVE", "TKTEMD_ACTIVE", "DIH_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-TKT"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvTkt.items[*]"},
          {"shouldNotBeEmpty": "$.inventoryPartnershipId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},"expr": ${createZorderKey}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}, {"item": "$.inventoryPartnershipId"}]}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {"blocks": [{"item": "$.ticketCouponId"}]}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"item": "$.inventorySegmentId"}, {"item": "$.inventoryPartnershipId"}]}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {"blocks": [{"corr": "$.toId"}]}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"corr": "$.toId"}]}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"corr": "$.toVersion"}]}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {"blocks": [{"root1": "$.mainResource.current.image.version"}]}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {"blocks": [{"root1": "$.mainResource.current.image.inventoryFileSnapshotDateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  },
  {
    "name": "INTERNAL_ASSO_COUPON_INV_CODESHARE_FLIGHT_SEGMENT_HISTO",
    "zorder-columns": ["INTERNAL_ZORDER"],
    "table-selectors": [["INV_ACTIVE", "TKTEMD_ACTIVE", "DAAS_CORRELATION"],["INV_ACTIVE", "RA_ACTIVE", "DAAS_CORRELATION"]],
    "mapping": {
      "merge": {
        "key-columns": ["INTERNAL_ZORDER", "COUPON_ID", "CODESHARE_FLIGHT_SEGMENT_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{
        "blocks": [
          {"root1": "$"},
          {"base": "$.correlatedResourcesCurrent.INV-TKT"},
          {"corr": "$.correlations[*]"},
          {"item": "$.corrInvTkt.items[*]"},
          {"shouldNotBeEmpty": "$.inventoryPartnershipId"}]
      }],
      "columns": [
        {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {}},
        {"name": "COUPON_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "CODESHARE_FLIGHT_SEGMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr":  "hashM({0})"},
        {"name": "REFERENCE_KEY_COUPON", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_CODESHARE_FLIGHT_SEGMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_TRAVEL_DOCUMENT", "column-type": "strColumn", "sources": {}},
        {"name": "REFERENCE_KEY_FLIGHT_DATE", "column-type": "strColumn", "sources": {}},
        {"name": "TRAVEL_DOCUMENT_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "FLIGHT_DATE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {}, "expr": "hashM({0})"},
        {"name": "VERSION_TRAVEL_DOCUMENT", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "VERSION", "column-type": ${versionTypeVal}, "sources": {}, "expr": ${versionExprVal}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn",  "is-mandatory": "true", "sources": {}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
      ],
      "pit": {
        "type": "secondary-pit-table"
      },
      "description": {
        "description": "It contains INV-??? correlation information.",
        "granularity": "???",
        "links": ["???"]
      }
    }
  }
]
