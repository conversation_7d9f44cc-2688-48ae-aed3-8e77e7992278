{"tables": [{"name": "FACT_BAG_GROUP_HISTO", "mapping": {"description": {"description": "Dummy <PERSON>", "granularity": "1 bags group"}, "merge": {"key-columns": ["BAG_GROUP_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}], "columns": [{"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}}, {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "BAG_GROUP_ID", "pit-version": "VERSION", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_VERSION"}}}}]}