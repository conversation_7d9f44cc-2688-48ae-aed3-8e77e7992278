{"correlatedResourcesCurrent": {"PNR-EMD": {"fromDomain": "PNR", "fromFullVersion": "20", "id": "22USLC-2022-08-16", "isFullUpdate": true, "toDomain": "EMD", "version": "EMD-3"}, "PNR-TKT": {"correlations": [{"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-152", "pnrTravelerId": "22USLC-2022-08-16-PT-2", "ticketCouponId": "0142172648260-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-152", "pnrTravelerId": "22USLC-2022-08-16-PT-2", "ticketCouponId": "0142172648260-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-152", "pnrTravelerId": "22USLC-2022-08-16-PT-2", "ticketCouponId": "0142172648260-2022-08-16-3"}]}, "fromVersion": "20", "toId": "0142172648260-2022-08-16", "toVersion": "9"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-153", "pnrTravelerId": "22USLC-2022-08-16-PT-3", "ticketCouponId": "0142172648262-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-153", "pnrTravelerId": "22USLC-2022-08-16-PT-3", "ticketCouponId": "0142172648262-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-153", "pnrTravelerId": "22USLC-2022-08-16-PT-3", "ticketCouponId": "0142172648262-2022-08-16-3"}]}, "fromVersion": "20", "toId": "0142172648262-2022-08-16", "toVersion": "9"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-154", "pnrTravelerId": "22USLC-2022-08-16-PT-4", "ticketCouponId": "0142172648264-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-154", "pnrTravelerId": "22USLC-2022-08-16-PT-4", "ticketCouponId": "0142172648264-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-154", "pnrTravelerId": "22USLC-2022-08-16-PT-4", "ticketCouponId": "0142172648264-2022-08-16-3"}]}, "fromVersion": "20", "toId": "0142172648264-2022-08-16", "toVersion": "9"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-155", "pnrTravelerId": "22USLC-2022-08-16-PT-5", "ticketCouponId": "0142172648266-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-155", "pnrTravelerId": "22USLC-2022-08-16-PT-5", "ticketCouponId": "0142172648266-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-155", "pnrTravelerId": "22USLC-2022-08-16-PT-5", "ticketCouponId": "0142172648266-2022-08-16-3"}]}, "fromVersion": "20", "toId": "0142172648266-2022-08-16", "toVersion": "9"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-156", "pnrTravelerId": "22USLC-2022-08-16-PT-6", "ticketCouponId": "0142172648268-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-156", "pnrTravelerId": "22USLC-2022-08-16-PT-6", "ticketCouponId": "0142172648268-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-156", "pnrTravelerId": "22USLC-2022-08-16-PT-6", "ticketCouponId": "0142172648268-2022-08-16-3"}]}, "fromVersion": "20", "toId": "0142172648268-2022-08-16", "toVersion": "9"}], "fromDomain": "PNR", "fromFullVersion": "20", "id": "22USLC-2022-08-16", "isFullUpdate": true, "toDomain": "TKT", "version": "TKT-8"}}, "correlatedResourcesPrevious": {"PNR-EMD": {"fromDomain": "PNR", "fromFullVersion": "3", "id": "22USLC-2022-08-16", "isFullUpdate": true, "toDomain": "EMD", "version": "EMD-2"}, "PNR-TKT": {"correlations": [{"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-152", "pnrTravelerId": "22USLC-2022-08-16-PT-2", "ticketCouponId": "0142172648260-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-152", "pnrTravelerId": "22USLC-2022-08-16-PT-2", "ticketCouponId": "0142172648260-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-152", "pnrTravelerId": "22USLC-2022-08-16-PT-2", "ticketCouponId": "0142172648260-2022-08-16-3"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-4", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-152", "pnrTravelerId": "22USLC-2022-08-16-PT-2", "ticketCouponId": "0142172648260-2022-08-16-4"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-5", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-152", "pnrTravelerId": "22USLC-2022-08-16-PT-2", "ticketCouponId": "0142172648260-2022-08-16-5"}]}, "fromVersion": "3", "toId": "0142172648260-2022-08-16", "toVersion": "0"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-153", "pnrTravelerId": "22USLC-2022-08-16-PT-3", "ticketCouponId": "0142172648262-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-153", "pnrTravelerId": "22USLC-2022-08-16-PT-3", "ticketCouponId": "0142172648262-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-153", "pnrTravelerId": "22USLC-2022-08-16-PT-3", "ticketCouponId": "0142172648262-2022-08-16-3"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-4", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-153", "pnrTravelerId": "22USLC-2022-08-16-PT-3", "ticketCouponId": "0142172648262-2022-08-16-4"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-5", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-153", "pnrTravelerId": "22USLC-2022-08-16-PT-3", "ticketCouponId": "0142172648262-2022-08-16-5"}]}, "fromVersion": "3", "toId": "0142172648262-2022-08-16", "toVersion": "0"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-154", "pnrTravelerId": "22USLC-2022-08-16-PT-4", "ticketCouponId": "0142172648264-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-154", "pnrTravelerId": "22USLC-2022-08-16-PT-4", "ticketCouponId": "0142172648264-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-154", "pnrTravelerId": "22USLC-2022-08-16-PT-4", "ticketCouponId": "0142172648264-2022-08-16-3"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-4", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-154", "pnrTravelerId": "22USLC-2022-08-16-PT-4", "ticketCouponId": "0142172648264-2022-08-16-4"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-5", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-154", "pnrTravelerId": "22USLC-2022-08-16-PT-4", "ticketCouponId": "0142172648264-2022-08-16-5"}]}, "fromVersion": "3", "toId": "0142172648264-2022-08-16", "toVersion": "0"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-155", "pnrTravelerId": "22USLC-2022-08-16-PT-5", "ticketCouponId": "0142172648266-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-155", "pnrTravelerId": "22USLC-2022-08-16-PT-5", "ticketCouponId": "0142172648266-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-155", "pnrTravelerId": "22USLC-2022-08-16-PT-5", "ticketCouponId": "0142172648266-2022-08-16-3"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-4", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-155", "pnrTravelerId": "22USLC-2022-08-16-PT-5", "ticketCouponId": "0142172648266-2022-08-16-4"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-5", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-155", "pnrTravelerId": "22USLC-2022-08-16-PT-5", "ticketCouponId": "0142172648266-2022-08-16-5"}]}, "fromVersion": "3", "toId": "0142172648266-2022-08-16", "toVersion": "0"}, {"corrTktPnr": {"items": [{"pnrAirSegmentId": "22USLC-2022-08-16-ST-1", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-156", "pnrTravelerId": "22USLC-2022-08-16-PT-6", "ticketCouponId": "0142172648268-2022-08-16-1"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-2", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-156", "pnrTravelerId": "22USLC-2022-08-16-PT-6", "ticketCouponId": "0142172648268-2022-08-16-2"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-3", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-156", "pnrTravelerId": "22USLC-2022-08-16-PT-6", "ticketCouponId": "0142172648268-2022-08-16-3"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-4", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-156", "pnrTravelerId": "22USLC-2022-08-16-PT-6", "ticketCouponId": "0142172648268-2022-08-16-4"}, {"pnrAirSegmentId": "22USLC-2022-08-16-ST-5", "pnrTicketingReferenceId": "22USLC-2022-08-16-OT-156", "pnrTravelerId": "22USLC-2022-08-16-PT-6", "ticketCouponId": "0142172648268-2022-08-16-5"}]}, "fromVersion": "3", "toId": "0142172648268-2022-08-16", "toVersion": "0"}], "fromDomain": "PNR", "fromFullVersion": "3", "id": "22USLC-2022-08-16", "isFullUpdate": false, "toDomain": "TKT", "version": "TKT-7"}}, "mainResource": {"current": {"correlations": [{"name": "PNR-EMD", "relation": {"rel": "related"}}, {"name": "PNR-TKT", "relation": {"rel": "related"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "automatedProcesses": [{"applicableCarrierCode": "AC", "code": "OK", "dateTime": "2022-08-16T00:00:00", "id": "22USLC-2022-08-16-OT-151", "isApplicableToInfants": false, "office": {"id": "YVRAC08ST"}, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}], "contacts": [{"id": "22USLC-2022-08-16-OT-71", "phone": {"number": "(44) *********"}, "purpose": ["STANDARD"], "travelerRefs": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"email": {"address": "<EMAIL>"}, "id": "22USLC-2022-08-16-OT-72", "purpose": ["STANDARD"], "travelerRefs": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}], "creation": {"comment": "DAPI-AC/CAACDCAWD", "dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA", "initials": "DA", "numericSign": "9999"}, "office": {"agentType": "AIRLINE", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "fareElements": [{"code": "FE", "id": "22USLC-2022-08-16-OT-75", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FE", "id": "22USLC-2022-08-16-OT-77", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FE", "id": "22USLC-2022-08-16-OT-79", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FE", "id": "22USLC-2022-08-16-OT-81", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FE", "id": "22USLC-2022-08-16-OT-83", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-76", "text": "PAX AC", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-78", "text": "PAX AC", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-80", "text": "PAX AC", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-82", "text": "PAX AC", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-84", "text": "PAX AC", "type": "fare-element"}], "flightItinerary": [{"flights": [{"connectedFlights": {"connectionTimeDuration": "3H45M", "connectionType": "CONNECTION", "flightSegments": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}]}, "id": "ST-1-ST-2"}], "id": "CONNECTION-ST-1-ST-2", "type": "CONNECTION"}, {"destinationIataCode": "ICN", "flights": [{"connectedFlights": {"connectionTimeDuration": "3H45M", "connectionType": "CONNECTION", "flightSegments": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}]}, "id": "ST-1-ST-2"}], "id": "SELL_AVAILABILITY-ST-1-ST-2", "originIataCode": "YYZ", "pointOfCommencement": {"address": {"countryCode": "CA"}}, "type": "SELL_AVAILABILITY", "yield": {"amount": "672.0", "elementaryPriceType": "ORIGIN_AND_DESTINATION_YIELD"}}], "id": "22USLC-2022-08-16", "lastModification": {"comment": "ARI-WS-AC/WSACARI", "dateTime": "2022-09-30T11:45:00Z", "pointOfSale": {"login": {"cityCode": "YUL", "countryCode": "CA", "dutyCode": "RC", "initials": "WS", "numericSign": "9999"}, "office": {"agentType": "AIRLINE", "iataNumber": "69992333", "id": "YULAC01RI", "systemCode": "AC"}}}, "nip": 5, "owner": {"login": {"cityCode": "YVR", "countryCode": "CA", "dutyCode": "RC", "initials": "WS"}, "office": {"agentType": "AIRLINE", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}, "paymentMethods": [{"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-100", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-101", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-102", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-103", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-104", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}], "products": [{"id": "22USLC-2022-08-16-OT-273", "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-09-30T11:45:00Z", "pointOfSale": {"office": {"id": "YULAC01RI"}}}, "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "ETKT REVOKED BY AC DUE TO NOSHOW"}, "subType": "SERVICE", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "22USLC-2022-08-16-OT-274", "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-09-30T11:45:00Z", "pointOfSale": {"office": {"id": "YULAC01RI"}}}, "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "NOSHOW ON AC 64/30SEP22 - DOWNLINE ITIN CANCELED"}, "subType": "SERVICE", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "77W"}, "arrival": {"dateTime": "2022-09-23T14:30:00Z", "iataCode": "AUH", "localDateTime": "2022-09-23T18:30:00", "terminal": "3"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA"}, "office": {"agentType": "WEB", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "00"}}}, "deliveries": [{"distributionId": "600210E9000019DC", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-165", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-170", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019DD", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-166", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-172", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019DE", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-167", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-174", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019DF", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-168", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-176", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E0", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-169", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-178", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-09-23T01:40:00Z", "iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00", "terminal": "3"}, "id": "2022-09-22-YYZ-AUH", "isInformational": false, "marketing": {"bookingClass": {"cabin": {"bidPrice": {"amount": "1.0", "elementaryPriceType": "BID_PRICE"}, "code": "Y"}, "code": "N", "levelOfService": "ECONOMY", "subClass": {"code": 2, "pointOfSale": {"login": {"countryCode": "CA"}, "office": {"systemCode": "AC"}}, "yields": [{"amount": "672.0", "elementaryPriceType": "ADJUSTED_YIELD"}, {"amount": "670.0", "elementaryPriceType": "EFFECTIVE_YIELD"}, {"amount": "672.0", "elementaryPriceType": "OND_YIELD_YYZ_ICN"}]}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "id": "EY-140-2022-09-22-YYZ-AUH", "isOpenNumber": false, "isPrime": true}}, "id": "22USLC-2022-08-16-ST-1", "products": [{"id": "22USLC-2022-08-16-OT-273", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-OT-274", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "781"}, "arrival": {"dateTime": "2022-09-24T02:40:00Z", "iataCode": "ICN", "localDateTime": "2022-09-24T11:40:00", "terminal": "1"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA"}, "office": {"agentType": "WEB", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "00"}}}, "deliveries": [{"distributionId": "600210E9000019E1", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-180", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-190", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E2", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-182", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-192", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E3", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-184", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-194", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E4", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-186", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-196", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E5", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-188", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-198", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-09-23T18:15:00Z", "iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00", "terminal": "3"}, "id": "2022-09-23-AUH-ICN", "isInformational": false, "marketing": {"bookingClass": {"cabin": {"bidPrice": {"amount": "1.0", "elementaryPriceType": "BID_PRICE"}, "code": "Y"}, "code": "N", "levelOfService": "ECONOMY", "subClass": {"code": 2, "pointOfSale": {"login": {"countryCode": "CA"}, "office": {"systemCode": "AC"}}, "yields": [{"amount": "672.0", "elementaryPriceType": "ADJUSTED_YIELD"}, {"amount": "670.0", "elementaryPriceType": "EFFECTIVE_YIELD"}, {"amount": "672.0", "elementaryPriceType": "OND_YIELD_YYZ_ICN"}]}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "id": "EY-856-2022-09-23-AUH-ICN", "isOpenNumber": false, "isPrime": true}}, "id": "22USLC-2022-08-16-ST-2", "products": [{"id": "22USLC-2022-08-16-OT-273", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-OT-274", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "789"}, "arrival": {"dateTime": "2022-09-30T18:35:00Z", "iataCode": "YVR", "localDateTime": "2022-09-30T11:35:00", "terminal": "M"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA"}, "office": {"agentType": "WEB", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "1A"}}}, "departure": {"dateTime": "2022-09-30T08:45:00Z", "iataCode": "ICN", "localDateTime": "2022-09-30T17:45:00", "terminal": "1"}, "id": "2022-09-30-ICN-YVR", "isInformational": false, "marketing": {"bookingClass": {"code": "T"}, "flightDesignator": {"carrierCode": "AC", "flightNumber": "64"}, "id": "AC-64-2022-09-30-ICN-YVR", "isOpenNumber": false}}, "id": "22USLC-2022-08-16-ST-3", "products": [{"id": "22USLC-2022-08-16-OT-273", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-OT-274", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}], "purgeDate": {"date": "2022-10-05"}, "queuingOffice": {"id": "YVRAC08ST"}, "quotations": [{"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-1-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-1-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-1-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YVR", "documentType": "ETICKET", "flags": ["PNR_CHANGED"], "id": "22USLC-2022-08-16-QT-1", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-09-30T00:00:00Z", "pointOfSale": {"office": {"id": "YULAC01RI"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "F", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "fareType": "PRIVATE", "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-2-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-2-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-2-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YVR", "documentType": "ETICKET", "flags": ["PNR_CHANGED"], "id": "22USLC-2022-08-16-QT-2", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-09-30T00:00:00Z", "pointOfSale": {"office": {"id": "YULAC01RI"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "F", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "fareType": "PRIVATE", "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-3-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-3-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-3-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YVR", "documentType": "ETICKET", "flags": ["PNR_CHANGED"], "id": "22USLC-2022-08-16-QT-3", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-09-30T00:00:00Z", "pointOfSale": {"office": {"id": "YULAC01RI"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "5", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-4-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-4-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-4-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YVR", "documentType": "ETICKET", "flags": ["PNR_CHANGED"], "id": "22USLC-2022-08-16-QT-4", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-09-30T00:00:00Z", "pointOfSale": {"office": {"id": "YULAC01RI"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "5", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-5-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-5-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-5-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YVR", "documentType": "ETICKET", "flags": ["PNR_CHANGED"], "id": "22USLC-2022-08-16-QT-5", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-09-30T00:00:00Z", "pointOfSale": {"office": {"id": "YULAC01RI"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "5", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}], "reference": "22USLC", "remarks": [{"content": "BENEFIT CODE PRWWPY USED FOR THIS BOOKING", "id": "22USLC-2022-08-16-OT-105", "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}], "sourcePublicationId": "2269", "ticketingReferences": [{"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648260", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-152", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648262", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-153", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648264", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-154", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648266", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-155", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648268", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-156", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}], "travelers": [{"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "1994-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-2", "names": [{"firstName": "RUBY", "lastName": "JOHNSON"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-160", "passengerDetails": {"dateOfBirth": "1994-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033AE"}, "passengerTypeCode": "ADT", "type": "stakeholder"}, {"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "1994-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-3", "names": [{"firstName": "JOHN", "lastName": "BILL"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-161", "passengerDetails": {"dateOfBirth": "1994-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033AF"}, "passengerTypeCode": "ADT", "type": "stakeholder"}, {"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2016-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-4", "names": [{"firstName": "JOHN", "lastName": "BILL"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-162", "passengerDetails": {"dateOfBirth": "2016-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033B0"}, "passengerTypeCode": "CHD", "type": "stakeholder"}, {"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2021-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-5", "names": [{"firstName": "JOHN", "lastName": "BILL"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-163", "passengerDetails": {"dateOfBirth": "2021-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033B1"}, "passengerTypeCode": "INS", "type": "stakeholder"}, {"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2021-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-6", "names": [{"firstName": "JOHN", "lastName": "BILL"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-164", "passengerDetails": {"dateOfBirth": "2021-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033B2"}, "passengerTypeCode": "INS", "type": "stakeholder"}], "type": "pnr", "version": "20"}}, "id": "22USLC-2022-08-16", "previous": {"correlations": [{"name": "PNR-EMD", "relation": {"rel": "related"}}, {"name": "PNR-TKT", "relation": {"rel": "related"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "automatedProcesses": [{"applicableCarrierCode": "AC", "code": "OK", "dateTime": "2022-08-16T00:00:00", "id": "22USLC-2022-08-16-OT-151", "isApplicableToInfants": false, "office": {"id": "YVRAC08ST"}, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}], "contacts": [{"id": "22USLC-2022-08-16-OT-71", "phone": {"number": "(44) *********"}, "purpose": ["STANDARD"], "travelerRefs": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"email": {"address": "<EMAIL>"}, "id": "22USLC-2022-08-16-OT-72", "purpose": ["STANDARD"], "travelerRefs": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}], "creation": {"comment": "DAPI-AC/CAACDCAWD", "dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA", "initials": "DA", "numericSign": "9999"}, "office": {"agentType": "AIRLINE", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "fareElements": [{"code": "FE", "id": "22USLC-2022-08-16-OT-75", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FE", "id": "22USLC-2022-08-16-OT-77", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FE", "id": "22USLC-2022-08-16-OT-79", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FE", "id": "22USLC-2022-08-16-OT-81", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FE", "id": "22USLC-2022-08-16-OT-83", "text": "PAX BG:EY", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-76", "text": "PAX AC", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-78", "text": "PAX AC", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-80", "text": "PAX AC", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-82", "text": "PAX AC", "type": "fare-element"}, {"code": "FV", "id": "22USLC-2022-08-16-OT-84", "text": "PAX AC", "type": "fare-element"}], "flightItinerary": [{"flights": [{"connectedFlights": {"connectionTimeDuration": "4H55M", "connectionType": "INTERACTIVE_MARRIAGE", "flightSegments": [{"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}]}, "id": "ST-3-ST-4"}, {"connectedFlights": {"connectionTimeDuration": "-18H-10M", "connectionType": "INTERACTIVE_MARRIAGE", "flightSegments": [{"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}]}, "id": "ST-4-ST-5"}], "id": "CONNECTION-ST-3-ST-4-ST-5", "type": "CONNECTION"}, {"flights": [{"connectedFlights": {"connectionTimeDuration": "3H45M", "connectionType": "CONNECTION", "flightSegments": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}]}, "id": "ST-1-ST-2"}], "id": "CONNECTION-ST-1-ST-2", "type": "CONNECTION"}, {"destinationIataCode": "ICN", "flights": [{"connectedFlights": {"connectionTimeDuration": "3H45M", "connectionType": "CONNECTION", "flightSegments": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}]}, "id": "ST-1-ST-2"}], "id": "SELL_AVAILABILITY-ST-1-ST-2", "originIataCode": "YYZ", "pointOfCommencement": {"address": {"countryCode": "CA"}}, "type": "SELL_AVAILABILITY", "yield": {"amount": "672.0", "elementaryPriceType": "ORIGIN_AND_DESTINATION_YIELD"}}], "id": "22USLC-2022-08-16", "lastModification": {"comment": "/DCS-SYNCUS-0001AA/LON1A0955", "dateTime": "2022-09-26T18:15:00Z", "pointOfSale": {"login": {"cityCode": "LON", "countryCode": "GB", "dutyCode": "SU", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE", "iataNumber": "00010301", "id": "LON1A0955", "systemCode": "1A"}}}, "loyaltyRequests": [{"code": "FQTR", "id": "22USLC-2022-08-16-OT-46", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-47", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-51", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-52", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-56", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-57", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-61", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-62", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-66", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}, {"code": "FQTR", "id": "22USLC-2022-08-16-OT-67", "membership": {"activeTier": {"companyCode": "AC"}, "id": "*********", "membershipType": "INDIVIDUAL", "verificationStatus": "VERIFIED"}, "nip": 1, "products": [{"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}], "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "travelers": [{"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "service"}], "nip": 5, "owner": {"login": {"cityCode": "YVR", "countryCode": "CA", "dutyCode": "SU", "initials": "AA"}, "office": {"agentType": "AIRLINE", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}, "paymentMethods": [{"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-100", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-101", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-102", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-103", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "IRU", "fopIndicator": "NEW", "freeText": "FFAAC*********-PTS66100*A"}, {"code": "IRU", "fopIndicator": "NEW", "freeText": "FPRAC*********-PRWWPY*A"}, {"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA222240XXXXXX0007/0330*CV/CAD140.21/A094855"}], "id": "22USLC-2022-08-16-OT-104", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}], "products": [{"id": "22USLC-2022-08-16-OT-14", "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "CHLD", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"initials": "DA", "numericSign": "9999"}, "office": {"id": "YVRAC08ST"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "01JAN16"}, "subType": "SERVICE", "travelers": [{"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "22USLC-2022-08-16-OT-15", "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"initials": "DA", "numericSign": "9999"}, "office": {"id": "YVRAC08ST"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "01JAN21 OCCUPYING SEAT"}, "subType": "SERVICE", "travelers": [{"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "22USLC-2022-08-16-OT-16", "products": [{"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"initials": "DA", "numericSign": "9999"}, "office": {"id": "YVRAC08ST"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "01JAN21 OCCUPYING SEAT"}, "subType": "SERVICE", "travelers": [{"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "22USLC-2022-08-16-OT-17", "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"initials": "DA", "numericSign": "9999"}, "office": {"id": "YVRAC08ST"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "01JAN21 OCCUPYING SEAT"}, "subType": "SERVICE", "travelers": [{"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "22USLC-2022-08-16-OT-18", "products": [{"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"initials": "DA", "numericSign": "9999"}, "office": {"id": "YVRAC08ST"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "01JAN21 OCCUPYING SEAT"}, "subType": "SERVICE", "travelers": [{"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "77W"}, "arrival": {"dateTime": "2022-09-23T14:30:00Z", "iataCode": "AUH", "localDateTime": "2022-09-23T18:30:00", "terminal": "3"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA"}, "office": {"agentType": "WEB", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "00"}}}, "deliveries": [{"distributionId": "600210E9000019DC", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-165", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-170", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019DD", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-166", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-172", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019DE", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-167", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-174", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019DF", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-168", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-176", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E0", "flightSegment": {"arrival": {"iataCode": "AUH"}, "departure": {"iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-169", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "AUH"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "YYZ"}, "handlingCarrier": "EY", "hasBags": false, "id": "22USLC-2022-08-16-OT-178", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-09-23T01:40:00Z", "iataCode": "YYZ", "localDateTime": "2022-09-22T21:40:00", "terminal": "3"}, "id": "2022-09-22-YYZ-AUH", "isInformational": false, "marketing": {"bookingClass": {"cabin": {"bidPrice": {"amount": "1.0", "elementaryPriceType": "BID_PRICE"}, "code": "Y"}, "code": "N", "levelOfService": "ECONOMY", "subClass": {"code": 2, "pointOfSale": {"login": {"countryCode": "CA"}, "office": {"systemCode": "AC"}}, "yields": [{"amount": "672.0", "elementaryPriceType": "ADJUSTED_YIELD"}, {"amount": "670.0", "elementaryPriceType": "EFFECTIVE_YIELD"}, {"amount": "672.0", "elementaryPriceType": "OND_YIELD_YYZ_ICN"}]}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "140"}, "id": "EY-140-2022-09-22-YYZ-AUH", "isOpenNumber": false, "isPrime": true}}, "id": "22USLC-2022-08-16-ST-1", "products": [{"id": "22USLC-2022-08-16-OT-14", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-OT-15", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-OT-17", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "22USLC-2022-08-16-PT-5-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "781"}, "arrival": {"dateTime": "2022-09-24T02:40:00Z", "iataCode": "ICN", "localDateTime": "2022-09-24T11:40:00", "terminal": "1"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA"}, "office": {"agentType": "WEB", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "00"}}}, "deliveries": [{"distributionId": "600210E9000019E1", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-180", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-190", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E2", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-182", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-192", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E3", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-184", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-194", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E4", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-186", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-196", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "600210E9000019E5", "flightSegment": {"arrival": {"iataCode": "ICN"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "N"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "isOpenNumber": false}}, "id": "22USLC-2022-08-16-OT-188", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "ICN"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "hasBags": false, "id": "22USLC-2022-08-16-OT-198", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-09-23T18:15:00Z", "iataCode": "AUH", "localDateTime": "2022-09-23T22:15:00", "terminal": "3"}, "id": "2022-09-23-AUH-ICN", "isInformational": false, "marketing": {"bookingClass": {"cabin": {"bidPrice": {"amount": "1.0", "elementaryPriceType": "BID_PRICE"}, "code": "Y"}, "code": "N", "levelOfService": "ECONOMY", "subClass": {"code": 2, "pointOfSale": {"login": {"countryCode": "CA"}, "office": {"systemCode": "AC"}}, "yields": [{"amount": "672.0", "elementaryPriceType": "ADJUSTED_YIELD"}, {"amount": "670.0", "elementaryPriceType": "EFFECTIVE_YIELD"}, {"amount": "672.0", "elementaryPriceType": "OND_YIELD_YYZ_ICN"}]}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "856"}, "id": "EY-856-2022-09-23-AUH-ICN", "isOpenNumber": false, "isPrime": true}}, "id": "22USLC-2022-08-16-ST-2", "products": [{"id": "22USLC-2022-08-16-OT-14", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-OT-16", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-OT-18", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "22USLC-2022-08-16-PT-5-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "789"}, "arrival": {"dateTime": "2022-09-30T18:35:00Z", "iataCode": "YVR", "localDateTime": "2022-09-30T11:35:00", "terminal": "M"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA"}, "office": {"agentType": "WEB", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "1A"}}}, "departure": {"dateTime": "2022-09-30T08:45:00Z", "iataCode": "ICN", "localDateTime": "2022-09-30T17:45:00", "terminal": "1"}, "id": "2022-09-30-ICN-YVR", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"code": "T"}, "flightDesignator": {"carrierCode": "AC", "flightNumber": "64"}, "id": "AC-64-2022-09-30-ICN-YVR", "isOpenNumber": false}}, "id": "22USLC-2022-08-16-ST-3", "subType": "AIR", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "7M8"}, "arrival": {"dateTime": "2022-10-01T01:05:00Z", "iataCode": "YEG", "localDateTime": "2022-09-30T19:05:00"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA"}, "office": {"agentType": "WEB", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "1A"}}}, "departure": {"dateTime": "2022-09-30T23:30:00Z", "iataCode": "YVR", "localDateTime": "2022-09-30T16:30:00", "terminal": "M"}, "id": "2022-09-30-YVR-YEG", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"code": "X"}, "flightDesignator": {"carrierCode": "AC", "flightNumber": "242"}, "id": "AC-242-2022-09-30-YVR-YEG", "isOpenNumber": false}}, "id": "22USLC-2022-08-16-ST-4", "subType": "AIR", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "7M8"}, "arrival": {"dateTime": "2022-10-01T10:38:00Z", "iataCode": "YYZ", "localDateTime": "2022-10-01T06:38:00", "terminal": "1"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"login": {"cityCode": "YVR", "countryCode": "CA"}, "office": {"agentType": "WEB", "iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "1A"}}}, "departure": {"dateTime": "2022-10-01T06:55:00Z", "iataCode": "YEG", "localDateTime": "2022-10-01T00:55:00"}, "id": "2022-10-01-YEG-YYZ", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"code": "T"}, "flightDesignator": {"carrierCode": "AC", "flightNumber": "176"}, "id": "AC-176-2022-10-01-YEG-YYZ", "isOpenNumber": false}}, "id": "22USLC-2022-08-16-ST-5", "subType": "AIR", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}], "purgeDate": {"date": "2022-10-05"}, "queuingOffice": {"id": "YVRAC08ST"}, "quotations": [{"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-1-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-1-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-1-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-1-4", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-1-5", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-10-01", "notValidBeforeDate": "2022-10-01"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YTO", "documentType": "ETICKET", "id": "22USLC-2022-08-16-QT-1", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "F", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "fareType": "PRIVATE", "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-2-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-2-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-2-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-2-4", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-2-5", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-10-01", "notValidBeforeDate": "2022-10-01"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YTO", "documentType": "ETICKET", "id": "22USLC-2022-08-16-QT-2", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "F", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "fareType": "PRIVATE", "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-3-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-3-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-3-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-3-4", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-3-5", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-10-01", "notValidBeforeDate": "2022-10-01"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YTO", "documentType": "ETICKET", "id": "22USLC-2022-08-16-QT-3", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "5", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-4-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-4-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-4-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-4-4", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-4-5", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-10-01", "notValidBeforeDate": "2022-10-01"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YTO", "documentType": "ETICKET", "id": "22USLC-2022-08-16-QT-4", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "5", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-5-1", "product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-22", "notValidBeforeDate": "2022-09-22"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "00TG", "primaryCode": "NBP", "ticketDesignatorCode": "AE4"}, "id": "22USLC-2022-08-16-QT-5-2", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-23", "notValidBeforeDate": "2022-09-23"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-5-3", "product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-5-4", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-09-30", "notValidBeforeDate": "2022-09-30"}}, {"baggageAllowance": {"quantity": 2}, "fareBasis": {"fareBasisCode": "ROTG", "primaryCode": "TAE", "ticketDesignatorCode": "AE1"}, "id": "22USLC-2022-08-16-QT-5-5", "isFromConnection": true, "product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-10-01", "notValidBeforeDate": "2022-10-01"}}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "destinationCityIataCode": "YTO", "documentType": "ETICKET", "id": "22USLC-2022-08-16-QT-5", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "lastTicketingDate": "2022-08-19", "originCityIataCode": "YTO", "price": {"detailedPrices": [{"amount": "140.21", "currency": "CAD", "elementaryPriceType": "TOTAL"}, {"amount": "0.00", "currency": "CAD", "elementaryPriceType": "BASE_FARE"}, {"amount": "140.21", "currency": "CAD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "39.00", "category": "NEW", "code": "DU", "currency": "CAD", "nature": "XX"}, {"amount": "25.91", "category": "NEW", "code": "CA", "currency": "CAD", "nature": "AE"}, {"amount": "3.90", "category": "NEW", "code": "RC", "currency": "CAD", "nature": "AB"}, {"amount": "30.00", "category": "NEW", "code": "SQ", "currency": "CAD", "nature": "AD"}, {"amount": "12.20", "category": "NEW", "code": "F6", "currency": "CAD", "nature": "TO"}, {"amount": "1.70", "category": "NEW", "code": "ZR", "currency": "CAD", "nature": "AP"}, {"amount": "27.50", "category": "NEW", "code": "BP", "currency": "CAD", "nature": "DP"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "5", "text": "YTO EY X/AUH0.00EY SEL0.00AC X/YVR0.00AC X/YEA0.00AC YTO0.00NUC0.00END ROE1.295097"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}], "reference": "22USLC", "remarks": [{"content": "BENEFIT CODE PRWWPY USED FOR THIS BOOKING", "id": "22USLC-2022-08-16-OT-105", "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "subType": "RM", "travelers": [{"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}], "sourcePublicationId": "2269", "ticketingReferences": [{"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648260", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-152", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-2", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648262", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-153", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648264", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-154", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-4", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648266", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-155", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}], "creation": {"dateTime": "2022-08-16T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "69991305", "id": "YVRAC08ST", "systemCode": "AC"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "CAD", "total": "140.21"}, "primaryDocumentNumber": "0142172648268", "status": "ISSUED"}], "id": "22USLC-2022-08-16-OT-156", "isInfant": false, "products": [{"id": "22USLC-2022-08-16-ST-1", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-2", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-3", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-4", "ref": "processedPnr.products", "type": "product"}, {"id": "22USLC-2022-08-16-ST-5", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}], "travelers": [{"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "1994-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-2", "identityDocuments": [{"code": "DOCS", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "document": {"birthDate": "1994-01-01", "gender": "UNKNOWN", "name": {"firstName": "RUBY", "fullName": "RUBY JOHNSON", "lastName": "JOHNSON"}}, "id": "22USLC-2022-08-16-OT-106", "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "////01JAN94/U//JOHNSON/RUBY", "type": "service"}], "names": [{"firstName": "RUBY", "lastName": "JOHNSON"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-160", "passengerDetails": {"dateOfBirth": "1994-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033AE"}, "passengerTypeCode": "ADT", "type": "stakeholder"}, {"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "1994-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-3", "identityDocuments": [{"code": "DOCS", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "document": {"birthDate": "1994-01-01", "gender": "UNKNOWN", "name": {"firstName": "JOHN", "fullName": "JOHN BILL", "lastName": "BILL"}}, "id": "22USLC-2022-08-16-OT-110", "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "////01JAN94/U//BILL/JOHN", "type": "service"}], "names": [{"firstName": "JOHN", "lastName": "BILL"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-161", "passengerDetails": {"dateOfBirth": "1994-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033AF"}, "passengerTypeCode": "ADT", "type": "stakeholder"}, {"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2016-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-4", "identityDocuments": [{"code": "DOCS", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "document": {"birthDate": "2016-01-01", "gender": "UNKNOWN", "name": {"firstName": "JOHN", "fullName": "JOHN BILL", "lastName": "BILL"}}, "id": "22USLC-2022-08-16-OT-114", "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "////01JAN16/U//BILL/JOHN", "type": "service"}], "names": [{"firstName": "JOHN", "lastName": "BILL"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-162", "passengerDetails": {"dateOfBirth": "2016-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033B0"}, "passengerTypeCode": "CHD", "type": "stakeholder"}, {"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2021-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-5", "identityDocuments": [{"code": "DOCS", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "document": {"birthDate": "2021-01-01", "gender": "UNKNOWN", "name": {"firstName": "JOHN", "fullName": "JOHN BILL", "lastName": "BILL"}}, "id": "22USLC-2022-08-16-OT-118", "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "////01JAN21/U//BILL/JOHN", "type": "service"}], "names": [{"firstName": "JOHN", "lastName": "BILL"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-163", "passengerDetails": {"dateOfBirth": "2021-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033B1"}, "passengerTypeCode": "INS", "type": "stakeholder"}, {"contacts": [{"id": "22USLC-2022-08-16-OT-71", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "22USLC-2022-08-16-OT-72", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2021-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-6", "identityDocuments": [{"code": "DOCS", "creation": {"dateTime": "2022-08-16T07:52:00Z", "pointOfSale": {"office": {"id": "YVRAC08ST"}}}, "document": {"birthDate": "2021-01-01", "gender": "UNKNOWN", "name": {"firstName": "JOHN", "fullName": "JOHN BILL", "lastName": "BILL"}}, "id": "22USLC-2022-08-16-OT-122", "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "////01JAN21/U//BILL/JOHN", "type": "service"}], "names": [{"firstName": "JOHN", "lastName": "BILL"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "22USLC-2022-08-16-OT-164", "passengerDetails": {"dateOfBirth": "2021-01-01", "gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "6101D0E8000033B2"}, "passengerTypeCode": "INS", "type": "stakeholder"}, {"adult": {"id": "22USLC-2022-08-16-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "dateOfBirth": "2021-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-5-INF", "passengerTypeCode": "INF", "type": "stakeholder"}, {"adult": {"id": "22USLC-2022-08-16-PT-6", "ref": "processedPnr.travelers", "type": "stakeholder"}, "dateOfBirth": "2021-01-01", "gender": "UNKNOWN", "id": "22USLC-2022-08-16-PT-6-INF", "passengerTypeCode": "INF", "type": "stakeholder"}], "type": "pnr", "version": "15"}}, "type": "com.amadeus.pulse.message.Pnr"}}