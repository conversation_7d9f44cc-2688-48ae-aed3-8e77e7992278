# Readme

## Closure for passenger disparition

The files in the data/pnr and data/tkt folders correspond to the following versions of PNR 6I2QQ4 and TKT 6076508511307, 6076508511308 and 6076508511306 (I dropped correlation block from TKT files, since we are focused on correlation from PNR):

```
PNR v4
PNR v5
TKT..06 v0
TKT..07 v0
TKT..08 v0
```

This data has been extracted from EY UAT data.

in `PNR v4`, we have the following passengers :
```
6I2QQ4-2022-11-21-PT-1          => connected to ticket ..06
6I2QQ4-2022-11-21-PT-1-INF      => connected to ticket ..08
6I2QQ4-2022-11-21-PT-2          => connected to ticket ..07
```
in `PNR v5`, the number of passengers will decrease :
```
6I2QQ4-2022-11-21-PT-1          => connected to ticket ..06
6I2QQ4-2022-11-21-PT-1-INF      => connected to ticket ..08
```

To make efficient test of closure, you have to feed input json progressively :
 - First you add PNR v4 and all TKT files and you make the first run
 - Then you add PNR v5 and you run again to update results