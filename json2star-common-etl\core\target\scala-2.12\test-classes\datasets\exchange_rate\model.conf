{
  "defaultComment": "Exchange Rate",
  "tables": [{
    "name": "EXCHANGE_RATE_HISTO",
    "mapping": {
      "description": {
        "description": "Exchange Rate",
        "granularity": "1 row per exchange rate currency pair",
      },
      "merge": {
        "key-columns": ["EXCHANGE_RATE_ID", "REFERENCE_DATE"],
        "if-dupe-take-higher": ["REFERENCE_DATE"]
      },
      "root-sources": [
        {
          "blocks": [
            {"base": "$.rows[*]"}
          ]
        }
      ],
      "columns": [
        {"name": "EXCHANGE_RATE_ID", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.label"}]}},
        {"name": "REFERENCE_DATE", "column-type": "strColumn", "sources": {"literal": "@LOAD_DATE"}},
        {"name": "FROM", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.content.Code.content"}]}},
        {"name": "TO", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.content.Base.content"}]}},
        {"name": "RATE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.content.Rate.content"}]}},
        {"name": "NUM_DECIMAL", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.content.NumberOfImpliedDecimals.content"}]}},
        {
          "name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"literal": "@LOAD_DATE"},
          "meta": {"description": {"value": "", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "DATE_END", "column-type": "timestampColumn", "sources": {},
          "meta": {"description": {"value": "Envelope/version validity end date/time UTC, computed: if last envelope/version: 9999-01-01, otherwise date_begin of following envelope/version", "rule": "replace"}, "gdpr-zone": "green"}
        },
        {
          "name": "IS_LAST_REFERENCE_DATE", "column-type": "booleanColumn", "sources": {},
          "meta": {"description": {"value": "Indicates if thi is the latest record", "rule": "replace"}, "gdpr-zone": "green"}
        }
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          "pit-key": "EXCHANGE_RATE_ID",
          "pit-version": "REFERENCE_DATE",
          "valid-from": "DATE_BEGIN",
          "valid-to": "DATE_END",
          "is-last": "IS_LAST_REFERENCE_DATE"
        }
      }
    }
  }]
}