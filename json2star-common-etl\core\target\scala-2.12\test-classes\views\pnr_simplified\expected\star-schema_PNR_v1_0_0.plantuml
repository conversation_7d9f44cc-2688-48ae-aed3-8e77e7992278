@startuml star-schema_PNR_v1_0_0

' Header
hide circle
hide <<assotable>> stereotype
hide <<dimtable>> stereotype
hide <<maintable>> stereotype
hide <<mainsubdomain>> stereotype
hide methods
left to right direction

!define TABLE_GRADIENT_BACKGROUND #F2F2F2-fcffd6
!define MAIN_TABLE_GRADIENT_HEADER Orange-%lighten("Yellow", 60)

!function $pk($content)
!return "<color:#ff0000>" + $content + "</color>"
!endfunction

!function $fk($content)
!return "<color:#0000ff>" + $content + "</color>"
!endfunction

skinparam {
    DefaultFontName Monospaced
    ranksep 250
    linetype polyline
    tabSize 4
    HyperlinkUnderline false
    HyperlinkColor #0000ff
}

skinparam frame {
    FontSize 28
    FontSize<<mainsubdomain>> 34
    BorderThickness<<mainsubdomain>> 3
}

skinparam class {
    BackgroundColor TABLE_GRADIENT_BACKGROUND
    HeaderBackgroundColor %lighten("Crimson", 40)
    HeaderBackgroundColor<<maintable>> MAIN_TABLE_GRADIENT_HEADER
    HeaderBackgroundColor<<dimtable>> LightBlue
    HeaderBackgroundColor<<assotable>> %lighten("LimeGreen", 30)
    ColorArrowSeparationSpace Red
    BorderColor Black
    BorderColor<<maintable>> MediumBlue
    BorderThickness<<maintable>> 3
    ArrowColor Blue
    FontSize 16
    FontSize<<maintable>> 20
    FontStyle Bold
}

' Frames
frame "Reservation"<<mainsubdomain>> #E4C7FF {

entity "FACT_RESERVATION_HISTO"<<maintable>> {
RESERVATION_ID                 Binary     NN    
REFERENCE_KEY                  String     NN    
RECORD_LOCATOR                 String     NN    
PNR_PROPERTIES                 String           
NUMBER_IN_PARTY                Integer          
GROUP_SIZE                     Integer          
GROUP_NAME                     String           
GROUP_SIZE_TAKEN               Integer          
GROUP_SIZE_REMAINING           Integer          
OWNER_OFFICE_IDENTIFIER        String           
CREATION_DATETIME_UTC          Timestamp  NN    
CREATION_OFFICE_IDENTIFIER     String           
CREATION_COMMENT               String           
LAST_UPDATE_DATETIME_UTC       Timestamp        
LAST_UPDATE_OFFICE_IDENTIFIER  String           
LAST_UPDATE_COMMENT            String           
QUEUING_OFFICE_IDENTIFIER      String           
$fk("[[#{DIM_POINT_OF_SALE} OWNER_POINT_OF_SALE_ID]]         Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]      Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} LAST_UPDATE_POINT_OF_SALE_ID]]   Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} QUEUING_POINT_OF_SALE_ID]]       Binary         FK")
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "FACT_KEYWORD_HISTO" {
$pk("KEYWORD_ID                  Binary     NN  PK")
REFERENCE_KEY               String     NN    
SERVICE_CODE                String           
SERVICE_SUB_TYPE            String           
SERVICE_PROVIDER            String           
TEXT                        String           
STATUS                      String           
CREATION_DATETIME_UTC       Timestamp        
CREATION_OFFICE_IDENTIFIER  String           
CREATION_COMMENT            String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]              Binary     NN  FK")
$fk("[[#{DIM_SERVICE} SERVICE_ID]]                  Binary         FK")
$fk("[[#{DIM_AIRLINE} SERVICE_PROVIDER_ID]]         Binary         FK")
$fk("[[#{DIM_BOOKING_STATUS} STATUS_ID]]                   Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]   Binary         FK")
RECORD_LOCATOR              String     NN    
PNR_CREATION_DATE           Timestamp  NN    
$pk("VERSION                     Integer    NN  PK")
DATE_BEGIN                  Timestamp  NN    
DATE_END                    Timestamp        
IS_LAST_VERSION             Boolean          
LOAD_DATE                   Timestamp        
}

entity "FACT_REMARK_HISTO" {
$pk("REMARK_ID          Binary     NN  PK")
REFERENCE_KEY      String     NN    
CATEGORY           String           
SUB_TYPE           String           
TEXT               String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]     Binary     NN  FK")
RECORD_LOCATOR     String     NN    
PNR_CREATION_DATE  Timestamp  NN    
$pk("VERSION            Integer    NN  PK")
DATE_BEGIN         Timestamp  NN    
DATE_END           Timestamp        
IS_LAST_VERSION    Boolean          
LOAD_DATE          Timestamp        
}

entity "FACT_ASSOCIATED_RESERVATION_HISTO" {
$pk("ASSOCIATED_RESERVATION_ID             Binary     NN  PK")
REFERENCE_KEY                         String     NN    
ASSOCIATION_TYPE                      String           
ASSOCIATION_DIRECTION                 String           
ASSOCIATED_RECORD_LOCATOR             String           
ASSOCIATED_CREATION_DATETIME_UTC      Timestamp        
ASSOCIATED_CREATION_SYSTEM_CODE       String           
$pk("RELATES_TO                            String         PK")
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                        Binary     NN  FK")
$fk("[[#{Segment Booking.FACT_FLIGHT_TRANSFER_HISTO} FLIGHT_TRANSFER_ID]]                    Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} ASSOCIATED_CREATION_POINT_OF_SALE_ID]]  Binary         FK")
RECORD_LOCATOR                        String     NN    
PNR_CREATION_DATE                     Timestamp  NN    
$pk("VERSION                               Integer    NN  PK")
DATE_BEGIN                            Timestamp  NN    
DATE_END                              Timestamp        
IS_LAST_VERSION                       Boolean          
LOAD_DATE                             Timestamp        
}
}

frame "Traveler" #FFD7D7 {

entity "FACT_TRAVELER_HISTO"<<maintable>> {
$pk("TRAVELER_ID                 Binary     NN  PK")
REFERENCE_KEY               String     NN    
UNIQUE_CUSTOMER_IDENTIFIER  String           
TITLE                       String           
FIRST_NAME                  String           
LAST_NAME                   String           
FULL_NAME                   String           
PASSENGER_TYPE_CODE         String           
GENDER                      String           
NATIONALITY                 String           
BIRTH_DATE                  Date             
BIRTH_PLACE                 String           
COUNTRY_OF_RESIDENCE        String           
IDENTIFICATION_CODE         String           
STAFF_TYPE                  String           
SPECIAL_SEAT                String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]              Binary     NN  FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} RELATED_INFANT_ID]]           Binary         FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} RELATED_ADULT_ID]]            Binary         FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} RELATED_CBBG_ID]]             Binary         FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} RELATED_EXST_ID]]             Binary         FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} RELATED_OWNER_CBBG_EXST_ID]]  Binary         FK")
$fk("[[#{DIM_PASSENGER_TYPE} PASSENGER_TYPE_ID]]           Binary         FK")
RECORD_LOCATOR              String     NN    
PNR_CREATION_DATE           Timestamp  NN    
$pk("VERSION                     Integer    NN  PK")
DATE_BEGIN                  Timestamp  NN    
DATE_END                    Timestamp        
IS_LAST_VERSION             Boolean          
LOAD_DATE                   Timestamp        
}

entity "FACT_ADDRESS_HISTO" {
$pk("ADDRESS_ID         Binary     NN  PK")
REFERENCE_KEY      String     NN    
CATEGORY           String           
LINES              String           
POSTAL_BOX         String           
POSTAL_CODE        String           
CITY_NAME          String           
STATE_CODE         String           
COUNTRY_CODE       String           
ADDRESSEE_NAME     String           
PURPOSE            String           
LANGUAGE           String           
TEXT               String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]     Binary     NN  FK")
RECORD_LOCATOR     String     NN    
PNR_CREATION_DATE  Timestamp  NN    
$pk("VERSION            Integer    NN  PK")
DATE_BEGIN         Timestamp  NN    
DATE_END           Timestamp        
IS_LAST_VERSION    Boolean          
LOAD_DATE          Timestamp        
}

entity "FACT_PHONE_HISTO" {
$pk("PHONE_ID           Binary     NN  PK")
REFERENCE_KEY      String     NN    
CATEGORY           String           
NUMBER             String           
DEVICE_TYPE        String           
ADDRESSEE_NAME     String           
PURPOSE            String           
LANGUAGE           String           
TEXT               String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]     Binary     NN  FK")
RECORD_LOCATOR     String     NN    
PNR_CREATION_DATE  Timestamp  NN    
$pk("VERSION            Integer    NN  PK")
DATE_BEGIN         Timestamp  NN    
DATE_END           Timestamp        
IS_LAST_VERSION    Boolean          
LOAD_DATE          Timestamp        
}

entity "FACT_EMAIL_HISTO" {
$pk("EMAIL_ID           Binary     NN  PK")
REFERENCE_KEY      String     NN    
EMAIL              String           
ADDRESSEE_NAME     String           
PURPOSE            String           
LANGUAGE           String           
TEXT               String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]     Binary     NN  FK")
RECORD_LOCATOR     String     NN    
PNR_CREATION_DATE  Timestamp  NN    
$pk("VERSION            Integer    NN  PK")
DATE_BEGIN         Timestamp  NN    
DATE_END           Timestamp        
IS_LAST_VERSION    Boolean          
LOAD_DATE          Timestamp        
}

entity "FACT_IDENTITY_DOCUMENT_PAX_HISTO" {
$pk("IDENTITY_DOCUMENT_PAX_ID    Binary     NN  PK")
REFERENCE_KEY               String     NN    
FIRST_NAME                  String           
LAST_NAME                   String           
FULL_NAME                   String           
BIRTH_DATE                  Date             
BIRTH_PLACE                 String           
NATIONALITY                 String           
GENDER                      String           
DOCUMENT_NUMBER             String           
DOCUMENT_TYPE               String           
ISSUANCE_DATE               Date             
ISSUANCE_LOCATION           String           
ISSUANCE_COUNTRY            String           
EXPIRY_DATE                 String           
SERVICE_CODE                String           
SERVICE_SUB_TYPE            String           
SERVICE_PROVIDER            String           
SERVICE_TEXT                String           
SERVICE_STATUS              String           
CREATION_DATETIME_UTC       Timestamp        
CREATION_OFFICE_IDENTIFIER  String           
CREATION_COMMENT            String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]              Binary     NN  FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} TRAVELER_ID]]                 Binary         FK")
$fk("[[#{DIM_SERVICE} SERVICE_ID]]                  Binary         FK")
$fk("[[#{DIM_AIRLINE} SERVICE_PROVIDER_ID]]         Binary         FK")
$fk("[[#{DIM_BOOKING_STATUS} SERVICE_STATUS_ID]]           Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]   Binary         FK")
RECORD_LOCATOR              String     NN    
PNR_CREATION_DATE           Timestamp  NN    
$pk("VERSION                     Integer    NN  PK")
DATE_BEGIN                  Timestamp  NN    
DATE_END                    Timestamp        
IS_LAST_VERSION             Boolean          
LOAD_DATE                   Timestamp        
}
}

frame "Segment Booking" #C8FFC0 {

entity "FACT_AIR_SEGMENT_PAX_HISTO"<<maintable>> {
$pk("AIR_SEGMENT_PAX_ID                     Binary     NN  PK")
REFERENCE_KEY                          String     NN    
DEPARTURE_AIRPORT                      String           
DEPARTURE_TERMINAL                     String           
ARRIVAL_AIRPORT                        String           
ARRIVAL_TERMINAL                       String           
DEPARTURE_DATETIME_UTC                 Timestamp        
DEPARTURE_DATETIME_LOCAL               Timestamp        
ARRIVAL_DATETIME_UTC                   Timestamp        
ARRIVAL_DATETIME_LOCAL                 Timestamp        
MKT_CARRIER                            String           
MKT_FLIGHT_NUMBER                      String           
MKT_OPERATIONAL_SUFFIX                 String           
OPE_CARRIER                            String           
OPE_FLIGHT_NUMBER                      String           
OPE_OPERATIONAL_SUFFIX                 String           
BOOKING_STATUS                         String           
MKT_CABIN                              String           
MKT_BOOKING_CLASS                      String           
MKT_BOOKING_SUB_CLASS                  String           
MKT_LEVEL_OF_SERVICE                   String           
MKT_OVERBOOK_REASON                    String           
MKT_CABIN_BID_PRICE_AMOUNT_ORIGINAL    Float            
MKT_CABIN_BID_PRICE_CURRENCY_ORIGINAL  String           
OPE_CABIN                              String           
OPE_BOOKING_CLASS                      String           
OPE_BOOKING_SUB_CLASS                  String           
OPE_LEVEL_OF_SERVICE                   String           
OPE_OVERBOOK_REASON                    String           
OPE_CODESHARE_AGREEMENT                String           
OPE_CABIN_BID_PRICE_AMOUNT_ORIGINAL    Float            
OPE_CABIN_BID_PRICE_CURRENCY_ORIGINAL  String           
IS_INFORMATIONAL                       Boolean          
IS_DOMINANT_IN_MARRIAGE                Boolean          
IS_OPEN_SEGMENT                        Boolean          
AIRCRAFT_TYPE                          String           
CREATION_DATETIME_UTC                  Timestamp        
CREATION_OFFICE_IDENTIFIER             String           
CREATION_COMMENT                       String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                         Binary     NN  FK")
AIR_SEGMENT_ID                         Binary     NN    
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} TRAVELER_ID]]                            Binary     NN  FK")
$fk("[[#{DIM_BOOKING_STATUS} BOOKING_STATUS_ID]]                      Binary         FK")
$fk("[[#{DIM_AIRPORT} DEPARTURE_AIRPORT_ID]]                   Binary         FK")
$fk("[[#{DIM_AIRPORT} ARRIVAL_AIRPORT_ID]]                     Binary         FK")
$fk("[[#{DIM_AIRLINE} MKT_CARRIER_ID]]                         Binary         FK")
$fk("[[#{DIM_AIRLINE} OPE_CARRIER_ID]]                         Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]              Binary         FK")
RECORD_LOCATOR                         String     NN    
PNR_CREATION_DATE                      Timestamp  NN    
$pk("VERSION                                Integer    NN  PK")
DATE_BEGIN                             Timestamp  NN    
DATE_END                               Timestamp        
IS_LAST_VERSION                        Boolean          
LOAD_DATE                              Timestamp        
}

entity "FACT_FLIGHT_TRANSFER_HISTO" {
$pk("FLIGHT_TRANSFER_ID   Binary     NN  PK")
REFERENCE_KEY        String     NN    
TRANSFER_IDENTIFIER  String           
SUB_TYPE             String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]       Binary     NN  FK")
RECORD_LOCATOR       String     NN    
PNR_CREATION_DATE    Timestamp  NN    
$pk("VERSION              Integer    NN  PK")
DATE_BEGIN           Timestamp  NN    
DATE_END             Timestamp        
IS_LAST_VERSION      Boolean          
LOAD_DATE            Timestamp        
}

entity "FACT_ORIGIN_DESTINATION_HISTO" {
$pk("ORIGIN_DESTINATION_ID        Binary     NN  PK")
REFERENCE_KEY                String     NN    
ORIGIN                       String           
DESTINATION                  String           
TYPE                         String           
OND_YIELD_TOTAL_ORIGINAL     Float            
OND_YIELD_CURRENCY_ORIGINAL  String           
COMMENCEMENT_IATA_CODE       String           
COMMENCEMENT_NAME            String           
COMMENCEMENT_SUB_TYPE        String           
COMMENCEMENT_COUNTRY         String           
COMMENCEMENT_TIME_ZONE       String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]               Binary     NN  FK")
RECORD_LOCATOR               String     NN    
PNR_CREATION_DATE            Timestamp  NN    
$pk("VERSION                      Integer    NN  PK")
DATE_BEGIN                   Timestamp  NN    
DATE_END                     Timestamp        
IS_LAST_VERSION              Boolean          
LOAD_DATE                    Timestamp        
}

entity "FACT_FLIGHT_BOUND_PAX_HISTO" {
$pk("FLIGHT_BOUND_PAX_ID          Binary     NN  PK")
REFERENCE_KEY                String     NN    
CONNECTION_TYPE              String           
CONNECTION_TIME_DURATION     String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]               Binary     NN  FK")
$fk("[[#{Segment Booking.FACT_ORIGIN_DESTINATION_HISTO} ORIGIN_DESTINATION_ID]]        Binary     NN  FK")
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} INBOUND_AIR_SEGMENT_PAX_ID]]   Binary         FK")
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} OUTBOUND_AIR_SEGMENT_PAX_ID]]  Binary         FK")
RECORD_LOCATOR               String     NN    
PNR_CREATION_DATE            Timestamp  NN    
$pk("VERSION                      Integer    NN  PK")
DATE_BEGIN                   Timestamp  NN    
DATE_END                     Timestamp        
IS_LAST_VERSION              Boolean          
LOAD_DATE                    Timestamp        
}

entity "FACT_SEGMENT_YIELD_HISTO" {
$pk("SEGMENT_YIELD_ID       Binary     NN  PK")
REFERENCE_KEY          String     NN    
ELEMENTARY_PRICE_TYPE  String           
AMOUNT_ORIGINAL        Float            
CURRENCY_ORIGINAL      String           
$pk("RELATES_TO             String         PK")
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]         Binary     NN  FK")
RECORD_LOCATOR         String     NN    
PNR_CREATION_DATE      Timestamp  NN    
$pk("VERSION                Integer    NN  PK")
DATE_BEGIN             Timestamp  NN    
DATE_END               Timestamp        
IS_LAST_VERSION        Boolean          
LOAD_DATE              Timestamp        
}
}

frame "Service Booking" #FFC5F9 {

entity "FACT_SERVICE_PAX_HISTO"<<maintable>> {
$pk("SERVICE_PAX_ID                   Binary     NN  PK")
REFERENCE_KEY                    String           
SERVICE_CODE                     String           
SERVICE_SUB_TYPE                 String           
SERVICE_PROVIDER                 String           
TEXT                             String           
STATUS                           String           
IS_CHARGEABLE                    Boolean          
REASON_FOR_ISSUANCE_CODE         String           
REASON_FOR_ISSUANCE_SUB_CODE     String           
REASON_FOR_ISSUANCE_DESCRIPTION  String           
CREATION_DATETIME_UTC            Timestamp        
CREATION_OFFICE_IDENTIFIER       String           
CREATION_COMMENT                 String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                   Binary     NN  FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} TRAVELER_ID]]                      Binary         FK")
$fk("[[#{DIM_SERVICE} SERVICE_ID]]                       Binary         FK")
$fk("[[#{DIM_AIRLINE} SERVICE_PROVIDER_ID]]              Binary         FK")
$fk("[[#{DIM_BOOKING_STATUS} STATUS_ID]]                        Binary         FK")
$fk("[[#{DIM_REASON_FOR_ISSUANCE} REASON_FOR_ISSUANCE_ID]]           Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]        Binary         FK")
RECORD_LOCATOR                   String     NN    
PNR_CREATION_DATE                Timestamp  NN    
$pk("VERSION                          Integer    NN  PK")
DATE_BEGIN                       Timestamp  NN    
DATE_END                         Timestamp        
IS_LAST_VERSION                  Boolean          
LOAD_DATE                        Timestamp        
}

entity "FACT_SEATING_PAX_HISTO" {
$pk("SEATING_PAX_ID                   Binary     NN  PK")
REFERENCE_KEY                    String     NN    
SERVICE_CODE                     String           
SERVICE_SUB_TYPE                 String           
SERVICE_PROVIDER                 String           
SEAT_NUMBER                      String           
SEAT_CHARACTERISTICS             String           
TEXT                             String           
STATUS                           String           
IS_CHARGEABLE                    Boolean          
REASON_FOR_ISSUANCE_CODE         String           
REASON_FOR_ISSUANCE_SUB_CODE     String           
REASON_FOR_ISSUANCE_DESCRIPTION  String           
CREATION_DATETIME_UTC            Timestamp        
CREATION_OFFICE_IDENTIFIER       String           
CREATION_COMMENT                 String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                   Binary     NN  FK")
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} TRAVELER_ID]]                      Binary         FK")
$fk("[[#{DIM_SERVICE} SERVICE_ID]]                       Binary         FK")
$fk("[[#{DIM_AIRLINE} SERVICE_PROVIDER_ID]]              Binary         FK")
$fk("[[#{DIM_BOOKING_STATUS} STATUS_ID]]                        Binary         FK")
$fk("[[#{DIM_REASON_FOR_ISSUANCE} REASON_FOR_ISSUANCE_ID]]           Binary         FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]        Binary         FK")
RECORD_LOCATOR                   String     NN    
PNR_CREATION_DATE                Timestamp  NN    
$pk("VERSION                          Integer    NN  PK")
DATE_BEGIN                       Timestamp  NN    
DATE_END                         Timestamp        
IS_LAST_VERSION                  Boolean          
LOAD_DATE                        Timestamp        
}
}

frame "Ticketing" #FFFEB0 {

entity "FACT_AUTOMATED_PROCESS_HISTO" {
$pk("AUTOMATED_PROCESS_ID           Binary     NN  PK")
REFERENCE_KEY                  String     NN    
CODE                           String           
SOURCE                         String           
APPLICABLE_CARRIER             String           
ACTION_DATETIME_UTC            Timestamp        
OFFICE_IDENTIFIER              String           
QUEUE_CATEGORY                 String           
QUEUE_NUMBER                   String           
IS_APPLICABLE_TO_INFANTS       Boolean          
DOCUMENT_DELIVERY_OPTIONS      String           
TEXT                           String           
CANCELLATION_RULES_IDENTIFIER  String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
$fk("[[#{DIM_AIRLINE} APPLICABLE_CARRIER_ID]]          Binary         FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "FACT_TRAVEL_DOCUMENT_HISTO"<<maintable>> {
$pk("TRAVEL_DOCUMENT_ID             Binary     NN  PK")
REFERENCE_KEY                  String     NN    
DOCUMENT_NUMBER_CONSOLIDATED   String           
DOCUMENT_NUMBER                String           
PRIMARY_DOCUMENT_NUMBER        String           
DOCUMENT_TYPE                  String           
DOCUMENT_STATUS                String           
ASSOCIATION_STATUS             String           
NUMBER_OF_BOOKLETS             Integer          
TOTAL_AMOUNT                   Float            
CURRENCY                       String           
TOTAL_AMOUNT_ORIGINAL          Float            
CURRENCY_ORIGINAL              String           
EXCHANGE_RATE_DATE_NEEDED      Date             
EXCHANGE_RATE_DATE_TAKEN       Date             
IS_INFANT                      Boolean          
BLACKLIST_CATEGORY             String           
TICKETING_REFERENCE_TYPE       String           
TICKETING_REFERENCE_STATUS     String           
TICKETING_REFERENCE_TEXT       String           
CREATION_DATETIME_UTC          Timestamp        
CREATION_OFFICE_IDENTIFIER     String           
CREATION_OFFICE_IATA_NUMBER    String           
CREATION_OFFICE_SYSTEM_CODE    String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
$fk("[[#{DIM_POINT_OF_SALE} CREATION_POINT_OF_SALE_ID]]      Binary         FK")
$fk("[[#{DIM_BOOKING_STATUS} TICKETING_REFERENCE_STATUS_ID]]  Binary         FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "FACT_COUPON_HISTO" {
$pk("COUPON_ID                        Binary     NN  PK")
REFERENCE_KEY                    String     NN    
COUPON_NUMBER_CONSOLIDATED       Integer          
COUPON_NUMBER                    Integer          
SEQUENCE_NUMBER                  Integer          
REASON_FOR_ISSUANCE_CODE         String           
REASON_FOR_ISSUANCE_SUB_CODE     String           
REASON_FOR_ISSUANCE_DESCRIPTION  String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                   Binary     NN  FK")
$fk("[[#{Ticketing.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]               Binary     NN  FK")
$fk("[[#{DIM_REASON_FOR_ISSUANCE} REASON_FOR_ISSUANCE_ID]]           Binary         FK")
RECORD_LOCATOR                   String     NN    
PNR_CREATION_DATE                Timestamp  NN    
$pk("VERSION                          Integer    NN  PK")
DATE_BEGIN                       Timestamp  NN    
DATE_END                         Timestamp        
IS_LAST_VERSION                  Boolean          
LOAD_DATE                        Timestamp        
}
}

' Free entities
entity "ASSO_AIR_SEGMENT_PAX_KEYWORD_HISTO"<<assotable>> {
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary     NN  FK")
$fk("[[#{Reservation.FACT_KEYWORD_HISTO} KEYWORD_ID]]                     Binary     NN  FK")
REFERENCE_KEY_AIR_SEGMENT_PAX  String     NN    
REFERENCE_KEY_KEYWORD          String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_AIR_SEGMENT_PAX_REMARK_HISTO"<<assotable>> {
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary     NN  FK")
$fk("[[#{Reservation.FACT_REMARK_HISTO} REMARK_ID]]                      Binary     NN  FK")
REFERENCE_KEY_AIR_SEGMENT_PAX  String     NN    
REFERENCE_KEY_REMARK           String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_AIR_SEGMENT_PAX_FLIGHT_TRANSFER_HISTO"<<assotable>> {
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary     NN  FK")
$fk("[[#{Segment Booking.FACT_FLIGHT_TRANSFER_HISTO} FLIGHT_TRANSFER_ID]]             Binary     NN  FK")
REFERENCE_KEY_AIR_SEGMENT_PAX  String     NN    
REFERENCE_KEY_FLIGHT_TRANSFER  String     NN    
$pk("RELATES_TO                     String         PK")
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_AIR_SEGMENT_PAX_SERVICE_HISTO"<<assotable>> {
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary     NN  FK")
$fk("[[#{Service Booking.FACT_SERVICE_PAX_HISTO} SERVICE_PAX_ID]]                 Binary     NN  FK")
REFERENCE_KEY_AIR_SEGMENT_PAX  String     NN    
REFERENCE_KEY_SERVICE_PAX      String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_AIR_SEGMENT_PAX_YIELD_HISTO"<<assotable>> {
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary     NN  FK")
$fk("[[#{Segment Booking.FACT_SEGMENT_YIELD_HISTO} SEGMENT_YIELD_ID]]               Binary     NN  FK")
REFERENCE_KEY_AIR_SEGMENT_PAX  String     NN    
REFERENCE_KEY_SEGMENT_YIELD    String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_AIR_SEGMENT_PAX_SEATING_HISTO"<<assotable>> {
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary     NN  FK")
$fk("[[#{Service Booking.FACT_SEATING_PAX_HISTO} SEATING_PAX_ID]]                 Binary     NN  FK")
REFERENCE_KEY_AIR_SEGMENT_PAX  String     NN    
REFERENCE_KEY_SEATING_PAX      String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS_HISTO"<<assotable>> {
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]               Binary     NN  FK")
$fk("[[#{Ticketing.FACT_AUTOMATED_PROCESS_HISTO} AUTOMATED_PROCESS_ID]]             Binary     NN  FK")
REFERENCE_KEY_AIR_SEGMENT_PAX    String     NN    
REFERENCE_KEY_AUTOMATED_PROCESS  String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                   Binary     NN  FK")
RECORD_LOCATOR                   String     NN    
PNR_CREATION_DATE                Timestamp  NN    
$pk("VERSION                          Integer    NN  PK")
DATE_BEGIN                       Timestamp  NN    
DATE_END                         Timestamp        
IS_LAST_VERSION                  Boolean          
LOAD_DATE                        Timestamp        
}

entity "ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX_HISTO"<<assotable>> {
$fk("[[#{Ticketing.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary     NN  FK")
$fk("[[#{Segment Booking.FACT_AIR_SEGMENT_PAX_HISTO} AIR_SEGMENT_PAX_ID]]             Binary     NN  FK")
REFERENCE_KEY_TRAVEL_DOCUMENT  String     NN    
REFERENCE_KEY_AIR_SEGMENT_PAX  String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_TRAVEL_DOCUMENT_SERVICE_PAX_HISTO"<<assotable>> {
$fk("[[#{Ticketing.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary     NN  FK")
$fk("[[#{Service Booking.FACT_SERVICE_PAX_HISTO} SERVICE_PAX_ID]]                 Binary     NN  FK")
REFERENCE_KEY_TRAVEL_DOCUMENT  String     NN    
REFERENCE_KEY_SERVICE_PAX      String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_TRAVEL_DOCUMENT_SEATING_PAX_HISTO"<<assotable>> {
$fk("[[#{Ticketing.FACT_TRAVEL_DOCUMENT_HISTO} TRAVEL_DOCUMENT_ID]]             Binary     NN  FK")
$fk("[[#{Service Booking.FACT_SEATING_PAX_HISTO} SEATING_PAX_ID]]                 Binary     NN  FK")
REFERENCE_KEY_TRAVEL_DOCUMENT  String     NN    
REFERENCE_KEY_SEATING_PAX      String     NN    
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]                 Binary     NN  FK")
RECORD_LOCATOR                 String     NN    
PNR_CREATION_DATE              Timestamp  NN    
$pk("VERSION                        Integer    NN  PK")
DATE_BEGIN                     Timestamp  NN    
DATE_END                       Timestamp        
IS_LAST_VERSION                Boolean          
LOAD_DATE                      Timestamp        
}

entity "ASSO_TRAVELER_CONTACT_HISTO"<<assotable>> {
$fk("[[#{Traveler.FACT_TRAVELER_HISTO} TRAVELER_ID]]             Binary     NN  FK")
$fk("[[#{Traveler.FACT_ADDRESS_HISTO} ADDRESS_ID]]              Binary         FK")
$fk("[[#{Traveler.FACT_PHONE_HISTO} PHONE_ID]]                Binary         FK")
$fk("[[#{Traveler.FACT_EMAIL_HISTO} EMAIL_ID]]                Binary         FK")
REFERENCE_KEY_TRAVELER  String     NN    
REFERENCE_KEY_ADDRESS   String           
REFERENCE_KEY_PHONE     String           
REFERENCE_KEY_EMAIL     String           
$fk("[[#{Reservation.FACT_RESERVATION_HISTO} RESERVATION_ID]]          Binary     NN  FK")
RECORD_LOCATOR          String     NN    
PNR_CREATION_DATE       Timestamp  NN    
$pk("VERSION                 Integer    NN  PK")
DATE_BEGIN              Timestamp  NN    
DATE_END                Timestamp        
IS_LAST_VERSION         Boolean          
LOAD_DATE               Timestamp        
}

entity "DIM_POINT_OF_SALE"<<dimtable>> {
$pk("POINT_OF_SALE_ID    Binary     NN  PK")
OFFICE_IDENTIFIER   String           
OFFICE_IATA_NUMBER  String           
OFFICE_SYSTEM_CODE  String           
OFFICE_AGENT_TYPE   String           
LOGIN_CITY_CODE     String           
LOGIN_COUNTRY_CODE  String           
LOGIN_NUMERIC_SIGN  String           
LOGIN_INITIALS      String           
LOGIN_DUTY_CODE     String           
LOAD_DATE           Timestamp        
}

entity "DIM_AIRLINE"<<dimtable>> {
$pk("AIRLINE_ID         Binary     NN  PK")
AIRLINE_IATA_CODE  String     NN    
LOAD_DATE          Timestamp        
}

entity "DIM_PASSENGER_TYPE"<<dimtable>> {
$pk("PASSENGER_TYPE_ID     Binary     NN  PK")
PASSENGER_TYPE_CODE   String     NN    
PASSENGER_TYPE_LABEL  String           
RECORD_SOURCE         String           
LOAD_DATE             Timestamp        
}

entity "DIM_BOOKING_STATUS"<<dimtable>> {
$pk("BOOKING_STATUS_ID     Binary     NN  PK")
BOOKING_STATUS_CODE   String     NN    
BOOKING_STATUS_LABEL  String           
BOOKING_MACRO_STATUS  String           
BOOKING_STATUS_TYPE   String           
RECORD_SOURCE         String           
LOAD_DATE             Timestamp        
}

entity "DIM_AIRPORT"<<dimtable>> {
$pk("AIRPORT_ID         Binary     NN  PK")
AIRPORT_IATA_CODE  String     NN    
LOAD_DATE          Timestamp        
}

entity "DIM_AIRPORT_TERMINAL"<<dimtable>> {
$pk("AIRPORT_TERMINAL_ID  Binary         PK")
AIRPORT_IATA_CODE    String           
TERMINAL             String           
$fk("[[#{DIM_AIRPORT} AIRPORT_ID]]           Binary         FK")
LOAD_DATE            Timestamp        
}

entity "DIM_SERVICE"<<dimtable>> {
$pk("SERVICE_ID       Binary         PK")
SERVICE_CODE     String           
SERVICE_SUBTYPE  String           
SERVICE_LABEL    String           
RECORD_SOURCE    String           
LOAD_DATE        Timestamp        
}

entity "DIM_REASON_FOR_ISSUANCE"<<dimtable>> {
$pk("REASON_FOR_ISSUANCE_ID     Binary         PK")
REASON_FOR_ISSUANCE_CODE   String           
REASON_FOR_ISSUANCE_LABEL  String           
RECORD_SOURCE              String           
LOAD_DATE                  Timestamp        
}

entity "DIM_FARE_CALC_PRICING_INDICATOR"<<dimtable>> {
$pk("FARE_CALC_PRICING_INDICATOR_ID     Binary         PK")
FARE_CALC_PRICING_INDICATOR_CODE   String           
FARE_CALC_PRICING_INDICATOR_LABEL  String           
RECORD_SOURCE                      String           
LOAD_DATE                          Timestamp        
}

entity "DIM_FARE_ELEMENT_TYPE"<<dimtable>> {
$pk("FARE_ELEMENT_TYPE_ID     Binary         PK")
FARE_ELEMENT_TYPE_CODE   String           
FARE_ELEMENT_TYPE_LABEL  String           
RECORD_SOURCE            String           
LOAD_DATE                Timestamp        
}

entity "DIM_TAX_CODE"<<dimtable>> {
$pk("TAX_CODE_ID     Binary         PK")
TAX_CODE        String           
TAX_CODE_LABEL  String           
RECORD_SOURCE   String           
LOAD_DATE       Timestamp        
}

entity "DIM_FORM_OF_PAYMENT_TYPE"<<dimtable>> {
$pk("FORM_OF_PAYMENT_TYPE_ID     Binary         PK")
FORM_OF_PAYMENT_TYPE_CODE   String           
FORM_OF_PAYMENT_TYPE_LABEL  String           
RECORD_SOURCE               String           
LOAD_DATE                   Timestamp        
}

entity "DIM_CARD_VENDOR"<<dimtable>> {
$pk("CARD_VENDOR_ID     Binary         PK")
CARD_VENDOR_CODE   String           
CARD_VENDOR_LABEL  String           
RECORD_SOURCE      String           
LOAD_DATE          Timestamp        
}

' Relationships
FACT_KEYWORD_HISTO::RESERVATION_ID --> FACT_RESERVATION_HISTO::RESERVATION_ID
FACT_REMARK_HISTO::RESERVATION_ID --> FACT_RESERVATION_HISTO::RESERVATION_ID
FACT_ASSOCIATED_RESERVATION_HISTO::RESERVATION_ID --> FACT_RESERVATION_HISTO::RESERVATION_ID
FACT_ASSOCIATED_RESERVATION_HISTO::FLIGHT_TRANSFER_ID --> FACT_FLIGHT_TRANSFER_HISTO::FLIGHT_TRANSFER_ID
FACT_TRAVELER_HISTO::RESERVATION_ID --> FACT_RESERVATION_HISTO::RESERVATION_ID
FACT_TRAVELER_HISTO::RELATED_INFANT_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_TRAVELER_HISTO::RELATED_ADULT_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_TRAVELER_HISTO::RELATED_CBBG_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_TRAVELER_HISTO::RELATED_EXST_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_TRAVELER_HISTO::RELATED_OWNER_CBBG_EXST_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_IDENTITY_DOCUMENT_PAX_HISTO::TRAVELER_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_AIR_SEGMENT_PAX_HISTO::RESERVATION_ID --> FACT_RESERVATION_HISTO::RESERVATION_ID
FACT_AIR_SEGMENT_PAX_HISTO::TRAVELER_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_FLIGHT_BOUND_PAX_HISTO::ORIGIN_DESTINATION_ID --> FACT_ORIGIN_DESTINATION_HISTO::ORIGIN_DESTINATION_ID
FACT_FLIGHT_BOUND_PAX_HISTO::INBOUND_AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
FACT_FLIGHT_BOUND_PAX_HISTO::OUTBOUND_AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
FACT_SERVICE_PAX_HISTO::RESERVATION_ID --> FACT_RESERVATION_HISTO::RESERVATION_ID
FACT_SERVICE_PAX_HISTO::TRAVELER_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_SEATING_PAX_HISTO::TRAVELER_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
FACT_TRAVEL_DOCUMENT_HISTO::RESERVATION_ID --> FACT_RESERVATION_HISTO::RESERVATION_ID
FACT_COUPON_HISTO::TRAVEL_DOCUMENT_ID --> FACT_TRAVEL_DOCUMENT_HISTO::TRAVEL_DOCUMENT_ID
ASSO_TRAVEL_DOCUMENT_SEATING_PAX_HISTO::TRAVEL_DOCUMENT_ID --> FACT_TRAVEL_DOCUMENT_HISTO::TRAVEL_DOCUMENT_ID
ASSO_TRAVEL_DOCUMENT_SEATING_PAX_HISTO::SEATING_PAX_ID --> FACT_SEATING_PAX_HISTO::SEATING_PAX_ID
ASSO_AIR_SEGMENT_PAX_FLIGHT_TRANSFER_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_FLIGHT_TRANSFER_HISTO::FLIGHT_TRANSFER_ID --> FACT_FLIGHT_TRANSFER_HISTO::FLIGHT_TRANSFER_ID
ASSO_TRAVELER_CONTACT_HISTO::TRAVELER_ID --> FACT_TRAVELER_HISTO::TRAVELER_ID
ASSO_TRAVELER_CONTACT_HISTO::ADDRESS_ID --> FACT_ADDRESS_HISTO::ADDRESS_ID
ASSO_TRAVELER_CONTACT_HISTO::PHONE_ID --> FACT_PHONE_HISTO::PHONE_ID
ASSO_TRAVELER_CONTACT_HISTO::EMAIL_ID --> FACT_EMAIL_HISTO::EMAIL_ID
ASSO_AIR_SEGMENT_PAX_REMARK_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_REMARK_HISTO::REMARK_ID --> FACT_REMARK_HISTO::REMARK_ID
ASSO_AIR_SEGMENT_PAX_YIELD_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_YIELD_HISTO::SEGMENT_YIELD_ID --> FACT_SEGMENT_YIELD_HISTO::SEGMENT_YIELD_ID
ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_AUTOMATED_PROCESS_HISTO::AUTOMATED_PROCESS_ID --> FACT_AUTOMATED_PROCESS_HISTO::AUTOMATED_PROCESS_ID
ASSO_AIR_SEGMENT_PAX_SERVICE_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_SERVICE_HISTO::SERVICE_PAX_ID --> FACT_SERVICE_PAX_HISTO::SERVICE_PAX_ID
ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX_HISTO::TRAVEL_DOCUMENT_ID --> FACT_TRAVEL_DOCUMENT_HISTO::TRAVEL_DOCUMENT_ID
ASSO_TRAVEL_DOCUMENT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_KEYWORD_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_KEYWORD_HISTO::KEYWORD_ID --> FACT_KEYWORD_HISTO::KEYWORD_ID
ASSO_AIR_SEGMENT_PAX_SEATING_HISTO::AIR_SEGMENT_PAX_ID --> FACT_AIR_SEGMENT_PAX_HISTO::AIR_SEGMENT_PAX_ID
ASSO_AIR_SEGMENT_PAX_SEATING_HISTO::SEATING_PAX_ID --> FACT_SEATING_PAX_HISTO::SEATING_PAX_ID
ASSO_TRAVEL_DOCUMENT_SERVICE_PAX_HISTO::TRAVEL_DOCUMENT_ID --> FACT_TRAVEL_DOCUMENT_HISTO::TRAVEL_DOCUMENT_ID
ASSO_TRAVEL_DOCUMENT_SERVICE_PAX_HISTO::SERVICE_PAX_ID --> FACT_SERVICE_PAX_HISTO::SERVICE_PAX_ID

@enduml
