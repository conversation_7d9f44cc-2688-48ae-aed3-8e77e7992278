
{
  "tables": [
    {
      "name": "ASSO_AIR_SEGMENT_PAX_COUPON_HISTO",
      "correlation": {
        "domain-a": {
          "name": "P<PERSON>",
          "partial": {
            "table": "PNR_TKT_PARTIAL_CORR_PIT",
            "start-date": "DATE_BEGIN",
            "end-date": "DATE_END",
            "partial-corr-version": "VERSION",
            "partial-corr-key": "AIR_SEGMENT_PAX_ID",
            "partial-corr-secondary-key": "COUPON_ID",
            "is-last": "IS_LAST_VERSION"
          },
          "pit": {
            "table": "FACT_RESERVATION_HISTO",
            "is-last": "IS_LAST_VERSION",
            "pit-version": "VERSION"
          }
        },
        "domain-b": {
          "name": "TKT",
          "partial": {
            "table": "TKT_PNR_PARTIAL_CORR_PIT",
            "start-date": "DATE_BEGIN",
            "end-date": "DATE_END",
            "partial-corr-version": "VERSION",
            "partial-corr-key": "COUPON_ID",
            "partial-corr-secondary-key": "AIR_SEGMENT_PAX_ID",
            "is-last": "IS_LAST_VERSION"
          },
          "pit": {
            "table": "FACT_TRAVEL_DOCUMENT_HISTO",
            "pit-version": "VERSION",
            "is-last": "IS_LAST_VERSION"
          }
        },
        "target": {
          "domain-a-key": { "name": "AIR_SEGMENT_PAX_ID", "fk": [{"schema": "PNR", "table":"FACT_AIR_SEGMENT_PAX_HISTO"}]},
          "domain-b-key": { "name": "COUPON_ID", "fk": [{"schema": "TKT", "table":"FACT_COUPON_HISTO"}]},
          "domain-a-version": { "name": "VERSION_PNR", "fk": [{"schema": "PNR", "table":"FACT_AIR_SEGMENT_PAX_HISTO", "column":"VERSION"}]},
          "domain-b-version": { "name": "VERSION_TRAVEL_DOCUMENT", "fk": [{"schema": "TKT", "table":"FACT_COUPON_HISTO", "column":"VERSION"}]},
          "start-date": "DATE_BEGIN",
          "end-date": "DATE_END",
          "is-last": "IS_LAST_VERSION",
          "if-dupe-take-highest": [ "DATE_BEGIN" ]
        },
        "correlation-field-a-to-b": "TRAVEL_DOCUMENT_ID",
        "correlation-field-b-to-a": "RESERVATION_ID"
      }
    },
    {
      "name": "ASSO_AIR_SEGMENT_PAX_COUPON",
      "latest": {
        "histo-table-name": "ASSO_AIR_SEGMENT_PAX_COUPON_HISTO"
        "additional-filter-expression": "IS_LAST_VERSION is not null"
      }
    }
  ]
}
