{"defaultComment": "A comment here", "partition-spec": {"key": "SERVICE_ID", "column-name": "PART_SERVICE_ID", "expr": "SERVICE_ID"}, "tables": [{"name": "FACT_SERVICE_HISTO", "mapping": {"merge": {"key-columns": ["SERVICE_ID", "VERSION"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"name": "seat", "rs": {"blocks": [{"tref": "$.mainResource.current.image"}, {"segdel": "$.segmentDeliveries[*]"}, {"legdel": "$.legDeliveries[*]"}, {"serv": "$.seating.chargeableSeat"}]}}, {"name": "service", "rs": {"blocks": [{"tref": "$.mainResource.current.image"}, {"serv": "$.services[*]"}]}}], "columns": [{"name": "CODE", "column-type": "strColumn", "sources": {"blocks": [{"serv": "$.code"}]}}, {"name": "PASSENGER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tref": "$.id"}]}, "expr": "hashM({0})"}, {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"tref": "$.recordLocator"}]}}, {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"tref": "$.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.latestEvent.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}, {"name": "SERVICE_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "seat", "blocks": [{"tref": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}]}, {"rs-name": "service", "blocks": [{"tref": "$.id"}, {"serv": "$.id"}]}]}, "expr": "hashM({0})"}, {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"root-specific": [{"rs-name": "seat", "blocks": [{"tref": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}, {"serv": "$.id"}]}, {"rs-name": "service", "blocks": [{"tref": "$.id"}, {"serv": "$.id"}]}]}}, {"name": "LEG_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "seat", "blocks": [{"tref": "$.id"}, {"segdel": "$.id"}, {"legdel": "$.id"}]}]}, "expr": "hashM({0})"}, {"name": "SEGMENT_DELIVERY_ID", "column-type": "binaryStrColumn", "sources": {"root-specific": [{"rs-name": "seat", "blocks": [{"tref": "$.id"}, {"segdel": "$.id"}]}]}, "expr": "hashM({0})"}, {"name": "SERVICE_SOURCE", "column-type": "strColumn", "sources": {"root-specific": [{"rs-name": "seat", "literal": "SEGDEL_LEGDEL_SEATING"}, {"rs-name": "service", "literal": "SERVICES"}]}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "SERVICE_ID", "pit-version": "VERSION", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_VERSION"}}}}], "links": []}