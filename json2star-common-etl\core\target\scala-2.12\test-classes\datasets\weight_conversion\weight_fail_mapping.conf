{
  "tables": [
    // validation ok because dummy configured to be ok
    {
      "name": "FACT_OK_HISTO",
      "mapping": {
        "description": {"description": "Contains information of a baggage group (a set of bags travelling together on an identical itinerary), such as number and weight of checked bags and hand bags, and the responsible passenger.", "granularity": "1 bags group"},
        "merge": {
          "key-columns": ["BAG_GROUP_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "BAG_GROUP_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        }
      },
      "stackable-addons": [
      ]
    },
    // validation fail because missing input cols
    {
      "name": "FACT_FAIL_HISTO",
      "mapping": {
        "description": {"description": "Contains information of an individual bag", "granularity": " 1 bag in 1 bags group"},
        "merge": {
          "key-columns": ["BAG_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {"blocks": [{"base": "$.mainResource.current.image"}, {"bag": "$.bags[*]"}]}
        ],
        "columns": [
          {"name": "BAG_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"bag": "$.id"}]}},
          {"name": "BAG_GROUP_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "BAG_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.bagType"}]}, "create-fk": {"column-type": "binaryStrColumn", "expr": "hashS({0})", "fk": [{"table": "DIM_BAG_TYPE"}]}},
          {"name": "WEIGHT_VALUE_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.weight.value"}]}},
          {"name": "WEIGHT_UNIT_ORIGINAL", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.weight.unit"}]}},
          {"name": "WEIGHT_VALUE", "column-type": "strColumn", "sources": {}}, // column is present but not filled
          {"name": "WEIGHT_UNIT", "column-type": "strColumn", "sources": {}},   // column is present but not filled
          {"name": "WEIGHT_VALUE_ORIGINAL_2", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.weight.value"}]}},
          {"name": "WEIGHT_UNIT_ORIGINAL_2", "column-type": "strColumn", "sources": {"blocks": [{"bag": "$.weight.unit"}]}},
          {"name": "WEIGHT_VALUE_2", "column-type": "strColumn", "sources": {}}, // column is present but not filled
          {"name": "WEIGHT_UNIT_2", "column-type": "strColumn", "sources": {}},   // column is present but not filled
          {"name": "VERSION", "column-type": "longColumn", "sources": {"blocks": [{"base": "$.version"}]}, "expr": "bigint({0})"},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }
      },
      "stackable-addons": [
        {
          "type": "weight-conversion",
          "conversions": [
            {"src-col": "WEIGHT_VALUE_ORIGINAL_MISSING", "src-unit-col": "WEIGHT_UNIT_ORIGINAL_MISSING", "dst-col": "WEIGHT_VALUE", "dst-unit-col": "WEIGHT_UNIT"}
          ]
        }
      ]
    },
    // validation fail because latest addon not supported
    {
      "name": "FACT_FAIL",
      "latest": {
        "histo-table-name": "FACT_BAG_HISTO"
      }
      "stackable-addons": [
        {
          "type": "weight-conversion",
          "conversions": [
            {"src-col": "WEIGHT_VALUE_ORIGINAL", "src-unit-col": "WEIGHT_UNIT_ORIGINAL", "dst-col": "WEIGHT_VALUE", "dst-unit-col": "WEIGHT_UNIT"}
          ]
        }
      ]
    }
  ]
}
