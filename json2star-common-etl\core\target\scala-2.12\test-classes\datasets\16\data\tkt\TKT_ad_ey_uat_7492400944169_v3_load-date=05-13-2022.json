{"correlatedResourcesCurrent": {"TKT-PNR": {"correlations": [{"corrTktPnr": {"items": [{"pnrTicketingReferenceId": "N389UA-2022-05-13-OT-37", "pnrTravelerId": "N389UA-2022-05-13-PT-2"}]}, "fromVersion": "1", "toId": "N389UA-2022-05-13", "toVersion": "9"}], "fromDomain": "TKT", "fromFullVersion": "3", "id": "7492400944169-2022-05-13", "isFullUpdate": true, "toDomain": "PNR", "version": "PNR-4"}}, "correlatedResourcesPrevious": {"TKT-PNR": {"correlations": [{"corrTktPnr": {"items": [{"pnrTicketingReferenceId": "N389UA-2022-05-13-OT-37", "pnrTravelerId": "N389UA-2022-05-13-PT-2"}]}, "fromVersion": "1", "toId": "N389UA-2022-05-13", "toVersion": "9"}], "fromDomain": "TKT", "fromFullVersion": "0", "id": "7492400944169-2022-05-13", "isFullUpdate": false, "toDomain": "PNR", "version": "PNR-3"}}, "mainResource": {"current": {"correlations": [{"name": "TKT-PNR", "relation": {"rel": "related"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.AirTravelDocument", "associatedPnrs": [{"creation": {"pointOfSale": {"office": {"systemCode": "1A"}}}, "reference": "N389UA"}, {"creation": {"pointOfSale": {"office": {"systemCode": "4Z"}}}, "reference": "N389UA"}], "conjunctiveDocumentNumbers": ["7492400944169"], "coupons": [{"baggageAllowance": {"weight": {"amount": "20", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "CPT", "localDateTime": "2022-07-11T08:30:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "JNB", "localDate": "2022-07-11", "localDateTime": "2022-07-11T06:20:00"}, "number": "893"}, "documentNumber": "7492400944169", "fareBasis": {"fareBasisCode": "W4ZOW"}, "id": "7492400944169-2022-05-13-1", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 1, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 1, "settlementAuthorizationCode": " 749IGZO76BFAH", "soldSegment": {"arrival": {"iataCode": "CPT", "localDateTime": "2022-07-11T08:30:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "JNB", "localDateTime": "2022-07-11T06:20:00"}, "number": "893"}, "status": "EXCHANGED", "usedSegment": {"arrival": {"iataCode": "CPT", "localDateTime": "2022-07-11T08:30:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "JNB", "localDateTime": "2022-07-11T06:20:00"}, "number": "893"}, "validityDates": {"notValidAfterDate": "2022-07-11", "notValidBeforeDate": "2022-07-11"}}, {"baggageAllowance": {"weight": {"amount": "20", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "JNB", "localDateTime": "2022-07-22T17:20:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "CPT", "localDate": "2022-07-22", "localDateTime": "2022-07-22T15:15:00"}, "number": "920"}, "documentNumber": "7492400944169", "fareBasis": {"fareBasisCode": "W4ZOW"}, "id": "7492400944169-2022-05-13-2", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 2, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 2, "settlementAuthorizationCode": " 749IGZO76BFAH", "soldSegment": {"arrival": {"iataCode": "JNB", "localDateTime": "2022-07-22T17:20:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "CPT", "localDateTime": "2022-07-22T15:15:00"}, "number": "920"}, "status": "EXCHANGED", "usedSegment": {"arrival": {"iataCode": "JNB", "localDateTime": "2022-07-22T17:20:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "CPT", "localDateTime": "2022-07-22T15:15:00"}, "number": "920"}, "validityDates": {"notValidAfterDate": "2022-07-22", "notValidBeforeDate": "2022-07-22"}}], "creation": {"dateTime": "2022-05-13", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA", "currencyCode": "ZAR", "dutyCode": "SU", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE_AGENT", "iataNumber": "77491024", "inHouseIdentification": "150333", "systemCode": "1A"}}}, "destinationCityIataCode": "JNB", "documentRefund": {}, "documentType": "TICKET", "endorsementFreeFlow": "NONEND NONREF/CHG PENALTY APPLY", "formsOfPayment": [{"authorization": {"approvalCode": "035143", "sourceOfApprovalCode": "S"}, "code": "CC", "displayedAmount": {"amount": "1779.92", "currency": "ZAR"}, "fopIndicator": "NEW", "freeText": "CCVIXXXXXXXXXXXX1003/0125", "paymentCard": {"expiryDate": "0125", "holderName": "Test", "maskedCardNumber": "XXXXXXXXXXXX1003", "vendorCode": "VI"}}], "id": "7492400944169-2022-05-13", "issuanceType": "FIRST_ISSUE", "latestEvent": {"dateTime": "2022-05-13T13:51:02.799388000", "id": "3", "pointOfSale": {"login": {"dutyCode": "SU", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE_AGENT", "iataNumber": "77490895", "inHouseIdentification": "234786", "systemCode": "1A"}}, "triggerEventName": "UPD"}, "numberOfBooklets": 1, "originCityIataCode": "JNB", "price": {"currency": "ZAR", "detailedPrices": [{"amount": "1779.92", "currency": "ZAR", "elementaryPriceType": "TotalFare"}, {"amount": "420.00", "currency": "ZAR", "elementaryPriceType": "BaseFare"}], "taxes": [{"amount": "1008.00", "category": "NEW", "code": "YQ", "currency": "ZAR", "nature": "AC", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}, {"amount": "55.08", "category": "NEW", "code": "EV", "currency": "ZAR", "nature": "AB", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}, {"amount": "48.00", "category": "NEW", "code": "UM", "currency": "ZAR", "nature": "SE", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}, {"amount": "185.84", "category": "NEW", "code": "ZA", "currency": "ZAR", "nature": "EB", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}, {"amount": "63.00", "category": "NEW", "code": "ZV", "currency": "ZAR", "nature": "GO", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}], "total": "1779.92", "totalTaxes": "1359.92"}, "pricingConditions": {"fareCalculation": {"pricingIndicator": "0", "text": "JNB 4Z CPT210.00 4Z JNB210.00ZAR420.00END"}, "isDomesticSale": true, "isNonEndorsable": false, "isNonExchangeable": false, "isNonRefundable": false, "isPenaltyRestriction": false}, "primaryDocumentNumber": "7492400944169", "traveler": {"contact": {"phone": {"number": "3365465465465"}}, "name": {"firstName": "XAVIER MR", "lastName": "MAUDINET"}, "passengerTypeCode": "ADT"}, "validatingCarrierCode": "4Z", "validatingCarrierPnr": {"reference": "N389UA"}, "version": "3", "void": {}}}, "id": "7492400944169-2022-05-13", "previous": {"correlations": [{"name": "TKT-PNR", "relation": {"rel": "related"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.AirTravelDocument", "associatedPnrs": [{"creation": {"pointOfSale": {"office": {"systemCode": "1A"}}}, "reference": "N389UA"}, {"creation": {"pointOfSale": {"office": {"systemCode": "4Z"}}}, "reference": "N389UA"}], "conjunctiveDocumentNumbers": ["7492400944169"], "coupons": [{"baggageAllowance": {"weight": {"amount": "20", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "CPT", "localDateTime": "2022-07-11T08:30:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "JNB", "localDate": "2022-07-11", "localDateTime": "2022-07-11T06:20:00"}, "number": "893"}, "documentNumber": "7492400944169", "fareBasis": {"fareBasisCode": "W4ZOW"}, "id": "7492400944169-2022-05-13-1", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 1, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 1, "settlementAuthorizationCode": " 749IGZO76BFAH", "soldSegment": {"arrival": {"iataCode": "CPT", "localDateTime": "2022-07-11T08:30:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "JNB", "localDateTime": "2022-07-11T06:20:00"}, "number": "893"}, "status": "EXCHANGED", "usedSegment": {"arrival": {"iataCode": "CPT", "localDateTime": "2022-07-11T08:30:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "JNB", "localDateTime": "2022-07-11T06:20:00"}, "number": "893"}, "validityDates": {"notValidAfterDate": "2022-07-11", "notValidBeforeDate": "2022-07-11"}}, {"baggageAllowance": {"weight": {"amount": "20", "unit": "KILOGRAMS"}}, "currentSegment": {"arrival": {"iataCode": "JNB", "localDateTime": "2022-07-22T17:20:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "CPT", "localDate": "2022-07-22", "localDateTime": "2022-07-22T15:15:00"}, "number": "920"}, "documentNumber": "7492400944169", "fareBasis": {"fareBasisCode": "W4ZOW"}, "id": "7492400944169-2022-05-13-2", "isCodeshare": false, "isFromConnection": true, "isNonExchangeable": false, "isNonRefundable": false, "number": 2, "reservationStatus": "CONFIRMED", "revalidation": {"pointOfSale": {}}, "sequenceNumber": 2, "settlementAuthorizationCode": " 749IGZO76BFAH", "soldSegment": {"arrival": {"iataCode": "JNB", "localDateTime": "2022-07-22T17:20:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "CPT", "localDateTime": "2022-07-22T15:15:00"}, "number": "920"}, "status": "EXCHANGED", "usedSegment": {"arrival": {"iataCode": "JNB", "localDateTime": "2022-07-22T17:20:00"}, "bookingClass": {"code": "W"}, "carrierCode": "4Z", "departure": {"iataCode": "CPT", "localDateTime": "2022-07-22T15:15:00"}, "number": "920"}, "validityDates": {"notValidAfterDate": "2022-07-22", "notValidBeforeDate": "2022-07-22"}}], "creation": {"dateTime": "2022-05-13", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA", "currencyCode": "ZAR", "dutyCode": "SU", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE_AGENT", "iataNumber": "77491024", "inHouseIdentification": "150333", "systemCode": "1A"}}}, "destinationCityIataCode": "JNB", "documentRefund": {}, "documentType": "TICKET", "endorsementFreeFlow": "NONEND NONREF/CHG PENALTY APPLY", "formsOfPayment": [{"authorization": {"approvalCode": "035143", "sourceOfApprovalCode": "S"}, "code": "CC", "displayedAmount": {"amount": "1779.92", "currency": "ZAR"}, "fopIndicator": "NEW", "freeText": "CCVIXXXXXXXXXXXX1003/0125", "paymentCard": {"expiryDate": "0125", "holderName": "Test", "maskedCardNumber": "XXXXXXXXXXXX1003", "vendorCode": "VI"}}], "id": "7492400944169-2022-05-13", "issuanceType": "FIRST_ISSUE", "latestEvent": {"dateTime": "2022-05-13T13:51:01.565699000", "id": "2", "pointOfSale": {"login": {"cityCode": "JNB", "countryCode": "ZA", "currencyCode": "ZAR", "dutyCode": "SU", "initials": "AA", "numericSign": "0001"}, "office": {"agentType": "AIRLINE_AGENT", "iataNumber": "77491024", "inHouseIdentification": "150333", "systemCode": "1A"}}, "triggerEventName": "142"}, "numberOfBooklets": 1, "originCityIataCode": "JNB", "price": {"currency": "ZAR", "detailedPrices": [{"amount": "1779.92", "currency": "ZAR", "elementaryPriceType": "TotalFare"}, {"amount": "420.00", "currency": "ZAR", "elementaryPriceType": "BaseFare"}], "taxes": [{"amount": "1008.00", "category": "NEW", "code": "YQ", "currency": "ZAR", "nature": "AC", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}, {"amount": "55.08", "category": "NEW", "code": "EV", "currency": "ZAR", "nature": "AB", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}, {"amount": "48.00", "category": "NEW", "code": "UM", "currency": "ZAR", "nature": "SE", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}, {"amount": "185.84", "category": "NEW", "code": "ZA", "currency": "ZAR", "nature": "EB", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}, {"amount": "63.00", "category": "NEW", "code": "ZV", "currency": "ZAR", "nature": "GO", "ticketReportedStatuses": ["REPORTED", "DISPLAYED"]}], "total": "1779.92", "totalTaxes": "1359.92"}, "pricingConditions": {"fareCalculation": {"pricingIndicator": "0", "text": "JNB 4Z CPT210.00 4Z JNB210.00ZAR420.00END"}, "isDomesticSale": true, "isNonEndorsable": false, "isNonExchangeable": false, "isNonRefundable": false, "isPenaltyRestriction": false}, "primaryDocumentNumber": "7492400944169", "traveler": {"contact": {"phone": {"number": "3365465465465"}}, "name": {"firstName": "XAVIER MR", "lastName": "MAUDINET"}, "passengerTypeCode": "ADT"}, "validatingCarrierCode": "4Z", "validatingCarrierPnr": {"reference": "N389UA"}, "version": "2", "void": {}}}, "type": "com.amadeus.pulse.message.AirTravelDocument"}}