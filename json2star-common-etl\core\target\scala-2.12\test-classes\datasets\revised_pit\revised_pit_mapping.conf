{"tables": [{"name": "FACT_MASTER_HISTO", "mapping": {"merge": {"key-columns": ["RESERVATION_ID", "ENVELOP_NB"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}], "columns": [{"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"}, {"name": "ENVELOP_NB", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}}, {"name": "NIP", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.nip"}]}}, {"name": "LABEL_TEST_VALUE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.type"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_ENVELOP", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "master-pit-table", "master": {"pit-key": "RESERVATION_ID", "pit-version": "ENVELOP_NB", "valid-from": "DATE_BEGIN", "valid-to": "DATE_END", "is-last": "IS_LAST_ENVELOP"}}}}, {"name": "FACT_SECONDARY_1_HISTO", "mapping": {"merge": {"key-columns": ["AIR_SEGMENT_ID", "ENVELOP_NB"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"}]}], "columns": [{"name": "AIR_SEGMENT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}]}, "expr": "hashM({0})"}, {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"}, {"name": "ENVELOP_NB", "column-type": "intColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_ENVELOP", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "secondary-pit-table"}}}, {"name": "FACT_SECONDARY_2_HISTO", "mapping": {"merge": {"key-columns": ["AIR_SEGMENT_PAX_ID", "ENVELOP_NB"], "if-dupe-take-higher": ["LOAD_DATE"]}, "root-sources": [{"blocks": [{"prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"}, {"tr": "$.travelers[*]"}]}], "columns": [{"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr": "hashM({0})"}, {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"}, {"name": "LABEL_TEST_VALUE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.code"}]}}, {"name": "ENVELOP_NB", "column-type": "intColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.version"}]}}, {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root": "$.mainResource.current.image.lastModification.dateTime"}]}}, {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}}, {"name": "IS_LAST_ENVELOP", "column-type": "booleanColumn", "sources": {}}], "pit": {"type": "secondary-pit-table"}}}]}