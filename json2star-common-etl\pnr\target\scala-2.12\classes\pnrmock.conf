{
  "defaultComment": "A coment here",
  "partition-spec": {
    "key": "PNR_CREATION_DATE",
    "column-name": "PART_PNR_CREATION_MONTH",
    "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
  },
  "tables": [
    {
      "name": "FACT_RESERVATION_HISTO",
      "mapping": {
        "merge": {
          "key-columns": [
            "RESERVATION_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          "$.mainResource.current.image"
        ],
        "columns": [
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.reference"
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.version"
              ]
            }
          },
          {
            "name": "NIP",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.nip"
              ]
            }
          },
          {
            "name": "GROUP_SIZE",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.size"
              ]
            }
          },
          {
            "name": "GROUP_NAME",
            "column-type": "strColumn",
            "sources": {
              "mapping": [
                "$.group.name"
              ]
            }
          },
          {
            "name": "GROUP_SIZE_TAKEN",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.sizeTaken"
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_OWNER_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.owner.office.id",
                "$.owner.office.iataNumber",
                "$.owner.office.systemCode",
                "$.owner.office.agentType",
                "$.owner.login.cityCode",
                "$.owner.login.countryCode",
                "$.owner.login.numericSign",
                "$.owner.login.initials",
                "$.owner.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_CREATION_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.creation.pointOfSale.office.id",
                "$.creation.pointOfSale.office.iataNumber",
                "$.creation.pointOfSale.office.systemCode",
                "$.creation.pointOfSale.office.agentType",
                "$.creation.pointOfSale.login.cityCode",
                "$.creation.pointOfSale.login.countryCode",
                "$.creation.pointOfSale.login.numericSign",
                "$.creation.pointOfSale.login.initials",
                "$.creation.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_LAST_UPDATE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.lastModification.pointOfSale.office.id",
                "$.lastModification.pointOfSale.office.iataNumber",
                "$.lastModification.pointOfSale.office.systemCode",
                "$.lastModification.pointOfSale.office.agentType",
                "$.lastModification.pointOfSale.login.cityCode",
                "$.lastModification.pointOfSale.login.countryCode",
                "$.lastModification.pointOfSale.login.numericSign",
                "$.lastModification.pointOfSale.login.initials",
                "$.lastModification.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.creation.dateTime"
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.lastModification.dateTime"
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "master-pit-table",
          "master": {
            "pit-key": "RESERVATION_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_RESERVATION_HISTO2",
      "mapping": {
        "merge": {
          "key-columns": [
            "RESERVATION_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          "$.mainResource.current.image"
        ],
        "columns": [
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.reference"
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.version"
              ]
            }
          },
          {
            "name": "NIP",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.nip"
              ]
            }
          },
          {
            "name": "GROUP_SIZE",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.size"
              ]
            }
          },
          {
            "name": "GROUP_NAME",
            "column-type": "strColumn",
            "sources": {
              "mapping": [
                "$.group.name"
              ]
            }
          },
          {
            "name": "GROUP_SIZE_TAKEN",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.sizeTaken"
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_OWNER_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.owner.office.id",
                "$.owner.office.iataNumber",
                "$.owner.office.systemCode",
                "$.owner.office.agentType",
                "$.owner.login.cityCode",
                "$.owner.login.countryCode",
                "$.owner.login.numericSign",
                "$.owner.login.initials",
                "$.owner.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_CREATION_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.creation.pointOfSale.office.id",
                "$.creation.pointOfSale.office.iataNumber",
                "$.creation.pointOfSale.office.systemCode",
                "$.creation.pointOfSale.office.agentType",
                "$.creation.pointOfSale.login.cityCode",
                "$.creation.pointOfSale.login.countryCode",
                "$.creation.pointOfSale.login.numericSign",
                "$.creation.pointOfSale.login.initials",
                "$.creation.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_LAST_UPDATE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.lastModification.pointOfSale.office.id",
                "$.lastModification.pointOfSale.office.iataNumber",
                "$.lastModification.pointOfSale.office.systemCode",
                "$.lastModification.pointOfSale.office.agentType",
                "$.lastModification.pointOfSale.login.cityCode",
                "$.lastModification.pointOfSale.login.countryCode",
                "$.lastModification.pointOfSale.login.numericSign",
                "$.lastModification.pointOfSale.login.initials",
                "$.lastModification.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.creation.dateTime"
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.lastModification.dateTime"
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_RESERVATION_HISTO3",
      "mapping": {
        "merge": {
          "key-columns": [
            "RESERVATION_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          "$.mainResource.current.image"
        ],
        "columns": [
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.reference"
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.version"
              ]
            }
          },
          {
            "name": "NIP",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.nip"
              ]
            }
          },
          {
            "name": "GROUP_SIZE",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.size"
              ]
            }
          },
          {
            "name": "GROUP_NAME",
            "column-type": "strColumn",
            "sources": {
              "mapping": [
                "$.group.name"
              ]
            }
          },
          {
            "name": "GROUP_SIZE_TAKEN",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.sizeTaken"
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_OWNER_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.owner.office.id",
                "$.owner.office.iataNumber",
                "$.owner.office.systemCode",
                "$.owner.office.agentType",
                "$.owner.login.cityCode",
                "$.owner.login.countryCode",
                "$.owner.login.numericSign",
                "$.owner.login.initials",
                "$.owner.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_CREATION_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.creation.pointOfSale.office.id",
                "$.creation.pointOfSale.office.iataNumber",
                "$.creation.pointOfSale.office.systemCode",
                "$.creation.pointOfSale.office.agentType",
                "$.creation.pointOfSale.login.cityCode",
                "$.creation.pointOfSale.login.countryCode",
                "$.creation.pointOfSale.login.numericSign",
                "$.creation.pointOfSale.login.initials",
                "$.creation.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_LAST_UPDATE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.lastModification.pointOfSale.office.id",
                "$.lastModification.pointOfSale.office.iataNumber",
                "$.lastModification.pointOfSale.office.systemCode",
                "$.lastModification.pointOfSale.office.agentType",
                "$.lastModification.pointOfSale.login.cityCode",
                "$.lastModification.pointOfSale.login.countryCode",
                "$.lastModification.pointOfSale.login.numericSign",
                "$.lastModification.pointOfSale.login.initials",
                "$.lastModification.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.creation.dateTime"
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.lastModification.dateTime"
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_RESERVATION_HISTO4",
      "mapping": {
        "merge": {
          "key-columns": [
            "RESERVATION_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          "$.mainResource.current.image"
        ],
        "columns": [
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.reference"
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.version"
              ]
            }
          },
          {
            "name": "NIP",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.nip"
              ]
            }
          },
          {
            "name": "GROUP_SIZE",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.size"
              ]
            }
          },
          {
            "name": "GROUP_NAME",
            "column-type": "strColumn",
            "sources": {
              "mapping": [
                "$.group.name"
              ]
            }
          },
          {
            "name": "GROUP_SIZE_TAKEN",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.sizeTaken"
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_OWNER_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.owner.office.id",
                "$.owner.office.iataNumber",
                "$.owner.office.systemCode",
                "$.owner.office.agentType",
                "$.owner.login.cityCode",
                "$.owner.login.countryCode",
                "$.owner.login.numericSign",
                "$.owner.login.initials",
                "$.owner.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_CREATION_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.creation.pointOfSale.office.id",
                "$.creation.pointOfSale.office.iataNumber",
                "$.creation.pointOfSale.office.systemCode",
                "$.creation.pointOfSale.office.agentType",
                "$.creation.pointOfSale.login.cityCode",
                "$.creation.pointOfSale.login.countryCode",
                "$.creation.pointOfSale.login.numericSign",
                "$.creation.pointOfSale.login.initials",
                "$.creation.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_LAST_UPDATE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.lastModification.pointOfSale.office.id",
                "$.lastModification.pointOfSale.office.iataNumber",
                "$.lastModification.pointOfSale.office.systemCode",
                "$.lastModification.pointOfSale.office.agentType",
                "$.lastModification.pointOfSale.login.cityCode",
                "$.lastModification.pointOfSale.login.countryCode",
                "$.lastModification.pointOfSale.login.numericSign",
                "$.lastModification.pointOfSale.login.initials",
                "$.lastModification.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.creation.dateTime"
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.lastModification.dateTime"
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_RESERVATION_HISTO5",
      "mapping": {
        "merge": {
          "key-columns": [
            "RESERVATION_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          "$.mainResource.current.image"
        ],
        "columns": [
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.reference"
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.version"
              ]
            }
          },
          {
            "name": "NIP",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.nip"
              ]
            }
          },
          {
            "name": "GROUP_SIZE",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.size"
              ]
            }
          },
          {
            "name": "GROUP_NAME",
            "column-type": "strColumn",
            "sources": {
              "mapping": [
                "$.group.name"
              ]
            }
          },
          {
            "name": "GROUP_SIZE_TAKEN",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.sizeTaken"
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_OWNER_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.owner.office.id",
                "$.owner.office.iataNumber",
                "$.owner.office.systemCode",
                "$.owner.office.agentType",
                "$.owner.login.cityCode",
                "$.owner.login.countryCode",
                "$.owner.login.numericSign",
                "$.owner.login.initials",
                "$.owner.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_CREATION_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.creation.pointOfSale.office.id",
                "$.creation.pointOfSale.office.iataNumber",
                "$.creation.pointOfSale.office.systemCode",
                "$.creation.pointOfSale.office.agentType",
                "$.creation.pointOfSale.login.cityCode",
                "$.creation.pointOfSale.login.countryCode",
                "$.creation.pointOfSale.login.numericSign",
                "$.creation.pointOfSale.login.initials",
                "$.creation.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_LAST_UPDATE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.lastModification.pointOfSale.office.id",
                "$.lastModification.pointOfSale.office.iataNumber",
                "$.lastModification.pointOfSale.office.systemCode",
                "$.lastModification.pointOfSale.office.agentType",
                "$.lastModification.pointOfSale.login.cityCode",
                "$.lastModification.pointOfSale.login.countryCode",
                "$.lastModification.pointOfSale.login.numericSign",
                "$.lastModification.pointOfSale.login.initials",
                "$.lastModification.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.creation.dateTime"
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.lastModification.dateTime"
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_AIR_SEGMENT_PAX_HISTO",
      "mapping": {
        "merge": {
          "key-columns": [
            "AIR_SEGMENT_PAX_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"
              },
              {
                "tr": "$.travelers[*]"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "AIR_SEGMENT_PAX_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            }
          },
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "TRAVELER_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "SEGMENT_SCHEDULE_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.departure.iataCode"
                },
                {
                  "prod": "$.airSegment.arrival.iataCode"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.departure.localDateTime"
                },
                {
                  "prod": "$.airSegment.arrival.localDateTime"
                },
                {
                  "prod": "$.airSegment.departure.terminal"
                },
                {
                  "prod": "$.airSegment.arrival.terminal"
                },
                {
                  "prod": "$.airSegment.operating.codeshareAgreement"
                },
                {
                  "prod": "$.airSegment.aircraft.aircraftType"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "CABIN_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_CLASS_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_STATUS_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.id"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.iataNumber"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.systemCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.agentType"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.cityCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.countryCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.numericSign"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.initials"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.dutyCode"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            // ADDED one to showcase access to different json sections
            "name": "FIRST_NAME",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"
                }
              ]
            },
            "has-variable": true
          },
          {
            "name": "CABIN_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_CLASS_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_STATUS",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            }
          },
          {
            "name": "IS_INFO",
            "column-type": "booleanColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.isInformational"
                }
              ]
            }
          },
          {
            "name": "PRODUCT_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                }
              ]
            }
          },
          // ADDED one for filtering the delivery
          {
            "name": "PDI_NOREC",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isNoRec"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_GOSHOW",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isGoShow"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_DISTRIBUTION_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].distributionId"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "PDI_TRANSFER_FLAG",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].transferFlag"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "DEPARTURE_DATE_TIME",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.departure.localDateTime"
                }
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.reference"
                }
              ]
            }
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.creation.dateTime"
                }
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.version"
                }
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.lastModification.dateTime"
                }
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to a Passenger-Segment, with DCS delivery info.",
          "granularity": "1 PAX-SEG"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_AIR_SEGMENT_PAX_HISTO2",
      "mapping": {
        "merge": {
          "key-columns": [
            "AIR_SEGMENT_PAX_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"
              },
              {
                "tr": "$.travelers[*]"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "AIR_SEGMENT_PAX_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            }
          },
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "TRAVELER_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "SEGMENT_SCHEDULE_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.departure.iataCode"
                },
                {
                  "prod": "$.airSegment.arrival.iataCode"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.departure.localDateTime"
                },
                {
                  "prod": "$.airSegment.arrival.localDateTime"
                },
                {
                  "prod": "$.airSegment.departure.terminal"
                },
                {
                  "prod": "$.airSegment.arrival.terminal"
                },
                {
                  "prod": "$.airSegment.operating.codeshareAgreement"
                },
                {
                  "prod": "$.airSegment.aircraft.aircraftType"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "CABIN_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_CLASS_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_STATUS_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.id"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.iataNumber"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.systemCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.agentType"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.cityCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.countryCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.numericSign"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.initials"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.dutyCode"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            // ADDED one to showcase access to different json sections
            "name": "FIRST_NAME",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"
                }
              ]
            },
            "has-variable": true
          },
          {
            "name": "CABIN_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_CLASS_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_STATUS",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            }
          },
          {
            "name": "IS_INFO",
            "column-type": "booleanColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.isInformational"
                }
              ]
            }
          },
          {
            "name": "PRODUCT_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                }
              ]
            }
          },
          // ADDED one for filtering the delivery
          {
            "name": "PDI_NOREC",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isNoRec"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_GOSHOW",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isGoShow"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_DISTRIBUTION_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].distributionId"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "PDI_TRANSFER_FLAG",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].transferFlag"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "DEPARTURE_DATE_TIME",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.departure.localDateTime"
                }
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.reference"
                }
              ]
            }
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.creation.dateTime"
                }
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.version"
                }
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.lastModification.dateTime"
                }
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to a Passenger-Segment, with DCS delivery info.",
          "granularity": "1 PAX-SEG"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_AIR_SEGMENT_PAX_HISTO3",
      "mapping": {
        "merge": {
          "key-columns": [
            "AIR_SEGMENT_PAX_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"
              },
              {
                "tr": "$.travelers[*]"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "AIR_SEGMENT_PAX_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            }
          },
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "TRAVELER_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "SEGMENT_SCHEDULE_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.departure.iataCode"
                },
                {
                  "prod": "$.airSegment.arrival.iataCode"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.departure.localDateTime"
                },
                {
                  "prod": "$.airSegment.arrival.localDateTime"
                },
                {
                  "prod": "$.airSegment.departure.terminal"
                },
                {
                  "prod": "$.airSegment.arrival.terminal"
                },
                {
                  "prod": "$.airSegment.operating.codeshareAgreement"
                },
                {
                  "prod": "$.airSegment.aircraft.aircraftType"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "CABIN_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_CLASS_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_STATUS_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.id"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.iataNumber"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.systemCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.agentType"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.cityCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.countryCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.numericSign"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.initials"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.dutyCode"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            // ADDED one to showcase access to different json sections
            "name": "FIRST_NAME",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"
                }
              ]
            },
            "has-variable": true
          },
          {
            "name": "CABIN_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_CLASS_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_STATUS",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            }
          },
          {
            "name": "IS_INFO",
            "column-type": "booleanColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.isInformational"
                }
              ]
            }
          },
          {
            "name": "PRODUCT_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                }
              ]
            }
          },
          // ADDED one for filtering the delivery
          {
            "name": "PDI_NOREC",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isNoRec"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_GOSHOW",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isGoShow"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_DISTRIBUTION_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].distributionId"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "PDI_TRANSFER_FLAG",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].transferFlag"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "DEPARTURE_DATE_TIME",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.departure.localDateTime"
                }
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.reference"
                }
              ]
            }
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.creation.dateTime"
                }
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.version"
                }
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.lastModification.dateTime"
                }
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to a Passenger-Segment, with DCS delivery info.",
          "granularity": "1 PAX-SEG"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_AIR_SEGMENT_PAX_HISTO4",
      "mapping": {
        "merge": {
          "key-columns": [
            "AIR_SEGMENT_PAX_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"
              },
              {
                "tr": "$.travelers[*]"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "AIR_SEGMENT_PAX_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            }
          },
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "TRAVELER_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "SEGMENT_SCHEDULE_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.departure.iataCode"
                },
                {
                  "prod": "$.airSegment.arrival.iataCode"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.departure.localDateTime"
                },
                {
                  "prod": "$.airSegment.arrival.localDateTime"
                },
                {
                  "prod": "$.airSegment.departure.terminal"
                },
                {
                  "prod": "$.airSegment.arrival.terminal"
                },
                {
                  "prod": "$.airSegment.operating.codeshareAgreement"
                },
                {
                  "prod": "$.airSegment.aircraft.aircraftType"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "CABIN_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_CLASS_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_STATUS_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.id"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.iataNumber"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.systemCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.agentType"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.cityCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.countryCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.numericSign"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.initials"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.dutyCode"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            // ADDED one to showcase access to different json sections
            "name": "FIRST_NAME",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"
                }
              ]
            },
            "has-variable": true
          },
          {
            "name": "CABIN_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_CLASS_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_STATUS",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            }
          },
          {
            "name": "IS_INFO",
            "column-type": "booleanColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.isInformational"
                }
              ]
            }
          },
          {
            "name": "PRODUCT_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                }
              ]
            }
          },
          // ADDED one for filtering the delivery
          {
            "name": "PDI_NOREC",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isNoRec"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_GOSHOW",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isGoShow"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_DISTRIBUTION_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].distributionId"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "PDI_TRANSFER_FLAG",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].transferFlag"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "DEPARTURE_DATE_TIME",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.departure.localDateTime"
                }
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.reference"
                }
              ]
            }
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.creation.dateTime"
                }
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.version"
                }
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.lastModification.dateTime"
                }
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to a Passenger-Segment, with DCS delivery info.",
          "granularity": "1 PAX-SEG"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "FACT_AIR_SEGMENT_PAX_HISTO5",
      "mapping": {
        "merge": {
          "key-columns": [
            "AIR_SEGMENT_PAX_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "prod": "$.mainResource.current.image.products[?(@.subType == 'AIR')]"
              },
              {
                "tr": "$.travelers[*]"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "AIR_SEGMENT_PAX_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                },
                {
                  "tr": "$.id"
                }
              ]
            }
          },
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.id"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "TRAVELER_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "SEGMENT_SCHEDULE_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.carrierCode"
                },
                {
                  "prod": "$.airSegment.departure.iataCode"
                },
                {
                  "prod": "$.airSegment.arrival.iataCode"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.marketing.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.flightNumber"
                },
                {
                  "prod": "$.airSegment.operating.flightDesignator.operationalSuffix"
                },
                {
                  "prod": "$.airSegment.departure.localDateTime"
                },
                {
                  "prod": "$.airSegment.arrival.localDateTime"
                },
                {
                  "prod": "$.airSegment.departure.terminal"
                },
                {
                  "prod": "$.airSegment.arrival.terminal"
                },
                {
                  "prod": "$.airSegment.operating.codeshareAgreement"
                },
                {
                  "prod": "$.airSegment.aircraft.aircraftType"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "CABIN_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_CLASS_CODE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "BOOKING_STATUS_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            },
            "expr": "hashS({0})"
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.id"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.iataNumber"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.systemCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.office.agentType"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.cityCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.countryCode"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.numericSign"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.initials"
                },
                {
                  "prod": "$.airSegment.creation.pointOfSale.login.dutyCode"
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            // ADDED one to showcase access to different json sections
            "name": "FIRST_NAME",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"
                }
              ]
            },
            "has-variable": true
          },
          {
            "name": "CABIN_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.cabin.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_CLASS_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.marketing.bookingClass.code"
                }
              ]
            }
          },
          {
            "name": "BOOKING_STATUS",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.bookingStatusCode"
                }
              ]
            }
          },
          {
            "name": "IS_INFO",
            "column-type": "booleanColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.isInformational"
                }
              ]
            }
          },
          {
            "name": "PRODUCT_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.id"
                }
              ]
            }
          },
          // ADDED one for filtering the delivery
          {
            "name": "PDI_NOREC",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isNoRec"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_GOSHOW",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isGoShow"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_DISTRIBUTION_ID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].distributionId"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "PDI_TRANSFER_FLAG",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].transferFlag"
                }
              ]
            },
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "DEPARTURE_DATE_TIME",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "prod": "$.airSegment.departure.localDateTime"
                }
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.reference"
                }
              ]
            }
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.creation.dateTime"
                }
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.version"
                }
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "blocks": [
                {
                  "root": "$.mainResource.current.image.lastModification.dateTime"
                }
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to a Passenger-Segment, with DCS delivery info.",
          "granularity": "1 PAX-SEG"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    { "name": "FACT_RESERVATION", "latest": { "histo-table-name": "FACT_RESERVATION_HISTO" } },
    { "name": "FACT_RESERVATION2", "latest": { "histo-table-name": "FACT_RESERVATION_HISTO2" } },
    { "name": "FACT_RESERVATION3", "latest": { "histo-table-name": "FACT_RESERVATION_HISTO3" } },
    { "name": "FACT_RESERVATION4", "latest": { "histo-table-name": "FACT_RESERVATION_HISTO4" } },
    { "name": "FACT_RESERVATION5", "latest": { "histo-table-name": "FACT_RESERVATION_HISTO5" } },
    { "name": "FACT_AIR_SEGMENT_PAX", "latest": { "histo-table-name": "FACT_AIR_SEGMENT_PAX_HISTO" } },
    { "name": "FACT_AIR_SEGMENT_PAX2", "latest": { "histo-table-name": "FACT_AIR_SEGMENT_PAX_HISTO2" } },
    { "name": "FACT_AIR_SEGMENT_PAX3", "latest": { "histo-table-name": "FACT_AIR_SEGMENT_PAX_HISTO3" } },
    { "name": "FACT_AIR_SEGMENT_PAX4", "latest": { "histo-table-name": "FACT_AIR_SEGMENT_PAX_HISTO4" } },
    { "name": "FACT_AIR_SEGMENT_PAX5", "latest": { "histo-table-name": "FACT_AIR_SEGMENT_PAX_HISTO5" } },
    {
      "name": "DIM_POINT_OF_SALE",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },

    {
      "name": "DIM_POINT_OF_SALE2",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_POINT_OF_SALE3",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_POINT_OF_SALE4",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_POINT_OF_SALE5",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_POINT_OF_SALE6",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_POINT_OF_SALE7",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_POINT_OF_SALE8",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_POINT_OF_SALE9",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_POINT_OF_SALE10",
      "mapping": {
        "merge": {
          "key-columns": [
            "POINT_OF_SALE_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "name": "owner",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.owner"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "creation",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "lastModification",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.lastModification.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          },
          {
            "name": "queuing",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image"
                },
                {
                  "office": "$.queuingOffice"
                }
              ]
            }
          },
          {
            "name": "air",
            "rs": {
              "blocks": [
                {
                  "pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"
                },
                {
                  "office": "$.office"
                }
              ]
            }
          }
        ],
        "columns": [
          {
            "name": "OFFICE_AMID",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.id"
                }
              ]
            }
          },
          {
            "name": "OFFICE_IATA_NUMBER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.iataNumber"
                }
              ]
            }
          },
          {
            "name": "OFFICE_SYSTEM_CODE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.systemCode"
                }
              ]
            }
          },
          {
            "name": "OFFICE_AGENT_TYPE",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "office": "$.agentType"
                }
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_ID",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.iataNumber"
                    },
                    // shouldn't this be {"pos": "$.login.cityCode"} ???
                    {
                      "pos": "$.login.systemCode"
                    },
                    // shouldn't this be {"pos": "$.login.countryCode"} ???
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "queuing",
                  "blocks": [
                    {
                      "office": "$.id"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "office": "$.id"
                    },
                    {
                      "office": "$.iataNumber"
                    },
                    {
                      "office": "$.systemCode"
                    },
                    {
                      "office": "$.agentType"
                    },
                    {
                      "pos": "$.login.cityCode"
                    },
                    {
                      "pos": "$.login.countryCode"
                    },
                    {
                      "pos": "$.login.numericSign"
                    },
                    {
                      "pos": "$.login.initials"
                    },
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.cityCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_COUNTRY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.countryCode"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_NUMERIC_SIGN",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.numericSign"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_INITIALS",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.initials"
                    }
                  ]
                }
              ]
            }
          },
          {
            "name": "LOGIN_DUTY_CODE",
            "column-type": "strColumn",
            "sources": {
              "root-specific": [
                {
                  "rs-name": "owner",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "creation",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "lastModification",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                },
                {
                  "rs-name": "air",
                  "blocks": [
                    {
                      "pos": "$.login.dutyCode"
                    }
                  ]
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CARRIER",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },

    {
      "name": "DIM_CARRIER2",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },

    {
      "name": "DIM_CARRIER3",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CARRIER4",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CARRIER5",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CARRIER6",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CARRIER7",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CARRIER8",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CARRIER9",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "DIM_CARRIER10",
      "mapping": {
        "merge": {
          "key-columns": [
            "CARRIER_ID"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].flightSegment.operating.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].handlingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.originatorCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.targetCompany"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.deliveries[*].legDeliveries[*].acceptance.interAirlineThroughCheckIn.onwardFlight.marketing.flightDesignator.carrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].bagsGroupDeliveries[*].bagTag.issuingCarrier"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.automatedProcesses[*].applicableCarrierCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.quotations[*].coupons[*].servicePresentation.toCarrierIataCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].passenger.passengerDeliveries[*].deliveryAirlineCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.travelers[*].identityDocuments[*].serviceProvider.code"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.activeTier.companyCode"
              }
            ]
          },
          {
            "blocks": [
              {
                "companyCode": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service.membership.allianceTier.companyCode"
              }
            ]
          }
        ],
        "columns": [
          {
            "name": "CARRIER_ID",
            "column-type": "longColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            },
            "expr": "hashXS({0})"
          },
          {
            "name": "CARRIER",
            "column-type": "strColumn",
            "sources": {
              "blocks": [
                {
                  "companyCode": "$.value"
                }
              ]
            }
          }
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    },
    {
      "name": "ASSO_RESERVATION_HISTO",
      "mapping": {
        "merge": {
          "key-columns": [
            "RESERVATION_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          "$.mainResource.current.image"
        ],
        "columns": [
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.reference"
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.version"
              ]
            }
          },
          {
            "name": "NIP",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.nip"
              ]
            }
          },
          {
            "name": "GROUP_SIZE",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.size"
              ]
            }
          },
          {
            "name": "GROUP_NAME",
            "column-type": "strColumn",
            "sources": {
              "mapping": [
                "$.group.name"
              ]
            }
          },
          {
            "name": "GROUP_SIZE_TAKEN",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.sizeTaken"
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_OWNER_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.owner.office.id",
                "$.owner.office.iataNumber",
                "$.owner.office.systemCode",
                "$.owner.office.agentType",
                "$.owner.login.cityCode",
                "$.owner.login.countryCode",
                "$.owner.login.numericSign",
                "$.owner.login.initials",
                "$.owner.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_CREATION_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.creation.pointOfSale.office.id",
                "$.creation.pointOfSale.office.iataNumber",
                "$.creation.pointOfSale.office.systemCode",
                "$.creation.pointOfSale.office.agentType",
                "$.creation.pointOfSale.login.cityCode",
                "$.creation.pointOfSale.login.countryCode",
                "$.creation.pointOfSale.login.numericSign",
                "$.creation.pointOfSale.login.initials",
                "$.creation.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_LAST_UPDATE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.lastModification.pointOfSale.office.id",
                "$.lastModification.pointOfSale.office.iataNumber",
                "$.lastModification.pointOfSale.office.systemCode",
                "$.lastModification.pointOfSale.office.agentType",
                "$.lastModification.pointOfSale.login.cityCode",
                "$.lastModification.pointOfSale.login.countryCode",
                "$.lastModification.pointOfSale.login.numericSign",
                "$.lastModification.pointOfSale.login.initials",
                "$.lastModification.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.creation.dateTime"
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.lastModification.dateTime"
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "ASSO_RESERVATION_HISTO2",
      "mapping": {
        "merge": {
          "key-columns": [
            "RESERVATION_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          "$.mainResource.current.image"
        ],
        "columns": [
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.reference"
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.version"
              ]
            }
          },
          {
            "name": "NIP",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.nip"
              ]
            }
          },
          {
            "name": "GROUP_SIZE",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.size"
              ]
            }
          },
          {
            "name": "GROUP_NAME",
            "column-type": "strColumn",
            "sources": {
              "mapping": [
                "$.group.name"
              ]
            }
          },
          {
            "name": "GROUP_SIZE_TAKEN",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.sizeTaken"
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_OWNER_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.owner.office.id",
                "$.owner.office.iataNumber",
                "$.owner.office.systemCode",
                "$.owner.office.agentType",
                "$.owner.login.cityCode",
                "$.owner.login.countryCode",
                "$.owner.login.numericSign",
                "$.owner.login.initials",
                "$.owner.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_CREATION_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.creation.pointOfSale.office.id",
                "$.creation.pointOfSale.office.iataNumber",
                "$.creation.pointOfSale.office.systemCode",
                "$.creation.pointOfSale.office.agentType",
                "$.creation.pointOfSale.login.cityCode",
                "$.creation.pointOfSale.login.countryCode",
                "$.creation.pointOfSale.login.numericSign",
                "$.creation.pointOfSale.login.initials",
                "$.creation.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_LAST_UPDATE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.lastModification.pointOfSale.office.id",
                "$.lastModification.pointOfSale.office.iataNumber",
                "$.lastModification.pointOfSale.office.systemCode",
                "$.lastModification.pointOfSale.office.agentType",
                "$.lastModification.pointOfSale.login.cityCode",
                "$.lastModification.pointOfSale.login.countryCode",
                "$.lastModification.pointOfSale.login.numericSign",
                "$.lastModification.pointOfSale.login.initials",
                "$.lastModification.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.creation.dateTime"
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.lastModification.dateTime"
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    },
    {
      "name": "ASSO_RESERVATION_HISTO3",
      "mapping": {
        "merge": {
          "key-columns": [
            "RESERVATION_ID",
            "VERSION"
          ],
          "if-dupe-take-higher": [
            "LOAD_DATE"
          ]
        },
        "root-sources": [
          "$.mainResource.current.image"
        ],
        "columns": [
          {
            "name": "RESERVATION_ID",
            "column-type": "binaryStrColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "REFERENCE_KEY",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.id"
              ]
            }
          },
          {
            "name": "RECORD_LOCATOR",
            "column-type": "strColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.reference"
              ]
            }
          },
          {
            "name": "VERSION",
            "column-type": "intColumn",
            "is-mandatory": "true",
            "sources": {
              "mapping": [
                "$.version"
              ]
            }
          },
          {
            "name": "NIP",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.nip"
              ]
            }
          },
          {
            "name": "GROUP_SIZE",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.size"
              ]
            }
          },
          {
            "name": "GROUP_NAME",
            "column-type": "strColumn",
            "sources": {
              "mapping": [
                "$.group.name"
              ]
            }
          },
          {
            "name": "GROUP_SIZE_TAKEN",
            "column-type": "intColumn",
            "sources": {
              "mapping": [
                "$.group.sizeTaken"
              ]
            }
          },
          {
            "name": "POINT_OF_SALE_OWNER_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.owner.office.id",
                "$.owner.office.iataNumber",
                "$.owner.office.systemCode",
                "$.owner.office.agentType",
                "$.owner.login.cityCode",
                "$.owner.login.countryCode",
                "$.owner.login.numericSign",
                "$.owner.login.initials",
                "$.owner.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_CREATION_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.creation.pointOfSale.office.id",
                "$.creation.pointOfSale.office.iataNumber",
                "$.creation.pointOfSale.office.systemCode",
                "$.creation.pointOfSale.office.agentType",
                "$.creation.pointOfSale.login.cityCode",
                "$.creation.pointOfSale.login.countryCode",
                "$.creation.pointOfSale.login.numericSign",
                "$.creation.pointOfSale.login.initials",
                "$.creation.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "POINT_OF_SALE_LAST_UPDATE_ID",
            "column-type": "binaryStrColumn",
            "sources": {
              "mapping": [
                "$.lastModification.pointOfSale.office.id",
                "$.lastModification.pointOfSale.office.iataNumber",
                "$.lastModification.pointOfSale.office.systemCode",
                "$.lastModification.pointOfSale.office.agentType",
                "$.lastModification.pointOfSale.login.cityCode",
                "$.lastModification.pointOfSale.login.countryCode",
                "$.lastModification.pointOfSale.login.numericSign",
                "$.lastModification.pointOfSale.login.initials",
                "$.lastModification.pointOfSale.login.dutyCode"
              ]
            },
            "expr": "hashM({0})"
          },
          {
            "name": "PNR_CREATION_DATE",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.creation.dateTime"
              ]
            }
          },
          {
            "name": "DATE_BEGIN",
            "column-type": "timestampColumn",
            "sources": {
              "mapping": [
                "$.lastModification.dateTime"
              ]
            }
          },
          {
            "name": "DATE_END",
            "column-type": "timestampColumn",
            "sources": {}
          },
          {
            "name": "IS_LAST_VERSION",
            "column-type": "booleanColumn",
            "sources": {}
          }
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to the booking on PNR-level.",
          "granularity": "1 PNR"
        }
      },
      "table-snowflake": {
        "cluster-by": [
          "date_trunc('WEEK', PNR_CREATION_DATE)"
        ]
      }
    }

  ]
}
