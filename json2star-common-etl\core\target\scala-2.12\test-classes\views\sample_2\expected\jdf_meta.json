{"version": "[VERSION]", "path": "output/path", "shard": "[SHARD]", "schemaName": "DEFAULT_DB.db", "status": "ACTIVATED", "tables": [{"name": "ASSO_AIR_SEGMENT_PAX_COUPON_HISTO", "type": "ASSO", "description": "Correlation table between FACT_AIR_SEGMENT_PAX_HISTO and FACT_COUPON_HISTO", "gdprZone": "red", "kind": "MATERIALIZED", "columns": [{"name": "AIR_SEGMENT_PAX_ID", "description": "CUSTOM DESCRIPTION FOR AIR_SEGMENT_PAX_ID", "type": "STRING", "primaryKey": true, "gdprZone": "red", "piiType": "CUSTOM PII TYPE FOR AIR_SEGMENT_PAX_ID", "example": "CUSTOM EXAMPLE FOR AIR_SEGMENT_PAX_ID", "fkRelationships": [{"schemaName": "PNR_DATABASE_NAME", "tableName": "FACT_AIR_SEGMENT_PAX_HISTO", "columnName": "AIR_SEGMENT_PAX_ID"}]}, {"name": "COUPON_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": true, "gdprZone": "red", "piiType": "", "fkRelationships": [{"schemaName": "TKT_DATABASE_NAME", "tableName": "FACT_COUPON_HISTO", "columnName": "COUPON_ID"}]}, {"name": "VERSION_PNR", "description": "", "type": "BIGINT", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [{"schemaName": "PNR_DATABASE_NAME", "tableName": "FACT_AIR_SEGMENT_PAX_HISTO", "columnName": "VERSION"}]}, {"name": "VERSION_TRAVEL_DOCUMENT", "description": "", "type": "BIGINT", "primaryKey": true, "gdprZone": "green", "piiType": "", "fkRelationships": [{"schemaName": "TKT_DATABASE_NAME", "tableName": "FACT_COUPON_HISTO", "columnName": "VERSION"}]}, {"name": "DATE_BEGIN", "description": "Validity start date of correlation", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}, {"name": "DATE_END", "description": "Validity end date of correlation", "type": "TIMESTAMP", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}, {"name": "IS_LAST_VERSION", "description": "True if it is the last correlation otherwise False", "type": "BOOLEAN", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}, {"name": "ASSO_AIR_SEGMENT_PAX_COUPON", "type": "ASSO", "description": "Latest view of ASSO_AIR_SEGMENT_PAX_COUPON_HISTO", "gdprZone": "red", "kind": "VIEW", "query": "SELECT AIR_SEGMENT_PAX_ID,COUPON_ID,VERSION_PNR,VERSION_TRAVEL_DOCUMENT FROM ASSO_AIR_SEGMENT_PAX_COUPON_HISTO WHERE IS_LAST_VERSION=true", "columns": [{"name": "AIR_SEGMENT_PAX_ID", "description": "CUSTOM DESCRIPTION FOR AIR_SEGMENT_PAX_ID", "type": "STRING", "primaryKey": true, "gdprZone": "red", "piiType": "CUSTOM PII TYPE FOR AIR_SEGMENT_PAX_ID", "example": "CUSTOM EXAMPLE FOR AIR_SEGMENT_PAX_ID", "fkRelationships": [{"schemaName": "PNR_DATABASE_NAME", "tableName": "FACT_AIR_SEGMENT_PAX", "columnName": "AIR_SEGMENT_PAX_ID"}]}, {"name": "COUPON_ID", "description": "Hashed foreign key", "type": "STRING", "primaryKey": true, "gdprZone": "red", "piiType": "", "fkRelationships": [{"schemaName": "TKT_DATABASE_NAME", "tableName": "FACT_COUPON", "columnName": "COUPON_ID"}]}, {"name": "VERSION_PNR", "description": "", "type": "BIGINT", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}, {"name": "VERSION_TRAVEL_DOCUMENT", "description": "", "type": "BIGINT", "primaryKey": false, "gdprZone": "green", "piiType": "", "fkRelationships": []}]}]}