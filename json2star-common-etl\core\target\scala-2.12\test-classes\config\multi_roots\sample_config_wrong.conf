{
  "tables" : [
    {
      "name": "DIM_POINT_OF_SALE_SIMPLIFIED_NEW",
      "mapping": {
        "merge": {
          "key-columns": ["POINT_OF_SALE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "name": "owner", "rs" : { "blocks": [{"pos": "$.mainResource.current.image.owner"},{"office" : "$.office"}]}},
          { "name": "creation", "rs" : { "blocks": [{"pos": "$.mainResource.current.image.creation.pointOfSale"},{"office" : "$.office"}]}},
          { "name": "lastModification", "rs" : { "blocks": [{"pos": "$.mainResource.current.image.lastModification.pointOfSale"},{"office" : "$.office"}]}},
          { "name": "dummy", "rs" : { "blocks": [{"pos": "$.mainResource.current.image.dummy.pointOfSale"},{"office" : "$.office"}]}}
        ],
        "columns": [
          {
            "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn", "sources": { "root-specific": [
              {"rs-name": "owner", "blocks": [{"office": "$.id1"}]},
              {"rs-name": "creation", "blocks": [{"office": "$.id2"}]},
              {"rs-name": "lastModification", "blocks": [{"office": "$.id3"}]},
              {"rs-name": "dummy", "blocks": [{"office": "$.id4"}]}
            ]},
            "expr": "hashM({0})"
          },
          {
            "name": "LOGIN_CITY_CODE", "column-type": "strColumn", "sources": { "root-specific": [
              {"rs-name": "owner", "blocks": [{"pos": "$.login.cityCode1"}]},
              {"rs-name": "creation", "blocks": [{"pos": "$.login.cityCode2"}]},
              {"rs-name": "lastModification", "blocks": [{"pos": "$.login.cityCode3"}]},
              {"rs-name": "dummy", "literal": "CONSTANT_VALUE", "blocks": [{"pos": "$.dummy"}]} // both literal and blocks is wrong!
            ]}
          },
          { "name": "OFFICE_AMID", "column-type": "strColumn", "sources": {"blocks": [{"office": "$.id"}]} },
          { "name": "OFFICE_IATA_NUMBER", "column-type": "strColumn", "sources":  {"blocks": [{"office": "$.iataNumber"}]}},
          { "name": "OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources":  {"blocks": [{"office": "$.systemCode"}]}},
          { "name": "OFFICE_AGENT_TYPE", "column-type": "strColumn", "sources":  {"blocks": [{"office": "$.agentType"}]}}
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    }

  ]
}