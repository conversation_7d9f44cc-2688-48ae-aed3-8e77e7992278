jsonPath^yamlPath^descriptionResult^exampleResult^NUM
$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.countries[*].code.value^attachments.payload.offerData.offer.context.pointOfBusiness.location.countries.code^Missing^Missing^5
$.attachments[*].payload.offerData.offer.context.pointOfBusiness.location.iataCode^attachments.payload.offerData.offer.context.pointOfBusiness.location.iataCode^Missing^Missing^3
$.attachments[*].payload.offerData.offer.id^attachments.payload.offerData.offer.id^Success^Empty^8
$.attachments[*].payload.offerData.offer.lifecycle.version^attachments.payload.offerData.offer.lifecycle.version^Empty^Empty^7
$.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].boardPointIataCode^attachments.payload.offerData.offer.offerItems.airBounds.boardPointIataCode^Success^Empty^1
$.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].boundPosition^attachments.payload.offerData.offer.offerItems.airBounds.boundPosition^Success^Empty^1
$.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].elapsedTravelingTime^attachments.payload.offerData.offer.offerItems.airBounds.elapsedTravelingTime^Success^Empty^1
$.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].offPointIataCode^attachments.payload.offerData.offer.offerItems.airBounds.offPointIataCode^Success^Empty^1
$.attachments[*].payload.offerData.offer.offerItems[*].id^attachments.payload.offerData.offer.offerItems.id^Success^Empty^2
$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].airBounds[*].id^attachments.payload.offerData.offer.offerItems.id^Success^Empty^2
$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeDetails.id^attachments.payload.offerData.offer.offerItems.id^Success^Empty^2
$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type^attachments.payload.offerData.offer.offerItems.id^Success^Empty^3
$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].type^attachments.payload.offerData.offer.offerItems.id^Success^Empty^2
$.attachments[*].payload.offerData.offer.offerItems[*].id - $.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].id^attachments.payload.offerData.offer.offerItems.id^Success^Empty^2
$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].arcNumber^attachments.payload.offerData.offer.offerItems.offices.arcNumber^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].countryCode^attachments.payload.offerData.offer.offerItems.offices.countryCode^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].erspNumber^attachments.payload.offerData.offer.offerItems.offices.erspNumber^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].iataLineNumber^attachments.payload.offerData.offer.offerItems.offices.iataLineNumber^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeDetails.IATANumber^attachments.payload.offerData.offer.offerItems.offices.officeDetails.IATANumber^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeDetails.id^attachments.payload.offerData.offer.offerItems.offices.officeDetails.id^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].officeType^attachments.payload.offerData.offer.offerItems.offices.officeType^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].offices[*].trueCityCode^attachments.payload.offerData.offer.offerItems.offices.trueCityCode^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].currency^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.composition.currency^Missing^Missing^2
$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].type^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.composition.type^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.composition[*].decimalPlaces^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.composition^Missing^Missing^2
$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.currency^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.currency^Missing^Missing^2
$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.type^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount.type^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.value - $.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].totalAmount.decimalPlaces^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.totalAmount^Missing^Missing^2
$.attachments[*].payload.offerData.offer.offerItems[*].priceInformation.amountsCollections[*].type^attachments.payload.offerData.offer.offerItems.priceInformation.amountsCollections.type^Missing^Missing^1
$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].code^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.code^Success^Empty^1
$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].fareFamilyDescription^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.fareFamilyDescription^Success^Empty^1
$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].id^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.id^Success^Empty^1
$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].owner^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.owner^Success^Empty^1
$.attachments[*].payload.offerData.offer.offerItems[*].retailing.branding.fareFamilies[*].ranking^attachments.payload.offerData.offer.offerItems.retailing.branding.fareFamilies.ranking^Success^Empty^1
$.attachments[*].payload.offerData.offerSet.id^attachments.payload.offerData.offerSet.id^Empty^Empty^1
$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].airlineLevel.company.name^attachments.payload.offerData.offerSet.loyaltyAccounts.airlineLevel.company.name^Missing^Missing^3
$.attachments[*].payload.offerData.offerSet.loyaltyAccounts[*].allianceLevel.company.name^attachments.payload.offerData.offerSet.loyaltyAccounts.allianceLevel.company.name^Missing^Missing^3
$.attachments[*].payload.offerData.offerSet.offerStakeholders[*].company.name^attachments.payload.offerData.offerSet.offerStakeholders.company.name^Success^Empty^3
$.data.currentImage.agreements.lifecycle.creation.location.address.countryCode.value^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.location.address.countryCode^Success^Empty^5
$.data.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode.value^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.location.address.countryCode^Success^Empty^5
$.data.currentImage.agreements[*].id^OrderOrderChangeEvent.currentImage.agreements.id^Success^Empty^1
$.data.currentImage.agreements[*].id - $.data.currentImage.agreements[*].items[*].id^OrderOrderChangeEvent.currentImage.agreements.id^Success^Empty^2
$.data.currentImage.agreements[*].items[*].offerItemRef.offerRef.href^OrderOrderChangeEvent.currentImage.agreements.items.offerItemRef.offerRef.href^Success^Empty^1
$.data.currentImage.agreements[*].items[*].stakeholderPtrByOfferStakeholderId^OrderOrderChangeEvent.currentImage.agreements.items.stakeholderPtrByOfferStakeholderId^Success^Empty^1
$.data.currentImage.agreements[*].lifecycle.creation.dateTime^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.dateTime^Success^Empty^1
$.data.currentImage.agreements[*].lifecycle.creation.dateTime - $.data.currentImage.agreements[*].id^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.dateTime^Success^Empty^1
$.data.currentImage.agreements[*].lifecycle.creation.user.iataNumber^OrderOrderChangeEvent.currentImage.agreements.lifecycle.creation.user.iataNumber^Success^Empty^1
$.data.currentImage.agreements[*].lifecycle.dataVersion^OrderOrderChangeEvent.currentImage.agreements.lifecycle.dataVersion^Success^Empty^1
$.data.currentImage.agreements[*].lifecycle.lastUpdate.dateTime^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.dateTime^Success^Empty^1
$.data.currentImage.agreements[*].lifecycle.lastUpdate.user.iataNumber^OrderOrderChangeEvent.currentImage.agreements.lifecycle.lastUpdate.user.iataNumber^Success^Empty^1
$.data.currentImage.agreements[*].sellerPtr^OrderOrderChangeEvent.currentImage.agreements.sellerPtr^Success^Empty^1
$.data.currentImage.id^OrderOrderChangeEvent.currentImage.id^Success^Empty^18
$.data.currentImage.lifecycle.creation.dateTime^OrderOrderChangeEvent.currentImage.lifecycle.creation.dateTime^Success^Empty^1
$.data.currentImage.lifecycle.creation.location.address.countryCode^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.address.countryCode^Success^Empty^1
$.data.currentImage.lifecycle.creation.location.address.countryCode.value^OrderOrderChangeEvent.currentImage.lifecycle.creation.location.address.countryCode^Success^Empty^5
$.data.currentImage.lifecycle.creation.user.company.name^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.company.name^Missing^Missing^3
$.data.currentImage.lifecycle.creation.user.iataNumber^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.iataNumber^Success^Empty^2
$.data.currentImage.lifecycle.creation.user.login^OrderOrderChangeEvent.currentImage.lifecycle.creation.user.login^Missing^Missing^1
$.data.currentImage.lifecycle.dataVersion^OrderOrderChangeEvent.currentImage.lifecycle.dataVersion^Success^Empty^8
$.data.currentImage.lifecycle.lastUpdate.dateTime^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.dateTime^Success^Empty^8
$.data.currentImage.lifecycle.lastUpdate.location.address.countryCode^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.address.countryCode^Success^Empty^1
$.data.currentImage.lifecycle.lastUpdate.location.address.countryCode.value^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.location.address.countryCode^Success^Empty^5
$.data.currentImage.lifecycle.lastUpdate.user.company.name^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.company.name^Missing^Missing^3
$.data.currentImage.lifecycle.lastUpdate.user.iataNumber^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.iataNumber^Success^Empty^2
$.data.currentImage.lifecycle.lastUpdate.user.login^OrderOrderChangeEvent.currentImage.lifecycle.lastUpdate.user.login^Missing^Missing^1
$.data.currentImage.orderItems[*].cancelledByAgreementItemPtr^OrderOrderChangeEvent.currentImage.orderItems.cancelledByAgreementItemPtr^Success^Empty^2
$.data.currentImage.orderItems[*].createdByAgreementItemPtr^OrderOrderChangeEvent.currentImage.orderItems.createdByAgreementItemPtr^Success^Empty^2
$.data.currentImage.orderItems[*].id^OrderOrderChangeEvent.currentImage.orderItems.id^Success^Empty^5
$.data.currentImage.orderItems[*].lifecycle.creation.location.address.countryCode.value^OrderOrderLifecycle.creation.location.address.countryCode^Success^Empty^5
$.data.currentImage.orderItems[*].lifecycle.lastUpdate.location.address.countryCode.value^OrderOrderLifecycle.lastUpdate.location.address.countryCode^Success^Empty^5
$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.href^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.href^Success^Empty^3
$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerRef.id^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerRef.id^Success^Empty^6
$.data.currentImage.orderItems[*].services[*].context.offerServiceItemRef.offerServiceItemPtr^OrderOrderChangeEvent.currentImage.orderItems.services.context.offerServiceItemRef.offerServiceItemPtr^Success^Empty^9
$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance.id^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^Success^Empty^6
$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].cabin.code^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.cabin.code^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].id^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^Success^Empty^2
$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].leg.aircraftEquipment.aircraftType^Leg\.v1.aircraftEquipment.aircraftType^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'airLegCabinProduct')].productType^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.productType^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'serviceProduct')].id^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.id^Success^Empty^2
$.data.currentImage.orderItems[*].services[*].context.soldProduct.productInstance[?(@.productType == 'serviceProduct')].productType^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productInstance.productType^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.productRef.id^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.productRef.id^Success^Empty^3
$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.bookingClass^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.bookingClass^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.flightConnectionQualifier^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.flightConnectionQualifier^Missing^Missing^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.flightStatus^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.flightStatus^Missing^Missing^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.itineraryOrder^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.itineraryOrder^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.segmentElapsedFlyingTime^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.segmentElapsedFlyingTime^Missing^Missing^1
$.data.currentImage.orderItems[*].services[*].context.soldProduct.serviceItemExecution.validatingCarrierRef^OrderOrderChangeEvent.currentImage.orderItems.services.context.soldProduct.serviceItemExecution.validatingCarrierRef^Missing^Missing^1
$.data.currentImage.orderItems[*].services[*].id^OrderOrderChangeEvent.currentImage.orderItems.services.id^Success^Empty^5
$.data.currentImage.orderItems[*].services[*].id - $.data.currentImage.orderItems[*].services[*].measure.stakeholders[*].personalData.personalDataRef.id^OrderOrderChangeEvent.currentImage.orderItems.services.id^Success^Empty^2
$.data.currentImage.orderItems[*].services[*].lifecycle.creation.location.address.countryCode.value^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.location.address.countryCode^Success^Empty^5
$.data.currentImage.orderItems[*].services[*].lifecycle.creation.user.iataNumber^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.creation.user.iataNumber^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.location.address.countryCode.value^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.location.address.countryCode^Success^Empty^5
$.data.currentImage.orderItems[*].services[*].lifecycle.lastUpdate.user.iataNumber^OrderOrderChangeEvent.currentImage.orderItems.services.lifecycle.lastUpdate.user.iataNumber^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.issuanceLocalDateTime^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.issuanceLocalDateTime^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.checkDigit^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.checkDigit^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.number^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.number^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenSale.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.numericalAirlineCode^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.documentType^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.documentType^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.issuanceLocalDateTime^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.issuanceLocalDateTime^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.number^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.number^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.structuredPrimaryDocumentNumber.numericalAirlineCode^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.structuredPrimaryDocumentNumber.numericalAirlineCode^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_OpenTicket.v1')].document.version^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.document.version^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.creation.dateTime^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.creation.dateTime^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.reference^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.reference^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.externalRecordReferences[?(@.referenceType == 'Order_ExternalRecordReference_Pnr.v1')].pnr.version^OrderOrderChangeEvent.currentImage.orderItems.services.measure.externalRecordReferences.pnr.version^Missing^Missing^3
$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'airSegmentBookingClassProduct')].cabin.code^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.cabin.code^Success^Empty^2
$.data.currentImage.orderItems[*].services[*].measure.product.productInstance[?(@.productType == 'serviceProduct')].baselineInformation.ssrCode^OrderOrderChangeEvent.currentImage.orderItems.services.measure.product.productInstance.baselineInformation.ssrCode^Missing^Missing^2
$.data.currentImage.orderItems[*].services[*].measure.stakeholders[*].personalData.personalDataRef.id^OrderOrderChangeEvent.currentImage.orderItems.services.measure.stakeholders.personalData.personalDataRef.id^Success^Empty^1
$.data.currentImage.orderItems[*].services[*].type^OrderOrderChangeEvent.currentImage.orderItems.services.type^Success^Empty^3
$.data.currentImage.payments[*].lifecycle.creation.location.address.countryCode.value^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.location.address.countryCode^Success^Empty^5
$.data.currentImage.payments[*].lifecycle.creation.user.iataNumber^OrderOrderChangeEvent.currentImage.payments.lifecycle.creation.user.iataNumber^Success^Empty^1
$.data.currentImage.payments[*].lifecycle.lastUpdate.location.address.countryCode.value^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.location.address.countryCode^Success^Empty^5
$.data.currentImage.payments[*].lifecycle.lastUpdate.user.iataNumber^OrderOrderChangeEvent.currentImage.payments.lifecycle.lastUpdate.user.iataNumber^Success^Empty^1
$.data.currentImage.stakeholders[*].id^OrderOrderChangeEvent.currentImage.stakeholders.id^Success^Empty^2
$.data.currentImage.stakeholders[*].personalDataRef.href^OrderServiceFulfillmentStakeholderPersonalData.personalDataRef.href^Success^Empty^1
$.data.currentImage.stakeholders[*].personalDataRef.id^OrderServiceFulfillmentStakeholderPersonalData.personalDataRef.id^Success^Empty^1
