{
  "defaultComment" : "A comment here",
  "partition-spec" : {
    "key" : "DATE_BEGIN",
    "column-name": "PART_DATE_BEGIN_MONTH",
    "expr": "date_format(DATE_BEGIN, \"yyyy-MM\")"
  },
  "tables": [
    {
      "name": "FACT_RESERVATION_DUMMY",
      "table-selectors": ["dummy_selector"]
      "latest": {
        "histo-table-name": "FACT_RESERVATION_HISTO"
      }
    },
    {
      "name": "FACT_RESERVATION",
      "zorder-columns": ["INTERNAL_ZORDER"],
      "latest": {
        "histo-table-name": "FACT_RESERVATION_HISTO"
      },
      "table-snowflake": {
        "cluster-by": ["date_trunc('WEEK', PNR_CREATION_DATE)"]
      }
    },
    {
      "name": "FACT_RESERVATION_HISTO",
      "zorder-columns": ["INTERNAL_ZORDER"],
      "mapping": {
        "description": {"description": "Contains information related to the PNR itself, accompanied by information on group bookings if any.", "granularity": "1 PNR"},
        "merge": {
          "key-columns": ["INTERNAL_ZORDER","RESERVATION_ID", "VERSION"], // "RESERVATION_ID" unicity contained in "INTERNAL_ZORDER" ? (to check with Martin)
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
            "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn",  "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "CPR_FEED_TYPE", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.cprFeedType"}]}},
          {"name": "ETAG", "fk": [{"schema": "DUMMY_S", "table":"FACT_DUMMY_HISTO", "column":"DUMMY_C"}], "column-type": "strColumn", "sources": {"blocks": [{"base": "$.etag"}]}},
          {"name": "DUMMY_FK_ID", "fk": [{"table":"DIM_DUMMY_ID"}], "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.etag"}]}},
          {"name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [
              {"base": "$.creation.pointOfSale.office.id"},
              {"base": "$.creation.pointOfSale.office.iataNumber"},
              {"base": "$.creation.pointOfSale.office.systemCode"},
              {"base": "$.creation.pointOfSale.office.agentType"},
              {"base": "$.creation.pointOfSale.login.cityCode"},
              {"base": "$.creation.pointOfSale.login.countryCode"},
              {"base": "$.creation.pointOfSale.login.numericSign"},
              {"base": "$.creation.pointOfSale.login.initials"},
              {"base": "$.creation.pointOfSale.login.dutyCode"}]}, "expr": "hashM({0})"},
          {"name": "SECONDARY_ID", "column-type": "binaryStrColumn",  "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.recordLocator"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "master-pit-table",
          "master" : {
            "pit-key": "RESERVATION_ID",
            "pit-version": "VERSION",
            "valid-from": "DATE_BEGIN",
            "valid-to": "DATE_END",
            "is-last": "IS_LAST_VERSION"
          }
        }},
      "table-snowflake": {
        "cluster-by": ["date_trunc('WEEK', DATE_BEGIN)"]
      }
    },
    {
      "name": "FACT_SECONDARY_HISTO",
      "mapping": {
        "merge": {
          "key-columns": ["INTERNAL_ZORDER", "SECONDARY_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
        "columns": [
          {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]},
            "meta": {"description": {"value": "Technical field required for database optimization.", "rule": "replace"}, "gdpr-zone": "orange"}},
          {"name": "SECONDARY_ID", "column-type": "binaryStrColumn",  "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "VERSION", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        }},
      "table-snowflake": {
        "cluster-by": ["date_trunc('WEEK', DATE_BEGIN)"]
      }
    },
    {
      "name": "DIM_POINT_OF_SALE",
      "mapping": {
        "description": {"description": "Lists the point of sales (office-level and user-level) used as PNR creator/owner/updater, or as creator of segment/service/seat bookings, keywords, loyalty requests, identity documents, ...", "granularity": "1 point of sale"},
        "merge": {
          "key-columns": ["POINT_OF_SALE_ID"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          { "name": "owner", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.owner"},
            {"office" : "$.office"}]}
          },
          { "name": "creation", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "lastModification", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.lastModification.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "queuing", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image"},
            {"office" : "$.queuingOffice"}]}
          },
          { "name": "assPnr", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.associatedPnrs[*].creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "transfer", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.flightTransfer.associatedOrder.creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "identityDoc", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.travelers[*].identityDocuments[*].creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "keyword", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.keywords[*].creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "seating", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.keywords[*].creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "travelDoc", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.ticketingReferences[*].documents[*].creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "air", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.products[?(@.subType == 'AIR')].airSegment.creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "loyServ", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.products[?(@.subType == 'SERVICE')].service[?(@.code == 'CLID' || @.code == 'FQTU')].creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          { "name": "loyReq", "rs": { "blocks": [
            {"pos": "$.mainResource.current.image.loyaltyRequests[*].creation.pointOfSale"},
            {"office" : "$.office"}]}
          },
          //@TODO: any more blocks from other parts of the model (e.g. Service, seating, loyalty requests)
        ],
        "columns": [
          { "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": { "root-specific": [
            {"rs-name": "owner", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
            {"rs-name": "creation", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
            {"rs-name": "lastModification", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
            {"rs-name": "queuing", "blocks": [
              {"office": "$.id"}
            ]},
            {"rs-name": "assPnr", "blocks": [
              {"office": "$.systemCode"}
            ]},
            {"rs-name": "transfer", "blocks": [
              {"office": "$.systemCode"}
            ]},
            {"rs-name": "identityDoc", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
            {"rs-name": "keyword", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
            {"rs-name": "seating", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
            {"rs-name": "travelDoc", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"}
            ]},
            {"rs-name": "air", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
            {"rs-name": "loySrv", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
            {"rs-name": "loyReq", "blocks": [
              {"office": "$.id"},
              {"office": "$.iataNumber"},
              {"office": "$.systemCode"},
              {"office": "$.agentType"},
              {"pos": "$.login.cityCode"},
              {"pos": "$.login.countryCode"},
              {"pos": "$.login.numericSign"},
              {"pos": "$.login.initials"},
              {"pos": "$.login.dutyCode"}
            ]},
          ]}, "expr": "hashM({0})",
            "meta": {"description": {"value": "Hash of the functional key (officeId-officeIATA-system-agentType-city-country-numSign-initials-duty)", "rule": "replace"}, "gdpr-zone": "green"}
          },
          { "name": "OFFICE_IDENTIFIER", "column-type": "strColumn","sources": { "blocks": [{"office": "$.id"}]}, "meta": {"gdpr-zone": "red"}},
          //@TODO: why do the login-fields have explicit rs-names and the office-fields not => fix all following fields in a consistent way to match all defined root sources
          { "name": "OFFICE_IATA_NUMBER","column-type": "strColumn", "sources":  { "blocks": [{"office": "$.iataNumber"}]}, "meta": {"gdpr-zone": "red"}},
          { "name": "OFFICE_SYSTEM_CODE", "column-type": "strColumn", "sources":  { "blocks": [{"office": "$.systemCode"}]}},
          { "name": "OFFICE_AGENT_TYPE","column-type": "strColumn","sources":  { "blocks": [{"office": "$.agentType"}]}},
          { "name": "LOGIN_CITY_CODE","column-type": "strColumn","sources": { "blocks": [{"pos": "$.login.cityCode"}]}},
          { "name": "LOGIN_COUNTRY_CODE","column-type": "strColumn","sources": { "blocks": [{"pos": "$.login.countryCode"}]}},
          { "name": "LOGIN_NUMERIC_SIGN", "column-type": "strColumn","sources": { "blocks": [{"pos": "$.login.numericSign"}]}, "meta": {"gdpr-zone": "red"}},
          { "name": "LOGIN_INITIALS", "column-type": "strColumn", "sources": { "blocks": [{"pos": "$.login.initials"}]}, "meta": {"gdpr-zone": "red"}},
          { "name": "LOGIN_DUTY_CODE", "column-type": "strColumn", "sources": { "blocks": [{"pos": "$.login.dutyCode"}]}},
        ],
        "pit": {
          "type": "no-pit-table"
        }
      }
    }
  ]
}