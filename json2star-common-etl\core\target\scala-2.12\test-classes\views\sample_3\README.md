Goal: integration test to verify the documentation generator with foreign keys

The mapping file is an example to define explictly foreign keys ("fk")

It contains the following tables:
 - FACT_RESERVATION_HISTO
   - this contains FK to the following 3 tables
   - in particular, the FK to FACT_A_HISTO and FACT_B_HISTO is composed by 2 columns
     - the column VERSION belongs to both FKs
 - DIM_POINT_OF_SALE
 - FACT_A_HISTO, FACT_A
 - FACT_B_HISTO, FACT_B
 - DIM_TABLE

Note: the full doc is verified as more foreign keys appears for the historical table