{"commitInfo":{"timestamp":1725526966630,"operation":"WRITE","operationParameters":{"mode":"ErrorIfExists","partitionBy":"[]"},"isolationLevel":"Serializable","isBlindAppend":true,"operationMetrics":{"numFiles":"1","numOutputRows":"1","numOutputBytes":"4341"},"engineInfo":"Apache-Spark/3.5.1 Delta-Lake/3.1.0","txnId":"e9d7edd2-89fa-4ce2-8b08-376427bd045b"}}
{"metaData":{"id":"5a2a3187-58c6-4452-bf6b-383d8261e08e","format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"timestamp\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"payload\",\"type\":\"binary\",\"nullable\":true,\"metadata\":{}},{\"name\":\"keys\",\"type\":{\"type\":\"map\",\"keyType\":\"string\",\"valueType\":\"string\",\"valueContainsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"replayContext\",\"type\":{\"type\":\"map\",\"keyType\":\"string\",\"valueType\":\"string\",\"valueContainsNull\":true},\"nullable\":true,\"metadata\":{}}]}","partitionColumns":[],"configuration":{},"createdTime":1725526966442}}
{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"add":{"path":"part-00000-ab18c1bd-10cd-4650-bab9-38eb85452f0d-c000.snappy.parquet","partitionValues":{},"size":4341,"modificationTime":1725526966621,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"timestamp\":1724414838890},\"maxValues\":{\"timestamp\":1724414838890},\"nullCount\":{\"timestamp\":0,\"payload\":0,\"keys\":0,\"replayContext\":0}}"}}
