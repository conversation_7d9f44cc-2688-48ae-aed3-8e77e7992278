-- MAGIC %md
-- MAGIC #Notebook Information
-- MAGIC This notebook contains **3** tests.
-- MAGIC
-- MAGIC Each cell starts by defining the values specific to the test. Then an INSERT INTO is run with a join between tables. For a test to pass, zero records are expected from the join.

-- COMMAND ----------

--The threshold value can be set individually for each validation test.
SET db.task = "skd-global-stats-checks.sql";
SET db.validation_database = ${valDatabase};
SET db.validation_table = ${valTableName};
SET db.now_datetime = current_timestamp();
SET db.table_fields = (domain, domain_version, customer, phase, test_time, task, covered_functional_case, status, nb_failed_records, nb_total_records, fail_ratio, result_details);

-- COMMAND ----------

--test 1
--test fails if the average number of legs per segment is greater than the legs_threshold
--set variables for this test
SET db.func_case = "Test 1 - Check the average number of legs per segment";
SET db.legs_threshold = 1.7;

WITH avg_legs AS (
  select avg(nbr_legs) avg_nb_legs from (
    select count(1) as nbr_legs from ${DB_NAME}.asso_flight_segment_leg asso
      group by asso.FLIGHT_SEGMENT_ID
  )
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select
  "${DOMAIN}",
  "${DOMAIN_VERSION}",
  "${CUSTOMER}",
  "${PHASE}",
  ${db.now_datetime},
  ${db.task},
  ${db.func_case},
  (avg_nb_legs <= ${db.legs_threshold}) as status,
  (CASE WHEN avg_nb_legs <= ${db.legs_threshold} THEN 0 else 1 end) as nb_failed_records,
  1 as nb_total_records,
  (CASE WHEN avg_nb_legs <= ${db.legs_threshold} THEN 0 else 1 end) as fail_ratio,
  concat("Average number of passengers : ", avg_nb_legs) as result_details
from avg_legs;

-- COMMAND ----------

--test 2
--test fails if a segment has more legs than the max_legs_threshold
--set variables for this test
SET db.func_case = "Test 2 - Check the number of legs per segment does not exceed threshold";
SET db.max_legs_threshold = 10;

WITH all_legs AS (select count(distinct FLIGHT_SEGMENT_ID) as tot_legs from ${DB_NAME}.asso_flight_segment_leg),
failed_legs AS (
  select count(1) as nb_max_legs from (
    select count(1) as max_legs from ${DB_NAME}.asso_flight_segment_leg asso
    group by asso.FLIGHT_SEGMENT_ID)
  where max_legs > ${db.max_legs_threshold}
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select
  "${DOMAIN}",
  "${DOMAIN_VERSION}",
  "${CUSTOMER}",
  "${PHASE}",
  ${db.now_datetime},
  ${db.task},
  ${db.func_case},
  (nb_max_legs = 0) as status,
  (CASE WHEN nb_max_legs = 0 THEN 0 else nb_max_legs end) as nb_failed_records,
  tot_legs as nb_total_records,
  nvl(nb_max_legs/tot_legs,0) as fail_ratio,
  concat("Number of segments with more legs than max threshold : ", nb_max_legs) as result_details
from all_legs, failed_legs;

-- COMMAND ----------

--test 3
--test fails if the average number of codeshare flights per segment is greater than the codeshare_threshold
--set variables for this test
SET db.func_case = "Test 3 - Check the average number of codeshare flights per segment";
SET db.codeshare_threshold = 6;

WITH avg_codeshare AS (
  select avg(nbr_codeshare) avg_nbr_codeshare from (
    select count(distinct partner_carrier, partner_flight_number) as nbr_codeshare from ${DB_NAME}.fact_codeshare_flight_segment
      where partner_carrier is not null
      group by flight_segment_id
  )
)
--run test and insert results into validation tracking table
insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select
  "${DOMAIN}",
  "${DOMAIN_VERSION}",
  "${CUSTOMER}",
  "${PHASE}",
  ${db.now_datetime},
  ${db.task},
  ${db.func_case},
  (avg_nbr_codeshare <= ${db.codeshare_threshold}) as status,
  (CASE WHEN avg_nbr_codeshare <= ${db.codeshare_threshold} THEN 0 else 1 end) as nb_failed_records,
  1 as nb_total_records,
  (CASE WHEN avg_nbr_codeshare <= ${db.codeshare_threshold} THEN 0 else 1 end) as fail_ratio,
  concat("Average number of codeshare flights per segment : ", avg_nbr_codeshare) as result_details
from avg_codeshare;