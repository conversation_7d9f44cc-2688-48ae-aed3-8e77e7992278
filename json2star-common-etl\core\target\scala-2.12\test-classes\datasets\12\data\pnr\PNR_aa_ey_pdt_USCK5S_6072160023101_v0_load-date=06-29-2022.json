{"correlatedResourcesCurrent": {"PNR-EMD": {"fromDomain": "PNR", "fromFullVersion": "0", "id": "USCK5S-2022-06-29", "isFullUpdate": true, "toDomain": "EMD", "version": "EMD-1"}, "PNR-TKT": {"fromDomain": "PNR", "fromFullVersion": "0", "id": "USCK5S-2022-06-29", "isFullUpdate": true, "toDomain": "TKT", "version": "TKT-1"}}, "mainResource": {"current": {"correlations": [{"name": "PNR-EMD", "relation": {"rel": "related"}}, {"name": "PNR-TKT", "relation": {"rel": "related"}}], "image": {"@type": "type.googleapis.com/com.amadeus.pulse.message.Pnr", "associatedPnrs": [{"associationType": "SPLIT", "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"systemCode": "1A"}}}, "direction": "PARENT", "reference": "USCLOE"}], "automatedProcesses": [{"applicableCarrierCode": "EY", "code": "OK", "dateTime": "2022-06-29T00:00:00", "id": "USCK5S-2022-06-29-OT-284", "isApplicableToInfants": false, "office": {"id": "XMYEY02AU"}, "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}, {"applicableCarrierCode": "EY", "code": "OK", "dateTime": "2022-06-29T00:00:00", "id": "USCK5S-2022-06-29-OT-285", "isApplicableToInfants": false, "office": {"id": "XMYEY02AU"}, "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "automated-process"}], "contacts": [{"email": {"address": "<EMAIL>"}, "id": "USCK5S-2022-06-29-OT-1", "purpose": ["STANDARD"], "travelerRefs": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}, {"id": "USCK5S-2022-06-29-OT-2", "phone": {"category": "PERSONAL", "deviceType": "MOBILE", "number": "+61 *********"}, "purpose": ["STANDARD"], "travelerRefs": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "contact"}], "creation": {"comment": "GST-EY/781808", "dateTime": "2022-06-29T08:10:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU", "initials": "PH", "numericSign": "0202"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "fareElements": [{"code": "FE", "id": "USCK5S-2022-06-29-OT-191", "text": "PAX NON ENDO/ REF", "type": "fare-element"}, {"code": "FE", "id": "USCK5S-2022-06-29-OT-193", "text": "PAX NON ENDO/ REF", "type": "fare-element"}, {"code": "FE", "id": "USCK5S-2022-06-29-OT-195", "text": "INF NON ENDO/ REF", "type": "fare-element"}, {"code": "FV", "id": "USCK5S-2022-06-29-OT-192", "text": "PAX EY", "type": "fare-element"}, {"code": "FV", "id": "USCK5S-2022-06-29-OT-194", "text": "PAX EY", "type": "fare-element"}, {"code": "FV", "id": "USCK5S-2022-06-29-OT-196", "text": "INF EY", "type": "fare-element"}], "flightItinerary": [{"flights": [{"connectedFlights": {"connectionTimeDuration": "-21H-25M", "connectionType": "INTERACTIVE_MARRIAGE", "flightSegments": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}, {"flights": [{"connectedFlights": {"connectionTimeDuration": "1H55M", "connectionType": "INTERACTIVE_MARRIAGE", "flightSegments": [{"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}, {"flights": [{"connectedFlights": {"connectionTimeDuration": "-21H-25M", "connectionType": "CONNECTION", "flightSegments": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}]}}, {"connectedFlights": {"connectionTimeDuration": "1H5M", "connectionType": "CONNECTION", "flightSegments": [{"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}, {"flights": [{"connectedFlights": {"connectionTimeDuration": "1H10M", "connectionType": "CONNECTION", "flightSegments": [{"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}]}}, {"connectedFlights": {"connectionTimeDuration": "1H55M", "connectionType": "CONNECTION", "flightSegments": [{"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}]}}], "type": "CONNECTION"}], "id": "USCK5S-2022-06-29", "lastModification": {"comment": "GST-EY/781808", "dateTime": "2022-06-29T08:10:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU", "dutyCode": "GS", "initials": "PH", "numericSign": "0202"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "nip": 2, "owner": {"login": {"cityCode": "XMY", "countryCode": "AU", "dutyCode": "GS", "initials": "PH"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}, "paymentMethods": [{"code": "FP", "formsOfPayment": [{"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD3167.05/AAPS1OK"}], "id": "USCK5S-2022-06-29-OT-200", "isInfant": false, "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD356.00/AAPS1OK"}], "id": "USCK5S-2022-06-29-OT-202", "isInfant": true, "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}, {"code": "FP", "formsOfPayment": [{"code": "CC", "fopIndicator": "NEW", "freeText": "CCCA512345XXXXXX0008/0139*CV/AUD2406.05/AAPS1OK"}], "id": "USCK5S-2022-06-29-OT-204", "isInfant": false, "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "travelers": [{"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "payment-method"}], "products": [{"id": "USCK5S-2022-06-29-OT-26", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "CHLD", "creation": {"dateTime": "2022-06-29T07:52:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "18JAN16"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-162", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-163", "products": [{"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-189", "products": [{"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-190", "products": [{"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "INFT", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 1, "serviceProvider": {"code": "EY"}, "status": "HK", "subType": "SPECIAL_SERVICE_REQUEST", "text": "GST/PATMISS 15AUG21"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-219", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS ADV PSGR MOBILE AND/OR EMAIL AS SSR CTCM/CTCE"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-232", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS CHECKIN 72HRS BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-233", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "MAAS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 2, "serviceProvider": {"code": "EY"}, "status": "NO", "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-234", "products": [{"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "MAAS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 2, "serviceProvider": {"code": "EY"}, "status": "NO", "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-237", "products": [{"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "MAAS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 2, "serviceProvider": {"code": "EY"}, "status": "NO", "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-238", "products": [{"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "MAAS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "nip": 2, "serviceProvider": {"code": "EY"}, "status": "NO", "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-251", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0600/31JUL/UTC"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-264", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "MAAS W2 KK6 MUCQYG6261Y31JUL.YY PLS CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-277", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "creation": {"dateTime": "2022-06-29T07:49:00Z", "pointOfSale": {"login": {"initials": "PH", "numericSign": "0202"}, "office": {"id": "XMYEY02AU"}}}, "isChargeable": false, "serviceProvider": {"code": "EY"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0800/07AUG/UTC"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-294", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS ADV PSGR MOBILE AND/OR EMAIL AS SSR CTCM/CTCE"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-295", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "PLS CHECKIN 72HRS BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-296", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "AUTO XX IF SSR TKNE NOT RCVD BY 0600/02JUL/UTC"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"id": "USCK5S-2022-06-29-OT-298", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "service": {"code": "OTHS", "isChargeable": false, "serviceProvider": {"code": "1A"}, "subType": "SPECIAL_SERVICE_REQUEST", "text": "YY <PERSON><PERSON> CHECKIN BEFORE DEPARTURE ON RAIL-CHECKIN.COM"}, "subType": "SERVICE", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "77W"}, "arrival": {"dateTime": "2022-07-01T19:55:00Z", "iataCode": "AUH", "localDateTime": "2022-07-01T23:55:00", "terminal": "3"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008A0B900002334", "id": "USCK5S-2022-06-29-OT-144", "traveler": {"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008A0B900002335", "id": "USCK5S-2022-06-29-OT-145", "traveler": {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-01T05:15:00Z", "iataCode": "SYD", "localDateTime": "2022-07-01T15:15:00", "terminal": "1"}, "id": "2022-07-01-SYD-AUH", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "Q", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "451"}, "id": "EY-451-2022-07-01-SYD-AUH", "isOpenNumber": false, "isPrime": true}}, "id": "USCK5S-2022-06-29-ST-17", "products": [{"id": "USCK5S-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-162", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-233", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "781"}, "arrival": {"dateTime": "2022-07-02T04:55:00Z", "iataCode": "MUC", "localDateTime": "2022-07-02T06:55:00", "terminal": "2"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"associatedTickets": [{"associationStatus": "ASSOCIATED", "coupons": [{"number": 2}], "documentNumber": "6072160023087", "documentType": "ETICKET", "primaryDocumentNumber": "6072160023087"}], "distributionId": "5008A0B90000233A", "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "USCK5S-2022-06-29-OT-310", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "USCK5S-2022-06-29-OT-312", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "CTN", "statusType": "TCS"}, {"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"associatedTickets": [{"associationStatus": "ASSOCIATED", "coupons": [{"number": 2}], "documentNumber": "6072160023089", "documentType": "ETICKET", "primaryDocumentNumber": "6072160023089"}], "distributionId": "5008A0B90000233B", "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "USCK5S-2022-06-29-OT-315", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "USCK5S-2022-06-29-OT-317", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "CTN", "statusType": "TCS"}, {"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"activeIdentityDocument": {"passengerTypeCode": "INF"}, "associatedTickets": [{"associationStatus": "ASSOCIATED", "coupons": [{"number": 2}], "documentNumber": "6072160023095", "documentType": "ETICKET", "primaryDocumentNumber": "6072160023095"}], "flightSegment": {"arrival": {"iataCode": "MUC"}, "departure": {"iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00"}, "isInformational": false, "marketing": {"bookingClass": {"code": "Q"}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "isOpenNumber": false}}, "id": "USCK5S-2022-06-29-OT-330", "isGoShow": false, "isNoRec": false, "legDeliveries": [{"acceptance": {"isForceAccepted": false, "isFrozen": false, "status": "NOT_ACCEPTED"}, "arrival": {"iataCode": "MUC"}, "boarding": {"boardingPassPrint": {"status": "NOT_PRINTED"}, "boardingStatus": "NOT_BOARDED"}, "departure": {"iataCode": "AUH"}, "handlingCarrier": "EY", "hasBags": false, "id": "USCK5S-2022-06-29-OT-332", "onload": {"cabinCode": "Y"}, "regrade": {"regradeType": "NO_REGRADE"}, "regulatoryChecks": [{"regulatoryProgram": {"name": "AQQ"}, "statuses": [{"statusCode": "X", "statusType": "AQQ"}]}, {"statuses": [{"statusCode": "CTN", "statusType": "TCS"}, {"statusCode": "P", "statusType": "APS"}]}], "type": "leg-delivery"}], "traveler": {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-01T22:30:00Z", "iataCode": "AUH", "localDateTime": "2022-07-02T02:30:00", "terminal": "3"}, "id": "2022-07-02-AUH-MUC", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "Q", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "5"}, "id": "EY-5-2022-07-02-AUH-MUC", "isOpenNumber": false, "isPrime": true}}, "id": "USCK5S-2022-06-29-ST-18", "products": [{"id": "USCK5S-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-163", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-234", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "TRN"}, "arrival": {"dateTime": "2022-07-02T07:00:00Z", "iataCode": "QYG", "localDateTime": "2022-07-02T09:00:00"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-02T06:00:00Z", "iataCode": "MUC", "localDateTime": "2022-07-02T08:00:00", "terminal": "1"}, "id": "2022-07-02-MUC-QYG", "isInformational": false, "marketing": {"bookingClass": {"code": "Y"}, "flightDesignator": {"carrierCode": "W2", "flightNumber": "6261"}, "id": "W2-6261-2022-07-02-MUC-QYG", "isOpenNumber": false}}, "id": "USCK5S-2022-06-29-ST-19", "products": [{"id": "USCK5S-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "TRN"}, "arrival": {"dateTime": "2022-07-09T09:00:00Z", "iataCode": "MUC", "localDateTime": "2022-07-09T11:00:00", "terminal": "1"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "departure": {"dateTime": "2022-07-09T08:00:00Z", "iataCode": "QYG", "localDateTime": "2022-07-09T10:00:00"}, "id": "2022-07-09-QYG-MUC", "isInformational": false, "marketing": {"bookingClass": {"code": "Y"}, "flightDesignator": {"carrierCode": "W2", "flightNumber": "6245"}, "id": "W2-6245-2022-07-09-QYG-MUC", "isOpenNumber": false}}, "id": "USCK5S-2022-06-29-ST-20", "products": [{"id": "USCK5S-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "781"}, "arrival": {"dateTime": "2022-07-09T15:55:00Z", "iataCode": "AUH", "localDateTime": "2022-07-09T19:55:00", "terminal": "3"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008A0B9000022BE", "id": "USCK5S-2022-06-29-OT-176", "traveler": {"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008A0B9000022BF", "id": "USCK5S-2022-06-29-OT-177", "traveler": {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-09T10:10:00Z", "iataCode": "MUC", "localDateTime": "2022-07-09T12:10:00", "terminal": "2"}, "id": "2022-07-09-MUC-AUH", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "Q", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "6"}, "id": "EY-6-2022-07-09-MUC-AUH", "isOpenNumber": false, "isPrime": true}}, "id": "USCK5S-2022-06-29-ST-21", "products": [{"id": "USCK5S-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-189", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-237", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}, {"airSegment": {"aircraft": {"aircraftType": "789"}, "arrival": {"dateTime": "2022-07-10T07:05:00Z", "iataCode": "MEL", "localDateTime": "2022-07-10T17:05:00", "terminal": "2"}, "bookingStatusCode": "HK", "creation": {"dateTime": "2022-06-29T08:04:00Z", "pointOfSale": {"login": {"cityCode": "XMY", "countryCode": "AU"}, "office": {"agentType": "AIRLINE", "iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "00"}}}, "deliveries": [{"distributionId": "5008A0B9000023F0", "id": "USCK5S-2022-06-29-OT-182", "traveler": {"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}, {"distributionId": "5008A0B9000023F1", "id": "USCK5S-2022-06-29-OT-183", "traveler": {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "segment-delivery"}], "departure": {"dateTime": "2022-07-09T17:50:00Z", "iataCode": "AUH", "localDateTime": "2022-07-09T21:50:00", "terminal": "3"}, "id": "2022-07-09-AUH-MEL", "isDominantInMarriage": true, "isInformational": false, "marketing": {"bookingClass": {"cabin": {"code": "Y"}, "code": "Q", "levelOfService": "ECONOMY", "subClass": {"code": 0, "pointOfSale": {"login": {"countryCode": "AU"}, "office": {"systemCode": "EY"}}}}, "flightDesignator": {"carrierCode": "EY", "flightNumber": "460"}, "id": "EY-460-2022-07-09-AUH-MEL", "isOpenNumber": false, "isPrime": true}}, "id": "USCK5S-2022-06-29-ST-22", "products": [{"id": "USCK5S-2022-06-29-OT-26", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-190", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-219", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-232", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-238", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-251", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-264", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-277", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-294", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-295", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-296", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-OT-298", "ref": "processedPnr.products", "type": "product"}], "subType": "AIR", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "product"}], "purgeDate": {"date": "2022-07-13"}, "queuingOffice": {"id": "XMYEY02AU"}, "quotations": [{"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-1-17", "product": {"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-1-18", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-1-19", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-1-20", "product": {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-1-21", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AU", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-1-22", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "USCK5S-2022-06-29-QT-1", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "3167.05", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "2880.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "3167.05", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "60.00", "category": "NEW", "code": "AU", "currency": "AUD", "nature": "DP"}, {"amount": "55.55", "category": "NEW", "code": "WY", "currency": "AUD", "nature": "DE"}, {"amount": "27.80", "category": "NEW", "code": "F6", "currency": "AUD", "nature": "TO"}, {"amount": "4.00", "category": "NEW", "code": "ZR", "currency": "AUD", "nature": "AP"}, {"amount": "89.00", "category": "NEW", "code": "OY", "currency": "AUD", "nature": "CB"}, {"amount": "13.30", "category": "NEW", "code": "DE", "currency": "AUD", "nature": "SE"}, {"amount": "37.40", "category": "NEW", "code": "RA", "currency": "AUD", "nature": "EB"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG1056.67W2 X/MUC EY X/AUH EY MEL933.90Q SYDMEL3.00NUC2043.57END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-2-17", "product": {"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-2-18", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-2-19", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-2-20", "product": {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-2-21", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 35}}, "fareBasis": {"fareBasisCode": "C2AUCH", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-2-22", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "USCK5S-2022-06-29-QT-2", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "2406.05", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "2179.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "2406.05", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}], "taxes": [{"amount": "55.55", "category": "NEW", "code": "WY", "currency": "AUD", "nature": "DE"}, {"amount": "27.80", "category": "NEW", "code": "F6", "currency": "AUD", "nature": "TO"}, {"amount": "4.00", "category": "NEW", "code": "ZR", "currency": "AUD", "nature": "AP"}, {"amount": "89.00", "category": "NEW", "code": "OY", "currency": "AUD", "nature": "CB"}, {"amount": "13.30", "category": "NEW", "code": "DE", "currency": "AUD", "nature": "SE"}, {"amount": "37.40", "category": "NEW", "code": "RA", "currency": "AUD", "nature": "EB"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG792.50W2 X/MUC EY X/AUH EY MEL700.42Q SYDMEL3.00NUC1545.92END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}, {"coupons": [{"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-3-17", "product": {"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-01", "notValidBeforeDate": "2022-07-01"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-3-18", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QHW"}, "id": "USCK5S-2022-06-29-QT-3-19", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-02", "notValidBeforeDate": "2022-07-02"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-3-20", "product": {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-3-21", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}, {"baggageAllowance": {"weight": {"unit": "KILOGRAMS", "value": 10}}, "fareBasis": {"fareBasisCode": "C2AUIN", "primaryCode": "QKW"}, "id": "USCK5S-2022-06-29-QT-3-22", "isFromConnection": true, "product": {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}, "validityDates": {"notValidAfterDate": "2022-07-09", "notValidBeforeDate": "2022-07-09"}}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "destinationCityIataCode": "MEL", "documentType": "TICKET", "id": "USCK5S-2022-06-29-QT-3", "isManual": false, "issuanceType": "FIRST_ISSUE", "lastModification": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"id": "XMYEY02AU"}}}, "originCityIataCode": "SYD", "price": {"detailedPrices": [{"amount": "356.00", "currency": "AUD", "elementaryPriceType": "TOTAL"}, {"amount": "356.00", "currency": "AUD", "elementaryPriceType": "BASE_FARE"}, {"amount": "356.00", "currency": "AUD", "elementaryPriceType": "GRAND_TOTAL"}]}, "pricingConditions": {"fareCalculation": {"isManual": false, "pricingIndicator": "0", "text": "SYD EY X/AUH Q50.00EY X/MUC W2 QYG105.66W2 X/MUC EY X/AUH EY MEL93.39Q SYDMEL3.00NUC252.05END ROE1.409137"}, "isInternationalSale": true}, "subType": "TST", "travelers": [{"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "quotation-record"}], "reference": "USCK5S", "remarks": [{"content": "PNR WITH RAIL SEGMENT", "id": "USCK5S-2022-06-29-OT-9", "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "subType": "RX", "travelers": [{"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}], "type": "remark"}], "sourcePublicationId": "2269", "ticketingReferences": [{"documents": [{"coupons": [{"product": {"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "3167.05"}, "primaryDocumentNumber": "6072160023087", "status": "ISSUED"}], "id": "USCK5S-2022-06-29-OT-288", "isInfant": false, "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "2406.05"}, "primaryDocumentNumber": "6072160023089", "status": "ISSUED"}], "id": "USCK5S-2022-06-29-OT-289", "isInfant": false, "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "USCK5S-2022-06-29-PT-5", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}, {"documents": [{"coupons": [{"product": {"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 1}, {"product": {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 2}, {"product": {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 3}, {"product": {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 4}, {"product": {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 5}, {"product": {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}, "sequenceNumber": 6}], "creation": {"dateTime": "2022-06-29T00:00:00Z", "pointOfSale": {"office": {"iataNumber": "02090082", "id": "XMYEY02AU", "systemCode": "EY"}}}, "documentType": "ETICKET", "numberOfBooklets": 1, "price": {"currency": "AUD", "total": "356.00"}, "primaryDocumentNumber": "6072160023095", "status": "ISSUED"}], "id": "USCK5S-2022-06-29-OT-292", "isInfant": true, "products": [{"id": "USCK5S-2022-06-29-ST-17", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-18", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-19", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-20", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-21", "ref": "processedPnr.products", "type": "product"}, {"id": "USCK5S-2022-06-29-ST-22", "ref": "processedPnr.products", "type": "product"}], "referenceTypeCode": "FA", "traveler": {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "type": "ticketing-reference"}], "travelers": [{"contacts": [{"id": "USCK5S-2022-06-29-OT-1", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "USCK5S-2022-06-29-OT-2", "ref": "processedPnr.contacts", "type": "contact"}], "gender": "MALE", "id": "USCK5S-2022-06-29-PT-3", "infant": {"id": "USCK5S-2022-06-29-PT-3-INF", "ref": "processedPnr.travelers", "type": "stakeholder"}, "names": [{"firstName": "CHARLES", "lastName": "GST", "title": "MR"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "USCK5S-2022-06-29-OT-309", "passengerDetails": {"gender": "MALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "510860B8000219E2"}, "passengerTypeCode": "ADT", "type": "stakeholder"}, {"adult": {"id": "USCK5S-2022-06-29-PT-3", "ref": "processedPnr.travelers", "type": "stakeholder"}, "dateOfBirth": "2021-08-15", "gender": "FEMALE", "id": "USCK5S-2022-06-29-PT-3-INF", "names": [{"firstName": "PAT", "lastName": "GST", "title": "MISS"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "USCK5S-2022-06-29-OT-329", "passengerDetails": {"dateOfBirth": "2021-08-15", "gender": "FEMALE"}, "type": "passenger-delivery"}]}, "passengerTypeCode": "INF", "type": "stakeholder"}, {"contacts": [{"id": "USCK5S-2022-06-29-OT-1", "ref": "processedPnr.contacts", "type": "contact"}, {"id": "USCK5S-2022-06-29-OT-2", "ref": "processedPnr.contacts", "type": "contact"}], "dateOfBirth": "2016-01-18", "gender": "FEMALE", "id": "USCK5S-2022-06-29-PT-5", "names": [{"firstName": "QUEEN", "lastName": "GST", "title": "MISS"}], "passenger": {"passengerDeliveries": [{"deliveryAirlineCode": "EY", "id": "USCK5S-2022-06-29-OT-314", "passengerDetails": {"dateOfBirth": "2016-01-18", "gender": "FEMALE", "passengerTypeCode": "ADT"}, "type": "passenger-delivery"}], "uniqueIdentifier": "510860B8000219E3"}, "passengerTypeCode": "CHD", "type": "stakeholder"}], "type": "pnr", "version": "0"}}, "id": "USCK5S-2022-06-29", "type": "com.amadeus.pulse.message.Pnr"}}