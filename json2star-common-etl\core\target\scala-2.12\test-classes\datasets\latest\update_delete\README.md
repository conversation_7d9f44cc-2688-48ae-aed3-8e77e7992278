# Readme

Test data for the Latest table computation.

## batch 1

There is only v1 for PNR OP27AX-2022-05-22.
The latest tables are empty at the beginning, so it's a nominal case where we do an insert all, keeping only the last rows.

## batch 2

Versions v2 and v3 arrive for PNR OP27AX-2022-05-22.

Notes:
- in v3 the segment OP27AX-2022-05-22-ST-11 does not exist anymore (manually removed in the data for testing), 
  so the corresponding line in the FACT_AIR_SEGMENT_PAX gets removed (delete use case)
- all the other pax/segments are still present, with a higher version and updated content (update use case)
