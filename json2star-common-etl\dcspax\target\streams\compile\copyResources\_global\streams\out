[debug] Copy resource mappings: 
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\dcspax\src\main\resources\DCSPAX-functional-checks.scala,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\dcspax\target\scala-2.12\classes\DCSPAX-functional-checks.scala)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\dcspax\src\main\resources\DCSPAX-global-stats-checks.sql,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\dcspax\target\scala-2.12\classes\DCSPAX-global-stats-checks.sql)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\dcspax\src\main\resources\dcspax.conf,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\dcspax\target\scala-2.12\classes\dcspax.conf)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\dcspax\src\main\resources\dcspax.yaml,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\dcspax\target\scala-2.12\classes\dcspax.yaml)
