[debug] Copy resource mappings: 
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\inv\src\main\resources\INV-functional-checks.scala,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\inv\target\scala-2.12\classes\INV-functional-checks.scala)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\inv\src\main\resources\INV-global-stats-checks.sql,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\inv\target\scala-2.12\classes\INV-global-stats-checks.sql)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\inv\src\main\resources\inv.conf,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\inv\target\scala-2.12\classes\inv.conf)
[debug] 	(C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\inv\src\main\resources\inv.yaml,C:\Users\<USER>\coderepos\BDP-scala-upgrade\json2star-common-etl\inv\target\scala-2.12\classes\inv.yaml)
