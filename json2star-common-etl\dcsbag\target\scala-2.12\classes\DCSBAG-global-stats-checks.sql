
--The threshold value can be set individually for each validation test.
SET db.task = "dcsbag-global-checks.sql";
SET db.validation_database = ${valDatabase};
SET db.validation_table = ${valTableName};
SET db.now_datetime = current_timestamp();
SET db.table_fields = (domain, domain_version, customer, phase, test_time, task, covered_functional_case, status, nb_failed_records, nb_total_records, fail_ratio, result_details);


-- COMMAND ----------

-- Test 1: "Average number of bags per responsible_passenger"
--set variables for this test
SET db.func_case = "Test 1 - Average number of bags per responsible_Passenger";
SET db.threshold_max = 3;

WITH total_records AS (
  SELECT COUNT(*) as total
  FROM ${DB_NAME}.fact_bag_group
),
avg_bags AS (
  select avg(checked_bags_count) as nb_avg_bags
  from ${DB_NAME}.fact_bag_group
)

insert into ${db.validation_database}.${db.validation_table} ${db.table_fields}
select
  "${DOMAIN}",
  "${DOMAIN_VERSION}",
  "${CUSTOMER}",
  "${PHASE}",
  ${db.now_datetime},
  ${db.task},
  ${db.func_case},
  (nb_avg_bags < ${db.threshold_max}) as status,
   null as nb_failed_records,
   total_records.total as nb_total_records,

 null as fail_ratio,
  concat("Average number of bags per passenger : ", nb_avg_bags) as result_details
from
 total_records,
  avg_bags;

-- COMMAND ----------


