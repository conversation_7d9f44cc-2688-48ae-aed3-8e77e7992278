# Integration test for source correlation tables

See the `README.md` in `batch1` and `batch2` folders for the details of the various use cases covered.

The mapping file is the same for all use cases.

---------------------------------------
SCENARIO - default-update - OK

`batch1` has
- 1 PNR with 1 PAX travelling on 2 segments
tables have
- FACT_PASSENGER_HISTO has 1 line with is_last = true
- INTERNAL_ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO has 2 lines with is_last = true
- ASSO_AIR_SEGMENT_PAX_SEGMENT_DELIVERY_HISTO has 2 lines with is_last = true


`batch2` has
- 1 PNR with 2 PAX travelling on 2 segments
- for 1 known PAX it is an update --
- for 1 new PAX it is a new record with 2 versions
