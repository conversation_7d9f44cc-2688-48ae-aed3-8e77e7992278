CREATE TABLE IF NOT EXISTS MY_DB.ASSO_COUPON_SERVICE_DELIVERY_HISTO (
	COUPON_ID STRING NOT NULL,
	SERVICE_DELIVERY_ID STRING NOT NULL,
	REFERENCE_KEY_TRAVEL_DOCUMENT STRING,
	REFERENCE_KEY_COUPON STRING,
	REFERENCE_KEY_PASSENGER STRING,
	REFERENCE_KEY_SERVICE_DELIVERY STRING,
	VERSION_TRAVEL_DOCUMENT STRING,
	VERSION_PASSENGER STRING,
	DATE_BEGIN TIMESTAMP,
	DATE_E<PERSON> TIMESTAMP,
	IS_LAST_VERSION BOOLEAN
)
USING DELTA PARTITIONED BY(IS_LAST_VERSION)
TBLPROPERTIES(delta.autoOptimize.optimizeWrite = true, delta.autoOptimize.autoCompact = true, delta.enableChangeDataFeed = true);

CREATE VIEW IF NOT EXISTS MY_DB.ASSO_COUPON_SERVICE_DELIVERY
AS SELECT COUPON_ID,SERVICE_DELIVERY_ID,REFERENCE_KEY_TRAVEL_DOCUMENT,REFERENCE_KEY_COUPON,REFERENCE_KEY_PASSENGER,REFERENCE_KEY_SERVICE_DELIVERY,VERSION_TRAVEL_DOCUMENT,VERSION_PASSENGER FROM ASSO_COUPON_SERVICE_DELIVERY_HISTO WHERE IS_LAST_VERSION=true
;