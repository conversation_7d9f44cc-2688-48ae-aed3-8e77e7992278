model-conf-file = /some/file

stream = {
  sink = {
    checkpoint-location = "/a/path"
    trigger = "once"
    delta-options = {}
  }
}
common = {
  domain = "DOMAIN"
  output-database = "input_PartialProcessingSpec"
  domain-version = "VERSION"
  output-path = "/output/path"
  shard = "CUSTOMER"
}
cloud-files-conf = {}
tables-selectors = [ "latest_selector" ]

partial-reprocessing-params = {
  previous-version = "PREVIOUS_VERSION"
  tables-to-reprocess = ["FACT_TABLE1", "FACT_TABLE1_HISTO"]
}