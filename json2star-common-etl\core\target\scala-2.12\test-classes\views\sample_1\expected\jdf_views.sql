CREATE VIEW FACT_PASSENGER_DUMMY AS SELECT INTERNAL_ZORDER,PASSENGER_ID,REFERENCE_KEY,CPR_FEED_TYPE,ETAG,GROUP_NAME,IS_MASTER_RECORD,IS_SAME_PHYSICAL_CUSTOMER,IS_SYSTEM_MARKED_SPC,TYPE,BIRTH_DATE,BIRTH_PLACE,PAX_TYPE,<PERSON>ND<PERSON>,NATIONALITY,SPECIAL_SEAT,FIRST_NAME,LAST_NAME,TITLE,RESIDENCE_COUNTRY,AGE,STAFF_CATEGORY,STAFF_COMPANY_CODE,STAFF_COMPANY_NAME,STAFF_ID,STAFF_BOOKING_TYPE,STAFF_JOINING_DATE,STAFF_RELATIONSHIP,STAFF_RETIREMENT_DATE,STAFF_TRANSFER_DAYS,STAFF_TRANSFERS_DURING_DAYS,RECORD_LOCATOR,VERSION,INTERNAL_DATE_BEGIN,LOAD_DATE FROM FACT_PASSENGER_HISTO WHERE IS_LAST_VERSION=true;
CREATE VIEW FACT_PASSENGER AS SELECT INTERNAL_ZORDER,PASSENGER_ID,REFERENCE_KEY,CPR_FEED_TYPE,ETAG,GROUP_NAME,IS_MASTER_RECORD,IS_SAME_PHYSICAL_CUSTOMER,IS_SYSTEM_MARKED_SPC,TYPE,BIRTH_DATE,BIRTH_PLACE,PAX_TYPE,GENDER,NATIONALITY,SPECIAL_SEAT,FIRST_NAME,LAST_NAME,TITLE,RESIDENCE_COUNTRY,AGE,STAFF_CATEGORY,STAFF_COMPANY_CODE,STAFF_COMPANY_NAME,STAFF_ID,STAFF_BOOKING_TYPE,STAFF_JOINING_DATE,STAFF_RELATIONSHIP,STAFF_RETIREMENT_DATE,STAFF_TRANSFER_DAYS,STAFF_TRANSFERS_DURING_DAYS,RECORD_LOCATOR,VERSION,INTERNAL_DATE_BEGIN,LOAD_DATE FROM FACT_PASSENGER_HISTO WHERE IS_LAST_VERSION=true;
