model-conf-file = views/sample_1/mapping.conf
stream = {
  sink = {
    checkpoint-location = "/a/path"
    trigger = "once"
    delta-options = {}
  }
}
common = {
  domain = "DOMAIN"
  output-database = "a_database"
  domain-version = "[VERSION]"
  output-path = "/output/path"
  shard = "[SHARD]"
}
cloud-files-conf = {}
tables-selectors = [ "latest_selector" ]

snowflake-params {
  stream {
    sink {
      checkpoint-location = "target/test/resources/checkpoints/snowflake"
      trigger = "availablenow"
      delta-options = {}
    }
  }

  azure-conf {
    secret-scope = keyvault
  }

  snowflake-conf {
    url = {type = clear, value = snowflakecomputing.com}
    user = {type = clear, value = ROBOTIC_DEV}
    pem_private_key = {type = secret, secret-name = local}
    role = {type = clear, value = APP_OWNER}
    database = {type = clear, value = my_db}
    schema = {type = clear, value = my_schema}
    warehouse = {type = clear, value = DIHDLK_WH_XS}
  }

}