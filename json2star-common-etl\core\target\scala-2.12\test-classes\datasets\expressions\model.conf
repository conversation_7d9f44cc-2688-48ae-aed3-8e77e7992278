{
  "tables": [
    {
      "name": "FACT_TRANSFORMATION",
     "mapping" : {
      "merge": {
        "key-columns": ["TRANSFORMATION_ID", "VERSION"],
        "if-dupe-take-higher": ["LOAD_DATE"]
      },
      "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
      "columns": [
        {"name": "TRANSFORMATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "SINGLE_HASHED", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
        {"name": "MULTI_HASHED", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reference"}, {"base": "$.id"}]}, "expr": "hashL({0})"},
        {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
        {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
        {"name": "MULTI_FIELD", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reference"}, {"base": "$.id"}]}},
        {"name": "MULTI_FIELD_WS", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reference"}, {"base": "$.id"}]},
          "sep": "/"},
        {"name": "SINGLE_FIELD_EXPR1", "column-type": "booleanColumn", "sources": {},
          "expr": "isnull({1})"},
        {"name": "SINGLE_FIELD_EXPR2", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]},
          "expr": "substring({1}, 1, 3)"},
        {"name": "MULTI_FIELD_EXPR1", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"base": "$.whatevernull"}]}, "expr": "coalesce({1},{2})"},

        {"name": "MULTI_FIELD_EXPR2", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reference"}, {"base": "$.id"}]},
          "expr": "concat(substring({1}, 1, 2), '.', substring({2}, 8, 11))"},
        {"name": "MULTI_FIELD_EXPR3", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reference"}, {"base": "$.whatevernull"}]},
          "expr": "concat(substring({1}, 1, 2), '.', substring({2}, 8, 11))"},
        {"name": "MULTI_FIELD_EXPR4", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}, {"base": "$.whatevernull"}]},
          "expr": "HashM({0})"},
        // @Mauricio change these if needed
        {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
        {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
        {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
        {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        {"name": "IS_LAST_VERSION_GENERATED", "column-type": "strColumn", "sources": {}, "post-expr": "substring(IS_LAST_VERSION, 0, 1)"}
        {"name": "DATE_END_GENERATED", "column-type": "strColumn", "sources": {}, "post-expr": "substring(DATE_END, 0, 10)"}
      ],
      "pit": {
        "type": "master-pit-table",
        "master": {
          pit-key: "TRANSFORMATION_ID",
          pit-version: "VERSION",
          valid-from: "DATE_BEGIN",
          valid-to: "DATE_END",
          is-last: "IS_LAST_VERSION"
        }
      }}
    }
  ]
}