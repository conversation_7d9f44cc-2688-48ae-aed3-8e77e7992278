// MAGIC %md
// MAGIC #Notebook Information
// MAGIC This notebook contains functional checks for PNRJ2S

// COMMAND ----------

import java.time.{OffsetDateTime, ZoneId, ZonedDateTime}
import java.time.format.DateTimeFormatter
import scala.collection.mutable.ListBuffer
import com.amadeus.airbi.json2star.common.validation.config.ValidationConf
import com.databricks.dbutils_v1.DBUtilsHolder.dbutils

// COMMAND ----------

val appConfigFile = dbutils.widgets.get("appConfigFile")
val valDatabase = dbutils.widgets.get("valDatabase")
val valTableName = dbutils.widgets.get("valTableName")
val daysBack = dbutils.widgets.get("daysBack")
val phase = dbutils.widgets.get("phase")
val inputFeed = try {
  dbutils.widgets.get("inputFeed")
} catch {
  case _: Exception => ""
}

val vConfig = ValidationConf.apply(Array(appConfigFile, valDatabase, valTableName, daysBack, "FUNCTIONAL_CASES", phase, inputFeed))

val domain = vConfig.appConfig.common.domain
val domain_version = vConfig.appConfig.common.domainVersion
val customer = vConfig.appConfig.common.shard
val dbName = vConfig.appConfig.common.outputDatabase

val currentTimestamp = OffsetDateTime.now().toString
val task = dbutils.notebook().getContext().notebookPath.get.split("/").last

val date_formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
val minTime = date_formatter format ZonedDateTime.now(ZoneId.of("UTC")).minusDays(daysBack.toInt)

// COMMAND ----------

case class TestRecord(domain : String, domain_version : String, customer : String, phase : String, test_time : String, task : String, covered_functional_case : String, status : Boolean, nb_failed_records : Long, nb_total_records : Long, fail_ratio : Float, result_details : String)
var testRecords : ListBuffer[TestRecord] = ListBuffer()

// COMMAND ----------

// MAGIC %md
// MAGIC ##begin tests

// COMMAND ----------

// MAGIC %md
// MAGIC ###test1

// COMMAND ----------

// no PNR without owner point of sales

val totalCount = spark.sql(s"""
  select * from $dbName.fact_reservation where load_date > to_date($minTime)
""").count()

val failCount = spark.sql(s"""
  select *
  from $dbName.fact_reservation res
    left outer join $dbName.dim_point_of_sale pos on pos.point_of_sale_id = res.owner_point_of_sale_id
  where pos.point_of_sale_id is null
    and res.load_date > to_date($minTime)
""").count()

val percentFail = failCount.toFloat/totalCount

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 1 : Percent of reservations without owner point of sale",
  failCount == 0,
  failCount,
  totalCount,
  percentFail,
  if ( failCount > 0) f"Percent of reservations without owner point of sale : ${percentFail*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ###test2

// COMMAND ----------

// less than 0.5% of quotations without elementary price type

val tc1Threshold = 0.005

val results = spark.sql(s"""
  select
    count(1) as quot_without,
    (select count(1) from $dbName.fact_quotation) as quot_total,
    quot_without / quot_total as percentage
  from $dbName.fact_quotation quot
    left outer join $dbName.fact_elementary_price price on price.quotation_id = quot.quotation_id
  where price.elementary_price_id is null
    and quot.load_date > to_date($minTime)
""").first().toString.replace("[","").replace("]","").split(",")

val (failCount, totalCount, failPercent) = (results(0).toLong, results(1).toLong, results(2).toFloat)

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 2 : Percent of quotations without elementary price type",
  failPercent < tc1Threshold,
  failCount,
  totalCount,
  failPercent,
  if ( failPercent > tc1Threshold) f"Percent of quotations without elementary price type : ${failPercent*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ###test3

// COMMAND ----------

// less than 0.5% of PNR without traveler

val tc1Threshold = 0.005

val results = spark.sql(s"""
  select
    count(1) as pnr_without,
    (select count(1) from $dbName.fact_reservation) as pnr_total,
    pnr_without / pnr_total as percentage
  from $dbName.fact_reservation res
    left outer join $dbName.fact_traveler trv on trv.reservation_id=res.reservation_id
  where trv.reservation_id is null
    and res.load_date > to_date($minTime)
""").first().toString.replace("[","").replace("]","").split(",")

val (failCount, totalCount, failPercent) = (results(0).toLong, results(1).toLong, results(2).toFloat)

testRecords += TestRecord(
  domain,
  domain_version,
  customer,
  phase,
  currentTimestamp,
  task,
  "Test 3 : Percent of reservations without traveler",
  failPercent < tc1Threshold,
  failCount,
  totalCount,
  failPercent,
  if ( failPercent > tc1Threshold) f"Percent of reservations without traveler : ${failPercent*100}%.1f%%" else ""
)

// COMMAND ----------

// MAGIC %md
// MAGIC ##write to DB

// COMMAND ----------

val validationDf = testRecords.toDF()
validationDf.withColumn("fail_ratio", $"fail_ratio".cast("Decimal(3,2)"))
display(validationDf)

// COMMAND ----------

validationDf.write.insertInto(s"$valDatabase.$valTableName")