{
   "defaultComment" : "A coment here"
   "partition-spec" : {
          "key" : "PNR_CREATION_DATE",
          "column-name": "PART_PNR_CREATION_MONTH",
          "expr": "date_format(PNR_CREATION_DATE, \"yyyy-MM\")"
    },
  "tables": [
    {
        "name": "FACT_RESERVATION_HISTO",
        "mapping" : {
          "merge": {
            "key-columns": ["RESERVATION_ID", "VERSION"],
            "if-dupe-take-higher": ["LOAD_DATE"]
          },
          "root-sources": [{"blocks": [{"base": "$.mainResource.current.image"}]}],
          "columns": [
            {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
            {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.id"}]}},
            {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.reference"}]}},
            {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
            {"name": "NIP", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.nip"}]}},
            {"name": "GROUP_SIZE", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.size"}]}},
            {"name": "GROUP_NAME", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.group.name"}]}},
            {"name": "GROUP_SIZE_TAKEN", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.group.sizeTaken"}]}},
            {"name": "POINT_OF_SALE_OWNER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.owner.office.id"}, {"base":"$.owner.office.iataNumber"}, {"base":"$.owner.office.systemCode"}, {"base":"$.owner.office.agentType"}, {"base":"$.owner.login.cityCode"}, {"base":"$.owner.login.countryCode"}, {"base":"$.owner.login.numericSign"}, {"base":"$.owner.login.initials"}, {"base":"$.owner.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "POINT_OF_SALE_CREATION_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.creation.pointOfSale.office.id"}, {"base": "$.creation.pointOfSale.office.iataNumber"}, {"base": "$.creation.pointOfSale.office.systemCode"}, {"base": "$.creation.pointOfSale.office.agentType"}, {"base": "$.creation.pointOfSale.login.cityCode"}, {"base": "$.creation.pointOfSale.login.countryCode"}, {"base": "$.creation.pointOfSale.login.numericSign"}, {"base": "$.creation.pointOfSale.login.initials"}, {"base": "$.creation.pointOfSale.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "POINT_OF_SALE_LAST_UPDATE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"base": "$.lastModification.pointOfSale.office.id"}, {"base": "$.lastModification.pointOfSale.office.iataNumber"}, {"base": "$.lastModification.pointOfSale.office.systemCode"}, {"base": "$.lastModification.pointOfSale.office.agentType"}, {"base": "$.lastModification.pointOfSale.login.cityCode"}, {"base": "$.lastModification.pointOfSale.login.countryCode"}, {"base": "$.lastModification.pointOfSale.login.numericSign"}, {"base": "$.lastModification.pointOfSale.login.initials"}, {"base": "$.lastModification.pointOfSale.login.dutyCode"}]},"expr": "hashM({0})"},
            {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}]}},
            {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
            {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
            {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
          ],
          "pit": {
            "type": "master-pit-table",
            "master" : {
              "pit-key": "RESERVATION_ID",
              "pit-version": "VERSION",
              "valid-from": "DATE_BEGIN",
              "valid-to": "DATE_END",
              "is-last": "IS_LAST_VERSION"
            }
          },
          "description": {
            "description": "It contains information related to the booking on PNR-level.",
            "granularity": "1 PNR"
          }
        },
    },
    {
      "name": "FACT_AIR_SEGMENT_PAX_HISTO",
      "mapping" : {
        "merge": {
          "key-columns": ["AIR_SEGMENT_PAX_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [{ "blocks": [
          {"all": "$"},
          {"base": "$.mainResource.current.image"},
          {"prod": "$.products[?(@.subType == 'AIR')]"},
          {"tr": "$.travelers[*]"}
        ]}],
        "columns": [
          {"name": "AIR_SEGMENT_PAX_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}, "expr":  "hashM({0})"},
          {"name": "REFERENCE_KEY", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.id"}, {"tr": "$.id"}]}},
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "sources":{"blocks": [{"root": "$.mainResource.current.image.id"}]}, "expr": "hashM({0})"},
          {"name": "TRAVELER_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"tr": "$.id"}]}, "expr": "hashM({0})"},
          {
            "name": "SEGMENT_SCHEDULE_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [
              {"prod": "$.airSegment.marketing.flightDesignator.*"},
              {"prod": "$.airSegment.operating.flightDesignator.*"},
              {"prod": "$.airSegment.departure.iataCode"},
              {"prod": "$.airSegment.arrival.iataCode"},
              {"prod": "$.airSegment.departure.localDateTime"},
              {"prod": "$.airSegment.arrival.localDateTime"},
              {"prod": "$.airSegment.departure.terminal"},
              {"prod": "$.airSegment.arrival.terminal"},
              {"prod": "$.airSegment.operating.codeshareAgreement"},
              {"prod": "$.airSegment.aircraft.aircraftType"}
            ]},
            "expr": "hashM({0})"
          },
          {"name": "CABIN_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.cabin.code"}]}, "expr": "hashXS({0})"},
          {"name": "BOOKING_CLASS_CODE_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.code"}]}, "expr": "hashXS({0})"},
          {"name": "BOOKING_STATUS_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.airSegment.bookingStatusCode"}]}, "expr": "hashXS({0})"},
          {
            "name": "POINT_OF_SALE_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [
              {"prod": "$.airSegment.creation.pointOfSale.office.*"},
              {"prod": "$.airSegment.creation.pointOfSale.login.*"},
            ]},
            "expr": "hashM({0})"
          },
          { // ADDED one to showcase access to different json sections
            "name": "FIRST_NAME", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.travelers[?(@.id=='{TRAVELER_ID}')].names[*].firstName"}]},
            "has-variable": true
          },
          {"name": "CABIN_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.cabin.code"}]}},
          {"name": "BOOKING_CLASS_CODE", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.marketing.bookingClass.code"}]}},
          {"name": "BOOKING_STATUS", "column-type": "strColumn", "sources": {"blocks": [{"prod": "$.airSegment.bookingStatusCode"}]}},
          {"name": "IS_INFO", "column-type": "booleanColumn", "sources": {"blocks": [{"prod": "$.airSegment.isInformational"}]}},
          {"name": "PRODUCT_ID", "column-type": "binaryStrColumn", "sources": {"blocks": [{"prod": "$.id"}]}}, // ADDED one for filtering the delivery
          {
            "name": "PDI_NOREC", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isNoRec"}]},
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_GOSHOW", "column-type": "strColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].isGoShow"}]},
            "expr": "substring_index({0}, '-', 1)",
            "has-variable": true
          },
          {
            "name": "PDI_DISTRIBUTION_ID", "column-type": "binaryStrColumn",
            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].distributionId"}]},
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {
            "name": "PDI_TRANSFER_FLAG", "column-type": "strColumn",

            "sources": {"blocks": [{"root" : "$.mainResource.current.image.products[?(@.id=='{PRODUCT_ID}')].airSegment.deliveries[?(@.traveler.id=='{TRAVELER_ID}')].transferFlag"}]},
            "expr": "substring_index({0}, '-', 3)",
            "has-variable": true
          },
          {"name": "DEPARTURE_DATE_TIME", "column-type": "timestampColumn", "sources": {"blocks": [{"prod": "$.airSegment.departure.localDateTime"}]}},
          {"name": "RECORD_LOCATOR", "column-type": "strColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.reference"}]}},
          {"name": "PNR_CREATION_DATE", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.creation.dateTime"}]}},
          {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"root" : "$.mainResource.current.image.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains information related to a Passenger-Segment, with DCS delivery info.",
          "granularity": "1 PAX-SEG"
        }
      },
    },
    {
      "name": "INTERNAL_ASSO_SEATING_PAX_COUPON_HISTO", //PNR-TKTEMD 4/5
      "zorder-columns": ["INTERNAL_ZORDER"],
      "mapping": {
        "merge": {
          "key-columns": ["INTERNAL_ZORDER", "SEATING_PAX_ID", "INTERNAL_CORR_ID", "VERSION"],
          "if-dupe-take-higher": ["LOAD_DATE"]
        },
        "root-sources": [
          {"name": "basePnr", "rs": {"blocks": [
            {"base": "$.mainResource.current.image"},  // {"doc": "$.documents[?(@.documentType in ['ETICKET','EMD','EMD_ASSOCIATED'])]"}
            {
              "cartesian": [
                [{"ref": "$.ticketingReferences[*]"},
                  {"cartesian": [
                    [{"doc": "$.documents[?(@.documentType in ['EMD', 'EMD_ASSOCIATED'])]"}],
                    [{"prd": "$.products[*]"}, {"shouldNotBeEmpty2": "$.id"}]
                  ]}
                ]
              ]
            }
          ]}},
          {"name": "associatedPnrs", "rs": {"blocks": [
            {"base": "$.mainResource.current.image"},
            {
              "cartesian": [
                [{"assPnr": "$.associatedPnrs[*]"}, {"creation": "$.creation"}],
                [{"ref": "$.ticketingReferences[*]"},
                  {"cartesian": [
                    [{"doc": "$.documents[?(@.documentType in ['EMD', 'EMD_ASSOCIATED'])]"}],
                    [{"prd": "$.products[*]"}, {"shouldNotBeEmpty2": "$.id"}]
                  ]}
                ]
              ]
            }
          ]}},
          {"name": "transferredPnrs", "rs": {"blocks": [
            {"base": "$.mainResource.current.image"},
            {
              "cartesian": [
                [{"trans": "$.flightTransfer"}, {"assPnr": "$.associatedOrder"}, {"creation": "$.creation"}],
                [{"ref": "$.ticketingReferences[*]"},
                  {"cartesian": [
                    [{"doc": "$.documents[?(@.documentType in ['EMD', 'EMD_ASSOCIATED'])]"}],
                    [{"prd": "$.products[*]"}, {"shouldNotBeEmpty2": "$.id"}]
                  ]}
                ]
              ]
            }
          ]}}
        ]
        "columns": [
          {"name": "INTERNAL_ZORDER", "column-type": "strColumn", "sources": {"blocks": [{"base": "$.creation.dateTime"}, {"base": "$.reference"}]}},
          {"name": "TICKET_PRODUCT_IDENTIFIER", "column-type": "strColumn", "sources": {"blocks": [{"prd": "$.id"}]}},
          {"name": "SEATING_PAX_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"prd": "$.id"}, {"ref": "$.traveler.id"}]}, "expr": "hashM({0})"},
          {"name": "INTERNAL_CORR_ID", "column-type": "binaryStrColumn", "is-mandatory": "true",
            "sources": {"root-specific": [
              {"rs-name": "basePnr", "blocks": [{"base": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.localDateTime"}]},
              {"rs-name": "associatedPnrs", "blocks": [{"assPnr": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.localDateTime"}]},
              {"rs-name": "transferredPnrs", "blocks": [{"assPnr": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.localDateTime"}]}
            ]}, "has-variable": true, "expr": "hashM({0})"
          },
          {"name": "RESERVATION_ID", "column-type": "binaryStrColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}, "expr": "hashM({0})"},
          {"name": "REFERENCE_KEY_RESERVATION", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"base": "$.id"}]}},
          {"name": "REFERENCE_KEY_SEATING_PAX", "column-type": "strColumn", "is-mandatory": "true", "sources": {"blocks": [{"prd": "$.id"}, {"ref": "$.traveler.id"}]}},
          {"name": "INTERNAL_CORR_IDENTIFIER", "column-type": "binaryStrColumn", "is-mandatory": "true",
            "sources": {"root-specific": [
              {"rs-name": "basePnr", "blocks": [{"base": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.localDateTime"}]},
              {"rs-name": "associatedPnrs", "blocks": [{"assPnr": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.localDateTime"}]},
              {"rs-name": "transferredPnrs", "blocks": [{"assPnr": "$.reference"}, {"doc": "$.primaryDocumentNumber"}, {"doc": "$.documentNumber"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.arrival.iataCode"}, {"base": "$.products[?(@.products[*].id anyof ['{TICKET_PRODUCT_IDENTIFIER}'])].airSegment.departure.localDateTime"}]}
            ]}, "has-variable": true
          },
          {"name": "RELATED_TRAVEL_DOCUMENT_TYPE", "column-type": "strColumn","sources": {"blocks": [{"doc": "$.documentType"}]}        },
          //col used to filter only air_segment_pax_id corresponding to the product listed in the ticketReference document coupon
          //COLUMN USING ANOTHER COLUMN as VARIABLE - level 1
          {"name": "TECH_RELATED_PRODUCT_IDENTIFIER_FOR_FILTERING_PURPOSE", "column-type": "strColumn","is-mandatory": "true",
            "sources": {"blocks": [{"base": "$.products[?(@.id=='{TICKET_PRODUCT_IDENTIFIER}' && @.subType == 'SEATING')].id"}]},
            "has-variable": true},
          //COLUMN USING ANOTHER COLUMN as VARIABLE - level 2
          {"name": "AIR_SEGMENT_IDENTIFIER", "column-type": "strColumn",
            "sources": {"blocks": [{"base": "$.products[?(@.subType == 'AIR' && @.products[*].id anyof ['{TECH_RELATED_PRODUCT_IDENTIFIER_FOR_FILTERING_PURPOSE}']  )].id"}]},
            "has-variable": true}
          //COLUMN USING ANOTHER COLUMN as VARIABLE - level 3
          {"name": "AIR_SEGMENT_FLIGHTDESIGNATOR", "column-type": "strColumn",
            "sources": {"blocks": [{"base": "$.products[?(@.id == '{AIR_SEGMENT_IDENTIFIER}'  )].airSegment.marketing.flightDesignator.*"}]},
            "has-variable": true}
          {"name": "VERSION", "column-type": "intColumn", "sources": {"blocks": [{"base": "$.version"}]}},
          {"name": "DATE_BEGIN", "column-type": "timestampColumn", "sources": {"blocks": [{"base": "$.lastModification.dateTime"}]}},
          {"name": "DATE_END", "column-type": "timestampColumn", "sources": {}},
          {"name": "IS_LAST_VERSION", "column-type": "booleanColumn", "sources": {}}
        ],
        "pit": {
          "type": "secondary-pit-table"
        },
        "description": {
          "description": "It contains SERVICE_PAX-COUPON correlation information as seen by PNR.",
          "granularity": "1 SERVICE_PAX-COUPON",
          "links": ["???"]
        }
      }
    }

  ],
  "links": [ ]
}